#!/usr/bin/env python3
"""
Health check script for International Trading System
"""
import json
import sys
from pathlib import Path
from datetime import datetime, timedelta

def check_system_health():
    """Perform comprehensive system health check."""
    health_status = {
        'timestamp': datetime.now().isoformat(),
        'status': 'healthy',
        'checks': {}
    }
    
    # Check if recent data exists
    international_data_dir = Path('international_data')
    if international_data_dir.exists():
        recent_files = list(international_data_dir.rglob('*.csv'))
        health_status['checks']['data_files'] = {
            'status': 'ok' if recent_files else 'warning',
            'file_count': len(recent_files)
        }
    else:
        health_status['checks']['data_files'] = {
            'status': 'error',
            'message': 'International data directory missing'
        }
        health_status['status'] = 'unhealthy'
    
    # Check model files
    models_dir = Path('models/enhanced')
    if models_dir.exists():
        model_files = list(models_dir.glob('*.pkl')) + list(models_dir.glob('*.h5'))
        health_status['checks']['models'] = {
            'status': 'ok' if model_files else 'warning',
            'model_count': len(model_files)
        }
    else:
        health_status['checks']['models'] = {
            'status': 'warning',
            'message': 'No trained models found'
        }
    
    # Check configuration
    config_file = Path('config/production_international_markets_config.json')
    health_status['checks']['configuration'] = {
        'status': 'ok' if config_file.exists() else 'error',
        'config_exists': config_file.exists()
    }
    
    if not config_file.exists():
        health_status['status'] = 'unhealthy'
    
    # Save health check result
    health_file = Path('monitoring/health_checks') / f"health_check_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    with open(health_file, 'w') as f:
        json.dump(health_status, f, indent=2)
    
    print(f"Health Status: {health_status['status']}")
    return health_status['status'] == 'healthy'

if __name__ == '__main__':
    sys.exit(0 if check_system_health() else 1)
