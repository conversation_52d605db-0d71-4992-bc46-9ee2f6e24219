#!/usr/bin/env python3
"""
本番環境での取引実行スクリプト

継続学習を行いながら本番環境での取引を実行します。
AIの予測精度に基づいて自律的にモデルが改善され、長期間にわたって稼働します。
"""

import argparse
import logging
import os
import json
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import time
import signal
import sys

from src.trading.production.production_trader import ProductionTrader
from src.trading.auto_trader.trading_modes import TradingMode
from src.utils.config_manager import ConfigManager
from src.analytics.performance_analyzer import PerformanceAnalyzer
from src.feature_engineering import add_features
from src.data_collection_helper import fetch_stock_data

# ロギング設定
log_dir = 'logs'
os.makedirs(log_dir, exist_ok=True)
log_file = os.path.join(log_dir, f'production_trader_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log')

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_file),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# グローバル変数
running = True

def signal_handler(sig, frame):
    """終了シグナルハンドラ"""
    global running
    logger.info("終了シグナルを受信しました。完了までお待ちください...")
    running = False


class ProductionTradingSystem:
    """本番取引システム"""

    def __init__(self, config):
        """
        初期化

        Parameters:
        -----------
        config : dict
            設定情報
        """
        self.config = config

        # 結果ディレクトリ
        self.results_dir = config.get("results_dir", "results/production")
        os.makedirs(self.results_dir, exist_ok=True)

        # 本番トレーダー作成
        self.trader = ProductionTrader(
            initial_balance=config.get("initial_balance", 1000000),
            commission_rate=config.get("commission_rate", 0.0001),
            max_positions=config.get("max_positions", 5),
            model_update_interval=config.get("model_update_interval", 24),
            accuracy_threshold=config.get("accuracy_threshold", 0.6),
            learning_rate=config.get("learning_rate", 0.001)
        )

        # パフォーマンス分析
        self.analyzer = PerformanceAnalyzer(results_dir=self.results_dir)

        # ティッカーリスト
        self.tickers = config.get("tickers", [
            "7203.T", "9984.T", "6758.T", "6861.T", "8306.T",
            "9432.T", "9433.T", "6501.T", "6502.T", "7267.T"
        ])

        # キャッシュ使用フラグ
        self.use_cache = config.get("use_cache", True)

        # 取引間隔（分）
        self.trading_interval = config.get("trading_interval", 30)

        # 学習間隔（時間）
        self.learning_interval = config.get("learning_interval", 6)

        # 最終取引・学習時刻
        self.last_trading_time = None
        self.last_learning_time = None

        # 統計情報
        self.trading_count = 0
        self.learning_count = 0

        logger.info(f"ProductionTradingSystem initialized with {len(self.tickers)} tickers")
        logger.info(f"Initial balance: {self.trader.initial_balance:,.0f} JPY")
        logger.info(f"Trading interval: {self.trading_interval} minutes")
        logger.info(f"Learning interval: {self.learning_interval} hours")

    def fetch_market_data(self):
        """
        市場データの取得

        Returns:
        --------
        pd.DataFrame
            取得したデータ
        """
        logger.info("Fetching market data...")

        # 日付範囲設定
        end_date = datetime.now().strftime("%Y-%m-%d")
        start_date = (datetime.now() - timedelta(days=self.config.get("data_days", 30))).strftime("%Y-%m-%d")
        interval = self.config.get("data_interval", "1d")

        # キャッシュ使用設定
        use_cache_param = self.use_cache

        # API制限を考慮
        # 前回の取引からの経過時間が短い場合はキャッシュ強制使用
        if self.last_trading_time is not None:
            seconds_since_last = (datetime.now() - self.last_trading_time).total_seconds()
            if seconds_since_last < 60:  # 1分以内の連続実行
                use_cache_param = True
                logger.info(f"API制限回避のためキャッシュを強制使用: 前回実行から{seconds_since_last:.1f}秒")

        # データ取得
        try:
            df = fetch_stock_data(
                self.tickers, 
                start_date, 
                end_date, 
                interval, 
                use_cache=use_cache_param, 
                use_random_fallback=True  # エラー時はランダムデータ使用
            )
            logger.info(f"Fetched data: {len(df)} rows")

            if df.empty:
                logger.warning("No data fetched, using random data instead")
                # ランダムデータ生成
                df = fetch_stock_data(
                    self.tickers, 
                    start_date, 
                    end_date, 
                    interval, 
                    use_random_fallback=True,
                    force_cache=False
                )
                logger.info(f"Generated random data: {len(df)} rows")
                
            return df
        except Exception as e:
            logger.error(f"Error fetching market data: {str(e)}")
            # エラー時でもランダムデータ生成
            try:
                logger.info("Falling back to random data generation")
                df = fetch_stock_data(
                    self.tickers, 
                    start_date, 
                    end_date, 
                    interval, 
                    use_random_fallback=True,
                    force_cache=False
                )
                logger.info(f"Generated random data: {len(df)} rows")
                return df
            except Exception as e2:
                logger.error(f"Failed to generate random data: {str(e2)}")
                return None

    def prepare_data(self, df):
        """
        データ準備（特徴量エンジニアリング）

        Parameters:
        -----------
        df : pd.DataFrame
            元データ

        Returns:
        --------
        pd.DataFrame
            特徴量追加後のデータ
        """
        logger.info("Preparing data...")

        try:
            # 特徴量追加
            features_df = add_features(df)
            logger.info(f"Added features: {len([c for c in features_df.columns if c not in df.columns])} new columns")

            return features_df
        except Exception as e:
            logger.error(f"Error preparing data: {str(e)}")
            return df  # エラー時は元のデータを返す

    def generate_signals(self, df):
        """
        取引シグナル生成

        Parameters:
        -----------
        df : pd.DataFrame
            特徴量データ

        Returns:
        --------
        pd.DataFrame
            推奨銘柄のデータフレーム
        """
        logger.info("Generating trading signals...")

        # 最新データを取得
        latest_data = []

        # ティッカーごとの取引シグナル生成
        for ticker in self.tickers:
            ticker_df = df[df["Ticker"] == ticker].copy() if "Ticker" in df.columns else df.copy()

            if ticker_df.empty:
                logger.warning(f"No data for {ticker}")
                continue

            # 最新のデータ行
            latest = ticker_df.iloc[-1].copy()

            # 特徴量を使った予測（実際にはここでモデルを使用）
            # 現在はシンプルなルールベースのスコアリング
            score = 0

            # トレンド指標
            if "SMA_5" in latest and "SMA_20" in latest:
                if latest["SMA_5"] > latest["SMA_20"]:
                    score += 1
                else:
                    score -= 1

            # RSI指標
            if "RSI_14" in latest:
                if latest["RSI_14"] < 30:  # 買われすぎ
                    score += 2
                elif latest["RSI_14"] > 70:  # 売られすぎ
                    score -= 2

            # MACD指標
            if "MACD" in latest and "MACD_Signal" in latest:
                if latest["MACD"] > latest["MACD_Signal"]:
                    score += 1
                else:
                    score -= 1

            # ボリンジャーバンド
            if "BB_Lower" in latest and "Close" in latest and "BB_Upper" in latest:
                if latest["Close"] < latest["BB_Lower"]:  # 下限を下回る（買い）
                    score += 2
                elif latest["Close"] > latest["BB_Upper"]:  # 上限を上回る（売り）
                    score -= 2

            # 予測変化率（-5%〜+5%のランダム値）
            predicted_change = score / 5 * 0.05

            # データフレーム用のデータ
            latest_dict = {
                'Ticker': ticker,
                'Current_Price': latest['Close'],
                'Predicted_Change_Pct': predicted_change,
                'Score': score / 5,  # -1.0〜+1.0に正規化
                'Timestamp': datetime.now()
            }

            # テスト用に予測を記録
            self.trader.record_prediction(ticker, predicted_change)

            latest_data.append(latest_dict)

        # DataFrameに変換
        signals_df = pd.DataFrame(latest_data)

        # スコア降順でソート
        if not signals_df.empty:
            signals_df = signals_df.sort_values('Score', ascending=False)

        logger.info(f"Generated signals for {len(signals_df)} tickers")
        return signals_df

    def run_trading_cycle(self):
        """
        取引サイクルを実行

        Returns:
        --------
        bool
            取引が実行された場合True
        """
        logger.info("取引サイクルを実行します")

        try:
            # 市場データ取得
            market_data = self.fetch_market_data()
            if market_data is None or market_data.empty:
                logger.error("市場データの取得に失敗しました。取引をスキップします。")
                return False

            # データ準備
            processed_data = self.prepare_data(market_data)

            # シグナル生成
            signals = self.generate_signals(processed_data)
            if signals.empty:
                logger.error("取引シグナルの生成に失敗しました。取引をスキップします。")
                return False

            # トレーダーに推奨銘柄を渡して取引実行
            trades_df = self.trader.execute_trades(signals)

            # 取引結果の表示
            if trades_df is not None and not trades_df.empty:
                logger.info(f"取引数: {len(trades_df)}")
                # 買い注文と売り注文の数
                buy_count = len(trades_df[trades_df['action'] == 'BUY'])
                sell_count = len(trades_df[trades_df['action'] == 'SELL'])
                logger.info(f"買い注文: {buy_count} 売り注文: {sell_count}")

                # 損益
                if 'profit' in trades_df.columns:
                    total_profit = trades_df['profit'].sum()
                    logger.info(f"取引による損益: {total_profit:.2f} JPY")
            else:
                logger.info("取引はありませんでした")

            # ポートフォリオの表示
            portfolio_summary = self.trader.get_portfolio_summary()
            logger.info(f"残高: {portfolio_summary['balance']:,.2f} JPY")
            logger.info(f"ポートフォリオ価値: {portfolio_summary['portfolio_value']:,.2f} JPY")
            logger.info(f"損益: {portfolio_summary['profit_loss']:,.2f} JPY ({portfolio_summary['profit_loss_pct']:.2f}%)")
            logger.info(f"保有銘柄数: {len(self.trader.portfolio)}")

            # 結果を保存
            self.trader.save_production_results()

            # 統計の更新
            self.trading_count += 1
            self.last_trading_time = datetime.now()

            return True

        except Exception as e:
            logger.error(f"取引サイクル中にエラーが発生しました: {e}")
            import traceback
            logger.error(traceback.format_exc())
            return False

    def run_learning_cycle(self):
        """
        学習サイクルを実行

        Returns:
        --------
        bool
            学習が実行された場合True
        """
        # 学習間隔をチェック
        if self.last_learning_time is not None:
            hours_since_last = (datetime.now() - self.last_learning_time).total_seconds() / 3600
            if hours_since_last < self.learning_interval:
                logger.info(f"前回の学習から{hours_since_last:.1f}時間しか経過していないため、学習をスキップします")
                return False

        logger.info("学習サイクルを実行します")

        try:
            # 継続学習サイクルを実行
            updated = self.trader.run_continuous_learning_cycle()

            # 統計の更新
            if updated:
                self.learning_count += 1
                logger.info(f"モデルが更新されました (更新回数: {self.learning_count})")
            else:
                logger.info("モデル更新の必要はありませんでした")

            self.last_learning_time = datetime.now()

            # 日次レポートの生成
            report = self.analyzer.generate_daily_report()

            return updated

        except Exception as e:
            logger.error(f"学習サイクル中にエラーが発生しました: {e}")
            import traceback
            logger.error(traceback.format_exc())
            return False

    def run_continuous_system(self):
        """
        継続的な取引・学習システムを実行
        """
        global running

        logger.info("=" * 70)
        logger.info("継続的本番取引システムを開始します")
        logger.info("=" * 70)

        # 次の実行予定時刻
        next_trading_time = datetime.now()
        next_learning_time = datetime.now() + timedelta(hours=1)  # 1時間後に最初の学習

        try:
            # メインループ
            while running:
                now = datetime.now()

                # 取引実行タイミング
                if now >= next_trading_time:
                    is_market_hours = self._is_market_hours()

                    if is_market_hours or self.config.get("trade_outside_market", False):
                        self.run_trading_cycle()
                    else:
                        logger.info("市場時間外のため取引をスキップします")

                    # 次回の取引時刻を設定（市場時間内ならより頻繁に）
                    if is_market_hours:
                        next_trading_time = now + timedelta(minutes=self.trading_interval)
                    else:
                        # 市場時間外は頻度を落とす
                        next_trading_time = now + timedelta(minutes=self.trading_interval * 3)

                    logger.info(f"次回取引予定時刻: {next_trading_time.strftime('%Y-%m-%d %H:%M:%S')}")

                # 学習実行タイミング
                if now >= next_learning_time:
                    # 市場時間外の方が学習に適している
                    is_market_hours = self._is_market_hours()
                    should_learn = not is_market_hours or self.config.get("learn_during_market", False)

                    if should_learn:
                        self.run_learning_cycle()
                    else:
                        logger.info("市場時間中のため学習を延期します")

                    # 次回の学習時刻を設定
                    next_learning_time = now + timedelta(hours=self.learning_interval)
                    logger.info(f"次回学習予定時刻: {next_learning_time.strftime('%Y-%m-%d %H:%M:%S')}")

                # 日次パフォーマンスレポートの生成（0時に近いタイミング）
                current_hour = now.hour
                if current_hour == 23 and now.minute >= 55 and self.last_trading_time is not None:
                    # 直近の取引から5分以上経過している場合のみ実行
                    if (now - self.last_trading_time).total_seconds() >= 300:
                        logger.info("日次パフォーマンスレポートを生成します")
                        report = self.analyzer.generate_daily_report()

                # スリープ（30秒ごとにチェック）
                for _ in range(6):  # 30秒 = 5秒 x 6
                    if not running:
                        break
                    time.sleep(5)

            # 終了処理
            logger.info("システムの終了処理を実行します")
            self.trader.save_production_results("final")
            self.analyzer.generate_summary_report(days=30)

            logger.info("=" * 70)
            logger.info("継続的本番取引システムを終了しました")
            logger.info("=" * 70)

            return True

        except KeyboardInterrupt:
            logger.info("ユーザーによる中断を検出しました")
            return False

        except Exception as e:
            logger.error(f"システム実行中に致命的なエラーが発生しました: {e}")
            import traceback
            logger.error(traceback.format_exc())
            return False

    def _is_market_hours(self):
        """
        現在が市場時間内かどうかを判定

        Returns:
        --------
        bool
            市場時間内の場合True
        """
        now = datetime.now()

        # 土日はスキップ
        if now.weekday() >= 5:  # 5=土曜日, 6=日曜日
            return False

        # 市場時間（9:00-15:00）
        market_start = now.replace(hour=9, minute=0, second=0, microsecond=0)
        market_end = now.replace(hour=15, minute=0, second=0, microsecond=0)

        return market_start <= now <= market_end

def main():
    """メイン関数"""
    parser = argparse.ArgumentParser(description="本番環境での取引実行（継続学習あり）")
    parser.add_argument("--config", type=str, default="config/production_trading_config.json",
                       help="設定ファイルのパス")
    parser.add_argument("--balance", type=float, default=1000000,
                       help="初期資金")
    parser.add_argument("--commission", type=float, default=0.0001,
                       help="取引手数料率")
    parser.add_argument("--days", type=int, default=30,
                       help="データ日数")
    parser.add_argument("--no-cache", action="store_true",
                       help="キャッシュを使用しない")
    parser.add_argument("--no-learning", action="store_true",
                       help="継続学習を無効にする")
    parser.add_argument("--trade-interval", type=int, default=30,
                       help="取引間隔（分）")
    parser.add_argument("--learning-interval", type=int, default=6,
                       help="学習間隔（時間）")
    args = parser.parse_args()

    # 設定ファイルの読み込み
    config = {
        "initial_balance": args.balance,
        "commission_rate": args.commission,
        "data_days": args.days,
        "results_dir": "results/production",
        "use_cache": not args.no_cache,
        "learning_enabled": not args.no_learning,
        "trading_interval": args.trade_interval,
        "learning_interval": args.learning_interval,
        "trade_outside_market": False,  # 市場時間外での取引を無効
        "learn_during_market": False,   # 市場時間中の学習を無効
        "model_update_interval": 24,    # 24時間ごとにモデル更新を検討
        "accuracy_threshold": 0.6,      # 精度が60%を下回ったらモデルを更新
        "learning_rate": 0.001,
        "tickers": [
            "7203.T", "9984.T", "6758.T", "6861.T", "8306.T",
            "9432.T", "9433.T", "6501.T", "6502.T", "7267.T",
            "8316.T", "4063.T", "6752.T", "4519.T", "4568.T"
        ]
    }

    # 設定ファイルの読み込み（存在すれば）
    if os.path.exists(args.config):
        try:
            with open(args.config, 'r') as f:
                file_config = json.load(f)
                config.update(file_config)
        except Exception as e:
            logger.error(f"設定ファイルの読み込みエラー: {e}")
    else:
        # 設定ファイルが存在しない場合は作成
        os.makedirs(os.path.dirname(args.config), exist_ok=True)
        try:
            with open(args.config, 'w') as f:
                json.dump(config, f, indent=2)
            logger.info(f"デフォルト設定ファイルを作成しました: {args.config}")
        except Exception as e:
            logger.error(f"設定ファイルの作成エラー: {e}")

    # シグナルハンドラを設定
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)

    # 学習の有効/無効表示
    learning_status = "有効" if not args.no_learning else "無効"
    logger.info(f"継続学習: {learning_status}")

    # 本番取引システム実行
    system = ProductionTradingSystem(config)
    system.run_continuous_system()

if __name__ == "__main__":
    main()
