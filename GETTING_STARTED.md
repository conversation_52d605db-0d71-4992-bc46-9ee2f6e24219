# 🚀 Getting Started with Nikkei225 AI Trading System

## 📋 Prerequisites

Before you begin, ensure you have the following:

### System Requirements
- **Operating System**: Linux, macOS, or Windows
- **Python**: 3.8 or higher (Python 3.12 recommended)
- **Memory**: 16GB RAM or more
- **Storage**: 10GB free space or more
- **GPU**: NVIDIA GPU with CUDA support (recommended for optimal performance)

### Required API Access
- **Kabu Station API**: Japanese stock trading platform
- **Alpha Vantage API**: International market data (optional)

## 🔧 Installation Options

### Option 1: Docker Setup (Recommended)

The fastest way to get started is using Docker. No manual Python setup required!

#### Development Setup (CPU-only)
```bash
# Clone the repository
git clone https://github.com/BB-Citron/kabu-Station_AI.git
cd kabu-Station_AI

# Copy environment file
cp .env.example .env
# Edit .env with your settings

# Start the system
docker-compose up -d

# Access web dashboard
open http://localhost:5001
```

#### Production Setup (GPU-accelerated)
For production trading with AI acceleration:
```bash
# Prerequisites: NVIDIA GPU + Docker GPU toolkit
# See DOCKER_SETUP.md for GPU setup

# Start GPU-accelerated system
docker-compose -f docker-compose.gpu.yml up -d
```

### Option 2: Manual Python Installation

#### Using Virtual Environment
```bash
# Clone the repository
git clone https://github.com/BB-Citron/kabu-Station_AI.git
cd kabu-Station_AI

# Create virtual environment
python -m venv venv

# Activate virtual environment
# On Linux/macOS:
source venv/bin/activate
# On Windows:
venv\Scripts\activate

# Install dependencies
pip install -r requirements.txt
```

#### Using Conda
```bash
# Create conda environment
conda create -n nikkei225-ai python=3.12
conda activate nikkei225-ai

# Install dependencies
pip install -r requirements.txt
```

### 3. Configure Environment Variables

Create a `.env` file in the project root:

```bash
# Copy template
cp .env.template .env

# Edit the file with your credentials
nano .env
```

Add your API credentials:
```bash
KABU_API_PWD=your_kabu_station_password
ALPHA_VANTAGE_API_KEY=your_alpha_vantage_key  # Optional
```

### 4. Initialize GPU Support (Optional but Recommended)

```bash
# Test GPU availability
python src/gpu_config.py

# If GPU is available, you should see:
# ✅ GPU detected: NVIDIA GeForce RTX XXXX
# ✅ CUDA version: XX.X
```

## 🏃‍♂️ First Run

### Option A: Quick Docker Start (Easiest)

```bash
# Navigate to production directory
cd production

# Start all services
docker-compose up -d

# Check if services are running
docker-compose ps

# Access web dashboard
open http://localhost:5000
```

### Option B: Manual Start

```bash
# Start the main trading system
python continuous_ai_trader.py --initial-balance 1000000 --max-stocks 5

# In a separate terminal, start the web interface
python src/web/run_web_app.py
```

## 🎛️ Configuration

### Trading Mode Setup

Start with the safest mode for testing:

```bash
# Set to dry run mode (no real trades, data collection only)
./set_kabu_mode.sh dryrun
```

Available modes:
- `nocash`: Algorithm validation without money simulation
- `paper`: Virtual trading with simulated funds
- `dryrun`: Data collection and AI learning without trades
- `live`: Real trading with actual funds ⚠️

### Initial Configuration

Edit configuration files in the `config/` directory:

```bash
# Basic trading configuration
nano config/simple_trading_config.json

# Continuous learning settings
nano config/continuous_learning_config.json
```

## 📊 Monitoring Your System

### Web Dashboard

Once the system is running, access the web dashboard:

```
# Development mode (CPU-only)
http://localhost:5001

# Production mode (GPU-accelerated)  
http://localhost:5001
```

Features available:
- Real-time system status
- Trading performance metrics
- AI model predictions
- Portfolio visualization
- Log monitoring

### Command Line Monitoring

```bash
# Check system logs
tail -f logs/continuous_trader_*.log

# Monitor trading activity
tail -f logs/kabu_system_*.log

# Check AI learning progress
tail -f logs/continuous_learning_*.log
```

## 🧪 Testing Your Setup

### Run System Tests

```bash
# Navigate to development directory
cd development

# Run all tests
python run_tests.py

# Run specific test types
python run_tests.py --type unit
python run_tests.py --type integration
```

### Demo Mode

Test the system with demo data:

```bash
# Run quick demo
python examples/demos/quick_demo.py

# Run learning pipeline demo
python examples/demos/run_learning_pipeline_demo.py
```

## 🔧 Troubleshooting

### Common Issues

#### 1. GPU Not Detected
```bash
# Check NVIDIA drivers
nvidia-smi

# Reinstall CUDA-compatible PyTorch
pip uninstall torch
pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu121
```

#### 2. API Connection Issues
```bash
# Test Kabu Station connection
python api_test.py

# Check environment variables
echo $KABU_API_PWD
```

#### 3. Memory Issues
```bash
# Monitor memory usage
top -p $(pgrep -f continuous_ai_trader)

# Reduce batch size in config files
nano config/simple_trading_config.json
# Set "batch_size": 16 (or lower)
```

#### 4. Docker Issues
```bash
# Check Docker status
docker-compose logs

# For development mode (CPU-only)
docker-compose down
docker-compose build --no-cache
docker-compose up -d

# For production mode (GPU)
docker-compose -f docker-compose.gpu.yml down
docker-compose -f docker-compose.gpu.yml build --no-cache
docker-compose -f docker-compose.gpu.yml up -d
```

### Log Analysis

Check specific log files for detailed error information:

```bash
# System errors
cat logs/errors/errors_$(date +%Y%m%d).jsonl

# Trading issues
cat logs/kabu_system_dryrun_*.log

# Web interface problems
cat logs/web_app_*.log
```

## 📈 Next Steps

### 1. Data Collection Verification

Ensure data is being collected properly:

```bash
# Check data directory
ls -la data/hourly_nikkei225/

# Verify recent data
find data/ -name "*.json" -mtime -1
```

### 2. AI Model Training

Let the system collect data for at least 24 hours, then check model performance:

```bash
# View learning progress
cat logs/continuous_learning_*.log | grep "Model performance"

# Check model files
ls -la models/
```

### 3. Gradual Mode Progression

Progress through trading modes as you gain confidence:

```bash
# After 1 week of successful dryrun
./set_kabu_mode.sh paper

# After successful paper trading
./set_kabu_mode.sh live  # ⚠️ Use real money
```

## 🆘 Getting Help

### Documentation
- **[Docker Setup Guide](DOCKER_SETUP.md)** - Complete Docker configuration guide
- [日本語ドキュメント](README_JA.md)
- [English Documentation](README_EN.md)
- [KabuStation Setup](kabu_station_setup.md) - API configuration guide

### Support Channels
- **GitHub Issues**: Report bugs and request features
- **Discussion Forum**: Community support and questions
- **Email Support**: Direct developer contact

### Community
- Join our Discord server for real-time help
- Follow updates on Twitter: @nikkei225ai
- Subscribe to our newsletter for release announcements

## 🐳 Docker Configuration Summary

### When to Use Each Setup

**🖥️ Development Mode (CPU-only)**
- ✅ Quick testing and development
- ✅ Learning the system features
- ✅ Compatible with any machine (ARM64/AMD64)
- ✅ No GPU drivers required
- ❌ Slower AI model training/inference

**🚀 Production Mode (GPU-accelerated)**  
- ✅ Production trading performance
- ✅ 10-100x faster AI operations
- ✅ Real-time model inference
- ✅ Continuous learning efficiency
- ❌ Requires NVIDIA GPU + drivers
- ❌ Larger resource requirements

### Quick Command Reference
```bash
# Development mode
docker-compose up -d

# Production mode  
docker-compose -f docker-compose.gpu.yml up -d

# Check status
docker-compose ps

# View logs
docker-compose logs -f

# Stop system
docker-compose down
```

---

**⚠️ Important**: Start with `dryrun` mode and thoroughly understand the system before using real funds. Always test extensively in paper trading mode first.