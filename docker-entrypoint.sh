#!/bin/bash
# Docker エントリーポイントスクリプト

set -e

# 環境変数の設定確認
if [ -z "$KABU_API_PWD" ]; then
    echo "警告: KABU_API_PWD環境変数が設定されていません"
fi

# 必要なディレクトリを作成
mkdir -p logs results models data

# 引数に基づいて実行するコマンドを決定
case "$1" in
    "continuous")
        echo "継続的AIトレーダーを開始します..."
        shift
        exec python continuous_ai_trader.py "$@"
        ;;
    "virtual")
        echo "仮想トレーダーを開始します..."
        shift
        exec python src/simple_virtual_trader.py "$@"
        ;;
    "web")
        echo "Webダッシュボードを開始します..."
        shift
        exec python src/web/run_web_app.py "$@"
        ;;
    "kabu")
        echo "KabuStation取引システムを開始します..."
        shift
        exec ./start_kabu_trading.sh "$@"
        ;;
    "international")
        echo "国際取引システムを開始します..."
        shift
        exec python international_trading_system.py "$@"
        ;;
    *)
        echo "使用方法: docker run [OPTIONS] IMAGE [COMMAND] [ARGS...]"
        echo ""
        echo "利用可能なコマンド:"
        echo "  continuous   継続的AIトレーダー"
        echo "  virtual      仮想トレーダー"
        echo "  web          Webダッシュボード"
        echo "  kabu         KabuStation取引システム"
        echo "  international 国際取引システム"
        echo ""
        echo "例:"
        echo "  docker run myimage continuous --initial-balance 1000000"
        echo "  docker run myimage web"
        exit 1
        ;;
esac