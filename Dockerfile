# 日経225 AI取引システム - 最終版 Docker環境
# Multi-platform support (ARM64/AMD64)
FROM python:3.12-slim

# 環境変数設定
ENV DEBIAN_FRONTEND=noninteractive
ENV TZ=Asia/Tokyo
ENV PYTHONUNBUFFERED=1
ENV PYTHONPATH=/app
ENV TF_CPP_MIN_LOG_LEVEL=2
ENV CUDA_VISIBLE_DEVICES=0

# 基本パッケージのインストール
RUN apt-get update && apt-get install -y \
    git \
    curl \
    wget \
    tzdata \
    build-essential \
    libssl-dev \
    libffi-dev \
    libbz2-dev \
    libreadline-dev \
    libsqlite3-dev \
    libncurses5-dev \
    libncursesw5-dev \
    xz-utils \
    tk-dev \
    libxml2-dev \
    libxmlsec1-dev \
    libhdf5-dev \
    pkg-config \
    && rm -rf /var/lib/apt/lists/*

# pipのアップグレード
RUN python3 -m pip install --upgrade pip setuptools wheel

# 作業ディレクトリの設定
WORKDIR /app

# Pythonの依存関係をコピーしてインストール
COPY requirements.txt .
RUN pip3 install --no-cache-dir -r requirements.txt

# CPU版ライブラリ（GPU無しでも動作）
RUN pip3 install --no-cache-dir \
    torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cpu \
    tensorflow

# アプリケーションコードをコピー
COPY . .

# ディレクトリの作成
RUN mkdir -p /app/logs/errors /app/logs/trading /app/data/cache /app/models /app/results

# 実行権限の設定
RUN chmod +x *.sh 2>/dev/null || true

# 環境設定スクリプト
RUN echo '#!/bin/bash' > /app/setup_env.sh && \
    echo 'export TF_ENABLE_ONEDNN_OPTS=0' >> /app/setup_env.sh && \
    echo 'export TF_CPP_MIN_LOG_LEVEL=2' >> /app/setup_env.sh && \
    echo 'python3 -c "import tensorflow as tf; print(f\"TensorFlow {tf.__version__} loaded\")"' >> /app/setup_env.sh && \
    chmod +x /app/setup_env.sh

# エントリーポイントスクリプト作成
RUN cat > /app/entrypoint.sh << 'EOF'
#!/bin/bash
set -e

echo "🚀 日経225 AI取引システム - Docker起動中..."

# 環境初期化
source /app/setup_env.sh

# 環境変数確認
if [ -z "$KABU_API_PWD" ]; then
    echo "⚠️  警告: KABU_API_PWD環境変数が設定されていません"
fi

# 引数に応じた実行
case "$1" in
    "web")
        echo "🖥️  Webダッシュボードを起動中..."
        exec python3 src/web/run_web_app.py
        ;;
    "trading")
        echo "💰 取引システムを起動中..."
        exec python3 -m src.trading.kabu_system
        ;;
    "data")
        echo "📊 データ収集システムを起動中..."
        exec python3 src/data_collector/optimal_data_collector.py
        ;;
    "test")
        echo "🧪 テストを実行中..."
        exec python3 -m pytest tests/ -v
        ;;
    *)
        echo "🔗 統合システムを起動中..."
        # バックグラウンドでデータ収集開始
        python3 src/data_collector/optimal_data_collector.py &
        # Webダッシュボード起動
        exec python3 src/web/run_web_app.py
        ;;
esac
EOF

RUN chmod +x /app/entrypoint.sh

# ヘルスチェック
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD python3 -c "import requests; requests.get('http://localhost:5000/health', timeout=5)" || exit 1

# ポートの公開
EXPOSE 5000 8080

# デフォルトエントリーポイント
ENTRYPOINT ["/app/entrypoint.sh"]
CMD ["web"]
