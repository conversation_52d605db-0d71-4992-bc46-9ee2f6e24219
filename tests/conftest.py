"""
日経225 AI取引システム - テスト共通設定

pytestのフィクスチャとテスト共通設定を定義するファイル
"""

import os
import sys
import pytest
import pandas as pd
import numpy as np
import json
import datetime
from pathlib import Path

# プロジェクトルートを取得
ROOT_DIR = Path(__file__).parent.parent.absolute()
sys.path.insert(0, str(ROOT_DIR))

# パスの設定
TEST_DATA_DIR = os.path.join(ROOT_DIR, "tests", "fixtures", "sample_data")
TEST_CONFIG_DIR = os.path.join(ROOT_DIR, "tests", "fixtures", "test_configs")
MOCK_RESPONSE_DIR = os.path.join(ROOT_DIR, "tests", "fixtures", "mock_responses")

# テスト日付
DEFAULT_TEST_START_DATE = "2024-01-01"
DEFAULT_TEST_END_DATE = "2024-03-31"

# テスト銘柄
DEFAULT_TEST_TICKERS = ["7203.T", "9984.T", "6758.T", "6861.T", "4755.T"]  # トヨタ、ソフトバンク、ソニー、キーエンス、楽天

@pytest.fixture(scope="session")
def test_config():
    """テスト設定を読み込む"""
    config_path = os.path.join(TEST_CONFIG_DIR, "test_config.json")
    
    # 設定ファイルが存在しない場合はデフォルト値
    if not os.path.exists(config_path):
        return {
            "test_mode": True,
            "data_source": "mock",
            "initial_balance": 1000000,
            "max_stocks": 3,
            "test_tickers": DEFAULT_TEST_TICKERS,
            "test_period": f"{DEFAULT_TEST_START_DATE},{DEFAULT_TEST_END_DATE}",
            "mock_server_port": 8888
        }
    
    # 設定ファイル読み込み
    with open(config_path, "r") as f:
        return json.load(f)

@pytest.fixture(scope="session")
def sample_ohlcv_data():
    """サンプルのOHLCVデータを生成"""
    np.random.seed(42)  # 再現性のために乱数シードを固定
    
    # 日付と時間の範囲
    start_date = pd.to_datetime(DEFAULT_TEST_START_DATE)
    end_date = pd.to_datetime(DEFAULT_TEST_END_DATE)
    date_range = pd.date_range(start=start_date, end=end_date, freq="1h")
    
    # OHLCVデータを生成
    data = []
    
    # 初期価格
    price = 1000
    
    for date in date_range:
        # 価格変動をシミュレート
        change_pct = np.random.normal(0, 0.01)  # 正規分布で変動率を生成
        price *= (1 + change_pct)
        
        # 値幅を設定
        high = price * (1 + abs(np.random.normal(0, 0.005)))
        low = price * (1 - abs(np.random.normal(0, 0.005)))
        
        # 始値と終値
        open_price = price * (1 + np.random.normal(0, 0.002))
        close_price = price
        
        # 出来高
        volume = int(np.random.normal(50000, 10000))
        if volume < 0:
            volume = 0
        
        data.append({
            "Timestamp": date,
            "Open": open_price,
            "High": high,
            "Low": low,
            "Close": close_price,
            "Volume": volume
        })
    
    return pd.DataFrame(data)

@pytest.fixture(scope="session")
def sample_portfolio():
    """サンプルのポートフォリオを生成"""
    return {
        "balance": 500000,
        "portfolio": {
            "7203.T": {"qty": 100, "price": 2000},
            "9984.T": {"qty": 50, "price": 5000}
        },
        "portfolio_value": 750000,
        "initial_balance": 1000000,
        "profit_loss": -250000,
        "profit_loss_pct": -25.0,
        "last_update": datetime.datetime.now().isoformat()
    }

@pytest.fixture(scope="session")
def sample_trades():
    """サンプルの取引履歴を生成"""
    trades = []
    
    # 買い注文
    trades.append({
        "Timestamp": (datetime.datetime.now() - datetime.timedelta(days=2)).isoformat(),
        "Ticker": "7203.T",
        "Action": "BUY",
        "Qty": 100,
        "Price": 2000,
        "Amount": 200000,
        "Commission": 200,
        "Profit": 0
    })
    
    # 買い注文
    trades.append({
        "Timestamp": (datetime.datetime.now() - datetime.timedelta(days=1)).isoformat(),
        "Ticker": "9984.T",
        "Action": "BUY",
        "Qty": 50,
        "Price": 5000,
        "Amount": 250000,
        "Commission": 250,
        "Profit": 0
    })
    
    # 売り注文
    trades.append({
        "Timestamp": datetime.datetime.now().isoformat(),
        "Ticker": "7203.T",
        "Action": "SELL",
        "Qty": 50,
        "Price": 2200,
        "Amount": 110000,
        "Commission": 110,
        "Profit": 10000 - 110  # 利益 - 手数料
    })
    
    return pd.DataFrame(trades)

@pytest.fixture(scope="function")
def mock_yfinance_data(sample_ohlcv_data, test_config):
    """YFinanceのモックデータを提供するフィクスチャ"""
    class MockYFinance:
        def download(self, tickers, start, end, interval="1h"):
            """モックのダウンロード機能"""
            data = sample_ohlcv_data.copy()
            
            # 日付でフィルタリング
            if start:
                data = data[data["Timestamp"] >= pd.to_datetime(start)]
            if end:
                data = data[data["Timestamp"] <= pd.to_datetime(end)]
                
            return data
    
    return MockYFinance()

@pytest.fixture(scope="function")
def temp_results_dir(tmpdir):
    """テスト用の一時結果ディレクトリ"""
    results_dir = tmpdir.mkdir("results")
    return results_dir

@pytest.fixture(scope="function")
def temp_data_dir(tmpdir):
    """テスト用の一時データディレクトリ"""
    data_dir = tmpdir.mkdir("data")
    cache_dir = data_dir.mkdir("cache")
    return data_dir
