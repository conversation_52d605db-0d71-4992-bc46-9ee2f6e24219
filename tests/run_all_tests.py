#!/usr/bin/env python3
"""
日経225 AI取引システム - テスト実行スクリプト

Python 3.12互換性対応版
"""

import argparse
import subprocess
import sys
import os
import datetime
import shutil
import importlib.util

def setup_test_environment():
    """テスト環境のセットアップ"""
    print("テスト環境をセットアップしています...")
    
    # テスト結果ディレクトリの作成
    os.makedirs("tests/reports/coverage", exist_ok=True)
    os.makedirs("tests/reports/performance", exist_ok=True)
    
    # テスト依存関係の確認
    required_modules = ["pytest", "pytest_html", "pytest_cov"]
    missing_modules = []
    
    for module in required_modules:
        if importlib.util.find_spec(module) is None:
            missing_modules.append(module)
    
    if missing_modules:
        print(f"必要なモジュールがインストールされていません: {', '.join(missing_modules)}")
        print("テスト依存関係をインストールしています...")
        subprocess.run([sys.executable, "-m", "pip", "install", "-r", "tests/test_requirements.txt"])

def run_tests(test_type=None, verbose=False, html_report=True):
    """テストを実行する"""
    base_cmd = ["pytest"]
    
    if verbose:
        base_cmd.append("-v")
    
    if test_type == "unit":
        base_cmd.append("tests/unit/")
    elif test_type == "integration":
        base_cmd.append("tests/integration/")
    elif test_type == "performance":
        base_cmd.append("tests/performance/")
    elif test_type == "backtest":
        base_cmd.append("tests/backtest/")
    else:
        # 全テスト実行
        pass
    
    # レポート出力設定
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    report_dir = f"tests/reports/{test_type or 'all'}_{timestamp}"
    os.makedirs(report_dir, exist_ok=True)
    
    if html_report:
        base_cmd.extend([
            f"--html={report_dir}/report.html",
            "--self-contained-html"
        ])
    
    base_cmd.extend([
        f"--cov=src",
        f"--cov-report=html:{report_dir}/coverage",
        f"--cov-report=term"
    ])
    
    print(f"実行コマンド: {' '.join(base_cmd)}")
    
    # テスト実行
    result = subprocess.run(base_cmd)
    
    # 結果表示
    if result.returncode == 0:
        print(f"\n✅ テスト成功!")
        if html_report:
            print(f"レポート: {report_dir}/report.html")
            print(f"カバレッジレポート: {report_dir}/coverage/index.html")
    else:
        print(f"\n❌ テスト失敗")
    
    return result.returncode

def clean_reports(days=7):
    """古いレポートを削除する"""
    print(f"{days}日以上前の古いレポートを削除しています...")
    
    reports_dir = "tests/reports"
    if not os.path.exists(reports_dir):
        return
    
    now = datetime.datetime.now()
    cutoff = now - datetime.timedelta(days=days)
    
    for item in os.listdir(reports_dir):
        item_path = os.path.join(reports_dir, item)
        
        # ディレクトリのみ処理
        if os.path.isdir(item_path):
            try:
                # ディレクトリ名からタイムスタンプを抽出
                if "_" in item:
                    timestamp_str = item.split("_")[-1]
                    if len(timestamp_str) == 14:  # YYYYMMDDhhmmss形式
                        item_date = datetime.datetime.strptime(timestamp_str, "%Y%m%d%H%M%S")
                        
                        # 古いレポートを削除
                        if item_date < cutoff:
                            print(f"  削除: {item_path}")
                            shutil.rmtree(item_path)
            except (ValueError, IndexError):
                # タイムスタンプ解析に失敗した場合はスキップ
                continue

def main():
    parser = argparse.ArgumentParser(description="日経225 AI取引システム - テスト実行")
    parser.add_argument("--type", choices=["unit", "integration", "performance", "backtest", "all"], 
                        default="all", help="実行するテストタイプ")
    parser.add_argument("-v", "--verbose", action="store_true", help="詳細出力")
    parser.add_argument("--no-html", action="store_true", help="HTMLレポートを生成しない")
    parser.add_argument("--clean", action="store_true", help="古いレポートを削除する")
    parser.add_argument("--clean-days", type=int, default=7, help="何日以上前のレポートを削除するか")
    parser.add_argument("--specific", help="特定のテストファイルのみを実行")
    
    args = parser.parse_args()
    
    print(f"{'='*50}")
    print(f"日経225 AI取引システム - {args.type}テスト実行")
    print(f"{'='*50}\n")
    
    # Python バージョンチェック
    py_version = sys.version_info
    print(f"Python バージョン: {py_version.major}.{py_version.minor}.{py_version.micro}")
    
    # 環境セットアップ
    setup_test_environment()
    
    # 古いレポート削除
    if args.clean:
        clean_reports(args.clean_days)
    
    # 特定のテストファイルを指定した場合
    if args.specific:
        base_cmd = ["pytest", args.specific]
        if args.verbose:
            base_cmd.append("-v")
        
        print(f"特定のテストファイルを実行: {args.specific}")
        result = subprocess.run(base_cmd)
        sys.exit(result.returncode)
    
    # テスト実行
    exit_code = run_tests(
        test_type=None if args.type == "all" else args.type,
        verbose=args.verbose,
        html_report=not args.no_html
    )
    
    sys.exit(exit_code)

if __name__ == "__main__":
    main()
