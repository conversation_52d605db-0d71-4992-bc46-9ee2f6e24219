import unittest
import pandas as pd
import numpy as np
import json
import tempfile
import shutil
from pathlib import Path
import sys
import os

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src'))

from data_collector.international_collector import InternationalDataCollector
from analysis.correlation_analyzer import InternationalCorrelationAnalyzer
from enhanced_model import EnhancedNikkeiModel

class TestInternationalDataCollector(unittest.TestCase):
    """Test cases for InternationalDataCollector."""
    
    def setUp(self):
        self.temp_dir = tempfile.mkdtemp()
        self.config_path = Path(self.temp_dir) / "test_config.json"
        
        # Create minimal test config
        test_config = {
            "international_markets": {
                "us_markets": {
                    "enabled": True,
                    "indices": {
                        "S&P 500": {
                            "symbol": "^GSPC",
                            "priority": "high"
                        }
                    }
                }
            },
            "forex_pairs": {
                "major_pairs": {
                    "USD/JPY": {
                        "symbol": "USDJPY=X",
                        "priority": "critical"
                    }
                }
            },
            "collection_settings": {
                "rate_limits": {
                    "yahoo_finance": 250
                }
            }
        }
        
        with open(self.config_path, 'w') as f:
            json.dump(test_config, f)
        
        self.collector = InternationalDataCollector(str(self.config_path))
    
    def tearDown(self):
        shutil.rmtree(self.temp_dir)
    
    def test_config_loading(self):
        """Test configuration loading."""
        self.assertIsInstance(self.collector.config, dict)
        self.assertIn('international_markets', self.collector.config)
        self.assertIn('forex_pairs', self.collector.config)
    
    def test_data_directory_creation(self):
        """Test data directory structure creation."""
        expected_dirs = ['indices', 'forex', 'commodities', 'indicators']
        for dir_name in expected_dirs:
            self.assertTrue((self.collector.data_dir / dir_name).exists())
    
    def test_rate_limiting(self):
        """Test rate limiting functionality."""
        import time
        
        # Test that rate limiting doesn't cause immediate return
        start_time = time.time()
        self.collector._respect_rate_limits('yahoo_finance')
        self.collector._respect_rate_limits('yahoo_finance')
        end_time = time.time()
        
        # Should take some time due to rate limiting
        self.assertGreaterEqual(end_time - start_time, 0)
    
    def test_collect_market_indices(self):
        """Test market indices collection."""
        # Use very short period to minimize API calls
        try:
            indices = self.collector.collect_market_indices(period="5d", interval="1d")
            self.assertIsInstance(indices, dict)
            
            if indices:  # If data was successfully collected
                for key, data in indices.items():
                    self.assertIsInstance(data, pd.DataFrame)
                    self.assertIn('Close', data.columns)
                    self.assertIn('market', data.columns)
        except Exception as e:
            # Network/API issues are acceptable in tests
            self.skipTest(f"API collection failed (expected in testing): {e}")
    
    def test_technical_indicators(self):
        """Test technical indicators calculation."""
        # Create sample data
        dates = pd.date_range('2023-01-01', periods=100, freq='D')
        sample_data = pd.DataFrame({
            'Close': np.cumsum(np.random.randn(100)) + 100,
            'High': np.cumsum(np.random.randn(100)) + 105,
            'Low': np.cumsum(np.random.randn(100)) + 95,
            'Volume': np.random.randint(1000, 10000, 100)
        }, index=dates)
        
        enhanced_data = self.collector._add_technical_indicators(sample_data)
        
        # Check that indicators were added
        expected_indicators = ['price_change', 'sma_5', 'sma_20', 'volatility_20d']
        for indicator in expected_indicators:
            self.assertIn(indicator, enhanced_data.columns)


class TestCorrelationAnalyzer(unittest.TestCase):
    """Test cases for InternationalCorrelationAnalyzer."""
    
    def setUp(self):
        self.temp_dir = tempfile.mkdtemp()
        self.config_path = Path(self.temp_dir) / "test_config.json"
        
        # Create test config
        test_config = {
            "correlation_analysis": {
                "lookback_periods": [5, 10, 20],
                "correlation_threshold": 0.3
            }
        }
        
        with open(self.config_path, 'w') as f:
            json.dump(test_config, f)
        
        self.analyzer = InternationalCorrelationAnalyzer(str(self.config_path))
        
        # Create sample data
        self.create_sample_data()
    
    def tearDown(self):
        shutil.rmtree(self.temp_dir)
    
    def create_sample_data(self):
        """Create sample data for testing."""
        dates = pd.date_range('2023-01-01', periods=100, freq='D')
        
        # Sample Nikkei data
        self.nikkei_data = pd.DataFrame({
            'Close': np.cumsum(np.random.randn(100)) + 30000,
            'Volume': np.random.randint(1000000, 5000000, 100)
        }, index=dates)
        
        # Sample international data
        self.international_data = {
            'index_SP500': pd.DataFrame({
                'Close': np.cumsum(np.random.randn(100)) + 4000,
                'priority': 'high'
            }, index=dates),
            'forex_USDJPY': pd.DataFrame({
                'Close': np.cumsum(np.random.randn(100) * 0.1) + 150,
                'priority': 'critical'
            }, index=dates)
        }
    
    def test_rolling_correlations(self):
        """Test rolling correlations calculation."""
        rolling_corr = self.analyzer.calculate_rolling_correlations(
            self.nikkei_data, self.international_data, window=20
        )
        
        self.assertIsInstance(rolling_corr, pd.DataFrame)
        self.assertGreater(len(rolling_corr.columns), 0)
    
    def test_lead_lag_analysis(self):
        """Test lead-lag relationship analysis."""
        lead_lag_results = self.analyzer.analyze_lead_lag_relationships(
            self.nikkei_data, self.international_data, max_lag=5
        )
        
        self.assertIsInstance(lead_lag_results, dict)
        
        for market_name, results in lead_lag_results.items():
            self.assertIn('best_lag', results)
            self.assertIn('best_correlation', results)
            self.assertIn('interpretation', results)
    
    def test_factor_loadings(self):
        """Test factor loadings calculation."""
        factor_loadings = self.analyzer.calculate_factor_loadings(
            self.nikkei_data, self.international_data
        )
        
        if factor_loadings:  # If PCA was successful
            self.assertIsInstance(factor_loadings, dict)
            
            for pc_name, pc_data in factor_loadings.items():
                self.assertIn('explained_variance', pc_data)
                self.assertIn('loadings', pc_data)
    
    def test_basic_correlations(self):
        """Test basic correlation calculation."""
        correlations = self.analyzer._calculate_basic_correlations(
            self.nikkei_data, self.international_data
        )
        
        self.assertIsInstance(correlations, dict)
        self.assertGreaterEqual(len(correlations), 0)


class TestEnhancedModel(unittest.TestCase):
    """Test cases for EnhancedNikkeiModel."""
    
    def setUp(self):
        self.temp_dir = tempfile.mkdtemp()
        self.config_path = Path(self.temp_dir) / "test_config.json"
        
        # Create test config
        test_config = {
            "collection_settings": {
                "priority_weighting": {
                    "critical": 1.0,
                    "high": 0.8,
                    "medium": 0.6,
                    "low": 0.4
                }
            }
        }
        
        with open(self.config_path, 'w') as f:
            json.dump(test_config, f)
        
        self.model = EnhancedNikkeiModel(str(self.config_path))
        
        # Create sample data
        self.create_sample_data()
    
    def tearDown(self):
        shutil.rmtree(self.temp_dir)
    
    def create_sample_data(self):
        """Create sample data for testing."""
        dates = pd.date_range('2023-01-01', periods=200, freq='H')
        
        # Sample Nikkei data with technical indicators
        self.nikkei_data = pd.DataFrame({
            'Close': np.cumsum(np.random.randn(200) * 0.01) + 30000,
            'Volume': np.random.randint(1000000, 5000000, 200),
            'sma_5': np.cumsum(np.random.randn(200) * 0.01) + 29900,
            'sma_20': np.cumsum(np.random.randn(200) * 0.01) + 29800,
            'rsi': np.random.uniform(20, 80, 200)
        }, index=dates)
        
        # Sample international data
        self.international_data = {
            'indices_SP500': pd.DataFrame({
                'Close': np.cumsum(np.random.randn(200) * 0.01) + 4000,
                'priority': 'high'
            }, index=dates),
            'forex_USDJPY': pd.DataFrame({
                'Close': np.cumsum(np.random.randn(200) * 0.001) + 150,
                'priority': 'critical'
            }, index=dates),
            'commodity_OIL': pd.DataFrame({
                'Close': np.cumsum(np.random.randn(200) * 0.02) + 70,
                'priority': 'medium'
            }, index=dates)
        }
        
        # Set data in model
        self.model.nikkei_data = self.nikkei_data
        self.model.international_data = self.international_data
    
    def test_feature_creation(self):
        """Test enhanced feature creation."""
        features = self.model.create_enhanced_features()
        
        self.assertIsInstance(features, pd.DataFrame)
        self.assertGreater(features.shape[1], 10)  # Should have many features
        
        # Check for key feature categories
        feature_names = features.columns.tolist()
        
        # Should have Nikkei features
        nikkei_features = [col for col in feature_names if 'nikkei' in col]
        self.assertGreater(len(nikkei_features), 0)
        
        # Should have international features
        international_features = [col for col in feature_names if any(
            market in col for market in ['indices', 'forex', 'commodity']
        )]
        self.assertGreater(len(international_features), 0)
    
    def test_cross_market_features(self):
        """Test cross-market feature creation."""
        base_features = pd.DataFrame({
            'forex_USDJPY_X_returns': np.random.randn(100),
            'index_SP500_returns': np.random.randn(100),
            'index_VIX_returns': np.random.randn(100)
        })
        
        enhanced_features = self.model._add_cross_market_features(base_features)
        
        # Check for interaction features
        interaction_features = [col for col in enhanced_features.columns if 'interaction' in col]
        self.assertGreaterEqual(len(interaction_features), 0)
    
    def test_time_features(self):
        """Test time-based feature creation."""
        dates = pd.date_range('2023-01-01', periods=100, freq='H')
        base_features = pd.DataFrame(index=dates)
        
        time_features = self.model._add_time_features(base_features)
        
        expected_time_features = ['day_of_week', 'month', 'quarter', 'year', 'hour']
        for feature in expected_time_features:
            self.assertIn(feature, time_features.columns)
    
    def test_training_data_preparation(self):
        """Test training data preparation."""
        features = self.model.create_enhanced_features()
        
        X, y = self.model.prepare_training_data(target_column='nikkei_returns')
        
        self.assertIsInstance(X, np.ndarray)
        self.assertIsInstance(y, np.ndarray)
        self.assertEqual(len(X), len(y))
        self.assertGreater(X.shape[1], 0)
    
    def test_model_training(self):
        """Test model training process."""
        features = self.model.create_enhanced_features()
        X, y = self.model.prepare_training_data()
        
        # Limit to fast models for testing
        self.model.model_types = ['random_forest', 'ridge']
        
        try:
            self.model.train_models(X, y, test_size=0.3, cv_folds=2)
            
            # Check that models were trained
            self.assertGreater(len(self.model.models), 0)
            self.assertGreater(len(self.model.performance_metrics), 0)
            
            # Check performance metrics structure
            for model_name, metrics in self.model.performance_metrics.items():
                self.assertIn('mse', metrics)
                self.assertIn('mae', metrics)
                self.assertIn('r2', metrics)
        
        except Exception as e:
            # Model training might fail in testing environment
            self.skipTest(f"Model training failed (expected in testing): {e}")
    
    def test_predictions(self):
        """Test model predictions."""
        # Create simple mock model
        from sklearn.linear_model import LinearRegression
        
        # Create simple features and train basic model
        X = np.random.randn(100, 5)
        y = np.random.randn(100)
        
        model = LinearRegression()
        model.fit(X, y)
        
        self.model.models['test_model'] = model
        self.model.scalers['main'] = None  # No scaling for test
        
        # Test predictions
        X_test = np.random.randn(10, 5)
        predictions = self.model.predict(X_test, model_name='test_model')
        
        self.assertEqual(len(predictions), 10)
        self.assertIsInstance(predictions, np.ndarray)


class TestIntegration(unittest.TestCase):
    """Integration tests for the complete system."""
    
    def setUp(self):
        self.temp_dir = tempfile.mkdtemp()
        self.setup_test_environment()
    
    def tearDown(self):
        shutil.rmtree(self.temp_dir)
    
    def setup_test_environment(self):
        """Set up complete test environment."""
        # Create config
        self.config_path = Path(self.temp_dir) / "config.json"
        
        config = {
            "international_markets": {
                "us_markets": {
                    "enabled": True,
                    "indices": {
                        "S&P 500": {
                            "symbol": "^GSPC",
                            "priority": "high"
                        }
                    }
                }
            },
            "forex_pairs": {
                "major_pairs": {
                    "USD/JPY": {
                        "symbol": "USDJPY=X",
                        "priority": "critical"
                    }
                }
            },
            "collection_settings": {
                "priority_weighting": {
                    "critical": 1.0,
                    "high": 0.8,
                    "medium": 0.6,
                    "low": 0.4
                }
            },
            "correlation_analysis": {
                "lookback_periods": [5, 10],
                "correlation_threshold": 0.3
            }
        }
        
        with open(self.config_path, 'w') as f:
            json.dump(config, f)
        
        # Create sample Nikkei data file
        self.nikkei_path = Path(self.temp_dir) / "nikkei_data.csv"
        dates = pd.date_range('2023-01-01', periods=100, freq='D')
        nikkei_data = pd.DataFrame({
            'Close': np.cumsum(np.random.randn(100) * 10) + 30000,
            'Volume': np.random.randint(1000000, 5000000, 100),
            'sma_5': np.cumsum(np.random.randn(100) * 10) + 29900,
            'sma_20': np.cumsum(np.random.randn(100) * 10) + 29800
        }, index=dates)
        nikkei_data.to_csv(self.nikkei_path)
    
    def test_end_to_end_workflow(self):
        """Test complete end-to-end workflow."""
        try:
            # 1. Initialize components
            collector = InternationalDataCollector(str(self.config_path))
            analyzer = InternationalCorrelationAnalyzer(str(self.config_path))
            model = EnhancedNikkeiModel(str(self.config_path))
            
            # 2. Create mock international data (skip actual collection for testing)
            dates = pd.date_range('2023-01-01', periods=100, freq='D')
            mock_international_data = {
                'index_SP500': pd.DataFrame({
                    'Close': np.cumsum(np.random.randn(100) * 20) + 4000,
                    'priority': 'high'
                }, index=dates),
                'forex_USDJPY': pd.DataFrame({
                    'Close': np.cumsum(np.random.randn(100) * 0.5) + 150,
                    'priority': 'critical'
                }, index=dates)
            }
            
            # 3. Load data
            nikkei_data = pd.read_csv(self.nikkei_path, index_col=0, parse_dates=True)
            
            # 4. Test correlation analysis
            correlations = analyzer._calculate_basic_correlations(nikkei_data, mock_international_data)
            self.assertIsInstance(correlations, dict)
            
            # 5. Test enhanced modeling
            model.nikkei_data = nikkei_data
            model.international_data = mock_international_data
            
            features = model.create_enhanced_features()
            self.assertGreater(features.shape[1], 5)
            
            X, y = model.prepare_training_data()
            self.assertEqual(len(X), len(y))
            
            self.logger_info("End-to-end workflow test completed successfully")
            
        except Exception as e:
            self.skipTest(f"End-to-end test failed (expected in testing environment): {e}")
    
    def logger_info(self, message):
        """Mock logger for testing."""
        print(f"TEST: {message}")


class TestRobustness(unittest.TestCase):
    """Test system robustness and edge cases."""
    
    def test_missing_data_handling(self):
        """Test handling of missing data."""
        dates = pd.date_range('2023-01-01', periods=100, freq='D')
        
        # Create data with missing values
        nikkei_data = pd.DataFrame({
            'Close': np.concatenate([
                np.cumsum(np.random.randn(50)) + 30000,
                [np.nan] * 10,
                np.cumsum(np.random.randn(40)) + 30000
            ])
        }, index=dates)
        
        international_data = {
            'test_market': pd.DataFrame({
                'Close': np.concatenate([
                    [np.nan] * 20,
                    np.cumsum(np.random.randn(80)) + 4000
                ])
            }, index=dates)
        }
        
        # Test that model handles missing data gracefully
        model = EnhancedNikkeiModel()
        model.nikkei_data = nikkei_data
        model.international_data = international_data
        
        try:
            features = model.create_enhanced_features()
            self.assertGreater(len(features), 0)
        except Exception as e:
            self.fail(f"Model should handle missing data gracefully: {e}")
    
    def test_empty_data_handling(self):
        """Test handling of empty datasets."""
        model = EnhancedNikkeiModel()
        model.international_data = {}
        
        # Should not crash with empty international data
        with self.assertRaises((ValueError, AttributeError)):
            model.create_enhanced_features()
    
    def test_single_data_point(self):
        """Test handling of insufficient data."""
        dates = pd.date_range('2023-01-01', periods=5, freq='D')
        
        nikkei_data = pd.DataFrame({
            'Close': [30000, 30001, 30002, 30003, 30004]
        }, index=dates)
        
        model = EnhancedNikkeiModel()
        model.nikkei_data = nikkei_data
        model.international_data = {}
        
        # Should handle insufficient data gracefully
        try:
            features = model.create_enhanced_features()
            # Should have very few valid features with insufficient data
            self.assertLessEqual(features.shape[0], 5)
        except Exception as e:
            # Acceptable to raise error with insufficient data
            pass


def run_all_tests():
    """Run all test suites."""
    test_loader = unittest.TestLoader()
    test_suite = unittest.TestSuite()
    
    # Add all test classes
    test_classes = [
        TestInternationalDataCollector,
        TestCorrelationAnalyzer,
        TestEnhancedModel,
        TestIntegration,
        TestRobustness
    ]
    
    for test_class in test_classes:
        tests = test_loader.loadTestsFromTestClass(test_class)
        test_suite.addTests(tests)
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    return result.wasSuccessful()


if __name__ == '__main__':
    # Run specific test or all tests
    import sys
    
    if len(sys.argv) > 1:
        # Run specific test
        unittest.main()
    else:
        # Run all tests
        success = run_all_tests()
        sys.exit(0 if success else 1)