"""
日経225 AI取引システム - 仮想取引機能のテスト
"""

import os
import sys
import pytest
import pandas as pd
import numpy as np
import json
from datetime import datetime, timedelta

# プロジェクトルートを追加
from pathlib import Path
ROOT_DIR = Path(__file__).parent.parent.parent.absolute()
sys.path.insert(0, str(ROOT_DIR))

# テスト対象のモジュールをインポート
from src.trading.virtual_trader import VirtualTrader
from src.trading.trading_strategy import TradingStrategy


class TestVirtualTrader:
    """仮想取引機能のテスト"""
    
    @pytest.fixture
    def trader(self, temp_results_dir):
        """仮想取引インスタンスを作成"""
        initial_balance = 1000000  # 初期資金: 100万円
        max_stocks = 3  # 最大保有銘柄数
        commission_rate = 0.0001  # 手数料率: 0.01%
        
        # 仮想取引インスタンス作成
        trader = VirtualTrader(
            initial_balance=initial_balance,
            max_stocks=max_stocks,
            commission_rate=commission_rate,
            results_dir=temp_results_dir
        )
        
        return trader
    
    def test_initialization(self, trader):
        """初期化のテスト"""
        # 初期化直後の状態を検証
        assert trader.initial_balance == 1000000
        assert trader.balance == 1000000
        assert trader.portfolio == {}
        assert trader.trades == []
        assert trader.max_stocks == 3
        assert trader.commission_rate == 0.0001
    
    def test_buy_stock(self, trader):
        """株式購入のテスト"""
        # 購入前の状態を保存
        initial_balance = trader.balance
        
        # テストデータ
        ticker = "7203.T"
        price = 2000
        qty = 100
        
        # 購入実行
        timestamp = datetime.now()
        trade_id = trader.buy_stock(ticker, price, qty, timestamp)
        
        # 検証
        assert trade_id is not None  # トレードIDが返される
        assert ticker in trader.portfolio  # ポートフォリオに銘柄が追加される
        assert trader.portfolio[ticker]["qty"] == qty  # 数量が正しい
        assert trader.portfolio[ticker]["price"] == price  # 価格が正しい
        
        # 残高の検証（手数料を考慮）
        expected_commission = price * qty * trader.commission_rate
        expected_balance = initial_balance - (price * qty) - expected_commission
        assert abs(trader.balance - expected_balance) < 0.01  # 浮動小数点の誤差を考慮
        
        # 取引履歴の検証
        assert len(trader.trades) == 1
        assert trader.trades[0]["Ticker"] == ticker
        assert trader.trades[0]["Action"] == "BUY"
        assert trader.trades[0]["Qty"] == qty
        assert trader.trades[0]["Price"] == price
        assert trader.trades[0]["Amount"] == price * qty
        assert trader.trades[0]["Commission"] == expected_commission
        assert trader.trades[0]["Profit"] == 0  # 購入時は利益なし
    
    def test_sell_stock(self, trader):
        """株式売却のテスト"""
        # まず購入
        ticker = "7203.T"
        buy_price = 2000
        qty = 100
        timestamp = datetime.now() - timedelta(days=1)
        trader.buy_stock(ticker, buy_price, qty, timestamp)
        
        # 購入後の状態を保存
        after_buy_balance = trader.balance
        
        # 売却
        sell_price = 2200  # 購入価格より10%上昇
        sell_qty = 50  # 半分売却
        timestamp = datetime.now()
        trade_id = trader.sell_stock(ticker, sell_price, sell_qty, timestamp)
        
        # 検証
        assert trade_id is not None  # トレードIDが返される
        assert ticker in trader.portfolio  # まだポートフォリオに銘柄が残っている
        assert trader.portfolio[ticker]["qty"] == qty - sell_qty  # 残数量が正しい
        
        # 残高の検証（手数料と利益を考慮）
        expected_commission = sell_price * sell_qty * trader.commission_rate
        expected_profit = (sell_price - buy_price) * sell_qty
        expected_balance = after_buy_balance + (sell_price * sell_qty) - expected_commission
        assert abs(trader.balance - expected_balance) < 0.01  # 浮動小数点の誤差を考慮
        
        # 取引履歴の検証
        assert len(trader.trades) == 2  # 購入と売却
        sell_trade = trader.trades[1]
        assert sell_trade["Ticker"] == ticker
        assert sell_trade["Action"] == "SELL"
        assert sell_trade["Qty"] == sell_qty
        assert sell_trade["Price"] == sell_price
        assert sell_trade["Amount"] == sell_price * sell_qty
        assert abs(sell_trade["Commission"] - expected_commission) < 0.01
        assert abs(sell_trade["Profit"] - (expected_profit - expected_commission)) < 0.01
    
    def test_calculate_portfolio_value(self, trader):
        """ポートフォリオ価値計算のテスト"""
        # 複数銘柄を購入
        trader.buy_stock("7203.T", 2000, 100, datetime.now())  # トヨタ: 200,000円分
        trader.buy_stock("9984.T", 5000, 50, datetime.now())   # ソフトバンク: 250,000円分
        
        # 現在価格（購入時より少し上昇）
        current_prices = {
            "7203.T": 2100,  # +5%
            "9984.T": 5200,  # +4%
        }
        
        # ポートフォリオ価値計算
        portfolio_value = trader.calculate_portfolio_value(current_prices)
        
        # 期待される価値
        expected_value = (2100 * 100) + (5200 * 50)  # 210,000 + 260,000 = 470,000
        
        # 検証
        assert abs(portfolio_value - expected_value) < 0.01
    
    def test_get_profit_loss(self, trader):
        """損益計算のテスト"""
        # 初期状態
        initial_balance = trader.initial_balance
        
        # 複数銘柄を購入
        trader.buy_stock("7203.T", 2000, 100, datetime.now())  # トヨタ: 200,000円分
        trader.buy_stock("9984.T", 5000, 50, datetime.now())   # ソフトバンク: 250,000円分
        
        # 現在価格
        current_prices = {
            "7203.T": 2100,  # +5%
            "9984.T": 5200,  # +4%
        }
        
        # ポートフォリオ価値
        portfolio_value = trader.calculate_portfolio_value(current_prices)
        
        # 損益計算
        profit_loss, profit_loss_pct = trader.get_profit_loss(portfolio_value)
        
        # 期待される損益
        expected_total_value = portfolio_value + trader.balance
        expected_profit_loss = expected_total_value - initial_balance
        expected_profit_loss_pct = (expected_profit_loss / initial_balance) * 100
        
        # 検証
        assert abs(profit_loss - expected_profit_loss) < 0.01
        assert abs(profit_loss_pct - expected_profit_loss_pct) < 0.01
    
    def test_save_and_load_portfolio(self, trader, temp_results_dir):
        """ポートフォリオの保存と読み込みのテスト"""
        # 複数銘柄を購入
        trader.buy_stock("7203.T", 2000, 100, datetime.now())  # トヨタ: 200,000円分
        trader.buy_stock("9984.T", 5000, 50, datetime.now())   # ソフトバンク: 250,000円分
        
        # 現在価格
        current_prices = {
            "7203.T": 2100,
            "9984.T": 5200,
        }
        
        # ポートフォリオを保存
        timestamp = datetime.now()
        file_path = trader.save_portfolio(timestamp, current_prices)
        
        # ファイルが作成されていることを確認
        assert os.path.exists(file_path)
        
        # ファイルを読み込み
        with open(file_path, "r") as f:
            portfolio_data = json.load(f)
        
        # 検証
        assert portfolio_data["balance"] == trader.balance
        assert portfolio_data["initial_balance"] == trader.initial_balance
        assert "portfolio" in portfolio_data
        assert "7203.T" in portfolio_data["portfolio"]
        assert "9984.T" in portfolio_data["portfolio"]
        assert portfolio_data["portfolio"]["7203.T"]["qty"] == 100
        assert portfolio_data["portfolio"]["9984.T"]["qty"] == 50
        
        # 損益が正しく計算されていることを確認
        portfolio_value = trader.calculate_portfolio_value(current_prices)
        profit_loss, profit_loss_pct = trader.get_profit_loss(portfolio_value)
        
        assert abs(portfolio_data["portfolio_value"] - portfolio_value) < 0.01
        assert abs(portfolio_data["profit_loss"] - profit_loss) < 0.01
        assert abs(portfolio_data["profit_loss_pct"] - profit_loss_pct) < 0.01
    
    def test_save_and_load_trades(self, trader, temp_results_dir):
        """取引履歴の保存と読み込みのテスト"""
        # 取引を実行
        trader.buy_stock("7203.T", 2000, 100, datetime.now() - timedelta(hours=2))
        trader.buy_stock("9984.T", 5000, 50, datetime.now() - timedelta(hours=1))
        trader.sell_stock("7203.T", 2100, 50, datetime.now())
        
        # 取引履歴を保存
        timestamp = datetime.now()
        file_path = trader.save_trades(timestamp)
        
        # ファイルが作成されていることを確認
        assert os.path.exists(file_path)
        
        # ファイルを読み込み
        trades_df = pd.read_csv(file_path)
        
        # 検証
        assert len(trades_df) == 3  # 取引数
        assert "Ticker" in trades_df.columns
        assert "Action" in trades_df.columns
        assert "Qty" in trades_df.columns
        assert "Price" in trades_df.columns
        assert "Amount" in trades_df.columns
        assert "Commission" in trades_df.columns
        assert "Profit" in trades_df.columns
        
        # 取引内容の検証
        assert trades_df["Action"].iloc[0] == "BUY"
        assert trades_df["Ticker"].iloc[0] == "7203.T"
        assert trades_df["Action"].iloc[1] == "BUY"
        assert trades_df["Ticker"].iloc[1] == "9984.T"
        assert trades_df["Action"].iloc[2] == "SELL"
        assert trades_df["Ticker"].iloc[2] == "7203.T"
        
    def test_max_stocks_constraint(self, trader):
        """最大保有銘柄数の制約テスト"""
        # 最大保有銘柄数まで購入
        trader.buy_stock("7203.T", 2000, 100, datetime.now())  # トヨタ
        trader.buy_stock("9984.T", 5000, 50, datetime.now())   # ソフトバンク
        trader.buy_stock("6758.T", 10000, 20, datetime.now())  # ソニー
        
        # これ以上は購入できないはず
        with pytest.raises(Exception):
            trader.buy_stock("6861.T", 50000, 5, datetime.now())  # キーエンス
            
        # ただし売却はできる
        trader.sell_stock("7203.T", 2100, 100, datetime.now())
        
        # 売却後なら再び購入可能
        trader.buy_stock("6861.T", 50000, 5, datetime.now())  # キーエンス
        
        # 再び最大保有銘柄数に達する
        assert len(trader.portfolio) == 3
