"""
日経225 AI取引システム - データ収集機能のテスト
"""

import os
import sys
import pytest
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

# プロジェクトルートを追加
from pathlib import Path
ROOT_DIR = Path(__file__).parent.parent.parent.absolute()
sys.path.insert(0, str(ROOT_DIR))

# テスト対象のモジュールをインポート
# 修正: data_collection_helper から fetch_stock_data をインポート
from src.data_collection_helper import fetch_stock_data
from src.trading.data_cache import DataCache


class TestDataCollection:
    """データ収集機能のテスト"""
    
    def test_fetch_stock_data(self, mock_yfinance_data, monkeypatch):
        """株価データ取得のテスト"""
        # YFinanceのモックを設定
        monkeypatch.setattr('yfinance.download', mock_yfinance_data.download)
        
        # テストデータ
        tickers = ["7203.T"]
        start_date = "2024-01-01"
        end_date = "2024-01-10"
        interval = "1h"
        
        # 関数実行
        df = fetch_stock_data(tickers, start_date, end_date, interval)
        
        # 検証
        assert df is not None
        assert not df.empty
        assert "Open" in df.columns
        assert "High" in df.columns
        assert "Low" in df.columns
        assert "Close" in df.columns
        assert "Volume" in df.columns
        
        # 日付範囲の検証
        assert pd.to_datetime(start_date) <= df["Timestamp"].min()
        assert pd.to_datetime(end_date) >= df["Timestamp"].max()
    
    def test_data_cache(self, temp_data_dir):
        """データキャッシュのテスト"""
        cache_dir = os.path.join(temp_data_dir, "cache")
        
        # キャッシュインスタンス作成
        cache = DataCache(cache_dir=cache_dir)
        
        # テストデータ
        ticker = "7203.T"
        start_date = datetime(2024, 1, 1)
        end_date = datetime(2024, 1, 31)
        
        # サンプルデータ作成
        np.random.seed(42)
        date_range = pd.date_range(start=start_date, end=end_date, freq="1h")
        data = []
        
        for date in date_range:
            data.append({
                "Timestamp": date,
                "Open": np.random.randint(1000, 2000),
                "High": np.random.randint(1000, 2000),
                "Low": np.random.randint(1000, 2000),
                "Close": np.random.randint(1000, 2000),
                "Volume": np.random.randint(10000, 100000)
            })
        
        sample_df = pd.DataFrame(data)
        
        # キャッシュに保存
        cache.save_data(ticker, start_date, end_date, sample_df)
        
        # キャッシュから読み込み
        cached_df = cache.get_data(ticker, start_date, end_date)
        
        # 検証
        assert cached_df is not None
        assert not cached_df.empty
        assert len(cached_df) == len(sample_df)
        assert all(col in cached_df.columns for col in ["Timestamp", "Open", "High", "Low", "Close", "Volume"])
        
        # キャッシュファイルが作成されていることを確認
        cache_file = os.path.join(cache_dir, f"{ticker}_{start_date.strftime('%Y%m%d')}_{end_date.strftime('%Y%m%d')}.csv")
        assert os.path.exists(cache_file)
    
    def test_cache_partial_overlap(self, temp_data_dir):
        """部分的に重複する日付範囲のキャッシュテスト"""
        cache_dir = os.path.join(temp_data_dir, "cache")
        
        # キャッシュインスタンス作成
        cache = DataCache(cache_dir=cache_dir)
        
        # テストデータ
        ticker = "7203.T"
        start_date1 = datetime(2024, 1, 1)
        end_date1 = datetime(2024, 1, 15)
        start_date2 = datetime(2024, 1, 10)
        end_date2 = datetime(2024, 1, 31)
        
        # サンプルデータ1作成（1/1～1/15）
        date_range1 = pd.date_range(start=start_date1, end=end_date1, freq="1h")
        data1 = []
        
        for date in date_range1:
            data1.append({
                "Timestamp": date,
                "Open": 1000,
                "High": 1100,
                "Low": 900,
                "Close": 1050,
                "Volume": 50000
            })
        
        sample_df1 = pd.DataFrame(data1)
        
        # サンプルデータ2作成（1/10～1/31）
        date_range2 = pd.date_range(start=start_date2, end=end_date2, freq="1h")
        data2 = []
        
        for date in date_range2:
            data2.append({
                "Timestamp": date,
                "Open": 2000,
                "High": 2100,
                "Low": 1900,
                "Close": 2050,
                "Volume": 60000
            })
        
        sample_df2 = pd.DataFrame(data2)
        
        # キャッシュに保存
        cache.save_data(ticker, start_date1, end_date1, sample_df1)
        
        # 重複する日付範囲のデータリクエスト
        missing_df = cache.get_missing_dates(ticker, start_date2, end_date2)
        
        # 検証：1/16～1/31のデータだけが欠けている
        assert missing_df is not None
        
        # 欠損データリストの形式を検証
        # もしget_missing_datesがDataFrameを返す場合
        if isinstance(missing_df, pd.DataFrame):
            missing_start_date = missing_df["start_date"].iloc[0]
            missing_end_date = missing_df["end_date"].iloc[0]
            
            # 欠損の開始日が1/16であることを確認
            expected_missing_start = datetime(2024, 1, 16)
            assert missing_start_date.replace(hour=0, minute=0, second=0).date() == expected_missing_start.date()
            
            # 欠損の終了日が1/31であることを確認
            assert missing_end_date.date() == end_date2.date()
        else:
            # もしget_missing_datesが(start, end)のタプルのリストを返す場合
            missing_start_date, missing_end_date = missing_df[0]
            
            # 欠損の開始日が1/16であることを確認
            expected_missing_start = datetime(2024, 1, 16)
            assert missing_start_date.replace(hour=0, minute=0, second=0).date() == expected_missing_start.date()
            
            # 欠損の終了日が1/31であることを確認
            assert missing_end_date.date() == end_date2.date()
