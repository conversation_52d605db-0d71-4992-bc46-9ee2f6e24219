"""
日経225 AI取引システム - エラーケースのテスト

このモジュールでは、システムのエラー処理能力をテストします。
エラーが適切に処理され、システムが堅牢に動作することを検証します。
"""

import os
import sys
import pytest
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import requests
import yfinance as yf
import json  # jsonモジュールを明示的にインポート
from unittest.mock import MagicMock, patch

# プロジェクトルートを追加
from pathlib import Path
ROOT_DIR = Path(__file__).parent.parent.parent.absolute()
sys.path.insert(0, str(ROOT_DIR))

# テスト対象のモジュールをインポート
from src.data_collection_helper import fetch_stock_data
from src.trading.data_cache import DataCache
# SimpleVirtualTraderクラスをインポート
from src.simple_virtual_trader import SimpleVirtualTrader


class TestErrorCases:
    """エラーケースのテスト"""
    
    def test_fetch_data_api_error(self, monkeypatch):
        """APIエラー時の処理テスト"""
        # YFinanceのダウンロード関数をモックして例外を発生させる
        def mock_yfinance_download_error(*args, **kwargs):
            raise requests.exceptions.RequestException("API接続エラー")
        
        monkeypatch.setattr('yfinance.download', mock_yfinance_download_error)
        
        # テストデータ
        tickers = ["7203.T"]
        start_date = "2024-01-01"
        end_date = "2024-01-10"
        interval = "1h"
        
        # APIエラーが発生するはず
        with pytest.raises(requests.exceptions.RequestException):
            fetch_stock_data(tickers, start_date, end_date, interval)
    
    def test_fetch_data_timeout(self, monkeypatch):
        """タイムアウト時の処理テスト"""
        # YFinanceのダウンロード関数をモックしてタイムアウトを発生させる
        def mock_yfinance_download_timeout(*args, **kwargs):
            raise requests.exceptions.Timeout("リクエストがタイムアウトしました")
        
        monkeypatch.setattr('yfinance.download', mock_yfinance_download_timeout)
        
        # テストデータ
        tickers = ["7203.T"]
        start_date = "2024-01-01"
        end_date = "2024-01-10"
        interval = "1h"
        
        # タイムアウトエラーが発生するはず
        with pytest.raises(requests.exceptions.Timeout):
            fetch_stock_data(tickers, start_date, end_date, interval)
    
    def test_invalid_ticker(self, monkeypatch):
        """無効なティッカーの処理テスト"""
        # YFinanceのダウンロード関数をモックして空のデータフレームを返す
        def mock_yfinance_download_empty(*args, **kwargs):
            return pd.DataFrame()  # 空のデータフレーム
        
        monkeypatch.setattr('yfinance.download', mock_yfinance_download_empty)
        
        # テストデータ（無効なティッカー）
        tickers = ["INVALID_TICKER"]
        start_date = "2024-01-01"
        end_date = "2024-01-10"
        interval = "1h"
        
        # 実行（例外を発生させるか、空のデータフレームを返すはず）
        result = fetch_stock_data(tickers, start_date, end_date, interval)
        assert result.empty, "無効なティッカーの場合、空のデータフレームが返されるべき"
    
    def test_cache_directory_error(self, tmp_path):
        """キャッシュディレクトリのエラー処理テスト"""
        # アクセス権のないディレクトリを使用
        non_writable_dir = tmp_path / "non_writable"
        non_writable_dir.mkdir()
        # 書き込み権限を削除（テスト環境によっては動作しない可能性があるため、skipを追加）
        try:
            non_writable_dir.chmod(0o500)  # 読み取り・実行のみ許可
        except Exception:
            pytest.skip("権限変更ができない環境のためテストをスキップ")
        
        cache_dir = non_writable_dir / "cache"
        
        # DataCacheインスタンス作成（キャッシュディレクトリ作成に失敗するはず）
        with pytest.raises(Exception):
            cache = DataCache(cache_dir=str(cache_dir))
            
            # テストデータ
            ticker = "7203.T"
            start_date = datetime(2024, 1, 1)
            end_date = datetime(2024, 1, 31)
            
            # サンプルデータ作成
            date_range = pd.date_range(start=start_date, end=end_date, freq="1h")
            data = []
            for date in date_range:
                data.append({
                    "Timestamp": date,
                    "Open": 1000,
                    "High": 1100,
                    "Low": 900,
                    "Close": 1050,
                    "Volume": 50000
                })
            sample_df = pd.DataFrame(data)
            
            # キャッシュに保存しようとする（失敗するはず）
            cache.save_data(ticker, start_date, end_date, sample_df)
    
    def test_trader_sell_nonexistent_stock(self):
        """存在しない銘柄の売却テスト"""
        # 仮想取引インスタンス作成
        trader = SimpleVirtualTrader(
            initial_balance=1000000,
            commission_rate=0.0001,
            results_dir="results"
        )
        
        # 存在しない銘柄を売却しようとする
        ticker = "NONEXISTENT"
        price = 2000
        qty = 100
        timestamp = datetime.now()
        
        # 例外が発生するはず
        with pytest.raises(ValueError):
            trader.sell_stock(ticker, price, qty, timestamp)
    
    def test_trader_sell_insufficient_qty(self):
        """不十分な数量の売却テスト"""
        # 仮想取引インスタンス作成
        trader = SimpleVirtualTrader(
            initial_balance=1000000,
            commission_rate=0.0001,
            results_dir="results"
        )
        
        # 銘柄を購入
        ticker = "7203.T"
        price = 2000
        qty = 10
        timestamp = datetime.now()
        trader.buy_stock(ticker, price, qty, timestamp)
        
        # 所有数量より多い数量を売却しようとする
        sell_qty = qty + 10
        
        # 例外が発生するはず
        with pytest.raises(ValueError):
            trader.sell_stock(ticker, price, sell_qty, timestamp)
    
    def test_trader_insufficient_balance(self):
        """残高不足での購入テスト"""
        # 少額の残高で仮想取引インスタンス作成
        trader = SimpleVirtualTrader(
            initial_balance=1000,  # 1000円
            commission_rate=0.0001,
            results_dir="results"
        )
        
        # 高額な銘柄を購入しようとする
        ticker = "9984.T"  # ソフトバンクグループ
        price = 10000  # 10,000円
        qty = 10  # 10株 = 100,000円
        timestamp = datetime.now()
        
        # 例外が発生するはず
        with pytest.raises(ValueError):
            trader.buy_stock(ticker, price, qty, timestamp)
    
    def test_invalid_json_config(self):
        """不正なJSON設定ファイルのテスト"""
        # 不正なJSONを含むファイルパス
        invalid_json_path = os.path.join(ROOT_DIR, "tests", "fixtures", "invalid_config.json")
        
        # fixtures ディレクトリが存在しない場合は作成
        fixtures_dir = os.path.join(ROOT_DIR, "tests", "fixtures")
        os.makedirs(fixtures_dir, exist_ok=True)
        
        # 不正なJSONファイルを作成
        with open(invalid_json_path, "w") as f:
            f.write("{this is not valid json")
        
        # JSONを読み込もうとする
        with pytest.raises(json.JSONDecodeError):
            with open(invalid_json_path, "r") as f:
                json.load(f)
        
        # テスト後にファイルを削除
        if os.path.exists(invalid_json_path):
            os.remove(invalid_json_path)
