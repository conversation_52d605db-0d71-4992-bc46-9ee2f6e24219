"""
日経225 AI取引システム - エンドツーエンドの統合テスト
"""

import os
import sys
import pytest
import pandas as pd
import numpy as np
import json
import time
from datetime import datetime, timedelta

# プロジェクトルートを追加
from pathlib import Path
ROOT_DIR = Path(__file__).parent.parent.parent.absolute()
sys.path.insert(0, str(ROOT_DIR))

# テスト対象のモジュールをインポート
# 修正: data_collection_helper から fetch_stock_data をインポート
from src.data_collection_helper import fetch_stock_data 
from src.trading.data_cache import DataCache
from src.feature_engineering import add_features
from src.model import LSTMModel
from src.trading.virtual_trader import VirtualTrader
from src.trading.trading_strategy import TradingStrategy


class TestEndToEnd:
    """エンドツーエンドテスト"""
    
    @pytest.fixture
    def setup_environment(self, temp_data_dir, temp_results_dir, monkeypatch, sample_ohlcv_data):
        """テスト環境のセットアップ"""
        # YFinanceのモックを設定
        def mock_yfinance_download(tickers, start, end, interval="1h"):
            """モックのyfinance.download関数"""
            data = sample_ohlcv_data.copy()
            
            # 日付でフィルタリング
            if start:
                data = data[data["Timestamp"] >= pd.to_datetime(start)]
            if end:
                data = data[data["Timestamp"] <= pd.to_datetime(end)]
                
            return data
        
        monkeypatch.setattr('yfinance.download', mock_yfinance_download)
        
        # テスト用のパス設定
        cache_dir = os.path.join(temp_data_dir, "cache")
        results_dir = temp_results_dir
        
        # 必要なディレクトリを作成
        os.makedirs(cache_dir, exist_ok=True)
        
        return {
            "cache_dir": cache_dir,
            "results_dir": results_dir,
            "sample_data": sample_ohlcv_data
        }
    
    def test_data_collection_to_trading(self, setup_environment):
        """データ収集から取引までの統合テスト"""
        # テスト環境
        env = setup_environment
        
        # ================ 1. データ収集 ================
        tickers = ["7203.T", "9984.T", "6758.T"]
        start_date = "2024-01-01"
        end_date = "2024-01-15"
        interval = "1h"
        
        # データ取得
        df = fetch_stock_data(tickers, start_date, end_date, interval)
        
        # 検証
        assert df is not None
        assert not df.empty
        assert all(col in df.columns for col in ["Timestamp", "Open", "High", "Low", "Close", "Volume"])
        
        # ================ 2. キャッシュの活用 ================
        # キャッシュインスタンス作成
        cache = DataCache(cache_dir=env["cache_dir"])
        
        # キャッシュに保存
        for ticker in tickers:
            ticker_df = df[df["Ticker"] == ticker] if "Ticker" in df.columns else df
            cache.save_data(ticker, pd.to_datetime(start_date), pd.to_datetime(end_date), ticker_df)
        
        # キャッシュから読み込み
        for ticker in tickers:
            cached_df = cache.get_data(ticker, pd.to_datetime(start_date), pd.to_datetime(end_date))
            assert cached_df is not None
            assert not cached_df.empty
        
        # ================ 3. 特徴量エンジニアリング ================
        # 特徴量を追加
        features_df = add_features(df.copy())
        
        # 検証
        assert features_df is not None
        assert not features_df.empty
        
        # 特徴量カラムの存在を確認
        feature_columns = [
            "SMA_5", "SMA_10", "SMA_20",
            "EMA_5", "EMA_10", "EMA_20",
            "RSI_14", "MACD", "MACD_Signal",
            "BB_Upper", "BB_Middle", "BB_Lower"
        ]
        
        for col in feature_columns:
            assert col in features_df.columns, f"特徴量 {col} が見つかりません"
        
        # ================ 4. モデル予測 ================
        # この部分は実際のモデルのロードや予測ロジックによって変わります
        # ここではモデル予測の代わりにモック予測を生成
        
        # モック予測データ（価格上昇率）
        predictions = {
            "7203.T": 0.03,  # 3%上昇予測
            "9984.T": 0.02,  # 2%上昇予測
            "6758.T": 0.01   # 1%上昇予測
        }
        
        # 現在価格の取得（最後のデータポイント）
        latest_prices = {}
        for ticker in tickers:
            ticker_df = df[df["Ticker"] == ticker] if "Ticker" in df.columns else df
            latest_prices[ticker] = ticker_df["Close"].iloc[-1]
        
        # ================ 5. 仮想取引 ================
        # 仮想取引インスタンス作成
        trader = VirtualTrader(
            initial_balance=1000000,  # 初期資金: 100万円
            commission_rate=0.0001,  # 手数料率: 0.01%
            results_dir=env["results_dir"]
        )
        
        # 取引戦略インスタンス作成
        strategy = TradingStrategy()
        
        # 取引実行（取引戦略に基づいて）
        timestamp = datetime.now()
        
        # 銘柄スコアを計算（予測上昇率をスコアとして使用）
        stock_scores = [(ticker, predictions[ticker]) for ticker in tickers]
        
        # スコアに基づいてソート
        stock_scores.sort(key=lambda x: x[1], reverse=True)
        
        # 上位銘柄のみ取得
        max_stocks = 3  # trader.max_stocks の代わりに直接指定
        top_stocks = stock_scores[:max_stocks]
        
        # 予測変化率に基づいて投資配分を決定
        portfolio_allocation = strategy.calculate_portfolio_allocation(
            top_stocks, 
            trader.balance
        )
        
        # 購入注文を実行
        for ticker, allocation in portfolio_allocation.items():
            # 現在価格
            current_price = latest_prices[ticker]
            
            # 数量計算
            qty = int(allocation / current_price)
            
            if qty > 0:
                trader.buy_stock(ticker, current_price, qty, timestamp)
        
        # ポートフォリオの状態を検証
        assert len(trader.portfolio) <= max_stocks
        assert trader.balance < 1000000  # 購入により残高減少
        
        # ポートフォリオを保存
        file_path = trader.save_portfolio(timestamp, latest_prices)
        assert os.path.exists(file_path)
        
        # 取引履歴を保存
        trades_file = trader.save_trades(timestamp)
        assert os.path.exists(trades_file)
        
        # ================ 6. 損益シミュレーション ================
        # 時間経過後、価格が予測通りに変動したと仮定
        updated_prices = {}
        for ticker in tickers:
            current_price = latest_prices[ticker]
            predicted_change = predictions[ticker]
            updated_prices[ticker] = current_price * (1 + predicted_change)
        
        # ポートフォリオ価値計算
        portfolio_value = trader.calculate_portfolio_value(updated_prices)
        
        # 損益計算
        profit_loss, profit_loss_pct = trader.get_profit_loss(portfolio_value)
        
        # 検証：正の利益が期待される
        assert profit_loss > 0
        assert profit_loss_pct > 0
        
        # ================ 7. 一部売却 ================
        # 最初の銘柄を売却
        if trader.portfolio:
            ticker = list(trader.portfolio.keys())[0]
            qty = trader.portfolio[ticker]["qty"] // 2  # 半分売却
            
            timestamp = datetime.now() + timedelta(hours=1)
            trader.sell_stock(ticker, updated_prices[ticker], qty, timestamp)
            
            # 残高増加を検証
            assert trader.balance > 0
        
        # 最終状態を保存
        trader.save_portfolio(timestamp, updated_prices)
        trader.save_trades(timestamp)
