# Docker Setup Guide

This project provides two Docker configurations:

## 🖥️ Development Setup (CPU-only)
**Best for**: Development, testing, web interface access, basic functionality

```bash
# Start development environment
docker-compose up -d

# Access web dashboard
open http://localhost:5001
```

**Features:**
- ✅ Quick startup (no GPU drivers needed)
- ✅ Web dashboard and API access
- ✅ Data collection and basic trading
- ✅ Compatible with any machine (ARM64/AMD64)
- ❌ Slower AI model inference
- ❌ Limited ML training capabilities

## 🚀 Production Setup (GPU-accelerated)
**Best for**: Production trading, AI model training, high-performance inference

### Prerequisites:
1. **NVIDIA GPU** with CUDA support
2. **NVIDIA Container Toolkit** installed:
   ```bash
   # Install NVIDIA Container Toolkit
   curl -fsSL https://nvidia.github.io/libnvidia-container/gpgkey | sudo gpg --dearmor -o /usr/share/keyrings/nvidia-container-toolkit-keyring.gpg
   curl -s -L https://nvidia.github.io/libnvidia-container/stable/deb/nvidia-container-toolkit.list | \
     sed 's#deb https://#deb [signed-by=/usr/share/keyrings/nvidia-container-toolkit-keyring.gpg] https://#g' | \
     sudo tee /etc/apt/sources.list.d/nvidia-container-toolkit.list
   sudo apt-get update
   sudo apt-get install -y nvidia-container-toolkit
   sudo systemctl restart docker
   ```

### Usage:
```bash
# Start GPU-accelerated environment
docker-compose -f docker-compose.gpu.yml up -d

# Or start just the trading engine with GPU
docker-compose -f docker-compose.gpu.yml --profile gpu-trading up -d
```

**Features:**
- ✅ GPU-accelerated TensorFlow and PyTorch
- ✅ Fast AI model inference (10-100x speedup)
- ✅ Efficient neural network training
- ✅ Mixed precision (FP16) support
- ✅ CUDA memory optimization
- ❌ Requires NVIDIA GPU and drivers
- ❌ Larger Docker image size

## Configuration

### Environment Variables
Copy and edit the environment file:
```bash
cp .env.example .env
# Edit .env with your settings
```

Key variables:
- `KABU_API_PWD`: Your Kabu Station password
- `GPU_ENABLED`: true/false for GPU mode
- `TRADING_MODE`: paper/dryrun/live

### Health Monitoring
Both setups include health checks:
```bash
# Check container health
docker-compose ps

# View logs
docker-compose logs -f
```

## Performance Comparison

| Component | CPU Mode | GPU Mode | Speedup |
|-----------|----------|----------|---------|
| Model Training | ~10 min | ~1-2 min | 5-10x |
| Inference | ~100ms | ~10ms | 10x |
| Data Processing | ~1 sec | ~100ms | 10x |
| Memory Usage | 2-4GB | 4-8GB | - |

## Troubleshooting

### Common Issues:

**Port 5000 in use:**
- The setup uses port 5001 to avoid conflicts with macOS AirPlay

**GPU not detected:**
```bash
# Test GPU access
docker run --rm --gpus all nvidia/cuda:12.1-base-ubuntu22.04 nvidia-smi
```

**Out of memory:**
- Reduce batch sizes in model training
- Check `PYTORCH_CUDA_ALLOC_CONF` settings

**Import errors:**
- Ensure all dependencies are installed
- Check requirements.txt for missing packages

## Development Workflow

1. **Start with CPU mode** for development:
   ```bash
   docker-compose up -d
   ```

2. **Test functionality** via web interface at http://localhost:5001

3. **Switch to GPU mode** for production:
   ```bash
   docker-compose down
   docker-compose -f docker-compose.gpu.yml up -d
   ```

4. **Monitor performance** and logs:
   ```bash
   docker-compose logs -f nikkei225-ai-trading-gpu
   ```