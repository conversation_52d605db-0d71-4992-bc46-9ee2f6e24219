#!/usr/bin/env python3
"""
Production Deployment Script for International Multi-Market Trading System

This script handles the complete deployment and validation of the enhanced
Nikkei 225 trading system with international market integration.
"""

import argparse
import json
import logging
import shutil
import subprocess
import sys
from pathlib import Path
from datetime import datetime
import pkg_resources

class ProductionDeployer:
    """Handle production deployment of the international trading system."""
    
    def __init__(self):
        self.logger = self._setup_logging()
        self.deployment_timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        self.backup_dir = Path(f'backups/deployment_{self.deployment_timestamp}')
        self.deployment_config = {}
        
    def _setup_logging(self):
        """Setup deployment logging."""
        Path('logs').mkdir(exist_ok=True)
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(f'logs/deployment_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log'),
                logging.StreamHandler()
            ]
        )
        return logging.getLogger('ProductionDeployer')
    
    def validate_system_requirements(self):
        """Validate system requirements and dependencies."""
        self.logger.info("Validating system requirements...")
        
        # Check Python version
        python_version = sys.version_info
        if python_version.major < 3 or (python_version.major == 3 and python_version.minor < 8):
            self.logger.error(f"Python 3.8+ required, found {python_version.major}.{python_version.minor}")
            return False
        
        # Check required packages
        required_packages = [
            'pandas>=1.3.0',
            'numpy>=1.20.0',
            'scikit-learn>=1.0.0',
            'xgboost>=1.5.0',
            'lightgbm>=3.2.0',
            'yfinance>=0.1.70'
        ]
        
        missing_packages = []
        for package in required_packages:
            try:
                pkg_resources.require(package)
            except (pkg_resources.DistributionNotFound, pkg_resources.VersionConflict) as e:
                missing_packages.append(package)
                self.logger.warning(f"Missing or incompatible package: {package}")
        
        if missing_packages:
            self.logger.error(f"Missing packages: {missing_packages}")
            self.logger.info("Install missing packages with: pip install " + " ".join(missing_packages))
            return False
        
        # Check directory structure
        required_dirs = [
            'src/data_collector',
            'src/analysis',
            'config',
            'data',
            'models',
            'results',
            'logs'
        ]
        
        for dir_path in required_dirs:
            if not Path(dir_path).exists():
                self.logger.error(f"Required directory missing: {dir_path}")
                return False
        
        # Check configuration files
        config_files = [
            'config/international_markets_config.json'
        ]
        
        for config_file in config_files:
            if not Path(config_file).exists():
                self.logger.error(f"Required configuration file missing: {config_file}")
                return False
        
        self.logger.info("System requirements validation passed")
        return True
    
    def create_backup(self):
        """Create backup of current system state."""
        self.logger.info(f"Creating system backup in {self.backup_dir}")
        
        self.backup_dir.mkdir(parents=True, exist_ok=True)
        
        # Backup directories to preserve
        backup_items = [
            ('config', 'config'),
            ('src', 'src'),
            ('models', 'models'),
            ('data', 'data'),
            ('results', 'results')
        ]
        
        for source, dest in backup_items:
            source_path = Path(source)
            dest_path = self.backup_dir / dest
            
            if source_path.exists():
                if source_path.is_dir():
                    try:
                        shutil.copytree(source_path, dest_path)
                    except Exception as e:
                        self.logger.warning(f"Partial backup of {source}: {e}")
                else:
                    shutil.copy2(source_path, dest_path)
                self.logger.info(f"Backed up {source} to {dest_path}")
        
        # Create backup manifest
        backup_manifest = {
            'backup_timestamp': self.deployment_timestamp,
            'system_state': 'pre_deployment',
            'backed_up_items': [item[0] for item in backup_items if Path(item[0]).exists()],
            'python_version': f"{sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}"
        }
        
        with open(self.backup_dir / 'backup_manifest.json', 'w') as f:
            json.dump(backup_manifest, f, indent=2)
        
        self.logger.info("Backup completed successfully")
        return True
    
    def run_comprehensive_tests(self):
        """Run comprehensive test suite before deployment."""
        self.logger.info("Running comprehensive test suite...")
        
        try:
            # Run unit tests
            result = subprocess.run([
                sys.executable, '-m', 'pytest', 
                'tests/test_international_system.py', 
                '-v', '--tb=short'
            ], capture_output=True, text=True, timeout=300)
            
            if result.returncode != 0:
                self.logger.error("Unit tests failed:")
                self.logger.error(result.stdout)
                self.logger.error(result.stderr)
                return False
            
            self.logger.info("Unit tests passed")
            
            # Run system integration test
            result = subprocess.run([
                sys.executable, 'quick_demo.py'
            ], capture_output=True, text=True, timeout=180)
            
            if result.returncode != 0:
                self.logger.error("Integration tests failed:")
                self.logger.error(result.stdout)
                self.logger.error(result.stderr)
                return False
            
            self.logger.info("Integration tests passed")
            
            # Test international trading system setup
            result = subprocess.run([
                sys.executable, 'international_trading_system.py', '--mode', 'setup'
            ], capture_output=True, text=True, timeout=60)
            
            if result.returncode != 0:
                self.logger.error("System setup test failed:")
                self.logger.error(result.stdout)
                self.logger.error(result.stderr)
                return False
            
            self.logger.info("System setup test passed")
            
        except subprocess.TimeoutExpired:
            self.logger.error("Tests timed out")
            return False
        except Exception as e:
            self.logger.error(f"Test execution failed: {e}")
            return False
        
        self.logger.info("All tests passed successfully")
        return True
    
    def configure_production_settings(self):
        """Configure production-specific settings."""
        self.logger.info("Configuring production settings...")
        
        # Load base configuration
        with open('config/international_markets_config.json', 'r') as f:
            config = json.load(f)
        
        # Production-specific adjustments
        production_config = config.copy()
        
        # Adjust collection settings for production
        if 'collection_settings' in production_config:
            production_config['collection_settings'].update({
                'rate_limits': {
                    'yahoo_finance': 200,  # Conservative rate limiting
                    'alpha_vantage': 5,
                    'quandl': 50
                },
                'retry_attempts': 3,
                'timeout_seconds': 30,
                'cache_duration_hours': 1
            })
        
        # Add production monitoring
        production_config['production_settings'] = {
            'monitoring_enabled': True,
            'alert_thresholds': {
                'data_collection_failure_rate': 0.1,
                'model_prediction_error_threshold': 0.001,
                'correlation_change_threshold': 0.2
            },
            'backup_frequency_hours': 24,
            'model_retrain_frequency_days': 7,
            'health_check_interval_minutes': 15
        }
        
        # Save production configuration
        production_config_path = 'config/production_international_markets_config.json'
        with open(production_config_path, 'w') as f:
            json.dump(production_config, f, indent=2)
        
        self.deployment_config = production_config
        self.logger.info(f"Production configuration saved to {production_config_path}")
        return True
    
    def setup_monitoring(self):
        """Setup monitoring and alerting systems."""
        self.logger.info("Setting up monitoring systems...")
        
        # Create monitoring directory structure
        monitoring_dirs = [
            'monitoring/logs',
            'monitoring/metrics',
            'monitoring/alerts',
            'monitoring/health_checks'
        ]
        
        for dir_path in monitoring_dirs:
            Path(dir_path).mkdir(parents=True, exist_ok=True)
        
        # Create health check script
        health_check_script = '''#!/usr/bin/env python3
"""
Health check script for International Trading System
"""
import json
import sys
from pathlib import Path
from datetime import datetime, timedelta

def check_system_health():
    """Perform comprehensive system health check."""
    health_status = {
        'timestamp': datetime.now().isoformat(),
        'status': 'healthy',
        'checks': {}
    }
    
    # Check if recent data exists
    international_data_dir = Path('international_data')
    if international_data_dir.exists():
        recent_files = list(international_data_dir.rglob('*.csv'))
        health_status['checks']['data_files'] = {
            'status': 'ok' if recent_files else 'warning',
            'file_count': len(recent_files)
        }
    else:
        health_status['checks']['data_files'] = {
            'status': 'error',
            'message': 'International data directory missing'
        }
        health_status['status'] = 'unhealthy'
    
    # Check model files
    models_dir = Path('models/enhanced')
    if models_dir.exists():
        model_files = list(models_dir.glob('*.pkl')) + list(models_dir.glob('*.h5'))
        health_status['checks']['models'] = {
            'status': 'ok' if model_files else 'warning',
            'model_count': len(model_files)
        }
    else:
        health_status['checks']['models'] = {
            'status': 'warning',
            'message': 'No trained models found'
        }
    
    # Check configuration
    config_file = Path('config/production_international_markets_config.json')
    health_status['checks']['configuration'] = {
        'status': 'ok' if config_file.exists() else 'error',
        'config_exists': config_file.exists()
    }
    
    if not config_file.exists():
        health_status['status'] = 'unhealthy'
    
    # Save health check result
    health_file = Path('monitoring/health_checks') / f"health_check_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    with open(health_file, 'w') as f:
        json.dump(health_status, f, indent=2)
    
    print(f"Health Status: {health_status['status']}")
    return health_status['status'] == 'healthy'

if __name__ == '__main__':
    sys.exit(0 if check_system_health() else 1)
'''
        
        with open('monitoring/health_check.py', 'w') as f:
            f.write(health_check_script)
        
        # Make health check executable
        import stat
        health_check_path = Path('monitoring/health_check.py')
        health_check_path.chmod(health_check_path.stat().st_mode | stat.S_IEXEC)
        
        self.logger.info("Monitoring systems configured")
        return True
    
    def validate_deployment(self):
        """Validate the deployment was successful."""
        self.logger.info("Validating deployment...")
        
        # Run health check
        try:
            result = subprocess.run([
                sys.executable, 'monitoring/health_check.py'
            ], capture_output=True, text=True, timeout=30)
            
            if result.returncode != 0:
                self.logger.error("Health check failed")
                return False
            
        except Exception as e:
            self.logger.error(f"Health check execution failed: {e}")
            return False
        
        # Test system components
        try:
            # Test international trading system initialization
            result = subprocess.run([
                sys.executable, 'international_trading_system.py', '--mode', 'setup'
            ], capture_output=True, text=True, timeout=60)
            
            if result.returncode != 0:
                self.logger.error("System initialization test failed")
                return False
            
        except Exception as e:
            self.logger.error(f"System test failed: {e}")
            return False
        
        self.logger.info("Deployment validation successful")
        return True
    
    def generate_deployment_report(self):
        """Generate comprehensive deployment report."""
        self.logger.info("Generating deployment report...")
        
        report = {
            'deployment_info': {
                'timestamp': self.deployment_timestamp,
                'deployment_date': datetime.now().isoformat(),
                'python_version': f"{sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}",
                'system_platform': sys.platform
            },
            'deployment_steps': {
                'requirements_validation': 'completed',
                'backup_creation': 'completed',
                'comprehensive_testing': 'completed',
                'production_configuration': 'completed',
                'monitoring_setup': 'completed',
                'deployment_validation': 'completed'
            },
            'system_capabilities': {
                'international_data_collection': True,
                'multi_market_correlation_analysis': True,
                'enhanced_ml_models': True,
                'cross_market_feature_engineering': True,
                'real_time_monitoring': True,
                'automated_health_checks': True
            },
            'production_configuration': {
                'config_file': 'config/production_international_markets_config.json',
                'monitoring_enabled': True,
                'backup_location': str(self.backup_dir),
                'health_check_script': 'monitoring/health_check.py'
            },
            'supported_markets': {
                'us_markets': ['S&P 500', 'NASDAQ', 'Dow Jones', 'VIX'],
                'european_markets': ['DAX', 'FTSE 100', 'CAC 40', 'EURO STOXX 50'],
                'asian_markets': ['Hang Seng', 'Shanghai Composite', 'KOSPI'],
                'australian_markets': ['ASX 200'],
                'forex_pairs': ['USD/JPY', 'EUR/JPY', 'GBP/JPY', 'AUD/JPY'],
                'commodities': ['Crude Oil', 'Gold', 'Silver', 'Copper'],
                'economic_indicators': ['10Y Treasury', '2Y Treasury']
            },
            'next_steps': [
                'Configure API keys for premium data sources',
                'Set up automated data collection schedule',
                'Configure alert notifications',
                'Implement model retraining pipeline',
                'Set up production monitoring dashboard'
            ]
        }
        
        # Save deployment report
        report_path = f'results/deployment_report_{self.deployment_timestamp}.json'
        with open(report_path, 'w') as f:
            json.dump(report, f, indent=2, default=str)
        
        # Generate human-readable summary
        summary_path = f'results/deployment_summary_{self.deployment_timestamp}.txt'
        with open(summary_path, 'w') as f:
            f.write("=== INTERNATIONAL MULTI-MARKET TRADING SYSTEM ===\n")
            f.write("           PRODUCTION DEPLOYMENT COMPLETE\n")
            f.write("================================================\n\n")
            
            f.write(f"Deployment Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"System Version: International Enhanced v1.0\n")
            f.write(f"Python Version: {sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}\n\n")
            
            f.write("DEPLOYMENT STATUS: ✓ SUCCESSFUL\n\n")
            
            f.write("SYSTEM CAPABILITIES:\n")
            f.write("✓ Real-time international market data collection\n")
            f.write("✓ Multi-market correlation analysis\n")
            f.write("✓ Enhanced ML models with international features\n")
            f.write("✓ Cross-market interaction modeling\n")
            f.write("✓ Automated monitoring and health checks\n")
            f.write("✓ Production-grade error handling and logging\n\n")
            
            f.write("SUPPORTED MARKETS:\n")
            f.write("• US Markets: S&P 500, NASDAQ, Dow Jones, VIX\n")
            f.write("• European Markets: DAX, FTSE 100, CAC 40\n")
            f.write("• Asian Markets: Hang Seng, Shanghai Composite, KOSPI\n")
            f.write("• Forex: USD/JPY, EUR/JPY, GBP/JPY, AUD/JPY\n")
            f.write("• Commodities: Oil, Gold, Silver, Copper\n")
            f.write("• Economic Indicators: Treasury rates, volatility indices\n\n")
            
            f.write("KEY ENHANCEMENTS OVER ORIGINAL SYSTEM:\n")
            f.write("• Expanded from Japan-only to global multi-market analysis\n")
            f.write("• Increased prediction accuracy through international correlations\n")
            f.write("• Enhanced risk management with global market sentiment\n")
            f.write("• Real-time cross-market interaction modeling\n")
            f.write("• Comprehensive monitoring and alerting system\n\n")
            
            f.write("PRODUCTION FILES:\n")
            f.write(f"• Configuration: config/production_international_markets_config.json\n")
            f.write(f"• Main System: international_trading_system.py\n")
            f.write(f"• Health Check: monitoring/health_check.py\n")
            f.write(f"• Backup Location: {self.backup_dir}\n")
            f.write(f"• Deployment Report: {report_path}\n\n")
            
            f.write("SYSTEM READY FOR PRODUCTION TRADING\n")
            f.write("================================================\n")
        
        self.logger.info(f"Deployment report saved to {report_path}")
        self.logger.info(f"Deployment summary saved to {summary_path}")
        
        return report_path, summary_path
    
    def deploy(self, skip_tests=False):
        """Execute complete production deployment."""
        self.logger.info("Starting production deployment of International Multi-Market Trading System")
        
        try:
            # Step 1: Validate requirements
            if not self.validate_system_requirements():
                self.logger.error("Requirements validation failed")
                return False
            
            # Step 2: Create backup
            if not self.create_backup():
                self.logger.error("Backup creation failed")
                return False
            
            # Step 3: Run tests (unless skipped)
            if not skip_tests:
                if not self.run_comprehensive_tests():
                    self.logger.error("Comprehensive tests failed")
                    return False
            else:
                self.logger.warning("Tests skipped by user request")
            
            # Step 4: Configure production settings
            if not self.configure_production_settings():
                self.logger.error("Production configuration failed")
                return False
            
            # Step 5: Setup monitoring
            if not self.setup_monitoring():
                self.logger.error("Monitoring setup failed")
                return False
            
            # Step 6: Validate deployment
            if not self.validate_deployment():
                self.logger.error("Deployment validation failed")
                return False
            
            # Step 7: Generate reports
            report_path, summary_path = self.generate_deployment_report()
            
            self.logger.info("="*60)
            self.logger.info("DEPLOYMENT COMPLETED SUCCESSFULLY!")
            self.logger.info("="*60)
            self.logger.info(f"Deployment report: {report_path}")
            self.logger.info(f"Deployment summary: {summary_path}")
            self.logger.info("International Multi-Market Trading System is ready for production")
            
            return True
            
        except Exception as e:
            self.logger.error(f"Deployment failed with unexpected error: {e}")
            return False

def main():
    """Main deployment entry point."""
    parser = argparse.ArgumentParser(description='Deploy International Multi-Market Trading System to Production')
    parser.add_argument('--skip-tests', action='store_true', 
                       help='Skip comprehensive testing (not recommended for production)')
    parser.add_argument('--force', action='store_true',
                       help='Force deployment even if some checks fail')
    
    args = parser.parse_args()
    
    deployer = ProductionDeployer()
    
    # Confirmation for production deployment
    if not args.force:
        print("This will deploy the International Multi-Market Trading System to production.")
        print("This includes creating backups, running tests, and configuring production settings.")
        confirmation = input("Continue with production deployment? (yes/no): ")
        
        if confirmation.lower() not in ['yes', 'y']:
            print("Deployment cancelled by user.")
            sys.exit(0)
    
    success = deployer.deploy(skip_tests=args.skip_tests)
    sys.exit(0 if success else 1)

if __name__ == '__main__':
    main()