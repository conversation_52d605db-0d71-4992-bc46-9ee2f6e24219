#!/usr/bin/env python3
"""
Nikkei225 AI Trading System - Complete System Setup

This script performs a complete setup of the trading system including:
- Environment setup
- Dependency installation
- Configuration initialization
- Directory structure creation
- Module fixes and imports
"""

import os
import sys
import subprocess
import logging
from pathlib import Path

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def run_environment_setup():
    """Run the environment setup script"""
    logger.info("🔧 Running environment setup...")
    try:
        result = subprocess.run([sys.executable, "setup_environment.py"], 
                              capture_output=True, text=True)
        if result.returncode == 0:
            logger.info("✅ Environment setup completed successfully")
            return True
        else:
            logger.error(f"❌ Environment setup failed: {result.stderr}")
            return False
    except Exception as e:
        logger.error(f"❌ Failed to run environment setup: {e}")
        return False

def fix_import_issues():
    """Fix remaining import issues in the codebase"""
    logger.info("🔧 Fixing import issues...")
    
    # Files that need import fixes
    import_fixes = [
        {
            "file": "scripts/data_collection/improved_nikkei_data_collector.py",
            "old_import": "from nikkei225_tickers import",
            "new_import": "from src.utils.nikkei225_tickers import"
        },
        {
            "file": "scripts/setup/verify_data_completeness.py", 
            "old_import": "from nikkei225_tickers import",
            "new_import": "from src.utils.nikkei225_tickers import"
        }
    ]
    
    for fix in import_fixes:
        file_path = Path(fix["file"])
        if file_path.exists():
            try:
                with open(file_path, 'r') as f:
                    content = f.read()
                
                if fix["old_import"] in content:
                    content = content.replace(fix["old_import"], fix["new_import"])
                    
                    with open(file_path, 'w') as f:
                        f.write(content)
                    
                    logger.info(f"✅ Fixed imports in {file_path}")
            except Exception as e:
                logger.warning(f"⚠ Could not fix imports in {file_path}: {e}")
    
    logger.info("✅ Import fixes completed")

def create_missing_init_files():
    """Create missing __init__.py files"""
    logger.info("🔧 Creating missing __init__.py files...")
    
    directories_needing_init = [
        "src",
        "src/utils", 
        "src/trading",
        "src/trading/auto_trader",
        "src/trading/production",
        "src/trading/test",
        "src/data_collector",
        "src/data_collector/fetchers",
        "src/analysis",
        "src/analytics",
        "src/web",
        "tests",
        "tests/unit",
        "tests/integration"
    ]
    
    for directory in directories_needing_init:
        dir_path = Path(directory)
        if dir_path.exists():
            init_file = dir_path / "__init__.py"
            if not init_file.exists():
                init_file.touch()
                logger.info(f"✅ Created {init_file}")

def create_configuration_files():
    """Create default configuration files"""
    logger.info("🔧 Creating configuration files...")
    
    config_dir = Path("config")
    config_dir.mkdir(exist_ok=True)
    
    # Simple trading config
    simple_config = {
        "trading": {
            "mode": "paper",
            "initial_balance": 1000000,
            "max_stocks": 5,
            "commission_rate": 0.0
        },
        "data": {
            "interval": "1d",
            "period": "3mo",
            "cache_enabled": True
        }
    }
    
    # Production trading config
    production_config = {
        "trading": {
            "mode": "live",
            "initial_balance": 1000000,
            "max_stocks": 3,
            "commission_rate": 0.0001,
            "risk_management": {
                "max_position_size": 0.2,
                "stop_loss_pct": 0.05,
                "take_profit_pct": 0.15
            }
        },
        "data": {
            "interval": "1h",
            "period": "6mo",
            "cache_enabled": True
        }
    }
    
    # International markets config
    international_config = {
        "markets": {
            "us": {
                "indices": ["^GSPC", "^DJI", "^IXIC"],
                "major_stocks": ["AAPL", "MSFT", "GOOGL", "AMZN", "TSLA"]
            },
            "europe": {
                "indices": ["^FTSE", "^GDAXI", "^FCHI"],
                "major_stocks": ["ASML", "SAP", "LVMH"]
            },
            "asia": {
                "indices": ["^HSI", "000001.SS", "^STI"],
                "major_stocks": ["0700.HK", "9988.HK", "2330.TW"]
            }
        },
        "forex": {
            "pairs": ["USDJPY=X", "EURJPY=X", "GBPJPY=X", "AUDJPY=X"]
        },
        "commodities": {
            "symbols": ["GC=F", "CL=F", "SI=F", "^TNX"]
        }
    }
    
    configs = [
        ("simple_trading_config.json", simple_config),
        ("production_trading_config.json", production_config),
        ("international_markets_config.json", international_config)
    ]
    
    for filename, config_data in configs:
        config_file = config_dir / filename
        if not config_file.exists():
            import json
            with open(config_file, 'w') as f:
                json.dump(config_data, f, indent=2)
            logger.info(f"✅ Created {config_file}")

def test_imports():
    """Test critical imports to ensure they work"""
    logger.info("🧪 Testing critical imports...")
    
    test_imports = [
        "import pandas as pd",
        "import numpy as np", 
        "import matplotlib.pyplot as plt",
        "from src.utils.nikkei225_tickers import get_all_tickers",
        "from src.utils.config_manager import config",
        "from src.ai_evaluator import AIEvaluator"
    ]
    
    for import_statement in test_imports:
        try:
            exec(import_statement)
            logger.info(f"✅ {import_statement}")
        except ImportError as e:
            logger.warning(f"⚠ {import_statement} - {e}")
        except Exception as e:
            logger.error(f"❌ {import_statement} - {e}")

def create_startup_scripts():
    """Create convenient startup scripts"""
    logger.info("🔧 Creating startup scripts...")
    
    # Quick start script
    quick_start_content = '''#!/bin/bash
# Quick Start Script for Nikkei225 AI Trading System

echo "🚀 Starting Nikkei225 AI Trading System..."

# Check if virtual environment exists
if [ ! -d "venv" ]; then
    echo "Creating virtual environment..."
    python3 -m venv venv
fi

# Activate virtual environment
source venv/bin/activate

# Install/update dependencies
echo "Installing dependencies..."
pip install -r requirements.txt

# Run system setup
echo "Running system setup..."
python setup_system.py

# Start the system
echo "Starting trading system..."
python continuous_ai_trader.py --demo-mode

echo "✅ System started successfully!"
'''
    
    with open("quick_start.sh", "w") as f:
        f.write(quick_start_content)
    
    # Make executable
    os.chmod("quick_start.sh", 0o755)
    
    logger.info("✅ Created quick_start.sh")

def main():
    """Main setup function"""
    logger.info("🚀 Starting complete system setup...")
    
    # Step 1: Run environment setup
    if not run_environment_setup():
        logger.error("❌ Environment setup failed. Please check the logs.")
        return False
    
    # Step 2: Fix import issues
    fix_import_issues()
    
    # Step 3: Create missing __init__.py files
    create_missing_init_files()
    
    # Step 4: Create configuration files
    create_configuration_files()
    
    # Step 5: Test imports
    test_imports()
    
    # Step 6: Create startup scripts
    create_startup_scripts()
    
    logger.info("✅ Complete system setup finished!")
    logger.info("")
    logger.info("🎯 Next steps:")
    logger.info("1. Copy .env.example to .env and configure your settings")
    logger.info("2. If using KabuStation, set KABU_API_PWD in .env")
    logger.info("3. Run: ./quick_start.sh to start the system")
    logger.info("4. Or run: python continuous_ai_trader.py --demo-mode")
    logger.info("")
    logger.info("📚 For more information, see README.md")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
