#!/usr/bin/env python3
"""
Test script to verify all fixes are working correctly
"""

import sys
import os
import importlib
import logging
from pathlib import Path

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_basic_imports():
    """Test basic Python package imports"""
    logger.info("🧪 Testing basic imports...")
    
    basic_packages = [
        "numpy",
        "pandas", 
        "matplotlib",
        "requests",
        "json",
        "os",
        "sys",
        "pathlib"
    ]
    
    success_count = 0
    for package in basic_packages:
        try:
            importlib.import_module(package)
            logger.info(f"✅ {package}")
            success_count += 1
        except ImportError as e:
            logger.error(f"❌ {package}: {e}")
    
    logger.info(f"Basic imports: {success_count}/{len(basic_packages)} successful")
    return success_count == len(basic_packages)

def test_custom_modules():
    """Test custom module imports"""
    logger.info("🧪 Testing custom modules...")
    
    # Add src to path
    sys.path.insert(0, str(Path("src").absolute()))
    
    custom_modules = [
        ("src.utils.nikkei225_tickers", "get_all_tickers"),
        ("src.utils.config_manager", "ConfigManager"),
        ("src.ai_evaluator", "AIEvaluator")
    ]
    
    success_count = 0
    for module_name, class_or_function in custom_modules:
        try:
            module = importlib.import_module(module_name)
            if hasattr(module, class_or_function):
                logger.info(f"✅ {module_name}.{class_or_function}")
                success_count += 1
            else:
                logger.error(f"❌ {module_name}: {class_or_function} not found")
        except ImportError as e:
            logger.error(f"❌ {module_name}: {e}")
        except Exception as e:
            logger.error(f"❌ {module_name}: Unexpected error - {e}")
    
    logger.info(f"Custom modules: {success_count}/{len(custom_modules)} successful")
    return success_count == len(custom_modules)

def test_configuration():
    """Test configuration management"""
    logger.info("🧪 Testing configuration...")
    
    try:
        sys.path.insert(0, str(Path("src").absolute()))
        from src.utils.config_manager import ConfigManager
        
        config = ConfigManager()
        
        # Test basic configuration access
        trading_mode = config.get("trading", "mode")
        initial_balance = config.get("trading", "initial_balance")
        
        logger.info(f"✅ Trading mode: {trading_mode}")
        logger.info(f"✅ Initial balance: {initial_balance}")
        
        # Test configuration validation
        is_valid = config.validate_config()
        logger.info(f"✅ Configuration valid: {is_valid}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Configuration test failed: {e}")
        return False

def test_nikkei_tickers():
    """Test Nikkei225 ticker utilities"""
    logger.info("🧪 Testing Nikkei225 tickers...")
    
    try:
        sys.path.insert(0, str(Path("src").absolute()))
        from src.utils.nikkei225_tickers import (
            get_all_tickers, get_major_tickers, get_sector_names,
            get_tickers_by_sector, is_valid_ticker
        )
        
        # Test basic functions
        all_tickers = get_all_tickers()
        major_tickers = get_major_tickers()
        sectors = get_sector_names()
        
        logger.info(f"✅ Total tickers: {len(all_tickers)}")
        logger.info(f"✅ Major tickers: {len(major_tickers)}")
        logger.info(f"✅ Sectors: {len(sectors)}")
        
        # Test specific ticker
        test_ticker = "7203.T"  # Toyota
        is_valid = is_valid_ticker(test_ticker)
        logger.info(f"✅ {test_ticker} is valid: {is_valid}")
        
        # Test sector filtering
        tech_tickers = get_tickers_by_sector("Technology")
        logger.info(f"✅ Technology tickers: {len(tech_tickers)}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Nikkei tickers test failed: {e}")
        return False

def test_ai_evaluator():
    """Test AI evaluator module"""
    logger.info("🧪 Testing AI evaluator...")
    
    try:
        sys.path.insert(0, str(Path("src").absolute()))
        from src.ai_evaluator import AIEvaluator
        import numpy as np
        
        # Create evaluator instance
        evaluator = AIEvaluator()
        
        # Test with dummy data
        y_true = np.array([100, 105, 102, 108, 110])
        y_pred = np.array([101, 104, 103, 107, 109])
        
        metrics = evaluator.evaluate_predictions(y_true, y_pred, "test_model")
        
        logger.info(f"✅ RMSE: {metrics['rmse']:.4f}")
        logger.info(f"✅ Direction accuracy: {metrics['direction_accuracy']:.2%}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ AI evaluator test failed: {e}")
        return False

def test_directory_structure():
    """Test that required directories exist"""
    logger.info("🧪 Testing directory structure...")
    
    required_dirs = [
        "src",
        "src/utils",
        "src/trading", 
        "config",
        "data",
        "models",
        "results",
        "logs"
    ]
    
    success_count = 0
    for directory in required_dirs:
        dir_path = Path(directory)
        if dir_path.exists():
            logger.info(f"✅ {directory}")
            success_count += 1
        else:
            logger.error(f"❌ {directory} - missing")
    
    logger.info(f"Directory structure: {success_count}/{len(required_dirs)} present")
    return success_count == len(required_dirs)

def test_config_files():
    """Test configuration files"""
    logger.info("🧪 Testing configuration files...")
    
    config_files = [
        "config/simple_trading_config.json",
        "config/production_trading_config.json",
        "config/international_markets_config.json"
    ]
    
    success_count = 0
    for config_file in config_files:
        file_path = Path(config_file)
        if file_path.exists():
            try:
                import json
                with open(file_path, 'r') as f:
                    config_data = json.load(f)
                logger.info(f"✅ {config_file}")
                success_count += 1
            except json.JSONDecodeError as e:
                logger.error(f"❌ {config_file} - invalid JSON: {e}")
        else:
            logger.warning(f"⚠ {config_file} - missing")
    
    logger.info(f"Config files: {success_count}/{len(config_files)} valid")
    return success_count >= 1  # At least one config file should exist

def main():
    """Run all tests"""
    logger.info("🚀 Starting comprehensive system tests...")
    logger.info("=" * 60)
    
    tests = [
        ("Basic Imports", test_basic_imports),
        ("Custom Modules", test_custom_modules),
        ("Configuration", test_configuration),
        ("Nikkei Tickers", test_nikkei_tickers),
        ("AI Evaluator", test_ai_evaluator),
        ("Directory Structure", test_directory_structure),
        ("Config Files", test_config_files)
    ]
    
    results = []
    for test_name, test_func in tests:
        logger.info(f"\n📋 Running {test_name} test...")
        try:
            result = test_func()
            results.append((test_name, result))
            if result:
                logger.info(f"✅ {test_name} test PASSED")
            else:
                logger.error(f"❌ {test_name} test FAILED")
        except Exception as e:
            logger.error(f"❌ {test_name} test ERROR: {e}")
            results.append((test_name, False))
    
    # Summary
    logger.info("\n" + "=" * 60)
    logger.info("📊 TEST SUMMARY")
    logger.info("=" * 60)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        logger.info(f"{status} - {test_name}")
    
    logger.info(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        logger.info("🎉 All tests passed! System is ready to use.")
        return True
    else:
        logger.warning(f"⚠ {total - passed} tests failed. Please check the issues above.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
