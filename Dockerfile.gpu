# 日経225 AI取引システム - GPU対応版 Docker環境
FROM nvidia/cuda:12.1-devel-ubuntu22.04

# 環境変数設定
ENV DEBIAN_FRONTEND=noninteractive
ENV TZ=Asia/Tokyo
ENV PYTHONUNBUFFERED=1
ENV PYTHONPATH=/app
ENV TF_CPP_MIN_LOG_LEVEL=2
ENV CUDA_VISIBLE_DEVICES=0

# 基本パッケージのインストール
RUN apt-get update && apt-get install -y \
    python3.12 \
    python3.12-dev \
    python3-pip \
    python3.12-venv \
    git \
    curl \
    wget \
    tzdata \
    build-essential \
    libssl-dev \
    libffi-dev \
    libbz2-dev \
    libreadline-dev \
    libsqlite3-dev \
    libncurses5-dev \
    libncursesw5-dev \
    xz-utils \
    tk-dev \
    libxml2-dev \
    libxmlsec1-dev \
    libhdf5-dev \
    pkg-config \
    && rm -rf /var/lib/apt/lists/*

# Python 3.12をデフォルトに設定
RUN update-alternatives --install /usr/bin/python3 python3 /usr/bin/python3.12 1
RUN update-alternatives --install /usr/bin/pip3 pip3 /usr/bin/pip3 1

# pipのアップグレード
RUN python3 -m pip install --upgrade pip setuptools wheel

# 作業ディレクトリの設定
WORKDIR /app

# Pythonの依存関係をコピーしてインストール
COPY requirements.txt .
RUN pip3 install --no-cache-dir -r requirements.txt

# GPU対応ライブラリ
RUN pip3 install --no-cache-dir \
    torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu121 \
    tensorflow[and-cuda]

# アプリケーションコードをコピー
COPY . .

# ディレクトリの作成
RUN mkdir -p /app/logs/errors /app/logs/trading /app/data/cache /app/models /app/results

# 実行権限の設定
RUN chmod +x *.sh 2>/dev/null || true

# GPU環境設定スクリプト
RUN echo '#!/bin/bash' > /app/setup_env.sh && \
    echo 'export TF_ENABLE_ONEDNN_OPTS=0' >> /app/setup_env.sh && \
    echo 'export PYTORCH_CUDA_ALLOC_CONF=max_split_size_mb:512' >> /app/setup_env.sh && \
    echo 'python3 src/gpu_config.py' >> /app/setup_env.sh && \
    chmod +x /app/setup_env.sh

# エントリーポイントスクリプト作成
RUN cat > /app/entrypoint.sh << 'EOF'
#!/bin/bash
set -e

echo "🚀 日経225 AI取引システム - GPU Docker起動中..."

# GPU環境初期化
source /app/setup_env.sh

# 環境変数確認
if [ -z "$KABU_API_PWD" ]; then
    echo "⚠️  警告: KABU_API_PWD環境変数が設定されていません"
fi

# 引数に応じた実行
case "$1" in
    "web")
        echo "🖥️  Webダッシュボードを起動中..."
        exec python3 src/web/run_web_app.py
        ;;
    "trading")
        echo "💰 取引システム（GPU加速）を起動中..."
        exec python3 -m src.trading.kabu_system
        ;;
    "data")
        echo "📊 データ収集システムを起動中..."
        exec python3 src/data_collector/optimal_data_collector.py
        ;;
    "test")
        echo "🧪 テストを実行中..."
        exec python3 -m pytest tests/ -v
        ;;
    *)
        echo "🔗 統合システム（GPU加速）を起動中..."
        # バックグラウンドでデータ収集開始
        python3 src/data_collector/optimal_data_collector.py &
        # Webダッシュボード起動
        exec python3 src/web/run_web_app.py
        ;;
esac
EOF

RUN chmod +x /app/entrypoint.sh

# ヘルスチェック
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD python3 -c "import requests; requests.get('http://localhost:5000/health', timeout=5)" || exit 1

# ポートの公開
EXPOSE 5000 8080

# デフォルトエントリーポイント
ENTRYPOINT ["/app/entrypoint.sh"]
CMD ["web"]