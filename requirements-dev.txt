# Development and Testing Requirements
# Install with: pip install -r requirements-dev.txt

# Include base requirements
-r requirements.txt

# Testing frameworks
pytest>=7.4.0
pytest-cov>=4.1.0
pytest-html>=3.2.0
pytest-mock>=3.11.0
pytest-asyncio>=0.21.0

# Code quality and formatting
black>=23.0.0
flake8>=6.0.0
isort>=5.12.0
mypy>=1.5.0

# Documentation
sphinx>=7.0.0
sphinx-rtd-theme>=1.3.0

# Development utilities
ipython>=8.14.0
jupyter>=1.0.0
notebook>=7.0.0

# Debugging and profiling
pdb++>=0.10.3
memory-profiler>=0.61.0
line-profiler>=4.1.0

# Additional data science tools for development
plotly>=5.15.0
dash>=2.12.0
streamlit>=1.25.0

# API testing
httpx>=0.24.0
responses>=0.23.0

# Performance monitoring
psutil>=5.9.0
