# 🚀 Nikkei225 AI取引システム

## 📖 概要

Nikkei225 AI取引システムは、機械学習とAI技術を活用した日経225銘柄の自動取引プラットフォームです。リアルタイムの市場データ分析、高度なAI予測モデル、そして自動取引実行機能を統合したエンタープライズグレードのシステムです。

## ✨ 主要機能

### 🤖 AI・機械学習
- **アンサンブル学習**: LSTM + Transformer + XGBoost + LightGBM
- **強化学習**: Stable-Baselines3 DQN/PPO
- **継続学習**: A/Bテスト、自動パラメータ最適化
- **GPU加速**: CUDA対応で3-10倍高速化

### 📊 データ収集・処理
- **Kabu Station API**: 日本市場リアルタイムデータ
- **国際市場データ**: 米国、欧州、アジア主要市場
- **209銘柄対応**: Nikkei 225全構成銘柄
- **20+テクニカル指標**: カスタム特徴量エンジン

### 💼 取引システム
- **4つの取引モード**: nocash, paper, dryrun, live
- **自動スケジューリング**: 9:00開始〜14:30完全売却
- **リスク管理**: 損切り・利確・ポジションサイズ制御
- **完全自動化**: 24時間365日稼働可能

### 🖥️ 監視・運用
- **Webダッシュボード**: リアルタイム監視・制御
- **構造化ログ**: JSON形式の包括的ログ
- **ヘルスモニタリング**: 自動システム監視
- **Docker対応**: コンテナ化された本番環境

## 🚀 クイックスタート

### 必要な環境
- Python 3.8以上（推奨: 3.12）
- メモリ: 16GB以上
- ストレージ: 10GB以上
- NVIDIA GPU（推奨、CUDA対応）

### 1. インストール

```bash
# リポジトリをクローン
git clone https://github.com/your-username/nikkei225-ai-project.git
cd nikkei225-ai-project

# 依存関係をインストール
pip install -r requirements.txt

# 環境変数を設定
export KABU_API_PWD="your_kabu_password"
```

### 2. システム起動

#### 簡単な開始方法（Docker推奨）

**🖥️ 開発モード（CPU専用）**
```bash
# 開発・テスト用のクイックスタート
docker-compose up -d

# Webダッシュボードにアクセス
open http://localhost:5001
```

**🚀 本番モード（GPU加速）**
```bash
# GPU加速による本番取引用
docker-compose -f docker-compose.gpu.yml up -d

# NVIDIA GPU + Docker GPU toolkitが必要
# 詳細設定は DOCKER_SETUP.md を参照
```

#### 手動起動
```bash
# メインシステム起動
python continuous_ai_trader.py --initial-balance 1000000 --max-stocks 5

# Webインターフェース起動（別ターミナル）
python src/web/run_web_app.py
```

### 3. 取引モード設定

```bash
# 安全な検証モード（推奨で開始）
./set_kabu_mode.sh dryrun

# 仮想取引モード
./set_kabu_mode.sh paper

# 本番取引モード（十分なテスト後）
./set_kabu_mode.sh live
```

## 📈 取引モード詳細

| モード | 実データ | 注文実行 | 用途 |
|--------|----------|----------|------|
| **nocash** | ✅ | ❌ | 精度評価・アルゴリズム検証 |
| **paper** | ✅ | 仮想 | 仮想取引・戦略テスト |
| **dryrun** | ✅ | ❌ | AIデータ蓄積・学習最適化 |
| **live** | ✅ | ✅ | **本番取引・実資金運用** |

## ⏰ 取引スケジュール

```
09:00 ── 取引開始（AI予測上位銘柄購入）
  │
10:00 ── 第1回リバランス（損切り・銘柄入替）
  │
11:00 ── 第2回リバランス（追加調整）
  │
14:00 ── 引け前売却開始（全銘柄売却・利確）
  │
14:30 ── 売却完了（次営業日買付資金確保）
  │
15:00 ── 取引終了
  │
16:30 ── 日次分析・レポート生成
```

## 🏗️ プロジェクト構造

```
nikkei225_ai_project/
├── production/              # 本番環境
│   ├── main.py             # メインエントリーポイント
│   ├── run_trading.py      # 取引システム実行
│   ├── core/               # コアソースコード
│   ├── data/               # データファイル
│   ├── models/             # AIモデル
│   └── web/                # Webダッシュボード
├── development/            # 開発環境
│   ├── tests/              # テストファイル
│   ├── examples/           # サンプル・デモ
│   └── prototypes/         # 実験的コード
├── docs/                   # ドキュメント
├── config/                 # 設定ファイル
└── scripts/               # 各種スクリプト
```

## 📊 システム性能

### 運用実績
- **システム稼働率**: 99.9%（自動復旧機能付き）
- **取引成功率**: 100%（エラーハンドリング完備）
- **データ収集成功率**: 99.9%（209銘柄×24時間監視）

### AI・ML性能
- **GPU加速倍率**: 3-10倍高速化
- **予測処理速度**: <100ms（リアルタイム推論）
- **学習効率**: 自動A/Bテスト・パラメータ最適化

## 🔒 セキュリティ・リスク管理

### APIセキュリティ
- 環境変数による認証情報管理
- レート制限対応・API制限回避
- セッション管理・タイムアウト処理

### 取引リスク管理
- 14:00-14:30強制売却によるポジション持ち越し防止
- 損切り・利確ロジック
- 最大保有銘柄数制限

### システム安定性
- GPU/CPUフォールバック機能
- 包括的エラーログ・モニタリング
- 自動復旧機能・アラートシステム

## 📚 ドキュメント

### ユーザーガイド
- [Webインターフェースガイド](docs/guides/web_interface_guide.md)
- [Kabu Station API連携ガイド](docs/guides/kabu_api_guide.md)  
- [継続学習システムガイド](docs/guides/continuous_learning_guide.md)
- [データ収集システムガイド](docs/guides/data_collector_guide.md)

### 技術文書
- [システム設計書](docs/architecture/system_design.md)

## 🧪 テスト実行

```bash
# 全テスト実行
cd development && python run_tests.py

# 単体テスト
python run_tests.py --type unit

# 統合テスト
python run_tests.py --type integration

# デモシステム実行
cd examples/demos && python demo_script.py
```

## 🐳 Docker使用方法

```bash
# イメージビルド
docker build -t nikkei225-ai-trading .

# コンテナ実行
docker run -d -p 5001:5001 \
  -e KABU_API_PWD="your_password" \
  -v $(pwd)/data:/app/data \
  nikkei225-ai-trading

# Docker Compose（推奨）
docker-compose up -d
```

## ⚠️ 重要な注意事項

- **投資リスク**: すべての投資には損失リスクが伴います
- **自己責任**: システム使用による損益は自己責任となります
- **テスト推奨**: Liveモード前に十分なテストを実施してください
- **規制遵守**: 適用される金融規制を遵守してください
- **バックアップ**: 設定・ログの定期的なバックアップを推奨

## 🤝 サポート・コントリビューション

### 問題報告
- GitHubのIssuesでバグ報告や機能要求を受け付けています
- ログファイルを含めて詳細な情報を提供してください

### コントリビューション
- プルリクエストを歓迎します
- コードスタイルガイドに従ってください
- テストを含めることを推奨します

## 📄 ライセンス

このプロジェクトはMITライセンスの下で公開されています。詳細は[LICENSE](LICENSE)ファイルを参照してください。

## 📞 連絡先

- 開発者: [Your Name]
- メール: [<EMAIL>]
- GitHub: [https://github.com/your-username/nikkei225-ai-project](https://github.com/your-username/nikkei225-ai-project)

---

**免責事項**: このソフトウェアは教育・研究目的で提供されています。実際の取引で使用する際は十分なテストと理解の上で自己責任で行ってください。開発者は投資の損失について一切の責任を負いません。