# Core data science and ML libraries - Python 3.12 compatible
numpy>=1.24.0
pandas>=2.0.0
matplotlib>=3.7.0
seaborn>=0.12.0
scikit-learn>=1.3.0

# Deep learning frameworks
tensorflow>=2.15.0
keras>=3.0.0
torch>=2.0.0
torchvision>=0.15.0
torchaudio>=2.0.0

# Gradient boosting
xgboost>=2.0.0
lightgbm>=4.0.0

# Financial data and technical analysis
yfinance>=0.2.18
pandas-datareader>=0.10.0
requests>=2.31.0
beautifulsoup4>=4.12.0
lxml>=4.9.0

# Reinforcement learning (gymnasium instead of deprecated gym)
gymnasium>=0.29.0
stable-baselines3>=2.0.0

# Utilities
pytz>=2023.3
schedule>=1.2.0
python-dotenv>=1.0.0
tqdm>=4.65.0

# Web interface
flask>=3.0.0
flask-socketio>=5.3.0
python-socketio>=5.8.0
eventlet>=0.33.0
flask-cors>=4.0.0

# System monitoring and APIs
psutil>=5.9.0
websocket-client>=1.6.0

# Optional: Technical analysis (install separately if needed)
# TA-Lib requires manual installation: conda install -c conda-forge ta-lib
# or follow instructions at https://github.com/mrjbq7/ta-lib

# Development and testing
pytest>=7.4.0
pytest-cov>=4.1.0
pytest-html>=3.2.0
