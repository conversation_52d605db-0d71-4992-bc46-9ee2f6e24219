# GPU対応 docker-compose.yml for Nikkei225 AI Trading System

services:
  # GPU対応のAI取引システム
  nikkei225-ai-trading-gpu:
    build:
      context: .
      dockerfile: Dockerfile.gpu
    container_name: nikkei225-ai-system-gpu
    restart: unless-stopped
    ports:
      - "5001:5000"    # Webダッシュボード
      - "8080:8080"    # API エンドポイント
    environment:
      - KABU_API_PWD=${KABU_API_PWD}
      - ALPHA_VANTAGE_API_KEY=${ALPHA_VANTAGE_API_KEY:-}
      - TF_ENABLE_ONEDNN_OPTS=0
      - PYTORCH_CUDA_ALLOC_CONF=max_split_size_mb:512
      - CUDA_VISIBLE_DEVICES=0
      - GPU_ENABLED=true
      - TZ=Asia/Tokyo
    volumes:
      # データ永続化
      - ./data:/app/data
      - ./logs:/app/logs
      - ./models:/app/models
      - ./results:/app/results
      - ./config:/app/config
      # タイムゾーン設定
      - /etc/localtime:/etc/localtime:ro
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: 1
              capabilities: [gpu]
    healthcheck:
      test: ["CMD", "python3", "-c", "import requests; requests.get('http://localhost:5000/health', timeout=5)"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    networks:
      - nikkei225-network

  # GPU対応取引システム専用
  nikkei225-trading-gpu:
    build:
      context: .
      dockerfile: Dockerfile.gpu
    container_name: nikkei225-trading-engine-gpu
    restart: unless-stopped
    environment:
      - KABU_API_PWD=${KABU_API_PWD}
      - TF_ENABLE_ONEDNN_OPTS=0
      - PYTORCH_CUDA_ALLOC_CONF=max_split_size_mb:512
      - CUDA_VISIBLE_DEVICES=0
      - GPU_ENABLED=true
      - TZ=Asia/Tokyo
    volumes:
      - ./data:/app/data
      - ./logs:/app/logs
      - ./models:/app/models
      - ./results:/app/results
      - ./config:/app/config
    command: ["trading"]
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: 1
              capabilities: [gpu]
    networks:
      - nikkei225-network
    profiles:
      - gpu-trading

  # Kabu Station モック（開発・テスト用）
  kabu-mock:
    build:
      context: ./kabu_mock
      dockerfile: Dockerfile
    container_name: kabu-mock
    environment:
      - TZ=Asia/Tokyo
      - KABU_MOCK_PORT=18082
      - KABU_WEBSOCKET_PORT=18083
      - KABU_API_PWD=${KABU_API_PWD:-marumori02}
      - DEBUG=false
    ports:
      - "18082:18082"
      - "18083:18083"
    restart: unless-stopped
    networks:
      - nikkei225-network
    profiles:
      - development

networks:
  nikkei225-network:
    driver: bridge

volumes:
  nikkei225-data:
    driver: local
  nikkei225-logs:
    driver: local
  nikkei225-models:
    driver: local