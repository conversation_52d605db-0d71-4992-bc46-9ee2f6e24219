#!/usr/bin/env python3
"""
yfinanceライブラリを使用して日経225銘柄のデータをダウンロードするスクリプト
"""

import os
import sys
import time
import random
import logging
import argparse
import pandas as pd
import yfinance as yf
from datetime import datetime, timedelta
from tqdm import tqdm

# ロギング設定
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f"logs/yfinance_download_{datetime.now().strftime('%Y%m%d')}.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# 保存ディレクトリの設定
DATA_DIR = "nikkei225_data"
CACHE_DIR = os.path.join(DATA_DIR, "cache")

# ティッカーリスト（簡略のため代表的な大型株30銘柄を定義）
MAJOR_TICKERS = [
    "7203.T",  # トヨタ自動車
    "9432.T",  # NTT
    "9984.T",  # ソフトバンク
    "8306.T",  # 三菱UFJ
    "6758.T",  # ソニー
    "6861.T",  # キーエンス
    "9433.T",  # KDDI
    "4063.T",  # 信越化学
    "8316.T",  # 三井住友FG
    "7974.T",  # 任天堂
    "4661.T",  # オリエンタルランド
    "9983.T",  # ファーストリテ
    "6367.T",  # ダイキン工業
    "6501.T",  # 日立製作所
    "6594.T",  # 日本電産
    "6902.T",  # デンソー
    "6981.T",  # 村田製作所
    "4519.T",  # 中外製薬
    "8035.T",  # 東京エレクトロン
    "7267.T",  # ホンダ
    "7741.T",  # HOYA
    "8766.T",  # 東京海上HD
    "8031.T",  # 三井物産
    "2914.T",  # JT
    "5401.T",  # 日本製鉄
    "6954.T",  # ファナック
    "4507.T",  # 塩野義製薬
    "4543.T",  # テルモ
    "4568.T",  # 第一三共
    "3382.T",  # セブン＆アイ
]


def ensure_dirs():
    """必要なディレクトリを作成"""
    os.makedirs(DATA_DIR, exist_ok=True)
    os.makedirs(CACHE_DIR, exist_ok=True)
    os.makedirs("logs", exist_ok=True)


def download_ticker_data(ticker, years=5, sleep_time=2, retries=3):
    """
    yfinanceを使用して銘柄の過去データをダウンロード
    
    Parameters:
    -----------
    ticker : str
        ティッカーシンボル
    years : int
        取得する年数
    sleep_time : float
        リクエスト間の待機時間
    retries : int
        リトライ回数
    
    Returns:
    --------
    pd.DataFrame or None
        成功した場合はデータフレーム、失敗した場合はNone
    """
    # 期間の計算
    end_date = datetime.now()
    start_date = end_date - timedelta(days=365 * years)
    
    # キャッシュファイル名
    cache_file = os.path.join(CACHE_DIR, f"{ticker}_{start_date.strftime('%Y%m%d')}_{end_date.strftime('%Y%m%d')}.csv")
    
    # キャッシュが存在する場合はそれを返す
    if os.path.exists(cache_file):
        try:
            df = pd.read_csv(cache_file, parse_dates=["Date"])
            logger.info(f"{ticker}のデータをキャッシュから読み込みました（{len(df)}行）")
            return df
        except Exception as e:
            logger.error(f"キャッシュ読み込みエラー: {str(e)}")
    
    logger.info(f"{ticker}の{start_date.strftime('%Y-%m-%d')}～{end_date.strftime('%Y-%m-%d')}のデータをダウンロード開始")
    
    # リトライループ
    for attempt in range(retries):
        try:
            # yfinanceを使用してデータ取得
            ticker_obj = yf.Ticker(ticker)
            df = ticker_obj.history(start=start_date, end=end_date, auto_adjust=True)
            
            # データが空でないか確認
            if df.empty:
                logger.warning(f"{ticker}のデータは空です")
                if attempt < retries - 1:
                    wait_time = sleep_time * (2 ** attempt) + random.uniform(0, 2)
                    logger.info(f"リトライ {attempt+1}/{retries} - {wait_time:.1f}秒待機...")
                    time.sleep(wait_time)
                continue
            
            # インデックスをリセットしてDateを列に変換
            df = df.reset_index()
            
            # 列名を標準化（yfinanceの出力はOpen, High, Low, Closeと大文字始まり）
            df = df.rename(columns={
                'Date': 'Date',
                'Open': 'Open',
                'High': 'High',
                'Low': 'Low',
                'Close': 'Close',
                'Volume': 'Volume'
            })
            
            # 必要な列のみ選択
            df = df[['Date', 'Open', 'High', 'Low', 'Close', 'Volume']]
            
            # ティッカー列を追加
            df['Ticker'] = ticker
            
            # キャッシュに保存
            df.to_csv(cache_file, index=False)
            
            logger.info(f"{ticker}のデータを取得しました（{len(df)}行）")
            time.sleep(sleep_time + random.uniform(0, 1))
            return df
        
        except Exception as e:
            logger.error(f"{ticker}のダウンロード中にエラー: {str(e)}")
            
            if attempt < retries - 1:
                wait_time = sleep_time * (2 ** attempt) + random.uniform(0, 2)
                logger.info(f"リトライ {attempt+1}/{retries} - {wait_time:.1f}秒待機...")
                time.sleep(wait_time)
    
    logger.error(f"{ticker}のダウンロードに{retries}回失敗しました")
    return None


def download_all_tickers(tickers=None, years=5, sleep_time=2, retries=3, output_file=None):
    """
    全銘柄のデータを一括ダウンロード
    
    Parameters:
    -----------
    tickers : list or None
        ティッカーのリスト（Noneの場合はMAJOR_TICKERSを使用）
    years : int
        取得する年数
    sleep_time : float
        リクエスト間の待機時間
    retries : int
        リトライ回数
    output_file : str or None
        出力CSVファイル名（Noneの場合は保存しない）
    
    Returns:
    --------
    pd.DataFrame
        全銘柄の結合データ
    """
    if tickers is None:
        tickers = MAJOR_TICKERS
    
    logger.info(f"{len(tickers)}銘柄の過去{years}年分のデータをダウンロード開始")
    
    all_data = []
    success_count = 0
    failure_count = 0
    failed_tickers = []
    
    for ticker in tqdm(tickers, desc="銘柄ダウンロード"):
        df = download_ticker_data(ticker, years, sleep_time, retries)
        
        if df is not None and not df.empty:
            all_data.append(df)
            success_count += 1
        else:
            failure_count += 1
            failed_tickers.append(ticker)
    
    if not all_data:
        logger.error("有効なデータがありません")
        return pd.DataFrame()
    
    # 全データを結合
    combined_df = pd.concat(all_data, ignore_index=True)
    
    logger.info(f"ダウンロード完了: 成功={success_count}, 失敗={failure_count}, 総レコード数={len(combined_df)}")
    
    if failed_tickers:
        logger.warning(f"失敗した銘柄: {', '.join(failed_tickers)}")
    
    # 指定があれば結合データを保存
    if output_file:
        os.makedirs(os.path.dirname(os.path.abspath(output_file)), exist_ok=True)
        combined_df.to_csv(output_file, index=False)
        logger.info(f"結合データを保存しました: {output_file}")
    
    return combined_df


def convert_to_feature_format(df, output_file=None):
    """
    ダウンロードしたデータを特徴量エンジニアリング用の形式に変換
    
    Parameters:
    -----------
    df : pd.DataFrame
        元のデータフレーム
    output_file : str or None
        出力ファイル名
    
    Returns:
    --------
    pd.DataFrame
        変換されたデータフレーム
    """
    # 日付でソート
    df = df.sort_values(['Ticker', 'Date'])
    
    # 前日比を計算
    df['PrevClose'] = df.groupby('Ticker')['Close'].shift(1)
    df['Change'] = df['Close'] - df['PrevClose']
    df['ChangePercent'] = (df['Change'] / df['PrevClose']) * 100
    
    # 5日、20日移動平均を計算
    df['MA5'] = df.groupby('Ticker')['Close'].transform(lambda x: x.rolling(window=5).mean())
    df['MA20'] = df.groupby('Ticker')['Close'].transform(lambda x: x.rolling(window=20).mean())
    
    # ボリンジャーバンド（20日）
    df['StdDev20'] = df.groupby('Ticker')['Close'].transform(lambda x: x.rolling(window=20).std())
    df['UpperBand'] = df['MA20'] + (df['StdDev20'] * 2)
    df['LowerBand'] = df['MA20'] - (df['StdDev20'] * 2)
    
    # 相対ストレングス指標（RSI）の計算（14日）
    df['Gain'] = df.groupby('Ticker')['Change'].transform(lambda x: x.clip(lower=0))
    df['Loss'] = df.groupby('Ticker')['Change'].transform(lambda x: -x.clip(upper=0))
    df['AvgGain'] = df.groupby('Ticker')['Gain'].transform(lambda x: x.rolling(window=14).mean())
    df['AvgLoss'] = df.groupby('Ticker')['Loss'].transform(lambda x: x.rolling(window=14).mean())
    df['RS'] = df['AvgGain'] / df['AvgLoss']
    df['RSI'] = 100 - (100 / (1 + df['RS']))
    
    # 不要な中間列を削除
    df = df.drop(['Gain', 'Loss', 'AvgGain', 'AvgLoss', 'RS'], axis=1)
    
    # NaNを削除
    df = df.dropna()
    
    # 指定されていれば保存
    if output_file:
        os.makedirs(os.path.dirname(os.path.abspath(output_file)), exist_ok=True)
        df.to_csv(output_file, index=False)
        logger.info(f"特徴量データを保存しました: {output_file}")
    
    return df


def main():
    """メイン関数"""
    # コマンドライン引数の解析
    parser = argparse.ArgumentParser(description='yfinanceを使用して株価データをダウンロード')
    parser.add_argument('--ticker', type=str, help='個別銘柄をダウンロード')
    parser.add_argument('--years', type=int, default=5, help='取得する年数（デフォルト: 5）')
    parser.add_argument('--sleep', type=float, default=2.0, help='リクエスト間の待機時間（デフォルト: 2秒）')
    parser.add_argument('--retries', type=int, default=3, help='リトライ回数（デフォルト: 3）')
    parser.add_argument('--output', type=str, default=os.path.join(DATA_DIR, "nikkei225_major_data.csv"),
                        help='結合データの出力ファイル（デフォルト: nikkei225_data/nikkei225_major_data.csv）')
    parser.add_argument('--all-major', action='store_true', help='主要銘柄を全てダウンロード')
    parser.add_argument('--with-features', action='store_true', help='特徴量エンジニアリングを実行')
    
    args = parser.parse_args()
    
    # 必要なディレクトリを作成
    ensure_dirs()
    
    # 個別銘柄のダウンロード
    if args.ticker:
        df = download_ticker_data(args.ticker, args.years, args.sleep, args.retries)
        if df is not None and not df.empty:
            ticker_file = os.path.join(DATA_DIR, f"{args.ticker.replace('.', '_')}_data.csv")
            df.to_csv(ticker_file, index=False)
            print(f"\n{args.ticker}のデータを保存しました: {ticker_file}")
            print(f"取得期間: {df['Date'].min()} ～ {df['Date'].max()}")
            print(f"データ件数: {len(df)}行")
            
            # 特徴量エンジニアリングを実行（要求された場合）
            if args.with_features:
                feature_file = os.path.join(DATA_DIR, f"{args.ticker.replace('.', '_')}_features.csv")
                feature_df = convert_to_feature_format(df, feature_file)
                print(f"\n特徴量データを保存しました: {feature_file}")
                print(f"特徴量データ件数: {len(feature_df)}行")
    
    # 主要銘柄を全てダウンロード
    elif args.all_major:
        combined_df = download_all_tickers(
            MAJOR_TICKERS,
            args.years,
            args.sleep,
            args.retries,
            args.output
        )
        
        if not combined_df.empty:
            print(f"\n結合データの概要:")
            print(f"総銘柄数: {combined_df['Ticker'].nunique()}")
            print(f"総レコード数: {len(combined_df)}行")
            print(f"期間: {combined_df['Date'].min()} ～ {combined_df['Date'].max()}")
            
            # 特徴量エンジニアリングを実行（要求された場合）
            if args.with_features:
                feature_file = args.output.replace('.csv', '_features.csv')
                feature_df = convert_to_feature_format(combined_df, feature_file)
                print(f"\n特徴量データを保存しました: {feature_file}")
                print(f"特徴量データ件数: {len(feature_df)}行")
    
    else:
        # ヘルプの表示
        parser.print_help()
        print("\n使用例:")
        print("  # トヨタ自動車の5年分のデータをダウンロード")
        print("  python yfinance_downloader.py --ticker 7203.T")
        print("\n  # 主要30銘柄の10年分のデータを一括ダウンロード")
        print("  python yfinance_downloader.py --all-major --years 10 --sleep 3")
        print("\n  # データダウンロードと特徴量エンジニアリングを一度に実行")
        print("  python yfinance_downloader.py --all-major --with-features")


if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n処理を中断しました")
    except Exception as e:
        logger.error(f"エラーが発生しました: {str(e)}")
        import traceback
        traceback.print_exc()
