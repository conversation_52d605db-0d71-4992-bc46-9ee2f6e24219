#!/usr/bin/env python3
import os
import pandas as pd
import numpy as np
import time
import logging
import json
import argparse
from datetime import datetime, timedelta
import random
import yfinance as yf

# 自作モジュールをインポート
from create_data_structure import create_directory_structure
from ticker_manager import TickerManager

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f"logs/historical_data_{datetime.now().strftime('%Y%m%d')}.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class HistoricalDataCollector:
    """
    ヒストリカルデータの収集・管理を行うクラス
    APIリクエスト制限を考慮し、段階的にデータを取得する
    """
    def __init__(self, base_dir="nikkei225_historical_data", ticker_manager=None):
        """
        初期化
        
        Parameters:
        -----------
        base_dir : str
            ベースディレクトリ
        ticker_manager : TickerManager or None
            ティッカー管理オブジェクト
        """
        self.base_dir = base_dir
        
        # ディレクトリパス
        self.daily_dir = os.path.join(base_dir, "daily")
        self.hourly_dir = os.path.join(base_dir, "hourly")
        self.metadata_dir = os.path.join(base_dir, "metadata")
        self.consolidated_dir = os.path.join(base_dir, "consolidated")
        
        # ティッカー管理
        if ticker_manager is None:
            self.ticker_manager = TickerManager(metadata_dir=self.metadata_dir)
        else:
            self.ticker_manager = ticker_manager
        
        # APIリクエスト制限管理
        self.request_count = 0
        self.last_request_time = 0
        
        # 最終更新情報
        self.last_update_file = os.path.join(self.metadata_dir, "last_update.json")
        self.last_update_info = self._load_last_update()
    
    def _load_last_update(self):
        """
        最終更新情報の読み込み
        
        Returns:
        --------
        dict
            最終更新情報
        """
        if os.path.exists(self.last_update_file):
            try:
                with open(self.last_update_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception as e:
                logger.error(f"最終更新情報の読み込みエラー: {str(e)}")
        
        # デフォルト値
        return {
            "daily": {
                "last_full_update": None,
                "last_incremental_update": None,
                "tickers_status": {}
            },
            "hourly": {
                "last_full_update": None,
                "last_incremental_update": None,
                "tickers_status": {}
            }
        }
    
    def _save_last_update(self):
        """最終更新情報の保存"""
        try:
            with open(self.last_update_file, 'w', encoding='utf-8') as f:
                json.dump(self.last_update_info, f, indent=2, ensure_ascii=False)
            logger.info("最終更新情報を保存しました")
        except Exception as e:
            logger.error(f"最終更新情報の保存エラー: {str(e)}")
    
    def _respect_rate_limit(self):
        """APIレート制限を尊重"""
        # 1分間に最大300リクエスト
        MAX_REQUESTS_PER_MINUTE = 300
        
        # 前回のリクエストからの経過時間
        elapsed = time.time() - self.last_request_time
        
        # リクエストカウントをリセット（1分経過）
        if elapsed > 60:
            self.request_count = 0
        
        # リクエスト数が制限に近づいたら待機
        if self.request_count > MAX_REQUESTS_PER_MINUTE * 0.8:  # 80%以上で待機
            wait_time = max(60 - elapsed, 0) + 1  # 残り時間 + 1秒
            logger.info(f"レート制限に近づいています。{wait_time:.1f}秒待機...")
            time.sleep(wait_time)
            self.request_count = 0
        
        # リクエストカウント更新
        self.request_count += 1
        self.last_request_time = time.time()
    
    def collect_daily_data(self, tickers, start_date, end_date=None, batch_size=5, sleep_time=2):
        """
        日次データの収集
        
        Parameters:
        -----------
        tickers : list
            ティッカーシンボルのリスト
        start_date : str
            開始日 (YYYY-MM-DD)
        end_date : str or None
            終了日 (YYYY-MM-DD)、Noneの場合は現在日付
        batch_size : int
            バッチサイズ
        sleep_time : int
            バッチ間のスリープ時間（秒）
            
        Returns:
        --------
        dict
            収集結果の概要
        """
        if end_date is None:
            end_date = datetime.now().strftime('%Y-%m-%d')
        
        logger.info(f"日次データの収集を開始します: {len(tickers)}銘柄, {start_date}～{end_date}")
        
        # バッチに分割
        batches = [tickers[i:i+batch_size] for i in range(0, len(tickers), batch_size)]
        total_batches = len(batches)
        
        # 結果の概要
        results = {
            "total_tickers": len(tickers),
            "start_date": start_date,
            "end_date": end_date,
            "success_count": 0,
            "failed_tickers": [],
            "saved_files": []
        }
        
        # 各バッチを処理
        for i, batch in enumerate(batches):
            logger.info(f"バッチ {i+1}/{total_batches} を処理中: {batch}")
            
            try:
                # APIリクエスト制限を考慮
                self._respect_rate_limit()
                
                # yfinanceでデータを取得
                data = yf.download(
                    batch,
                    start=start_date,
                    end=end_date,
                    interval="1d",
                    group_by="ticker",
                    auto_adjust=True,
                    prepost=False,
                    threads=True
                )
                
                # データを処理
                for ticker in batch:
                    try:
                        ticker_data = None
                        
                        # 単一銘柄の場合とマルチ銘柄の場合で処理を分ける
                        if isinstance(data.columns, pd.MultiIndex):
                            if ticker in data.columns.levels[0]:
                                ticker_data = data[ticker].copy()
                        else:
                            # 単一銘柄の場合
                            if len(batch) == 1:
                                ticker_data = data.copy()
                        
                        # データが取得できなかった場合
                        if ticker_data is None or ticker_data.empty:
                            logger.warning(f"{ticker}: データが取得できませんでした")
                            results["failed_tickers"].append(ticker)
                            continue
                        
                        # インデックスをリセットしてDateTime列を追加
                        ticker_data = ticker_data.reset_index()
                        ticker_data.rename(columns={"Date": "Datetime"}, inplace=True)
                        
                        # 銘柄情報を追加
                        ticker_data["Ticker"] = ticker
                        
                        # 整理
                        if "Adj Close" in ticker_data.columns:
                            ticker_data = ticker_data.drop(columns=["Adj Close"])
                        
                        # 年と月でグループ化してファイルに保存
                        for (year, month), group in ticker_data.groupby([
                            ticker_data["Datetime"].dt.year, 
                            ticker_data["Datetime"].dt.month
                        ]):
                            # ディレクトリパス
                            year_month_dir = os.path.join(self.daily_dir, str(year), f"{year}-{month:02d}")
                            os.makedirs(year_month_dir, exist_ok=True)
                            
                            # ファイルパス
                            file_path = os.path.join(year_month_dir, f"{ticker}.csv")
                            
                            # 既存ファイルがあれば読み込んで結合
                            if os.path.exists(file_path):
                                existing_data = pd.read_csv(file_path, parse_dates=["Datetime"])
                                group = pd.concat([existing_data, group]).drop_duplicates(subset=["Datetime"]).sort_values("Datetime")
                            
                            # 保存
                            group.to_csv(file_path, index=False)
                            results["saved_files"].append(file_path)
                        
                        # ステータスを更新
                        self.last_update_info["daily"]["tickers_status"][ticker] = {
                            "last_update": datetime.now().isoformat(),
                            "start_date": start_date,
                            "end_date": end_date,
                            "status": "success"
                        }
                        
                        results["success_count"] += 1
                        
                    except Exception as e:
                        logger.error(f"{ticker}: データ処理エラー: {str(e)}")
                        results["failed_tickers"].append(ticker)
                        
                        # ステータスを更新
                        self.last_update_info["daily"]["tickers_status"][ticker] = {
                            "last_update": datetime.now().isoformat(),
                            "start_date": start_date,
                            "end_date": end_date,
                            "status": "error",
                            "error": str(e)
                        }
                
                # バッチ間のスリープ
                time.sleep(sleep_time)
                
            except Exception as e:
                logger.error(f"バッチ {i+1}/{total_batches} の処理エラー: {str(e)}")
                results["failed_tickers"].extend(batch)
        
        # 最終更新情報を更新
        self.last_update_info["daily"]["last_incremental_update"] = datetime.now().isoformat()
        if results["success_count"] == len(tickers):
            self.last_update_info["daily"]["last_full_update"] = datetime.now().isoformat()
        
        # 保存
        self._save_last_update()
        
        logger.info(f"日次データの収集が完了しました: 成功={results['success_count']}/{len(tickers)}銘柄")
        
        return results
    
    def collect_hourly_data(self, tickers, days_back=30, batch_size=2, sleep_time=5):
        """
        時間足データの収集
        
        Parameters:
        -----------
        tickers : list
            ティッカーシンボルのリスト
        days_back : int
            過去何日分のデータを取得するか
        batch_size : int
            バッチサイズ
        sleep_time : int
            バッチ間のスリープ時間（秒）
            
        Returns:
        --------
        dict
            収集結果の概要
        """
        logger.info(f"時間足データの収集を開始します: {len(tickers)}銘柄, 過去{days_back}日分")
        
        # 期間の設定（yfinanceでは2年以上前の時間足データは取得できないため制限）
        days_back = min(days_back, 730)  # 最大2年
        start_date = (datetime.now() - timedelta(days=days_back)).strftime('%Y-%m-%d')
        end_date = datetime.now().strftime('%Y-%m-%d')
        
        # バッチに分割（時間足データはAPI制限が厳しいため、バッチサイズを小さくする）
        batches = [tickers[i:i+batch_size] for i in range(0, len(tickers), batch_size)]
        total_batches = len(batches)
        
        # 結果の概要
        results = {
            "total_tickers": len(tickers),
            "days_back": days_back,
            "start_date": start_date,
            "end_date": end_date,
            "success_count": 0,
            "failed_tickers": [],
            "saved_files": []
        }
        
        # 各バッチを処理
        for i, batch in enumerate(batches):
            logger.info(f"バッチ {i+1}/{total_batches} を処理中: {batch}")
            
            try:
                # APIリクエスト制限を考慮
                self._respect_rate_limit()
                
                # yfinanceでデータを取得（時間足は一度に多くのデータをリクエストしない）
                for ticker in batch:
                    try:
                        # 個別に取得
                        ticker_data = yf.download(
                            ticker,
                            start=start_date,
                            end=end_date,
                            interval="1h",
                            auto_adjust=True,
                            prepost=False
                        )
                        
                        # データが取得できなかった場合
                        if ticker_data is None or ticker_data.empty:
                            logger.warning(f"{ticker}: 時間足データが取得できませんでした")
                            results["failed_tickers"].append(ticker)
                            continue
                        
                        # インデックスをリセットしてDateTime列を追加
                        ticker_data = ticker_data.reset_index()
                        
                        # 銘柄情報を追加
                        ticker_data["Ticker"] = ticker
                        
                        # 整理
                        if "Adj Close" in ticker_data.columns:
                            ticker_data = ticker_data.drop(columns=["Adj Close"])
                        
                        # 年と月でグループ化してファイルに保存
                        for (year, month), group in ticker_data.groupby([
                            ticker_data["Datetime"].dt.year, 
                            ticker_data["Datetime"].dt.month
                        ]):
                            # ディレクトリパス
                            year_month_dir = os.path.join(self.hourly_dir, str(year), f"{year}-{month:02d}")
                            os.makedirs(year_month_dir, exist_ok=True)
                            
                            # ファイルパス
                            file_path = os.path.join(year_month_dir, f"{ticker}.csv")
                            
                            # 既存ファイルがあれば読み込んで結合
                            if os.path.exists(file_path):
                                existing_data = pd.read_csv(file_path, parse_dates=["Datetime"])
                                group = pd.concat([existing_data, group]).drop_duplicates(subset=["Datetime"]).sort_values("Datetime")
                            
                            # 保存
                            group.to_csv(file_path, index=False)
                            results["saved_files"].append(file_path)
                        
                        # ステータスを更新
                        self.last_update_info["hourly"]["tickers_status"][ticker] = {
                            "last_update": datetime.now().isoformat(),
                            "days_back": days_back,
                            "status": "success"
                        }
                        
                        results["success_count"] += 1
                        
                        # ティッカーごとにも少し待機
                        time.sleep(1)
                        
                    except Exception as e:
                        logger.error(f"{ticker}: 時間足データ処理エラー: {str(e)}")
                        results["failed_tickers"].append(ticker)
                        
                        # ステータスを更新
                        self.last_update_info["hourly"]["tickers_status"][ticker] = {
                            "last_update": datetime.now().isoformat(),
                            "days_back": days_back,
                            "status": "error",
                            "error": str(e)
                        }
                
                # バッチ間のスリープ
                time.sleep(sleep_time)
                
            except Exception as e:
                logger.error(f"バッチ {i+1}/{total_batches} の処理エラー: {str(e)}")
                results["failed_tickers"].extend(batch)
        
        # 最終更新情報を更新
        self.last_update_info["hourly"]["last_incremental_update"] = datetime.now().isoformat()
        if results["success_count"] == len(tickers):
            self.last_update_info["hourly"]["last_full_update"] = datetime.now().isoformat()
        
        # 保存
        self._save_last_update()
        
        logger.info(f"時間足データの収集が完了しました: 成功={results['success_count']}/{len(tickers)}銘柄")
        
        return results
    
    def create_consolidated_files(self, data_type="daily", force_update=False):
        """
        統合データファイルの作成
        
        Parameters:
        -----------
        data_type : str
            データタイプ（'daily'または'hourly'）
        force_update : bool
            強制更新フラグ
            
        Returns:
        --------
        str
            作成されたファイルパス
        """
        logger.info(f"{data_type}データの統合ファイルを作成します")
        
        # 出力ファイル
        output_file = os.path.join(self.consolidated_dir, f"nikkei225_{data_type}_all.csv")
        
        # 前回の更新から変更がなければスキップ
        if not force_update and os.path.exists(output_file):
            last_update = self.last_update_info[data_type]["last_incremental_update"]
            if last_update:
                last_update_time = datetime.fromisoformat(last_update)
                output_file_mtime = datetime.fromtimestamp(os.path.getmtime(output_file))
                
                if output_file_mtime > last_update_time:
                    logger.info(f"統合ファイルは最新です: {output_file}")
                    return output_file
        
        # 読み込むディレクトリ
        base_dir = self.daily_dir if data_type == "daily" else self.hourly_dir
        
        # 全ファイルを検索
        all_files = []
        for root, _, files in os.walk(base_dir):
            for file in files:
                if file.endswith(".csv"):
                    all_files.append(os.path.join(root, file))
        
        if not all_files:
            logger.warning(f"{data_type}データのCSVファイルが見つかりません")
            return None
        
        # 全データを読み込み
        all_data = []
        for file in all_files:
            try:
                df = pd.read_csv(file, parse_dates=["Datetime"])
                all_data.append(df)
            except Exception as e:
                logger.error(f"{file}の読み込みエラー: {str(e)}")
        
        if not all_data:
            logger.warning("有効なデータがありません")
            return None
        
        # 結合
        combined_data = pd.concat(all_data, ignore_index=True)
        
        # 重複を削除
        combined_data = combined_data.drop_duplicates(subset=["Ticker", "Datetime"]).sort_values(["Ticker", "Datetime"])
        
        # 保存
        combined_data.to_csv(output_file, index=False)
        
        logger.info(f"統合ファイルを保存しました: {output_file}, {len(combined_data)}行, {combined_data['Ticker'].nunique()}銘柄")
        
        return output_file
    
    def backfill_daily_data(self, group_size=25, years_back=5, sleep_between_groups=600):
        """
        日次データのバックフィル
        銘柄をグループに分け、長期間にわたって段階的に取得
        
        Parameters:
        -----------
        group_size : int
            グループサイズ
        years_back : int
            過去何年分のデータを取得するか
        sleep_between_groups : int
            グループ間のスリープ時間（秒）
            
        Returns:
        --------
        dict
            バックフィル結果の概要
        """
        logger.info(f"日次データのバックフィルを開始します: 過去{years_back}年分")
        
        # 期間の設定
        end_date = datetime.now().strftime('%Y-%m-%d')
        start_date = (datetime.now() - timedelta(days=365*years_back)).strftime('%Y-%m-%d')
        
        # ティッカーリストを取得
        all_tickers = self.ticker_manager.get_all_tickers()
        
        if not all_tickers:
            logger.error("ティッカーリストが空です")
            return {"status": "error", "reason": "empty_ticker_list"}
        
        # グループに分割（ランダムに並び替え）
        ticker_groups = self.ticker_manager.get_ticker_groups(group_size=group_size)
        
        # 結果の概要
        results = {
            "total_tickers": len(all_tickers),
            "total_groups": len(ticker_groups),
            "start_date": start_date,
            "end_date": end_date,
            "completed_groups": 0,
            "success_count": 0,
            "failed_tickers": []
        }
        
        # 各グループを処理
        for i, group in enumerate(ticker_groups):
            logger.info(f"グループ {i+1}/{len(ticker_groups)} の処理を開始: {group[:5]}... ({len(group)}銘柄)")
            
            try:
                # データを収集
                group_result = self.collect_daily_data(
                    tickers=group,
                    start_date=start_date,
                    end_date=end_date,
                    batch_size=5,
                    sleep_time=3
                )
                
                # 結果を更新
                results["completed_groups"] += 1
                results["success_count"] += group_result["success_count"]
                results["failed_tickers"].extend(group_result["failed_tickers"])
                
                # 次のグループの前に長めに待機（APIクォータを回復させる）
                if i < len(ticker_groups) - 1:
                    logger.info(f"次のグループの前に{sleep_between_groups/60:.1f}分待機します")
                    time.sleep(sleep_between_groups)
                
            except Exception as e:
                logger.error(f"グループ {i+1} の処理エラー: {str(e)}")
                results["failed_tickers"].extend(group)
        
        # 統合ファイルを作成
        self.create_consolidated_files(data_type="daily", force_update=True)
        
        logger.info(f"日次データのバックフィルが完了しました: 成功={results['success_count']}/{len(all_tickers)}銘柄")
        
        return results
    
    def backfill_hourly_data(self, group_size=10, days_back=30, sleep_between_groups=900):
        """
        時間足データのバックフィル
        銘柄をグループに分け、段階的に取得
        
        Parameters:
        -----------
        group_size : int
            グループサイズ
        days_back : int
            過去何日分のデータを取得するか
        sleep_between_groups : int
            グループ間のスリープ時間（秒）
            
        Returns:
        --------
        dict
            バックフィル結果の概要
        """
        logger.info(f"時間足データのバックフィルを開始します: 過去{days_back}日分")
        
        # ティッカーリストを取得
        all_tickers = self.ticker_manager.get_all_tickers()
        
        if not all_tickers:
            logger.error("ティッカーリストが空です")
            return {"status": "error", "reason": "empty_ticker_list"}
        
        # グループに分割（ランダムに並び替え）
        ticker_groups = self.ticker_manager.get_ticker_groups(group_size=group_size)
        
        # 結果の概要
        results = {
            "total_tickers": len(all_tickers),
            "total_groups": len(ticker_groups),
            "days_back": days_back,
            "completed_groups": 0,
            "success_count": 0,
            "failed_tickers": []
        }
        
        # 各グループを処理
        for i, group in enumerate(ticker_groups):
            logger.info(f"グループ {i+1}/{len(ticker_groups)} の処理を開始: {group[:5]}... ({len(group)}銘柄)")
            
            try:
                # データを収集
                group_result = self.collect_hourly_data(
                    tickers=group,
                    days_back=days_back,
                    batch_size=2,
                    sleep_time=10
                )
                
                # 結果を更新
                results["completed_groups"] += 1
                results["success_count"] += group_result["success_count"]
                results["failed_tickers"].extend(group_result["failed_tickers"])
                
                # 次のグループの前に長めに待機（APIクォータを回復させる）
                if i < len(ticker_groups) - 1:
                    logger.info(f"次のグループの前に{sleep_between_groups/60:.1f}分待機します")
                    time.sleep(sleep_between_groups)
                
            except Exception as e:
                logger.error(f"グループ {i+1} の処理エラー: {str(e)}")
                results["failed_tickers"].extend(group)
        
        # 統合ファイルを作成
        self.create_consolidated_files(data_type="hourly", force_update=True)
        
        logger.info(f"時間足データのバックフィルが完了しました: 成功={results['success_count']}/{len(all_tickers)}銘柄")
        
        return results
    
    def update_daily_data(self, days_back=30):
        """
        日次データの更新（最近のデータのみ）
        
        Parameters:
        -----------
        days_back : int
            過去何日分のデータを更新するか
            
        Returns:
        --------
        dict
            更新結果の概要
        """
        logger.info(f"日次データの更新を開始します: 過去{days_back}日分")
        
        # 期間の設定
        end_date = datetime.now().strftime('%Y-%m-%d')
        start_date = (datetime.now() - timedelta(days=days_back)).strftime('%Y-%m-%d')
        
        # ティッカーリストを取得
        all_tickers = self.ticker_manager.get_all_tickers()
        
        if not all_tickers:
            logger.error("ティッカーリストが空です")
            return {"status": "error", "reason": "empty_ticker_list"}
        
        # データを収集
        results = self.collect_daily_data(
            tickers=all_tickers,
            start_date=start_date,
            end_date=end_date,
            batch_size=5,
            sleep_time=2
        )
        
        # 統合ファイルを更新
        self.create_consolidated_files(data_type="daily", force_update=True)
        
        return results
    
    def update_hourly_data(self, days_back=7):
        """
        時間足データの更新（最近のデータのみ）
        
        Parameters:
        -----------
        days_back : int
            過去何日分のデータを更新するか
            
        Returns:
        --------
        dict
            更新結果の概要
        """
        logger.info(f"時間足データの更新を開始します: 過去{days_back}日分")
        
        # ティッカーリストを取得
        all_tickers = self.ticker_manager.get_all_tickers()
        
        if not all_tickers:
            logger.error("ティッカーリストが空です")
            return {"status": "error", "reason": "empty_ticker_list"}
        
        # データを収集
        results = self.collect_hourly_data(
            tickers=all_tickers,
            days_back=days_back,
            batch_size=2,
            sleep_time=5
        )
        
        # 統合ファイルを更新
        self.create_consolidated_files(data_type="hourly", force_update=True)
        
        return results

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='日経225の歴史的データを収集・管理')
    parser.add_argument('--base-dir', type=str, default="nikkei225_historical_data", help='ベースディレクトリ')
    parser.add_argument('--setup', action='store_true', help='ディレクトリ構造をセットアップ')
    parser.add_argument('--update-tickers', action='store_true', help='ティッカー情報を更新')
    parser.add_argument('--update-daily', action='store_true', help='日次データを更新')
    parser.add_argument('--update-hourly', action='store_true', help='時間足データを更新')
    parser.add_argument('--backfill-daily', action='store_true', help='日次データのバックフィル')
    parser.add_argument('--backfill-hourly', action='store_true', help='時間足データのバックフィル')
    parser.add_argument('--days-back', type=int, default=30, help='過去何日分のデータを取得するか')
    parser.add_argument('--years-back', type=int, default=5, help='過去何年分のデータを取得するか')
    parser.add_argument('--group-size', type=int, default=25, help='バックフィル時のグループサイズ')
    parser.add_argument('--sleep-between-groups', type=int, default=600, help='グループ間のスリープ時間（秒）')
    
    args = parser.parse_args()
    
    # logs ディレクトリがなければ作成
    os.makedirs("logs", exist_ok=True)
    
    # ディレクトリ構造をセットアップ
    if args.setup:
        base_dir = create_directory_structure(args.base_dir)
        logger.info(f"ディレクトリ構造をセットアップしました: {base_dir}")
    
    # ティッカー管理
    ticker_manager = TickerManager(metadata_dir=os.path.join(args.base_dir, "metadata"))
    
    # ティッカー情報を更新
    if args.update_tickers:
        result = ticker_manager.update_index_composition(force_update=True)
        logger.info(f"ティッカー情報の更新結果: {result}")
    
    # データコレクター
    collector = HistoricalDataCollector(base_dir=args.base_dir, ticker_manager=ticker_manager)
    
    # 日次データを更新
    if args.update_daily:
        result = collector.update_daily_data(days_back=args.days_back)
        logger.info(f"日次データの更新結果: {result}")
    
    # 時間足データを更新
    if args.update_hourly:
        result = collector.update_hourly_data(days_back=args.days_back)
        logger.info(f"時間足データの更新結果: {result}")
    
    # 日次データのバックフィル
    if args.backfill_daily:
        result = collector.backfill_daily_data(
            group_size=args.group_size,
            years_back=args.years_back,
            sleep_between_groups=args.sleep_between_groups
        )
        logger.info(f"日次データのバックフィル結果: {result}")
    
    # 時間足データのバックフィル
    if args.backfill_hourly:
        result = collector.backfill_hourly_data(
            group_size=args.group_size,
            days_back=args.days_back,
            sleep_between_groups=args.sleep_between_groups
        )
        logger.info(f"時間足データのバックフィル結果: {result}")
