#!/usr/bin/env python3
"""
Yahoo Financeから直接CSVファイルをダウンロードするシンプルなスクリプト
"""

import os
import sys
import time
import random
import logging
import argparse
import requests
import pandas as pd
from datetime import datetime, timedelta
from tqdm import tqdm

# ロギング設定
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f"logs/yahoo_download_{datetime.now().strftime('%Y%m%d')}.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# 保存ディレクトリの設定
DATA_DIR = "nikkei225_data"
CACHE_DIR = os.path.join(DATA_DIR, "cache")

# ティッカーリスト（簡略のため代表的な大型株30銘柄を定義）
MAJOR_TICKERS = [
    "7203.T",  # トヨタ自動車
    "9432.T",  # NTT
    "9984.T",  # ソフトバンク
    "8306.T",  # 三菱UFJ
    "6758.T",  # ソニー
    "6861.T",  # キーエンス
    "9433.T",  # KDDI
    "4063.T",  # 信越化学
    "8316.T",  # 三井住友FG
    "7974.T",  # 任天堂
    "4661.T",  # オリエンタルランド
    "9983.T",  # ファーストリテ
    "6367.T",  # ダイキン工業
    "6501.T",  # 日立製作所
    "6594.T",  # 日本電産
    "6902.T",  # デンソー
    "6981.T",  # 村田製作所
    "4519.T",  # 中外製薬
    "8035.T",  # 東京エレクトロン
    "7267.T",  # ホンダ
    "7741.T",  # HOYA
    "8766.T",  # 東京海上HD
    "8031.T",  # 三井物産
    "2914.T",  # JT
    "5401.T",  # 日本製鉄
    "6954.T",  # ファナック
    "4507.T",  # 塩野義製薬
    "4543.T",  # テルモ
    "4568.T",  # 第一三共
    "3382.T",  # セブン＆アイ
]


def ensure_dirs():
    """必要なディレクトリを作成"""
    os.makedirs(DATA_DIR, exist_ok=True)
    os.makedirs(CACHE_DIR, exist_ok=True)
    os.makedirs("logs", exist_ok=True)


def get_unix_timestamp(dt):
    """日時をUnixタイムスタンプに変換"""
    return int(dt.timestamp())


def download_historical_data(ticker, start_date, end_date, sleep_time=2, retries=3):
    """
    Yahoo FinanceからCSVを直接ダウンロード
    
    Parameters:
    -----------
    ticker : str
        ティッカーシンボル (例: "7203.T")
    start_date : datetime
        開始日
    end_date : datetime
        終了日
    sleep_time : float
        リクエスト間の待機時間
    retries : int
        リトライ回数
    
    Returns:
    --------
    pd.DataFrame or None
        成功した場合はデータフレーム、失敗した場合はNone
    """
    # URLパラメータを構築
    period1 = get_unix_timestamp(start_date)
    period2 = get_unix_timestamp(end_date)
    
    # Yahoo FinanceのCSVダウンロードURL
    url = f"https://query1.finance.yahoo.com/v7/finance/download/{ticker}"
    params = {
        "period1": period1,
        "period2": period2,
        "interval": "1d",
        "events": "history",
        "includeAdjustedClose": "true"
    }
    
    # キャッシュファイル名
    cache_file = os.path.join(CACHE_DIR, f"{ticker}_{start_date.strftime('%Y%m%d')}_{end_date.strftime('%Y%m%d')}.csv")
    
    # キャッシュが存在する場合はそれを返す
    if os.path.exists(cache_file):
        try:
            df = pd.read_csv(cache_file, parse_dates=["Date"])
            logger.info(f"{ticker}のデータをキャッシュから読み込みました（{len(df)}行）")
            return df
        except Exception as e:
            logger.error(f"キャッシュ読み込みエラー: {str(e)}")
    
    # ヘッダー（一般的なブラウザのように振る舞う）
    headers = {
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
        "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8",
        "Accept-Language": "en-US,en;q=0.5",
        "Cache-Control": "no-cache",
    }
    
    logger.info(f"{ticker}の{start_date.strftime('%Y-%m-%d')}～{end_date.strftime('%Y-%m-%d')}のデータをダウンロード開始")
    
    for attempt in range(retries):
        try:
            response = requests.get(url, params=params, headers=headers, timeout=30)
            
            if response.status_code == 200:
                # CSVデータを解析
                try:
                    df = pd.read_csv(pd.io.common.StringIO(response.text), parse_dates=["Date"])
                    
                    # 空のデータフレームをチェック
                    if df.empty:
                        logger.warning(f"{ticker}のデータは空です")
                        return None
                    
                    # ティッカー列を追加
                    df["Ticker"] = ticker
                    
                    # キャッシュに保存
                    df.to_csv(cache_file, index=False)
                    
                    logger.info(f"{ticker}のデータを取得しました（{len(df)}行）")
                    time.sleep(sleep_time + random.uniform(0, 1))
                    return df
                except Exception as e:
                    logger.error(f"CSVパースエラー: {str(e)}")
                    logger.debug(f"レスポンス内容: {response.text[:200]}...")
            else:
                logger.warning(f"HTTP エラー: {response.status_code} - {response.reason}")
                
            # リトライ前の待機
            if attempt < retries - 1:
                wait_time = sleep_time * (2 ** attempt) + random.uniform(0, 2)
                logger.info(f"リトライ {attempt+1}/{retries} - {wait_time:.1f}秒待機...")
                time.sleep(wait_time)
        
        except Exception as e:
            logger.error(f"{ticker}のダウンロード中にエラー: {str(e)}")
            
            if attempt < retries - 1:
                wait_time = sleep_time * (2 ** attempt) + random.uniform(0, 2)
                logger.info(f"リトライ {attempt+1}/{retries} - {wait_time:.1f}秒待機...")
                time.sleep(wait_time)
    
    logger.error(f"{ticker}のダウンロードに{retries}回失敗しました")
    return None


def download_ticker_data(ticker, years=5, sleep_time=2, retries=3):
    """
    指定の銘柄の過去数年のデータをダウンロード
    
    Parameters:
    -----------
    ticker : str
        ティッカーシンボル
    years : int
        取得する年数
    sleep_time : float
        リクエスト間の待機時間
    retries : int
        リトライ回数
    
    Returns:
    --------
    pd.DataFrame or None
        成功した場合はデータフレーム、失敗した場合はNone
    """
    end_date = datetime.now()
    start_date = end_date - timedelta(days=365 * years)
    
    return download_historical_data(ticker, start_date, end_date, sleep_time, retries)


def download_all_tickers(tickers=None, years=5, sleep_time=2, retries=3, output_file=None):
    """
    全銘柄のデータを一括ダウンロード
    
    Parameters:
    -----------
    tickers : list or None
        ティッカーのリスト（Noneの場合はMAJOR_TICKERSを使用）
    years : int
        取得する年数
    sleep_time : float
        リクエスト間の待機時間
    retries : int
        リトライ回数
    output_file : str or None
        出力CSVファイル名（Noneの場合は保存しない）
    
    Returns:
    --------
    pd.DataFrame
        全銘柄の結合データ
    """
    if tickers is None:
        tickers = MAJOR_TICKERS
    
    logger.info(f"{len(tickers)}銘柄の過去{years}年分のデータをダウンロード開始")
    
    all_data = []
    success_count = 0
    failure_count = 0
    
    for ticker in tqdm(tickers, desc="銘柄ダウンロード"):
        df = download_ticker_data(ticker, years, sleep_time, retries)
        
        if df is not None and not df.empty:
            all_data.append(df)
            success_count += 1
        else:
            failure_count += 1
    
    if not all_data:
        logger.error("有効なデータがありません")
        return pd.DataFrame()
    
    # 全データを結合
    combined_df = pd.concat(all_data, ignore_index=True)
    
    logger.info(f"ダウンロード完了: 成功={success_count}, 失敗={failure_count}, 総レコード数={len(combined_df)}")
    
    # 指定があれば結合データを保存
    if output_file:
        os.makedirs(os.path.dirname(os.path.abspath(output_file)), exist_ok=True)
        combined_df.to_csv(output_file, index=False)
        logger.info(f"結合データを保存しました: {output_file}")
    
    return combined_df


def main():
    """メイン関数"""
    # コマンドライン引数の解析
    parser = argparse.ArgumentParser(description='Yahoo FinanceからCSVを直接ダウンロード')
    parser.add_argument('--ticker', type=str, help='個別銘柄をダウンロード')
    parser.add_argument('--years', type=int, default=5, help='取得する年数（デフォルト: 5）')
    parser.add_argument('--sleep', type=float, default=2.0, help='リクエスト間の待機時間（デフォルト: 2秒）')
    parser.add_argument('--retries', type=int, default=3, help='リトライ回数（デフォルト: 3）')
    parser.add_argument('--output', type=str, default=os.path.join(DATA_DIR, "nikkei225_major_data.csv"),
                        help='結合データの出力ファイル（デフォルト: nikkei225_data/nikkei225_major_data.csv）')
    parser.add_argument('--all-major', action='store_true', help='主要銘柄を全てダウンロード')
    
    args = parser.parse_args()
    
    # 必要なディレクトリを作成
    ensure_dirs()
    
    # 個別銘柄のダウンロード
    if args.ticker:
        df = download_ticker_data(args.ticker, args.years, args.sleep, args.retries)
        if df is not None and not df.empty:
            ticker_file = os.path.join(DATA_DIR, f"{args.ticker.replace('.', '_')}_data.csv")
            df.to_csv(ticker_file, index=False)
            print(f"\n{args.ticker}のデータを保存しました: {ticker_file}")
            print(f"取得期間: {df['Date'].min()} ～ {df['Date'].max()}")
            print(f"データ件数: {len(df)}行")
    
    # 主要銘柄を全てダウンロード
    elif args.all_major:
        combined_df = download_all_tickers(
            MAJOR_TICKERS,
            args.years,
            args.sleep,
            args.retries,
            args.output
        )
        
        if not combined_df.empty:
            print(f"\n結合データの概要:")
            print(f"総銘柄数: {combined_df['Ticker'].nunique()}")
            print(f"総レコード数: {len(combined_df)}行")
            print(f"期間: {combined_df['Date'].min()} ～ {combined_df['Date'].max()}")
    
    else:
        # ヘルプの表示
        parser.print_help()
        print("\n使用例:")
        print("  # トヨタ自動車の5年分のデータをダウンロード")
        print("  python yahoo_direct_download.py --ticker 7203.T")
        print("\n  # 主要30銘柄の10年分のデータを一括ダウンロード")
        print("  python yahoo_direct_download.py --all-major --years 10 --sleep 3")


if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n処理を中断しました")
    except Exception as e:
        logger.error(f"エラーが発生しました: {str(e)}")
        import traceback
        traceback.print_exc()
