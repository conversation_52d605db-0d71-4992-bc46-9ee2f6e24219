#!/usr/bin/env python3
import os
import time
import logging
import pandas as pd
import yfinance as yf
from datetime import datetime, timedelta
from src.data_collection import DataCollector

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def create_directories():
    """Create necessary directories"""
    directories = ["data", "logs", "models", "results"]
    for directory in directories:
        os.makedirs(directory, exist_ok=True)
        logger.info(f"Created directory: {directory}")

def download_with_retry(tickers, period, interval, batch_size=1, max_retries=5, delay_between_batches=5):
    """
    Download stock data with retry logic and increased delays to handle rate limiting
    
    Parameters:
    -----------
    tickers : list
        List of ticker symbols
    period : str
        Period to download (e.g., "1mo", "3mo", "1y")
    interval : str
        Data interval (e.g., "1h", "1d")
    batch_size : int
        Number of tickers per batch
    max_retries : int
        Maximum number of retry attempts per batch
    delay_between_batches : int
        Delay in seconds between batch downloads
        
    Returns:
    --------
    pd.DataFrame
        Combined dataframe with all downloaded data
    """
    all_data = []
    
    for i in range(0, len(tickers), batch_size):
        batch = tickers[i:i+batch_size]
        logger.info(f"Downloading batch {i//batch_size + 1}/{len(tickers)//batch_size + 1}: {batch}")
        
        retry_count = 0
        success = False
        
        while retry_count < max_retries and not success:
            try:
                # Calculate exponential backoff delay
                backoff_delay = delay_between_batches * (2 ** retry_count)
                if retry_count > 0:
                    logger.info(f"Retry {retry_count}/{max_retries} for batch {batch}, waiting {backoff_delay} seconds...")
                    time.sleep(backoff_delay)
                
                # Download data
                batch_data = yf.download(
                    batch,
                    period=period,
                    interval=interval,
                    group_by='ticker',
                    progress=False,
                    timeout=30
                )
                
                # Process the data
                if len(batch) == 1:
                    # Handle single ticker case
                    ticker = batch[0]
                    if not batch_data.empty:
                        df = batch_data.reset_index()
                        df['Ticker'] = ticker
                        all_data.append(df)
                else:
                    # Multiple tickers
                    for ticker in batch:
                        if ticker in batch_data.columns.levels[0]:
                            ticker_data = batch_data[ticker].copy()
                            if not ticker_data.empty:
                                df = ticker_data.reset_index()
                                df['Ticker'] = ticker
                                all_data.append(df)
                
                success = True
                logger.info(f"Successfully downloaded batch {batch}")
                
                # Always wait between batches to avoid rate limiting
                time.sleep(delay_between_batches)
                
            except Exception as e:
                retry_count += 1
                logger.error(f"Error downloading batch {batch}: {str(e)}")
                
                if retry_count >= max_retries:
                    logger.error(f"Failed to download batch {batch} after {max_retries} retries")
        
    if not all_data:
        logger.error("No data was collected")
        return pd.DataFrame()
    
    # Combine all data
    try:
        combined_data = pd.concat(all_data, ignore_index=True)
        return combined_data
    except Exception as e:
        logger.error(f"Error combining data: {str(e)}")
        return pd.DataFrame()

def prepare_historical_data(test_mode=False):
    """
    Prepare historical data for Nikkei 225 stocks
    
    Parameters:
    -----------
    test_mode : bool
        If True, only download data for 5 tickers as a test
    """
    logger.info("Starting historical data preparation")
    
    # Create directories
    create_directories()
    
    # Get list of Nikkei 225 tickers
    collector = DataCollector(output_dir="data", interval="1h", period="3mo")
    tickers = collector.tickers
    
    # If in test mode, use only a few tickers
    if test_mode:
        tickers = tickers[:3]  # Use only first 3 tickers for testing
        logger.info(f"TEST MODE: Using only {len(tickers)} tickers")
    
    logger.info(f"Preparing to download data for {len(tickers)} stocks")
    
    # Download with conservative settings to avoid rate limiting
    # Use a small batch size, long delays, and multiple retries
    data = download_with_retry(
        tickers=tickers,
        period="3mo",  # Get 3 months of data
        interval="1h", # Hourly data
        batch_size=1,  # One ticker at a time
        max_retries=10,
        delay_between_batches=10  # Wait 10 seconds between batches
    )
    
    if data.empty:
        logger.error("Failed to collect any data")
        return False
    
    # Save raw data
    raw_data_file = os.path.join("data", "nikkei225_data.csv")
    data.to_csv(raw_data_file, index=False)
    logger.info(f"Raw data saved to {raw_data_file}")
    
    # Clean and process the data
    cleaned_data = collector.clean_data_from_df(data)
    
    # Save cleaned data
    cleaned_file = os.path.join("data", "nikkei225_cleaned.csv")
    cleaned_data.to_csv(cleaned_file, index=False)
    logger.info(f"Cleaned data saved to {cleaned_file}")
    
    logger.info(f"Collected data for {cleaned_data['Ticker'].nunique()} tickers with {len(cleaned_data)} rows")
    return True

if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description='Prepare historical stock data')
    parser.add_argument('--test', action='store_true', help='Run in test mode with only a few tickers')
    args = parser.parse_args()
    
    try:
        success = prepare_historical_data(test_mode=args.test)
        if success:
            logger.info("Historical data preparation completed successfully")
        else:
            logger.error("Failed to prepare historical data")
    except Exception as e:
        logger.error(f"Error preparing historical data: {str(e)}")
