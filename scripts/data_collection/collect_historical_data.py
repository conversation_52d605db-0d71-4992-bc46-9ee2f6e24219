#!/usr/bin/env python3
"""
日経225ヒストリカルデータ収集スクリプト

複数のデータソース（Stooq、investpy、JPX、Yahoo Finance）から
日経225の株価データを収集し、整理されたディレクトリ構造に保存します。
"""

import argparse
import logging
import os
from datetime import datetime, timedelta
import time
import sys

# 自作モジュールをインポート
from alternative_data_collector import AlternativeDataCollector

# ロギング設定
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f"logs/collect_historical_{datetime.now().strftime('%Y%m%d')}.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def setup_directory():
    """必要なディレクトリの作成"""
    dirs = ["logs", "data/cache", "nikkei225_historical_data"]
    for d in dirs:
        os.makedirs(d, exist_ok=True)
    logger.info("ディレクトリ構造を確認しました")

def collect_recent_data(base_dir, days=30, batch_size=1, sleep_time=5):
    """最近のデータを収集"""
    collector = AlternativeDataCollector(base_dir=base_dir)
    
    # ティッカー情報を更新
    logger.info("ティッカー情報を更新しています...")
    tickers = collector.update_ticker_list(force_update=True)
    logger.info(f"ティッカー情報を更新しました: {len(tickers)}銘柄")
    
    if not tickers:
        logger.error("ティッカーリストが空です。終了します。")
        return False
    
    # 期間の設定
    end_date = datetime.now()
    start_date = end_date - timedelta(days=days)
    
    logger.info(f"データ収集を開始します: 期間={start_date.strftime('%Y-%m-%d')}～{end_date.strftime('%Y-%m-%d')}")
    
    # データ収集
    result = collector.collect_daily_data(
        tickers=tickers,
        start_date=start_date,
        end_date=end_date,
        batch_size=batch_size,
        sleep_time=sleep_time
    )
    
    # 結果の表示
    success_rate = result["success_count"] / result["total_tickers"] * 100
    logger.info(f"データ収集が完了しました: 成功={result['success_count']}/{result['total_tickers']}銘柄 ({success_rate:.1f}%)")
    
    if result["failed_tickers"]:
        logger.warning(f"失敗した銘柄: {result['failed_tickers'][:10]}" + ("..." if len(result["failed_tickers"]) > 10 else ""))
    
    # 統合ファイルの作成
    consolidated_file = collector.create_consolidated_files(data_type="daily", force_update=True)
    logger.info(f"統合ファイルを作成しました: {consolidated_file}")
    
    return True

def backfill_historical_data(base_dir, start_year, end_year=None, batch_size=1):
    """過去データのバックフィル"""
    if end_year is None:
        end_year = datetime.now().year
    
    collector = AlternativeDataCollector(base_dir=base_dir)
    
    # ティッカー情報を確認
    tickers = collector.get_all_tickers()
    if not tickers:
        logger.info("ティッカー情報を更新しています...")
        tickers = collector.update_ticker_list(force_update=True)
        logger.info(f"ティッカー情報を更新しました: {len(tickers)}銘柄")
    
    if not tickers:
        logger.error("ティッカーリストが空です。終了します。")
        return False
    
    logger.info(f"バックフィルを開始します: 期間={start_year}年～{end_year}年, {len(tickers)}銘柄")
    
    # 各年を処理
    for year in range(start_year, end_year + 1):
        logger.info(f"{year}年のデータ収集を開始します...")
        
        # 期間の設定
        start_date = datetime(year, 1, 1)
        end_date = datetime(year, 12, 31)
        if year == datetime.now().year:
            end_date = datetime.now()
        
        # データ収集
        result = collector.collect_daily_data(
            tickers=tickers,
            start_date=start_date,
            end_date=end_date,
            batch_size=batch_size,
            sleep_time=10
        )
        
        # 結果の表示
        success_rate = result["success_count"] / result["total_tickers"] * 100
        logger.info(f"{year}年のデータ収集が完了しました: 成功={result['success_count']}/{result['total_tickers']}銘柄 ({success_rate:.1f}%)")
        
        # 年間の処理の間は長めに待機
        if year < end_year:
            wait_time = 30 * 60  # 30分
            logger.info(f"次の年の処理の前に{wait_time/60:.1f}分待機します...")
            time.sleep(wait_time)
    
    # 統合ファイルの作成
    consolidated_file = collector.create_consolidated_files(data_type="daily", force_update=True)
    logger.info(f"統合ファイルを作成しました: {consolidated_file}")
    
    return True

def import_jpx_data(base_dir, source_dir):
    """JPXのCSVデータをインポート"""
    collector = AlternativeDataCollector(base_dir=base_dir)
    
    logger.info(f"JPXデータのインポートを開始します: {source_dir}")
    
    result = collector.import_jpx_bulk_data(source_dir=source_dir)
    
    if result.get("status") == "error":
        logger.error(f"JPXデータのインポートに失敗しました: {result.get('reason')}")
        return False
    
    logger.info(f"JPXデータのインポート結果: {result['imported_files']}/{result['total_files']}ファイル, {result['processed_records']}レコード")
    
    # 統合ファイルの作成
    if result["imported_files"] > 0:
        consolidated_file = collector.create_consolidated_files(data_type="daily", force_update=True)
        logger.info(f"統合ファイルを作成しました: {consolidated_file}")
    
    return True

def test_data_access(base_dir):
    """データアクセスのテスト"""
    import pandas as pd
    
    collector = AlternativeDataCollector(base_dir=base_dir)
    
    # 統合ファイルのパス
    consolidated_file = os.path.join(base_dir, "consolidated", "nikkei225_daily_all.csv")
    
    if not os.path.exists(consolidated_file):
        logger.error(f"統合ファイルが見つかりません: {consolidated_file}")
        return False
    
    # データを読み込み
    df = pd.read_csv(consolidated_file, parse_dates=["Date"])
    
    # 基本情報を表示
    logger.info(f"統合データの期間: {df['Date'].min()} ～ {df['Date'].max()}")
    logger.info(f"収録銘柄数: {df['Ticker'].nunique()}")
    logger.info(f"総レコード数: {len(df)}")
    
    # 銘柄ごとのレコード数トップ10
    ticker_counts = df.groupby("Ticker").size().sort_values(ascending=False)
    logger.info("銘柄ごとのレコード数トップ10:")
    for ticker, count in ticker_counts.head(10).items():
        logger.info(f"  {ticker}: {count}レコード")
    
    return True

def print_help_and_exit():
    """ヘルプメッセージを表示して終了"""
    print("""
日経225ヒストリカルデータ収集スクリプト

使用例:
  # 最近30日間のデータを収集
  python collect_historical_data.py --recent 30
  
  # 過去5年分のデータをバックフィル
  python collect_historical_data.py --backfill --start-year 2020
  
  # JPXからダウンロードしたCSVデータをインポート
  python collect_historical_data.py --import-jpx data/jpx_downloads
  
  # 収集したデータの基本情報を表示
  python collect_historical_data.py --test-access
    """)
    sys.exit(0)

if __name__ == "__main__":
    # コマンドライン引数のパース
    parser = argparse.ArgumentParser(description='日経225ヒストリカルデータ収集')
    parser.add_argument('--base-dir', type=str, default="nikkei225_historical_data", help='ベースディレクトリ')
    parser.add_argument('--recent', type=int, help='最近のデータを収集（過去N日分）')
    parser.add_argument('--backfill', action='store_true', help='過去データをバックフィル')
    parser.add_argument('--start-year', type=int, help='バックフィル開始年')
    parser.add_argument('--end-year', type=int, help='バックフィル終了年（デフォルト: 現在の年）')
    parser.add_argument('--batch-size', type=int, default=1, help='バッチサイズ（デフォルト: 1）')
    parser.add_argument('--import-jpx', type=str, help='JPXデータをインポートするディレクトリ')
    parser.add_argument('--test-access', action='store_true', help='データアクセスのテスト')
    parser.add_argument('--help-examples', action='store_true', help='使用例を表示')
    
    args = parser.parse_args()
    
    # ヘルプを表示
    if args.help_examples:
        print_help_and_exit()
    
    # ディレクトリのセットアップ
    setup_directory()
    
    # 処理の開始
    if args.recent:
        collect_recent_data(args.base_dir, days=args.recent, batch_size=args.batch_size)
    elif args.backfill:
        if args.start_year is None:
            print("エラー: バックフィルには --start-year パラメータが必要です")
            sys.exit(1)
        backfill_historical_data(args.base_dir, args.start_year, args.end_year, batch_size=args.batch_size)
    elif args.import_jpx:
        import_jpx_data(args.base_dir, args.import_jpx)
    elif args.test_access:
        test_data_access(args.base_dir)
    else:
        print("エラー: 処理タイプを指定してください（--recent、--backfill、--import-jpx、または --test-access）")
        print_help_and_exit()
