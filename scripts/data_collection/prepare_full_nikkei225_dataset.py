#!/usr/bin/env python3
"""
日経225 - 5年分のデータセット準備スクリプト

2020年から2025年までの5年分の日経225全銘柄のデータを収集し、
継続学習用のデータセットを準備します。
"""

import os
import sys
import argparse
import logging
from datetime import datetime, timedelta
import pandas as pd
from tqdm import tqdm
import multiprocessing
import time

# 自作モジュールをインポート
from src.data_collector.collector import StockDataCollector
from src.data_collector.utils import NikkeiUtils, FileUtils

# ロギング設定
log_dir = 'logs'
os.makedirs(log_dir, exist_ok=True)

log_file = os.path.join(log_dir, f"prepare_dataset_{datetime.now().strftime('%Y%m%d')}.log")
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_file),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("データセット準備")

def collect_yearly_data(year, output_dir, parallel=True, workers=4):
    """指定された年の全銘柄データを収集"""
    logger.info(f"{year}年のデータ収集を開始します")
    
    # 日付範囲の設定
    start_date = datetime(year, 1, 1)
    
    # 現在の年の場合は今日まで、それ以外は年末まで
    current_year = datetime.now().year
    if year == current_year:
        end_date = datetime.now()
    else:
        end_date = datetime(year, 12, 31)
    
    # データコレクターの初期化
    collector = StockDataCollector(
        base_dir=output_dir,
        cache_dir=os.path.join(output_dir, "cache"),
        debug=True
    )
    
    # 日経225の全銘柄を取得
    all_tickers = NikkeiUtils.get_all_nikkei225_tickers()
    logger.info(f"収集対象銘柄数: {len(all_tickers)}銘柄")
    
    # 並列処理用の関数
    def process_ticker(ticker):
        try:
            df = collector.get_stock_data(ticker, start_date, end_date, use_cache=True)
            if df is not None and not df.empty:
                return ticker, len(df), True, None
            else:
                return ticker, 0, False, "データなし"
        except Exception as e:
            return ticker, 0, False, str(e)
    
    # 並列処理で銘柄データを収集
    results = []
    
    if parallel and workers > 1:
        # 並列処理
        logger.info(f"並列処理モード: {workers}ワーカー")
        with multiprocessing.Pool(workers) as pool:
            results = list(tqdm(
                pool.imap(process_ticker, all_tickers),
                total=len(all_tickers),
                desc=f"{year}年データ収集"
            ))
    else:
        # 直列処理
        logger.info("直列処理モード")
        for ticker in tqdm(all_tickers, desc=f"{year}年データ収集"):
            results.append(process_ticker(ticker))
    
    # 結果の集計
    success_count = sum(1 for _, _, success, _ in results if success)
    failure_count = len(results) - success_count
    
    logger.info(f"{year}年のデータ収集完了: 成功={success_count}銘柄, 失敗={failure_count}銘柄")
    
    # データの統合
    yearly_data = collector.consolidate_data(year=year)
    logger.info(f"{year}年の統合ファイル: {yearly_data}")
    
    return yearly_data, success_count, failure_count

def prepare_full_dataset(start_year=2020, end_year=2025, output_dir="nikkei225_full_data", parallel=True, workers=4):
    """複数年のデータセットを準備"""
    start_time = time.time()
    logger.info(f"{start_year}年から{end_year}年までのデータセット準備を開始します")
    
    # 出力ディレクトリの作成
    full_output_dir = os.path.join(output_dir)
    yearly_dir = os.path.join(full_output_dir, "yearly")
    consolidated_dir = os.path.join(full_output_dir, "consolidated")
    metadata_dir = os.path.join(full_output_dir, "metadata")
    
    for dir_path in [full_output_dir, yearly_dir, consolidated_dir, metadata_dir]:
        os.makedirs(dir_path, exist_ok=True)
    
    # 各年のデータを収集
    yearly_results = []
    yearly_files = []
    
    for year in range(start_year, end_year + 1):
        year_output_dir = os.path.join(yearly_dir, str(year))
        os.makedirs(year_output_dir, exist_ok=True)
        
        yearly_file, success_count, failure_count = collect_yearly_data(
            year, 
            year_output_dir, 
            parallel=parallel, 
            workers=workers
        )
        
        if yearly_file:
            yearly_files.append(yearly_file)
            yearly_results.append({
                "year": year,
                "success_count": success_count,
                "failure_count": failure_count,
                "file": yearly_file
            })
    
    # 全年のデータを統合
    logger.info("全年のデータを統合します")
    
    # 統合ファイルパス
    full_dataset_file = os.path.join(consolidated_dir, "nikkei225_full_dataset.csv")
    
    # 各年のデータを読み込んで結合
    all_data = []
    for file in yearly_files:
        if os.path.exists(file):
            try:
                df = pd.read_csv(file)
                all_data.append(df)
                logger.info(f"ファイル読み込み: {file}, {len(df)}行")
            except Exception as e:
                logger.error(f"ファイル読み込みエラー: {file}, {str(e)}")
    
    if all_data:
        # データを結合
        full_df = pd.concat(all_data, ignore_index=True)
        
        # 重複の除去
        full_df = full_df.drop_duplicates(subset=['Ticker', 'Date'], keep='last')
        
        # 日付でソート
        if 'Date' in full_df.columns:
            full_df['Date'] = pd.to_datetime(full_df['Date'])
            full_df = full_df.sort_values(by=['Ticker', 'Date'])
        
        # 保存
        full_df.to_csv(full_dataset_file, index=False)
        logger.info(f"全データセット保存完了: {full_dataset_file}, {len(full_df)}行")
        
        # 統計情報
        ticker_count = full_df['Ticker'].nunique()
        start_date = full_df['Date'].min()
        end_date = full_df['Date'].max()
        logger.info(f"データセット統計: {ticker_count}銘柄, 期間:{start_date} - {end_date}")
        
        # メタデータの保存
        metadata = {
            "creation_time": datetime.now().isoformat(),
            "start_year": start_year,
            "end_year": end_year,
            "ticker_count": ticker_count,
            "total_rows": len(full_df),
            "start_date": start_date.strftime('%Y-%m-%d'),
            "end_date": end_date.strftime('%Y-%m-%d'),
            "yearly_results": yearly_results,
            "processing_time_seconds": time.time() - start_time
        }
        
        metadata_file = os.path.join(metadata_dir, "dataset_metadata.json")
        FileUtils.save_json_file(metadata_file, metadata, logger)
        
        return full_dataset_file, metadata
    else:
        logger.error("統合するデータがありません")
        return None, None

def main():
    """メイン関数"""
    parser = argparse.ArgumentParser(description='日経225の5年分データセット準備')
    
    parser.add_argument('--start-year', type=int, default=2020, help='開始年 (デフォルト: 2020)')
    parser.add_argument('--end-year', type=int, default=2025, help='終了年 (デフォルト: 2025)')
    parser.add_argument('--output-dir', type=str, default='nikkei225_full_data', help='出力ディレクトリ')
    parser.add_argument('--sequential', action='store_true', help='並列処理を無効化')
    parser.add_argument('--workers', type=int, default=4, help='並列ワーカー数 (デフォルト: 4)')
    
    args = parser.parse_args()
    
    # 現在の年が終了年より大きい場合は調整
    current_year = datetime.now().year
    if args.end_year > current_year:
        logger.warning(f"終了年が現在の年より大きいため、{current_year}に調整します")
        args.end_year = current_year
    
    # データセット準備実行
    result_file, metadata = prepare_full_dataset(
        start_year=args.start_year,
        end_year=args.end_year,
        output_dir=args.output_dir,
        parallel=not args.sequential,
        workers=args.workers
    )
    
    if result_file:
        print(f"\n--- データセット準備完了 ---")
        print(f"統合ファイル: {result_file}")
        print(f"収集期間: {args.start_year}年 - {args.end_year}年")
        print(f"銘柄数: {metadata['ticker_count']}")
        print(f"データ行数: {metadata['total_rows']}")
        print(f"処理時間: {metadata['processing_time_seconds']:.1f}秒")
        return 0
    else:
        print("\n--- データセット準備に失敗しました ---")
        return 1

if __name__ == "__main__":
    sys.exit(main())
