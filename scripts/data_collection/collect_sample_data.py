#!/usr/bin/env python3
"""
日経225主要銘柄サンプルデータ収集スクリプト

複数のデータソースから日経225の主要銘柄データを収集するデモスクリプト
"""

import os
import pandas as pd
import logging
from datetime import datetime, timedelta
import time
import argparse
import sys

# ロギング設定
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f"logs/collect_sample_{datetime.now().strftime('%Y%m%d')}.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# サンプル主要銘柄リスト
MAJOR_TICKERS = [
    "7203.T",   # トヨタ自動車
    "9984.T",   # ソフトバンクグループ
    "6758.T",   # ソニーグループ
    "6861.T",   # キーエンス
    "9433.T",   # KDDI
    "8306.T",   # 三菱UFJフィナンシャル・グループ
    "6367.T",   # ダイキン工業
    "6501.T",   # 日立製作所
    "9983.T",   # ファーストリテイリング
    "4063.T"    # 信越化学工業
]

def setup_directory():
    """必要なディレクトリの作成"""
    dirs = ["logs", "data/cache", "nikkei225_sample_data", "nikkei225_sample_data/daily"]
    for d in dirs:
        os.makedirs(d, exist_ok=True)
    logger.info("ディレクトリ構造を確認しました")

def collect_data_with_pandas_datareader(ticker, start_date, end_date, data_dir):
    """pandas-datareaderでデータを収集"""
    try:
        import pandas_datareader as pdr
        from pandas_datareader import data as pdr_data
        
        logger.info(f"{ticker}のデータをStooqから取得しています...")
        
        # Stooqからデータを取得
        stooq_ticker = ticker.replace(".T", ".JP")
        df = pdr_data.get_data_stooq(stooq_ticker, start=start_date, end=end_date)
        
        if df.empty:
            logger.warning(f"Stooqから{ticker}のデータを取得できませんでした。Yahoo Financeを試します...")
            # Yahoo Financeをバックアップとして使用
            df = pdr_data.get_data_yahoo(ticker, start=start_date, end=end_date)
        
        if df.empty:
            logger.error(f"{ticker}のデータを取得できませんでした")
            return None
        
        # インデックスをリセット
        df = df.reset_index()
        
        # ティッカー情報を追加
        df["Ticker"] = ticker
        
        # 年月ディレクトリの作成
        for (year, month), group in df.groupby([df['Date'].dt.year, df['Date'].dt.month]):
            dir_path = os.path.join(data_dir, "daily", str(year), f"{year}-{month:02d}")
            os.makedirs(dir_path, exist_ok=True)
            
            # 保存
            file_path = os.path.join(dir_path, f"{ticker}.csv")
            group.to_csv(file_path, index=False)
            logger.info(f"{ticker}の{year}年{month}月データを保存しました: {len(group)}行")
        
        return df
    
    except Exception as e:
        logger.error(f"{ticker}のデータ取得エラー: {str(e)}")
        return None

def collect_sample_data(days_back=30, sleep_time=5):
    """サンプル銘柄のデータを収集"""
    logger.info(f"サンプル銘柄のデータ収集を開始します: {len(MAJOR_TICKERS)}銘柄, 過去{days_back}日")
    
    # 期間設定
    end_date = datetime.now()
    start_date = end_date - timedelta(days=days_back)
    
    data_dir = "nikkei225_sample_data"
    os.makedirs(os.path.join(data_dir, "consolidated"), exist_ok=True)
    
    # 結果
    results = {
        "success": [],
        "failed": []
    }
    
    # 各銘柄のデータを取得
    all_data = []
    for ticker in MAJOR_TICKERS:
        try:
            # データ取得
            df = collect_data_with_pandas_datareader(ticker, start_date, end_date, data_dir)
            
            if df is not None and not df.empty:
                results["success"].append(ticker)
                all_data.append(df)
            else:
                results["failed"].append(ticker)
            
            # リクエストの間隔を調整
            time.sleep(sleep_time)
            
        except Exception as e:
            logger.error(f"{ticker}の処理中にエラーが発生しました: {str(e)}")
            results["failed"].append(ticker)
    
    # 結果サマリー
    logger.info(f"データ収集が完了しました: 成功={len(results['success'])}/{len(MAJOR_TICKERS)}銘柄")
    
    # 統合ファイルの作成
    if all_data:
        consolidated_df = pd.concat(all_data, ignore_index=True)
        consolidated_file = os.path.join(data_dir, "consolidated", "nikkei225_major_tickers.csv")
        consolidated_df.to_csv(consolidated_file, index=False)
        logger.info(f"統合ファイルを作成しました: {consolidated_file}, {len(consolidated_df)}行")
        
        return consolidated_file
    else:
        logger.warning("統合ファイルを作成できませんでした: データがありません")
        return None

def display_data_info(file_path):
    """収集したデータの基本情報を表示"""
    if not os.path.exists(file_path):
        logger.error(f"ファイルが見つかりません: {file_path}")
        return
    
    try:
        # データを読み込み
        df = pd.read_csv(file_path, parse_dates=["Date"])
        
        # 基本情報を表示
        print("\n===== 収集したデータの概要 =====")
        print(f"データ期間: {df['Date'].min()} ～ {df['Date'].max()}")
        print(f"収集銘柄数: {df['Ticker'].nunique()}")
        print(f"総レコード数: {len(df)}")
        
        # 銘柄ごとのレコード数
        ticker_counts = df.groupby("Ticker").size()
        print("\n銘柄ごとのレコード数:")
        for ticker, count in ticker_counts.items():
            print(f"  {ticker}: {count}レコード")
        
        # サンプルデータ
        print("\n最新のデータサンプル:")
        latest_data = df.sort_values("Date", ascending=False).head(5)
        print(latest_data[["Date", "Ticker", "Open", "High", "Low", "Close", "Volume"]].to_string(index=False))
        
    except Exception as e:
        logger.error(f"データ表示エラー: {str(e)}")

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='日経225主要銘柄サンプルデータ収集')
    parser.add_argument('--days', type=int, default=30, help='過去何日分のデータを取得するか（デフォルト: 30）')
    parser.add_argument('--sleep', type=int, default=5, help='リクエスト間の待機時間（秒）（デフォルト: 5）')
    
    args = parser.parse_args()
    
    try:
        # ディレクトリ設定
        setup_directory()
        
        # サンプルデータの収集
        consolidated_file = collect_sample_data(days_back=args.days, sleep_time=args.sleep)
        
        if consolidated_file:
            # データ情報の表示
            display_data_info(consolidated_file)
        
    except Exception as e:
        logger.error(f"実行エラー: {str(e)}")
        sys.exit(1)
