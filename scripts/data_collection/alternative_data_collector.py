#!/usr/bin/env python3
import os
import pandas as pd
import numpy as np
import logging
import json
import time
import random
import argparse
from datetime import datetime, timedelta
import requests
from bs4 import BeautifulSoup
import pandas_datareader as pdr
from pathlib import Path
import traceback

# 必要に応じてinvestpyをインポート
try:
    import investpy
    INVESTPY_AVAILABLE = True
except ImportError:
    INVESTPY_AVAILABLE = False
    print("investpyが利用できません。このソースからのデータ取得はスキップされます。")

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f"logs/alternative_data_{datetime.now().strftime('%Y%m%d')}.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class AlternativeDataCollector:
    """
    複数のデータソースを使用した代替データ収集クラス
    """
    def __init__(self, base_dir="nikkei225_historical_data", cache_dir="data/cache"):
        """
        初期化
        
        Parameters:
        -----------
        base_dir : str
            ベースディレクトリ
        cache_dir : str
            キャッシュディレクトリ
        """
        self.base_dir = base_dir
        self.cache_dir = cache_dir
        
        # ディレクトリパス
        self.daily_dir = os.path.join(base_dir, "daily")
        self.hourly_dir = os.path.join(base_dir, "hourly")
        self.metadata_dir = os.path.join(base_dir, "metadata")
        self.consolidated_dir = os.path.join(base_dir, "consolidated")
        
        # キャッシュディレクトリがなければ作成
        os.makedirs(self.cache_dir, exist_ok=True)
        os.makedirs(self.daily_dir, exist_ok=True)
        os.makedirs(self.hourly_dir, exist_ok=True)
        os.makedirs(self.metadata_dir, exist_ok=True)
        os.makedirs(self.consolidated_dir, exist_ok=True)
        
        # 最終更新情報
        self.last_update_file = os.path.join(self.metadata_dir, "last_update.json")
        self.last_update_info = self._load_last_update()
        
        # ティッカーリスト
        self.tickers_file = os.path.join(self.metadata_dir, "tickers_info.json")
        self.tickers_info = self._load_tickers()
        
    def _load_last_update(self):
        """
        最終更新情報の読み込み
        
        Returns:
        --------
        dict
            最終更新情報
        """
        if os.path.exists(self.last_update_file):
            try:
                with open(self.last_update_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception as e:
                logger.error(f"最終更新情報の読み込みエラー: {str(e)}")
        
        # デフォルト値
        return {
            "daily": {
                "last_full_update": None,
                "last_incremental_update": None,
                "tickers_status": {}
            },
            "hourly": {
                "last_full_update": None,
                "last_incremental_update": None,
                "tickers_status": {}
            }
        }
    
    def _save_last_update(self):
        """最終更新情報の保存"""
        try:
            with open(self.last_update_file, 'w', encoding='utf-8') as f:
                json.dump(self.last_update_info, f, indent=2, ensure_ascii=False)
            logger.info("最終更新情報を保存しました")
        except Exception as e:
            logger.error(f"最終更新情報の保存エラー: {str(e)}")
            
    def _load_tickers(self):
        """
        ティッカー情報の読み込み
        
        Returns:
        --------
        dict
            ティッカー情報
        """
        if os.path.exists(self.tickers_file):
            try:
                with open(self.tickers_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception as e:
                logger.error(f"ティッカー情報の読み込みエラー: {str(e)}")
        
        # ファイルがなければ空のデータを返す
        return {"tickers": {}}
    
    def get_all_tickers(self):
        """
        全ティッカーのリストを取得
        
        Returns:
        --------
        list
            ティッカーリスト
        """
        return list(self.tickers_info.get("tickers", {}).keys())
    
    def update_ticker_list(self, force_update=False):
        """
        ティッカーリストの更新
        
        Parameters:
        -----------
        force_update : bool
            強制更新フラグ
            
        Returns:
        --------
        list
            更新されたティッカーリスト
        """
        if self.tickers_info.get("tickers") and not force_update:
            logger.info("既存のティッカー情報を使用します")
            return list(self.tickers_info.get("tickers", {}).keys())
        
        try:
            # JPX公式サイトから日経225構成銘柄を取得
            logger.info("JPX公式サイトから日経225構成銘柄を取得します")
            url = "https://www.jpx.co.jp/markets/indices/nikkei225/component.html"
            response = requests.get(url, timeout=30)
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # テーブルから銘柄情報を抽出
            table = soup.find('table', {'class': 'tbl-data'})
            if not table:
                logger.error("銘柄情報を含むテーブルが見つかりませんでした")
                # 既存のティッカーを返す
                return self.get_all_tickers()
            
            tickers = {}
            for row in table.find_all('tr')[1:]:  # ヘッダー行をスキップ
                cols = row.find_all('td')
                if len(cols) >= 3:
                    code = cols[0].text.strip()
                    name = cols[1].text.strip()
                    sector = cols[2].text.strip() if len(cols) > 2 else "不明"
                    
                    # コードが数字のみの場合、.Tを追加
                    if code.isdigit():
                        ticker = f"{code}.T"
                    else:
                        ticker = code
                    
                    tickers[ticker] = {
                        "name": name,
                        "sector": sector,
                        "last_update": datetime.now().isoformat()
                    }
            
            if not tickers:
                logger.warning("銘柄が見つかりませんでした。既存のデータを使用します。")
                return self.get_all_tickers()
            
            # ティッカー情報を更新
            self.tickers_info = {
                "tickers": tickers,
                "last_update": datetime.now().isoformat(),
                "source": "JPX Official Website"
            }
            
            # 保存
            with open(self.tickers_file, 'w', encoding='utf-8') as f:
                json.dump(self.tickers_info, f, indent=2, ensure_ascii=False)
            
            logger.info(f"ティッカー情報を更新しました: {len(tickers)}銘柄")
            return list(tickers.keys())
            
        except Exception as e:
            logger.error(f"ティッカー情報の更新エラー: {str(e)}")
            logger.debug(traceback.format_exc())
            # 既存のティッカーを返す
            return self.get_all_tickers()
    
    def get_stooq_data(self, ticker, start_date, end_date=None):
        """
        Stooqからのデータ取得
        
        Parameters:
        -----------
        ticker : str
            ティッカーシンボル
        start_date : str or datetime
            開始日
        end_date : str or datetime or None
            終了日（Noneの場合は現在日付）
            
        Returns:
        --------
        pandas.DataFrame
            取得したデータ
        """
        logger.info(f"Stooqから{ticker}のデータを取得します")
        
        # ティッカー形式を変換 (.T → .jp)
        stooq_ticker = ticker.replace(".T", ".jp")
        
        try:
            # 日付形式の変換
            if isinstance(start_date, str):
                start_date = pd.to_datetime(start_date)
            if end_date is None:
                end_date = datetime.now()
            elif isinstance(end_date, str):
                end_date = pd.to_datetime(end_date)
            
            # キャッシュファイル名
            cache_file = os.path.join(
                self.cache_dir, 
                f"stooq_{stooq_ticker}_{start_date.strftime('%Y%m%d')}_{end_date.strftime('%Y%m%d')}.csv"
            )
            
            # キャッシュがあれば使用
            if os.path.exists(cache_file):
                logger.info(f"キャッシュから{ticker}のデータを読み込みます: {cache_file}")
                df = pd.read_csv(cache_file, parse_dates=["Date"])
                return df
            
            # pandas-datareaderを使用
            df = pdr.data.get_data_stooq(
                stooq_ticker,
                start=start_date,
                end=end_date
            )
            
            # インデックスをリセット
            df = df.reset_index()
            
            # ティッカー情報を追加
            df["Ticker"] = ticker
            
            # キャッシュに保存
            df.to_csv(cache_file, index=False)
            
            # 少し待機
            time.sleep(random.uniform(1.0, 3.0))
            
            return df
            
        except Exception as e:
            logger.error(f"Stooqからのデータ取得エラー ({ticker}): {str(e)}")
            logger.debug(traceback.format_exc())
            return pd.DataFrame()
    
    def get_investpy_data(self, ticker, start_date, end_date=None):
        """
        investpyからのデータ取得
        
        Parameters:
        -----------
        ticker : str
            ティッカーシンボル
        start_date : str or datetime
            開始日
        end_date : str or datetime or None
            終了日（Noneの場合は現在日付）
            
        Returns:
        --------
        pandas.DataFrame
            取得したデータ
        """
        if not INVESTPY_AVAILABLE:
            logger.warning("investpyが利用できないため、このソースからのデータ取得をスキップします")
            return pd.DataFrame()
        
        logger.info(f"investpyから{ticker}のデータを取得します")
        
        try:
            # ティッカー形式を変換 (.T を削除)
            stock_code = ticker.replace(".T", "")
            
            # 日付形式の変換
            if isinstance(start_date, str):
                start_date = pd.to_datetime(start_date)
            if end_date is None:
                end_date = datetime.now()
            elif isinstance(end_date, str):
                end_date = pd.to_datetime(end_date)
            
            # investpy用の日付形式
            from_date = start_date.strftime('%d/%m/%Y')
            to_date = end_date.strftime('%d/%m/%Y')
            
            # キャッシュファイル名
            cache_file = os.path.join(
                self.cache_dir, 
                f"investpy_{stock_code}_{start_date.strftime('%Y%m%d')}_{end_date.strftime('%Y%m%d')}.csv"
            )
            
            # キャッシュがあれば使用
            if os.path.exists(cache_file):
                logger.info(f"キャッシュから{ticker}のデータを読み込みます: {cache_file}")
                df = pd.read_csv(cache_file, parse_dates=["Date"])
                return df
            
            # investpyを使用してデータ取得
            df = investpy.get_stock_historical_data(
                stock=stock_code,
                country='japan',
                from_date=from_date,
                to_date=to_date
            )
            
            # インデックスをリセット
            df = df.reset_index()
            
            # カラム名を標準化
            df = df.rename(columns={
                'Open': 'Open',
                'High': 'High',
                'Low': 'Low',
                'Close': 'Close',
                'Volume': 'Volume',
                'Date': 'Date'
            })
            
            # ティッカー情報を追加
            df["Ticker"] = ticker
            
            # キャッシュに保存
            df.to_csv(cache_file, index=False)
            
            # 少し待機
            time.sleep(random.uniform(1.0, 3.0))
            
            return df
            
        except Exception as e:
            logger.error(f"investpyからのデータ取得エラー ({ticker}): {str(e)}")
            logger.debug(traceback.format_exc())
            return pd.DataFrame()
    
    def get_jpx_data(self, year, month=None):
        """
        JPXからのデータ取得（手動ダウンロードされたCSVファイルを想定）
        
        Parameters:
        -----------
        year : int
            年
        month : int or None
            月（Noneの場合は年間データ）
            
        Returns:
        --------
        pandas.DataFrame
            取得したデータ
        """
        logger.info(f"JPXデータの読み込み: 年={year}, 月={month if month else '全て'}")
        
        try:
            # ファイルパス
            file_pattern = f"JPX_data_{year}"
            if month:
                file_pattern += f"_{month:02d}"
            file_pattern += ".csv"
            
            jpx_file = None
            
            # データディレクトリから検索
            for root, _, files in os.walk("data"):
                for file in files:
                    if file == file_pattern:
                        jpx_file = os.path.join(root, file)
                        break
            
            if not jpx_file:
                logger.warning(f"JPXデータファイルが見つかりません: {file_pattern}")
                return pd.DataFrame()
            
            # CSVファイルを読み込み
            df = pd.read_csv(jpx_file)
            
            # カラム名を標準化
            if "日付" in df.columns:
                df = df.rename(columns={"日付": "Date"})
            if "銘柄コード" in df.columns:
                df = df.rename(columns={"銘柄コード": "Code"})
            if "始値" in df.columns:
                df = df.rename(columns={"始値": "Open"})
            if "高値" in df.columns:
                df = df.rename(columns={"高値": "High"})
            if "安値" in df.columns:
                df = df.rename(columns={"安値": "Low"})
            if "終値" in df.columns:
                df = df.rename(columns={"終値": "Close"})
            if "出来高" in df.columns:
                df = df.rename(columns={"出来高": "Volume"})
                
            # 日付をパース
            if "Date" in df.columns:
                df["Date"] = pd.to_datetime(df["Date"])
            
            # ティッカー情報を追加
            if "Code" in df.columns:
                df["Ticker"] = df["Code"].astype(str) + ".T"
            
            return df
            
        except Exception as e:
            logger.error(f"JPXデータの読み込みエラー: {str(e)}")
            logger.debug(traceback.format_exc())
            return pd.DataFrame()
    
    def get_multi_source_data(self, ticker, start_date, end_date=None):
        """
        複数のソースからデータを取得
        
        Parameters:
        -----------
        ticker : str
            ティッカーシンボル
        start_date : str or datetime
            開始日
        end_date : str or datetime or None
            終了日（Noneの場合は現在日付）
            
        Returns:
        --------
        pandas.DataFrame
            統合されたデータ
        """
        logger.info(f"{ticker}のデータを複数ソースから取得します")
        
        # 日付形式の変換
        if isinstance(start_date, str):
            start_date = pd.to_datetime(start_date)
        if end_date is None:
            end_date = datetime.now()
        elif isinstance(end_date, str):
            end_date = pd.to_datetime(end_date)
        
        # キャッシュファイル名
        cache_file = os.path.join(
            self.cache_dir, 
            f"multi_source_{ticker}_{start_date.strftime('%Y%m%d')}_{end_date.strftime('%Y%m%d')}.csv"
        )
        
        # キャッシュがあれば使用
        if os.path.exists(cache_file):
            logger.info(f"キャッシュから{ticker}のデータを読み込みます: {cache_file}")
            df = pd.read_csv(cache_file, parse_dates=["Date"])
            return df
        
        # 複数ソースからデータを取得
        dfs = []
        
        # Stooqからのデータ
        stooq_df = self.get_stooq_data(ticker, start_date, end_date)
        if not stooq_df.empty:
            stooq_df["Source"] = "Stooq"
            dfs.append(stooq_df)
        
        # investpyからのデータ
        if INVESTPY_AVAILABLE:
            investpy_df = self.get_investpy_data(ticker, start_date, end_date)
            if not investpy_df.empty:
                investpy_df["Source"] = "Investpy"
                dfs.append(investpy_df)
        
        # pandas-datareaderのYahoo Financeからのデータ (バックアップとして)
        try:
            yahoo_df = pdr.data.get_data_yahoo(ticker, start=start_date, end=end_date)
            if not yahoo_df.empty:
                yahoo_df = yahoo_df.reset_index()
                yahoo_df["Ticker"] = ticker
                yahoo_df["Source"] = "Yahoo"
                dfs.append(yahoo_df)
        except Exception as e:
            logger.warning(f"Yahoo Financeからのデータ取得エラー ({ticker}): {str(e)}")
        
        if not dfs:
            logger.error(f"{ticker}のデータが取得できませんでした")
            return pd.DataFrame()
        
        # データの統合
        all_data = pd.concat(dfs, ignore_index=True)
        
        # 重複を削除（同じ日付のデータがある場合は最初のものを残す）
        all_data = all_data.drop_duplicates(subset=["Date"], keep="first")
        
        # 日付でソート
        all_data = all_data.sort_values("Date")
        
        # キャッシュに保存
        all_data.to_csv(cache_file, index=False)
        
        return all_data
    
    def save_to_directory_structure(self, df, data_type="daily"):
        """
        データを年月ディレクトリ構造に保存
        
        Parameters:
        -----------
        df : pandas.DataFrame
            保存するデータ
        data_type : str
            データタイプ（'daily'または'hourly'）
            
        Returns:
        --------
        list
            保存されたファイルパスのリスト
        """
        if df.empty:
            logger.warning("保存するデータが空です")
            return []
        
        logger.info(f"{data_type}データをディレクトリ構造に保存します: {len(df)}行")
        
        base_dir = self.daily_dir if data_type == "daily" else self.hourly_dir
        saved_files = []
        
        # ティッカーごとに処理
        for ticker, ticker_data in df.groupby("Ticker"):
            # 年と月でグループ化
            for (year, month), group in ticker_data.groupby([
                ticker_data["Date"].dt.year, 
                ticker_data["Date"].dt.month
            ]):
                # ディレクトリパス
                year_month_dir = os.path.join(base_dir, str(year), f"{year}-{month:02d}")
                os.makedirs(year_month_dir, exist_ok=True)
                
                # ファイルパス
                file_path = os.path.join(year_month_dir, f"{ticker}.csv")
                
                # 既存ファイルがあれば読み込んで結合
                if os.path.exists(file_path):
                    existing_data = pd.read_csv(file_path, parse_dates=["Date"])
                    group = pd.concat([existing_data, group]).drop_duplicates(subset=["Date"]).sort_values("Date")
                
                # DatetimeとTickerカラムがあることを確認
                if "Date" not in group.columns or "Ticker" not in group.columns:
                    logger.error(f"必須カラムがありません: {group.columns}")
                    continue
                
                # 保存
                group.to_csv(file_path, index=False)
                saved_files.append(file_path)
                
                logger.info(f"保存しました: {file_path}, {len(group)}行")
        
        return saved_files
    
    def create_consolidated_files(self, data_type="daily", force_update=False):
        """
        統合データファイルの作成
        
        Parameters:
        -----------
        data_type : str
            データタイプ（'daily'または'hourly'）
        force_update : bool
            強制更新フラグ
            
        Returns:
        --------
        str
            作成されたファイルパス
        """
        logger.info(f"{data_type}データの統合ファイルを作成します")
        
        # 出力ファイル
        output_file = os.path.join(self.consolidated_dir, f"nikkei225_{data_type}_all.csv")
        
        # 前回の更新から変更がなければスキップ
        if not force_update and os.path.exists(output_file):
            last_update = self.last_update_info[data_type]["last_incremental_update"]
            if last_update:
                last_update_time = datetime.fromisoformat(last_update)
                output_file_mtime = datetime.fromtimestamp(os.path.getmtime(output_file))
                
                if output_file_mtime > last_update_time:
                    logger.info(f"統合ファイルは最新です: {output_file}")
                    return output_file
        
        # 読み込むディレクトリ
        base_dir = self.daily_dir if data_type == "daily" else self.hourly_dir
        
        # 全ファイルを検索
        all_files = []
        for root, _, files in os.walk(base_dir):
            for file in files:
                if file.endswith(".csv"):
                    all_files.append(os.path.join(root, file))
        
        if not all_files:
            logger.warning(f"{data_type}データのCSVファイルが見つかりません")
            return None
        
        # 全データを読み込み
        all_data = []
        for file in all_files:
            try:
                df = pd.read_csv(file, parse_dates=["Date"])
                all_data.append(df)
            except Exception as e:
                logger.error(f"{file}の読み込みエラー: {str(e)}")
        
        if not all_data:
            logger.warning("有効なデータがありません")
            return None
        
        # 結合
        combined_data = pd.concat(all_data, ignore_index=True)
        
        # 重複を削除
        combined_data = combined_data.drop_duplicates(subset=["Ticker", "Date"]).sort_values(["Ticker", "Date"])
        
        # 保存
        combined_data.to_csv(output_file, index=False)
        
        logger.info(f"統合ファイルを保存しました: {output_file}, {len(combined_data)}行, {combined_data['Ticker'].nunique()}銘柄")
        
        return output_file
    
    def collect_daily_data(self, tickers, start_date, end_date=None, batch_size=1, sleep_time=5):
        """
        日次データの収集
        
        Parameters:
        -----------
        tickers : list
            ティッカーシンボルのリスト
        start_date : str or datetime
            開始日
        end_date : str or datetime or None
            終了日（Noneの場合は現在日付）
        batch_size : int
            バッチサイズ
        sleep_time : int
            バッチ間のスリープ時間（秒）
            
        Returns:
        --------
        dict
            収集結果の概要
        """
        logger.info(f"日次データの収集を開始します: {len(tickers)}銘柄, {start_date}～{end_date if end_date else '現在'}")
        
        # 日付形式の変換
        if isinstance(start_date, str):
            start_date = pd.to_datetime(start_date)
        if end_date is None:
            end_date = datetime.now()
        elif isinstance(end_date, str):
            end_date = pd.to_datetime(end_date)
        
        # バッチに分割
        batches = [tickers[i:i+batch_size] for i in range(0, len(tickers), batch_size)]
        
        # 結果の概要
        results = {
            "total_tickers": len(tickers),
            "start_date": start_date.strftime('%Y-%m-%d'),
            "end_date": end_date.strftime('%Y-%m-%d'),
            "success_count": 0,
            "failed_tickers": [],
            "saved_files": []
        }
        
        # 各バッチを処理
        for batch_idx, batch in enumerate(batches):
            logger.info(f"バッチ {batch_idx+1}/{len(batches)} を処理中: {batch}")
            
            for ticker in batch:
                try:
                    # 複数ソースからデータを取得
                    df = self.get_multi_source_data(ticker, start_date, end_date)
                    
                    if df.empty:
                        logger.warning(f"{ticker}のデータが取得できませんでした")
                        results["failed_tickers"].append(ticker)
                        continue
                    
                    # ディレクトリ構造に保存
                    saved_files = self.save_to_directory_structure(df, data_type="daily")
                    results["saved_files"].extend(saved_files)
                    
                    if saved_files:
                        results["success_count"] += 1
                        
                        # ステータスを更新
                        self.last_update_info["daily"]["tickers_status"][ticker] = {
                            "last_update": datetime.now().isoformat(),
                            "start_date": start_date.strftime('%Y-%m-%d'),
                            "end_date": end_date.strftime('%Y-%m-%d'),
                            "status": "success"
                        }
                    else:
                        results["failed_tickers"].append(ticker)
                    
                except Exception as e:
                    logger.error(f"{ticker}のデータ処理エラー: {str(e)}")
                    logger.debug(traceback.format_exc())
                    results["failed_tickers"].append(ticker)
                
                # ティッカー間の待機
                time.sleep(random.uniform(1.0, 3.0))
            
            # バッチ間の待機
            if batch_idx < len(batches) - 1:  # 最後のバッチでなければ待機
                logger.info(f"次のバッチの前に{sleep_time}秒待機します")
                time.sleep(sleep_time)
        
        # 最終更新情報を更新
        self.last_update_info["daily"]["last_incremental_update"] = datetime.now().isoformat()
        if results["success_count"] == len(tickers):
            self.last_update_info["daily"]["last_full_update"] = datetime.now().isoformat()
        
        # 保存
        self._save_last_update()
        
        return results
    
    def collect_hourly_data(self, tickers, days_back=30, batch_size=1, sleep_time=5):
        """
        時間足データの収集
        
        Parameters:
        -----------
        tickers : list
            ティッカーシンボルのリスト
        days_back : int
            過去何日分のデータを取得するか
        batch_size : int
            バッチサイズ
        sleep_time : int
            バッチ間のスリープ時間（秒）
            
        Returns:
        --------
        dict
            収集結果の概要
        """
        logger.info(f"時間足データの収集を開始します: {len(tickers)}銘柄, 過去{days_back}日分")
        
        # 期間の設定
        end_date = datetime.now()
        start_date = end_date - timedelta(days=days_back)
        
        # バッチに分割
        batches = [tickers[i:i+batch_size] for i in range(0, len(tickers), batch_size)]
        
        # 結果の概要
        results = {
            "total_tickers": len(tickers),
            "days_back": days_back,
            "start_date": start_date.strftime('%Y-%m-%d'),
            "end_date": end_date.strftime('%Y-%m-%d'),
            "success_count": 0,
            "failed_tickers": [],
            "saved_files": []
        }
        
        # 各バッチを処理
        for batch_idx, batch in enumerate(batches):
            logger.info(f"バッチ {batch_idx+1}/{len(batches)} を処理中: {batch}")
            
            for ticker in batch:
                try:
                    # 複数ソースからデータを取得
                    # 注意: 時間足データは一部のソースでしか取得できない可能性あり
                    df = self.get_multi_source_data(ticker, start_date, end_date)
                    
                    if df.empty:
                        logger.warning(f"{ticker}の時間足データが取得できませんでした")
                        results["failed_tickers"].append(ticker)
                        continue
                    
                    # ディレクトリ構造に保存
                    saved_files = self.save_to_directory_structure(df, data_type="hourly")
                    results["saved_files"].extend(saved_files)
                    
                    if saved_files:
                        results["success_count"] += 1
                        
                        # ステータスを更新
                        self.last_update_info["hourly"]["tickers_status"][ticker] = {
                            "last_update": datetime.now().isoformat(),
                            "start_date": start_date.strftime('%Y-%m-%d'),
                            "end_date": end_date.strftime('%Y-%m-%d'),
                            "status": "success"
                        }
                    else:
                        results["failed_tickers"].append(ticker)
                    
                except Exception as e:
                    logger.error(f"{ticker}の時間足データ処理エラー: {str(e)}")
                    logger.debug(traceback.format_exc())
                    results["failed_tickers"].append(ticker)
                
                # ティッカー間の待機
                time.sleep(random.uniform(1.0, 3.0))
            
            # バッチ間の待機
            if batch_idx < len(batches) - 1:
                logger.info(f"次のバッチの前に{sleep_time}秒待機します")
                time.sleep(sleep_time)
        
        # 最終更新情報を更新
        self.last_update_info["hourly"]["last_incremental_update"] = datetime.now().isoformat()
        if results["success_count"] == len(tickers):
            self.last_update_info["hourly"]["last_full_update"] = datetime.now().isoformat()
        
        # 保存
        self._save_last_update()
        
        return results
    
    def backfill_daily_data(self, start_year, end_year=None, batch_size=1, sleep_between_years=1800):
        """
        日次データのバックフィル
        
        Parameters:
        -----------
        start_year : int
            開始年
        end_year : int or None
            終了年（Noneの場合は現在の年）
        batch_size : int
            バッチサイズ
        sleep_between_years : int
            年間隔のスリープ時間（秒）
            
        Returns:
        --------
        dict
            バックフィル結果の概要
        """
        if end_year is None:
            end_year = datetime.now().year
        
        logger.info(f"日次データのバックフィルを開始します: {start_year}年～{end_year}年")
        
        # ティッカーリストを取得
        all_tickers = self.get_all_tickers()
        
        if not all_tickers:
            logger.error("ティッカーリストが空です")
            return {"status": "error", "reason": "empty_ticker_list"}
        
        # 結果の概要
        results = {
            "total_years": end_year - start_year + 1,
            "total_tickers": len(all_tickers),
            "completed_years": 0,
            "success_count": 0,
            "failed_tickers": []
        }
        
        # 各年を処理
        for year in range(start_year, end_year + 1):
            logger.info(f"{year}年のデータ収集を開始します")
            
            # 期間の設定
            start_date = datetime(year, 1, 1)
            end_date = datetime(year, 12, 31)
            if year == datetime.now().year:
                end_date = datetime.now()
            
            # データを収集
            year_result = self.collect_daily_data(
                tickers=all_tickers,
                start_date=start_date,
                end_date=end_date,
                batch_size=batch_size,
                sleep_time=5
            )
            
            # 結果を更新
            results["completed_years"] += 1
            results["success_count"] += year_result["success_count"]
            results["failed_tickers"].extend(year_result["failed_tickers"])
            
            # 複数の年にまたがる場合は長めに待機
            if year < end_year:
                logger.info(f"次の年の処理の前に{sleep_between_years/60:.1f}分待機します")
                time.sleep(sleep_between_years)
        
        # 統合ファイルを作成
        self.create_consolidated_files(data_type="daily", force_update=True)
        
        return results
    
    def import_jpx_bulk_data(self, source_dir="data/jpx"):
        """
        JPXバルクデータのインポート
        
        Parameters:
        -----------
        source_dir : str
            JPXデータの格納されたディレクトリ
            
        Returns:
        --------
        dict
            インポート結果の概要
        """
        logger.info(f"JPXバルクデータのインポートを開始します: {source_dir}")
        
        # ディレクトリの存在確認
        if not os.path.exists(source_dir) or not os.path.isdir(source_dir):
            logger.error(f"ディレクトリが存在しません: {source_dir}")
            return {"status": "error", "reason": "directory_not_found"}
        
        # 結果の概要
        results = {
            "total_files": 0,
            "imported_files": 0,
            "processed_records": 0,
            "saved_files": []
        }
        
        # CSVファイルを検索
        for root, _, files in os.walk(source_dir):
            for file in files:
                if file.endswith(".csv"):
                    file_path = os.path.join(root, file)
                    results["total_files"] += 1
                    
                    try:
                        # CSVを読み込み
                        df = pd.read_csv(file_path)
                        
                        # カラム名を標準化
                        if "日付" in df.columns:
                            df = df.rename(columns={"日付": "Date"})
                        if "銘柄コード" in df.columns:
                            df = df.rename(columns={"銘柄コード": "Code"})
                        if "始値" in df.columns:
                            df = df.rename(columns={"始値": "Open"})
                        if "高値" in df.columns:
                            df = df.rename(columns={"高値": "High"})
                        if "安値" in df.columns:
                            df = df.rename(columns={"安値": "Low"})
                        if "終値" in df.columns:
                            df = df.rename(columns={"終値": "Close"})
                        if "出来高" in df.columns:
                            df = df.rename(columns={"出来高": "Volume"})
                        
                        # 日付をパース
                        if "Date" in df.columns:
                            df["Date"] = pd.to_datetime(df["Date"])
                        
                        # ティッカー情報を追加
                        if "Code" in df.columns:
                            df["Ticker"] = df["Code"].astype(str) + ".T"
                        
                        # 必須カラムの確認
                        if "Date" not in df.columns or "Ticker" not in df.columns:
                            logger.warning(f"必須カラムがありません: {file_path}")
                            continue
                        
                        # データ保存
                        saved_files = self.save_to_directory_structure(df, data_type="daily")
                        
                        results["imported_files"] += 1
                        results["processed_records"] += len(df)
                        results["saved_files"].extend(saved_files)
                        
                        logger.info(f"JPXデータをインポートしました: {file_path}, {len(df)}行")
                        
                    except Exception as e:
                        logger.error(f"JPXデータのインポートエラー: {file_path}, {str(e)}")
                        logger.debug(traceback.format_exc())
        
        # 統合ファイルを作成
        if results["imported_files"] > 0:
            self.create_consolidated_files(data_type="daily", force_update=True)
        
        return results


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='代替データソースを使用した日経225株価データ収集')
    parser.add_argument('--base-dir', type=str, default="nikkei225_historical_data", help='ベースディレクトリ')
    parser.add_argument('--update-tickers', action='store_true', help='ティッカー情報を更新')
    parser.add_argument('--collect-daily', action='store_true', help='日次データを収集')
    parser.add_argument('--start-date', type=str, help='取得開始日 (YYYY-MM-DD)')
    parser.add_argument('--end-date', type=str, help='取得終了日 (YYYY-MM-DD)')
    parser.add_argument('--years', type=str, help='取得対象年（カンマ区切り、例: 2020,2021,2022）')
    parser.add_argument('--backfill', action='store_true', help='バックフィルを実行')
    parser.add_argument('--start-year', type=int, help='バックフィル開始年')
    parser.add_argument('--end-year', type=int, help='バックフィル終了年')
    parser.add_argument('--import-jpx', type=str, help='JPXデータをインポートするディレクトリ')
    parser.add_argument('--batch-size', type=int, default=1, help='バッチサイズ')
    parser.add_argument('--sleep-time', type=int, default=5, help='バッチ間の待機時間（秒）')
    parser.add_argument('--tickers', type=str, help='対象ティッカー（カンマ区切り、例: 7203.T,9984.T）')
    parser.add_argument('--consolidate', action='store_true', help='統合ファイルを作成')
    
    args = parser.parse_args()
    
    # ログディレクトリがなければ作成
    os.makedirs("logs", exist_ok=True)
    
    # データコレクター
    collector = AlternativeDataCollector(base_dir=args.base_dir)
    
    # ティッカー情報を更新
    if args.update_tickers:
        tickers = collector.update_ticker_list(force_update=True)
        logger.info(f"ティッカー情報を更新しました: {len(tickers)}銘柄")
    
    # 処理対象のティッカーを設定
    target_tickers = None
    if args.tickers:
        target_tickers = args.tickers.split(',')
        logger.info(f"指定されたティッカーを使用します: {target_tickers}")
    else:
        target_tickers = collector.get_all_tickers()
        logger.info(f"全ティッカーを使用します: {len(target_tickers)}銘柄")
    
    # 日次データを収集
    if args.collect_daily:
        start_date = args.start_date or (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d')
        end_date = args.end_date
        
        result = collector.collect_daily_data(
            tickers=target_tickers,
            start_date=start_date,
            end_date=end_date,
            batch_size=args.batch_size,
            sleep_time=args.sleep_time
        )
        
        logger.info(f"日次データの収集結果: 成功={result['success_count']}/{result['total_tickers']}銘柄")
    
    # 年指定の場合
    if args.years:
        years = [int(y) for y in args.years.split(',')]
        for year in years:
            logger.info(f"{year}年のデータを収集します")
            
            start_date = datetime(year, 1, 1)
            end_date = datetime(year, 12, 31)
            if year == datetime.now().year:
                end_date = datetime.now()
            
            result = collector.collect_daily_data(
                tickers=target_tickers,
                start_date=start_date,
                end_date=end_date,
                batch_size=args.batch_size,
                sleep_time=args.sleep_time
            )
            
            logger.info(f"{year}年のデータ収集結果: 成功={result['success_count']}/{result['total_tickers']}銘柄")
            
            # 年間の処理の間は長めに待機
            if year != years[-1]:
                sleep_time = 60  # 1分待機
                logger.info(f"次の年の処理の前に{sleep_time}秒待機します")
                time.sleep(sleep_time)
    
    # バックフィル
    if args.backfill:
        start_year = args.start_year or (datetime.now().year - 5)
        end_year = args.end_year or datetime.now().year
        
        result = collector.backfill_daily_data(
            start_year=start_year,
            end_year=end_year,
            batch_size=args.batch_size,
            sleep_between_years=1800  # 30分
        )
        
        logger.info(f"バックフィル結果: 成功={result['success_count']}/{result['total_tickers']}銘柄, 完了年数={result['completed_years']}/{result['total_years']}年")
    
    # JPXデータのインポート
    if args.import_jpx:
        result = collector.import_jpx_bulk_data(source_dir=args.import_jpx)
        
        logger.info(f"JPXデータのインポート結果: {result['imported_files']}/{result['total_files']}ファイル, {result['processed_records']}レコード")
    
    # 統合ファイルの作成
    if args.consolidate:
        daily_file = collector.create_consolidated_files(data_type="daily", force_update=True)
        logger.info(f"日次データの統合ファイルを作成しました: {daily_file}")
