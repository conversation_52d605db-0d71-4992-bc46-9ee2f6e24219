#!/usr/bin/env python3
"""
日経225データ収集の改良版スクリプト - 最適化版

主な改良点:
1. 複数のデータソースを順次試行（yfinance, pandas-datareader, 直接API, stooq_web_scraper）
2. 詳細なエラーログと診断情報
3. 堅牢なエラーハンドリングと回復メカニズム
4. より効率的なキャッシング
5. スマートなリトライ機能
6. 最新の仕様に基づくデータ取得の最適化
"""

import os
import logging
import json
import time
import argparse
import sys
from datetime import datetime, timedelta
import pandas as pd
import numpy as np
import random
import traceback
import requests
from tqdm import tqdm
import re

# ロギングセットアップ
def setup_logging(debug=False):
    log_level = logging.DEBUG if debug else logging.INFO
    log_dir = "logs"
    os.makedirs(log_dir, exist_ok=True)
    
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    log_file = f"{log_dir}/nikkei_data_collector_{timestamp}.log"
    
    logging.basicConfig(
        level=log_level,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file),
            logging.StreamHandler()
        ]
    )
    
    logger = logging.getLogger(__name__)
    return logger

# 利用可能なデータソースを管理
class DataSourceManager:
    def __init__(self):
        self.sources = {}
        self.available_sources = []
        
        # Pandas DataReader
        try:
            import pandas_datareader as pdr
            self.sources["pandas_datareader"] = pdr
            self.available_sources.append("pandas_datareader")
        except ImportError:
            pass
        
        # yfinance
        try:
            import yfinance as yf
            self.sources["yfinance"] = yf
            self.available_sources.append("yfinance")
        except ImportError:
            pass
        
        # Stooq Web Scraper
        try:
            import stooq_web_scraper_fixed
            self.sources["stooq_scraper"] = stooq_web_scraper_fixed
            self.available_sources.append("stooq_scraper")
        except ImportError:
            try:
                import stooq_web_scraper
                self.sources["stooq_scraper"] = stooq_web_scraper
                self.available_sources.append("stooq_scraper")
            except ImportError:
                pass
        
        # ティッカー情報モジュール
        try:
            from nikkei225_tickers import (
                get_all_tickers,
                get_tickers_by_sector,
                get_sector_names,
                get_major_tickers,
                get_topix_core30
            )
            self.get_all_tickers = get_all_tickers
            self.get_tickers_by_sector = get_tickers_by_sector
            self.get_sector_names = get_sector_names
            self.get_major_tickers = get_major_tickers
            self.get_topix_core30 = get_topix_core30
        except ImportError:
            # デフォルトの簡易関数
            self.get_all_tickers = lambda: ["8306.T", "9432.T", "9984.T", "6758.T", "7203.T"]
            self.get_tickers_by_sector = lambda sector: self.get_all_tickers()
            self.get_sector_names = lambda: ["全銘柄"]
            self.get_major_tickers = lambda: self.get_all_tickers()
            self.get_topix_core30 = lambda: self.get_all_tickers()[:10]
    
    def get_source(self, source_name):
        """指定ソースのモジュールを取得"""
        return self.sources.get(source_name)
    
    def is_available(self, source_name):
        """ソースが利用可能かチェック"""
        return source_name in self.available_sources
    
    def get_available_sources(self):
        """利用可能なソース一覧を取得"""
        return self.available_sources

class ImprovedStockDataCollector:
    """改良版日経225株価データ収集クラス"""
    
    def __init__(self, base_dir="nikkei225_improved_data", cache_dir="data/improved_cache", debug=False):
        """初期化"""
        self.base_dir = base_dir
        self.cache_dir = cache_dir
        self.debug = debug
        
        # ロガー設定
        self.logger = setup_logging(debug=debug)
        
        # データソース管理
        self.source_manager = DataSourceManager()
        self.logger.info(f"利用可能なデータソース: {', '.join(self.source_manager.get_available_sources())}")
        
        # ディレクトリ構造
        self.daily_dir = os.path.join(base_dir, "daily")
        self.metadata_dir = os.path.join(base_dir, "metadata")
        self.consolidated_dir = os.path.join(base_dir, "consolidated")
        self.debug_dir = os.path.join(base_dir, "debug")
        
        # ディレクトリ作成
        for dir_path in [self.daily_dir, self.metadata_dir, self.consolidated_dir, self.cache_dir, self.debug_dir]:
            os.makedirs(dir_path, exist_ok=True)
        
        # ステータスとソース統計ファイル
        self.status_file = os.path.join(self.metadata_dir, "collection_status.json")
        self.source_stats_file = os.path.join(self.metadata_dir, "source_stats.json")
        
        # ステータスとソース統計の読み込み
        self.status = self._load_json_file(self.status_file, self._get_default_status())
        self.source_stats = self._load_json_file(self.source_stats_file, self._get_default_source_stats())
        
        # ユーザーエージェントのローテーション
        self.user_agents = [
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Safari/605.1.15",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:90.0) Gecko/20100101 Firefox/90.0",
            "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/92.0.4515.107 Safari/537.36",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/92.0.4515.131 Safari/537.36 Edg/92.0.902.67"
        ]
        
        # ソースの優先度（低いほど優先）
        self.source_weights = {
            "yfinance": 1.0,
            "pandas_datareader": 2.0,
            "stooq_scraper": 3.0,
            "direct_request": 4.0
        }
    
    def _load_json_file(self, filepath, default_value):
        """JSONファイルを読み込むユーティリティメソッド"""
        if os.path.exists(filepath):
            try:
                with open(filepath, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception as e:
                self.logger.error(f"ファイル読み込みエラー: {filepath} - {str(e)}")
        return default_value
    
    def _save_json_file(self, filepath, data):
        """JSONファイルを保存するユーティリティメソッド"""
        try:
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
            return True
        except Exception as e:
            self.logger.error(f"ファイル保存エラー: {filepath} - {str(e)}")
            return False
    
    def _get_default_status(self):
        """デフォルトステータスの取得"""
        return {
            "sectors": {},
            "years": {},
            "tickers": {},
            "last_update": None,
            "total_records": 0,
            "total_symbols": 0,
            "completed_years": []
        }
    
    def _get_default_source_stats(self):
        """デフォルトのデータソース統計情報の取得"""
        return {
            "success_rate": {
                "yfinance": {"success": 0, "failure": 0},
                "pandas_datareader": {"success": 0, "failure": 0},
                "stooq_scraper": {"success": 0, "failure": 0},
                "direct_request": {"success": 0, "failure": 0}
            },
            "average_time": {
                "yfinance": 0,
                "pandas_datareader": 0,
                "stooq_scraper": 0,
                "direct_request": 0
            },
            "tickers_by_source": {},
            "last_update": None
        }
    
    def _update_status(self):
        """ステータスの更新"""
        self.status["last_update"] = datetime.now().isoformat()
        return self._save_json_file(self.status_file, self.status)
    
    def _update_source_stats(self, ticker, source, success, elapsed_time=0):
        """データソース統計の更新"""
        # 成功/失敗カウントを更新
        if success:
            self.source_stats["success_rate"][source]["success"] += 1
        else:
            self.source_stats["success_rate"][source]["failure"] += 1
        
        # 平均時間の更新
        total_attempts = (self.source_stats["success_rate"][source]["success"] + 
                           self.source_stats["success_rate"][source]["failure"])
        
        current_avg = self.source_stats["average_time"][source]
        if total_attempts > 1:
            new_avg = current_avg + (elapsed_time - current_avg) / total_attempts
        else:
            new_avg = elapsed_time
        
        self.source_stats["average_time"][source] = new_avg
        
        # ティッカーごとの最適ソースを記録
        if success:
            self.source_stats["tickers_by_source"][ticker] = source
        
        # 統計情報を保存
        self.source_stats["last_update"] = datetime.now().isoformat()
        self._save_json_file(self.source_stats_file, self.source_stats)
        
        # ソースウェイトの調整
        self._adjust_source_weights()
    
    def _adjust_source_weights(self):
        """データソースの優先度を成功率に応じて動的に調整"""
        for source in self.source_weights.keys():
            success = self.source_stats["success_rate"][source]["success"]
            failure = self.source_stats["success_rate"][source]["failure"]
            
            if success + failure > 10:  # 十分なデータがある場合のみ調整
                success_rate = success / (success + failure)
                # 成功率が高いほど優先度を高く（ウェイト値を低く）
                adjusted_weight = 5.0 - 4.0 * success_rate
                self.source_weights[source] = max(0.5, min(5.0, adjusted_weight))
    
    def _get_prioritized_sources(self, ticker):
        """ティッカーに最適なデータソースの優先順位を取得"""
        # この銘柄で以前成功したソース
        previous_source = self.source_stats["tickers_by_source"].get(ticker)
        
        # 利用可能なソースをウェイトでソート
        sources = [(s, w) for s, w in self.source_weights.items() 
                   if self.source_manager.is_available(s)]
        sources.sort(key=lambda x: x[1])  # ウェイトの昇順（優先度の降順）
        
        prioritized = [s[0] for s in sources]
        
        # 以前成功したソースを最優先
        if previous_source and previous_source in prioritized:
            prioritized.remove(previous_source)
            prioritized.insert(0, previous_source)
        
        return prioritized
    
    def _get_random_user_agent(self):
        """ランダムなユーザーエージェントを取得"""
        return random.choice(self.user_agents)
    
    def _fetch_data_with_yfinance(self, ticker, start_date, end_date):
        """yfinanceでデータ取得（改良版）"""
        if not self.source_manager.is_available("yfinance"):
            return None, "yfinance not available"
        
        yf = self.source_manager.get_source("yfinance")
        
        try:
            start_time = time.time()
            self.logger.debug(f"yfinanceでの取得を試行: {ticker}, {start_date} - {end_date}")
            
            # プロキシやタイムアウトの設定
            proxies = None
            timeout = 20
            
            # Tickerオブジェクト取得
            stock = yf.Ticker(ticker)
            
            # データ取得
            df = stock.history(
                start=start_date.strftime('%Y-%m-%d'),
                end=end_date.strftime('%Y-%m-%d'),
                interval="1d",
                proxy=proxies,
                timeout=timeout
            )
            
            if df.empty:
                elapsed_time = time.time() - start_time
                self._update_source_stats(ticker, "yfinance", False, elapsed_time)
                return None, "Empty dataframe returned"
            
            # インデックスリセットとカラム名標準化
            df = df.reset_index()
            
            elapsed_time = time.time() - start_time
            self._update_source_stats(ticker, "yfinance", True, elapsed_time)
            
            return df, None
            
        except Exception as e:
            elapsed_time = time.time() - start_time
            self._update_source_stats(ticker, "yfinance", False, elapsed_time)
            
            error_msg = str(e)
            self.logger.error(f"yfinanceでの取得エラー: {error_msg}")
            if self.debug:
                self.logger.debug(traceback.format_exc())
            
            return None, error_msg
    
    def _fetch_data_with_pandas_datareader(self, ticker, start_date, end_date):
        """pandas-datareaderでデータ取得（改良版）"""
        if not self.source_manager.is_available("pandas_datareader"):
            return None, "pandas_datareader not available"
        
        pdr = self.source_manager.get_source("pandas_datareader")
        
        try:
            start_time = time.time()
            self.logger.debug(f"pandas-datareaderでの取得を試行: {ticker}, {start_date} - {end_date}")
            
            # 直接パラメータを構築してYahoo Financeからデータを取得
            try:
                df = pdr.data.get_data_yahoo(
                    ticker,
                    start=start_date,
                    end=end_date
                )
            except AttributeError as e:
                if "'NoneType' object has no attribute 'group'" in str(e):
                    # 通常のエラーハンドリングが失敗した場合のフォールバック
                    self.logger.warning("pandas-datareaderで正規表現エラーが発生しました。代替方法を試行します。")
                    return self._fetch_data_with_direct_request(ticker, start_date, end_date)
                else:
                    raise
            
            if df.empty:
                elapsed_time = time.time() - start_time
                self._update_source_stats(ticker, "pandas_datareader", False, elapsed_time)
                return None, "Empty dataframe returned"
            
            # インデックスリセット
            df = df.reset_index()
            
            elapsed_time = time.time() - start_time
            self._update_source_stats(ticker, "pandas_datareader", True, elapsed_time)
            
            return df, None
            
        except Exception as e:
            elapsed_time = time.time() - start_time
            self._update_source_stats(ticker, "pandas_datareader", false, elapsed_time)
            
            error_msg = str(e)
            self.logger.error(f"pandas-datareaderでの取得エラー: {error_msg}")
            if self.debug:
                self.logger.debug(traceback.format_exc())
            
            return None, error_msg
    
    def _fetch_data_with_stooq_scraper(self, ticker, start_date, end_date):
        """Stooq Webスクレイパーでデータ取得（改良版）"""
        if not self.source_manager.is_available("stooq_scraper"):
            return None, "stooq_scraper not available"
        
        scraper = self.source_manager.get_source("stooq_scraper")
        
        try:
            start_time = time.time()
            self.logger.debug(f"stooq_scraperでの取得を試行: {ticker}, {start_date} - {end_date}")
            
            # Stooq用にティッカーを変換（.T -> .JP）
            stooq_ticker = ticker.replace(".T", ".JP")
            
            # 日付形式の変換
            start_date_str = start_date.strftime('%Y%m%d')
            end_date_str = end_date.strftime('%Y%m%d')
            
            # StooqScraper クラスのインスタンスを作成
            if hasattr(scraper, "StooqScraper"):
                scraper_instance = scraper.StooqScraper()
                
                # データ取得 (get_stock_data メソッドを使用)
                df = scraper_instance.get_stock_data(
                    stooq_ticker, 
                    start_date, 
                    end_date
                )
            # または直接関数を呼び出し
            elif hasattr(scraper, "get_stock_data"):
                df = scraper.get_stock_data(
                    stooq_ticker, 
                    start_date, 
                    end_date
                )
            else:
                # 直接Stooqからデータを取得する最後の手段
                try:
                    df = self._fetch_stooq_data_directly(stooq_ticker, start_date_str, end_date_str)
                except Exception as direct_error:
                    self.logger.error(f"直接Stooqからのデータ取得エラー: {str(direct_error)}")
                    return None, f"stooq_scraper module error and direct fetch error: {str(direct_error)}"
            
            if df is None or df.empty:
                elapsed_time = time.time() - start_time
                self._update_source_stats(ticker, "stooq_scraper", False, elapsed_time)
                return None, "Empty dataframe returned"
            
            elapsed_time = time.time() - start_time
            self._update_source_stats(ticker, "stooq_scraper", True, elapsed_time)
            
            return df, None
            
        except Exception as e:
            elapsed_time = time.time() - start_time
            self._update_source_stats(ticker, "stooq_scraper", False, elapsed_time)
            
            error_msg = str(e)
            self.logger.error(f"stooq_scraperでの取得エラー: {error_msg}")
            if self.debug:
                self.logger.debug(traceback.format_exc())
            
            return None, error_msg
    
    def _fetch_stooq_data_directly(self, ticker, start_date_str, end_date_str):
        """直接Stooqからデータを取得する"""
        url = f"https://stooq.com/q/d/l/?s={ticker}&d1={start_date_str}&d2={end_date_str}&i=d"
        
        headers = {
            "User-Agent": self._get_random_user_agent(),
            "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8",
            "Accept-Language": "en-US,en;q=0.5",
            "Referer": "https://stooq.com/",
            "Connection": "keep-alive"
        }
        
        # セッションを使用
        with requests.Session() as session:
            response = session.get(url, headers=headers, timeout=30)
            
            if response.status_code != 200:
                raise Exception(f"HTTP error {response.status_code}")
            
            # CSVデータを解析
            try:
                df = pd.read_csv(pd.StringIO(response.text))
                
                # 日付形式の確認と変換
                if 'Date' in df.columns:
                    df['Date'] = pd.to_datetime(df['Date'])
                
                return df
            except Exception as e:
                self.logger.error(f"Stooqデータ解析エラー: {str(e)}")
                
                # HTMLからテーブルをパース（CSVダウンロードが失敗した場合）
                try:
                    tables = pd.read_html(response.text)
                    if tables and len(tables) > 0:
                        df = tables[0]
                        
                        # カラム名のマッピング
                        mapping = {
                            'Date': 'Date',
                            'Open': 'Open',
                            'High': 'High',
                            'Low': 'Low',
                            'Close': 'Close',
                            'Volume': 'Volume'
                        }
                        
                        # カラム名を標準化
                        df = df.rename(columns={k: v for k, v in mapping.items() if k in df.columns})
                        
                        # 日付を変換
                        if 'Date' in df.columns:
                            df['Date'] = pd.to_datetime(df['Date'])
                        
                        return df
                except Exception as html_error:
                    self.logger.error(f"Stooq HTMLテーブル解析エラー: {str(html_error)}")
                    raise
    
    def _fetch_data_with_direct_request(self, ticker, start_date, end_date):
        """直接HTTPリクエストでデータ取得（最適化版）"""
        try:
            start_time = time.time()
            self.logger.debug(f"直接リクエストでの取得を試行: {ticker}, {start_date} - {end_date}")
            
            # Yahoo Finance APIパラメータを計算
            period1 = int(start_date.timestamp())
            period2 = int(end_date.timestamp())
            interval = '1d'
            
            # ヘッダー設定
            headers = {
                "User-Agent": self._get_random_user_agent(),
                "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8",
                "Accept-Language": "en-US,en;q=0.5",
                "Connection": "keep-alive"
            }
            
            # query1エンドポイント（CSVダウンロード）
            url = f"https://query1.finance.yahoo.com/v7/finance/download/{ticker}?period1={period1}&period2={period2}&interval={interval}&events=history"
            
            # リクエスト送信
            response = requests.get(url, headers=headers, timeout=20)
            
            if response.status_code != 200:
                self.logger.warning(f"直接リクエスト(query1)でHTTPエラー: {response.status_code}")
                # query2エンドポイントを試行
                return self._try_query2_endpoint(ticker, start_date, end_date, headers)
            
            # CSVとしてパース
            try:
                df = pd.read_csv(pd.StringIO(response.text))
            except Exception as e:
                self.logger.warning(f"CSVパースエラー: {str(e)}")
                return self._try_query2_endpoint(ticker, start_date, end_date, headers)
            
            if df.empty:
                self.logger.warning("直接リクエスト(query1)で空のデータフレームが返されました")
                # query2エンドポイントを試行
                return self._try_query2_endpoint(ticker, start_date, end_date, headers)
            
            # 日付を変換
            df['Date'] = pd.to_datetime(df['Date'])
            
            elapsed_time = time.time() - start_time
            self._update_source_stats(ticker, "direct_request", True, elapsed_time)
            
            return df, None
            
        except Exception as e:
            self.logger.error(f"直接リクエスト(query1)での取得エラー: {str(e)}")
            
            try:
                # エラー発生時はquery2エンドポイントを試行
                return self._try_query2_endpoint(ticker, start_date, end_date, headers=None)
            except Exception as e2:
                elapsed_time = time.time() - start_time
                self._update_source_stats(ticker, "direct_request", False, elapsed_time)
                return None, f"両方のクエリエンドポイントが失敗: {str(e)}, {str(e2)}"
    
    def _try_query2_endpoint(self, ticker, start_date, end_date, headers=None):
        """query2エンドポイントからJSONデータを取得"""
        start_time = time.time()
        self.logger.debug(f"query2エンドポイントでの取得を試行: {ticker}")
        
        if headers is None:
            # ヘッダーが提供されていない場合は新しく生成
            headers = {
                "User-Agent": self._get_random_user_agent(),
                "Accept": "application/json, text/plain, */*",
                "Accept-Language": "en-US,en;q=0.5",
                "Origin": "https://finance.yahoo.com",
                "Referer": f"https://finance.yahoo.com/quote/{ticker}/history"
            }
        
        try:
            # query2エンドポイントではJSONデータを取得
            period1 = int(start_date.timestamp())
            period2 = int(end_date.timestamp())
            interval = '1d'
            
            url = f"https://query2.finance.yahoo.com/v8/finance/chart/{ticker}?period1={period1}&period2={period2}&interval={interval}"
            
            response = requests.get(url, headers=headers, timeout=20)
            
            if response.status_code != 200:
                elapsed_time = time.time() - start_time
                self._update_source_stats(ticker, "direct_request", False, elapsed_time)
                return None, f"query2 HTTP error: {response.status_code}"
            
            # JSONデータをパース
            data = response.json()
            
            # JSONからデータフレームを作成
            try:
                chart_data = data.get('chart', {}).get('result', [{}])[0]
                
                # タイムスタンプ
                timestamps = chart_data.get('timestamp', [])
                
                # 価格データ
                quote = chart_data.get('indicators', {}).get('quote', [{}])[0]
                opens = quote.get('open', [])
                highs = quote.get('high', [])
                lows = quote.get('low', [])
                closes = quote.get('close', [])
                volumes = quote.get('volume', [])
                
                # 調整後終値
                adjclose_data = chart_data.get('indicators', {}).get('adjclose', [{}])
                adjcloses = adjclose_data[0].get('adjclose', []) if adjclose_data else []
                
                # データフレーム作成
                data_dict = {
                    'Date': [datetime.fromtimestamp(ts) for ts in timestamps if ts],
                    'Open': opens,
                    'High': highs,
                    'Low': lows,
                    'Close': closes,
                    'Volume': volumes
                }
