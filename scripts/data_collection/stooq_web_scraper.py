#!/usr/bin/env python3
"""
Stooqのウェブサイトから株価データを直接スクレイピングするモジュール

pandas_datareaderのget_data_stooqが動作しない場合の代替手段として使用します。
"""

import requests
from bs4 import BeautifulSoup
import pandas as pd
import time
import random
import logging
import os
from datetime import datetime, timedelta
import re

# ロギング設定
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class StooqScraper:
    """StooqウェブサイトからのデータスクレイピングクラS"""
    
    def __init__(self, cache_dir="data/scraper_cache"):
        """
        初期化
        
        Parameters:
        -----------
        cache_dir : str
            キャッシュディレクトリのパス
        """
        self.cache_dir = cache_dir
        self.base_url = "https://stooq.com/q/d/"
        
        # ヘッダー設定
        self.headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
            "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8",
            "Accept-Language": "en-US,en;q=0.5",
            "Connection": "keep-alive",
            "Upgrade-Insecure-Requests": "1",
            "Pragma": "no-cache",
            "Cache-Control": "no-cache",
        }
        
        # キャッシュディレクトリの作成
        os.makedirs(cache_dir, exist_ok=True)
    
    def _get_cache_filename(self, ticker, start_date, end_date):
        """キャッシュファイル名を生成"""
        start_str = start_date.strftime('%Y%m%d')
        end_str = end_date.strftime('%Y%m%d')
        return os.path.join(self.cache_dir, f"{ticker}_{start_str}_{end_str}.csv")
    
    def _is_cached(self, ticker, start_date, end_date):
        """キャッシュが存在するかチェック"""
        cache_file = self._get_cache_filename(ticker, start_date, end_date)
        return os.path.exists(cache_file)
    
    def _load_from_cache(self, ticker, start_date, end_date):
        """キャッシュからデータを読み込み"""
        cache_file = self._get_cache_filename(ticker, start_date, end_date)
        try:
            df = pd.read_csv(cache_file, parse_dates=['Date'])
            logger.info(f"{ticker}のデータをキャッシュから読み込みました")
            return df
        except Exception as e:
            logger.error(f"キャッシュ読み込みエラー: {str(e)}")
            return None
    
    def _save_to_cache(self, ticker, start_date, end_date, df):
        """データをキャッシュに保存"""
        if df is None or df.empty:
            logger.warning(f"{ticker}のデータが空のため、キャッシュに保存しません")
            return
        
        cache_file = self._get_cache_filename(ticker, start_date, end_date)
        try:
            df.to_csv(cache_file, index=False)
            logger.info(f"{ticker}のデータをキャッシュに保存しました: {cache_file}")
        except Exception as e:
            logger.error(f"キャッシュ保存エラー: {str(e)}")
    
    def get_stock_data(self, ticker, start_date, end_date, retry=3, sleep_time=2):
        """
        指定された銘柄の株価データを取得
        
        Parameters:
        -----------
        ticker : str
            ティッカーシンボル（例: "7203.T"）
        start_date : datetime
            開始日
        end_date : datetime
            終了日
        retry : int
            リトライ回数
        sleep_time : float
            リクエスト間の待機時間
        
        Returns:
        --------
        pd.DataFrame
            株価データ（Date, Open, High, Low, Close, Volume）
        """
        # キャッシュがあれば読み込み
        if self._is_cached(ticker, start_date, end_date):
            return self._load_from_cache(ticker, start_date, end_date)
        
        # ティッカー形式を変換（必要な場合）
        stooq_ticker = ticker.replace('.T', '.JP')
        
        # パラメータの準備
        params = {
            's': stooq_ticker,
            'i': 'd',  # 日次データ
            'd1': start_date.strftime('%Y%m%d'),
            'd2': end_date.strftime('%Y%m%d'),
            'l': '0',  # 英語表示
        }
        
        logger.info(f"{ticker}のデータをStooqからスクレイピングします（期間: {start_date.strftime('%Y-%m-%d')}～{end_date.strftime('%Y-%m-%d')}）")
        
        # リトライループ
        for attempt in range(retry):
            try:
                response = requests.get(
                    self.base_url,
                    params=params,
                    headers=self.headers,
                    timeout=30
                )
                
                if response.status_code != 200:
                    logger.warning(f"HTTPエラー: {response.status_code} - {response.reason}")
                    time.sleep(sleep_time + random.uniform(1, 3))
                    continue
                
                # HTMLの解析
                soup = BeautifulSoup(response.text, 'html.parser')
                
                # テーブルを探す
                table = soup.find('table', {'id': 'fth1'})
                if not table:
                    logger.warning(f"データテーブルが見つかりません。ページ構造が変更された可能性があります。")
                    if attempt < retry - 1:
                        sleep_time_with_jitter = sleep_time + random.uniform(1, 5) * (attempt + 1)
                        logger.info(f"{sleep_time_with_jitter:.1f}秒待機してリトライします（{attempt+1}/{retry}）")
                        time.sleep(sleep_time_with_jitter)
                    continue
                
                # テーブルデータの抽出
                rows = table.find_all('tr')
                if len(rows) <= 1:
                    logger.warning(f"データが見つかりません。期間内にデータがない可能性があります。")
                    return pd.DataFrame()
                
                # 列名の取得
                headers = []
                header_row = rows[0]
                for th in header_row.find_all('th'):
                    headers.append(th.text.strip())
                
                # データの抽出
                data = []
                for row in rows[1:]:
                    cells = row.find_all('td')
                    if len(cells) >= len(headers):
                        row_data = {}
                        for i, cell in enumerate(cells):
                            if i < len(headers):
                                row_data[headers[i]] = cell.text.strip()
                        data.append(row_data)
                
                # DataFrameの作成
                df = pd.DataFrame(data)
                
                # 列名の標準化
                column_mapping = {
                    'Date': 'Date',
                    'Open': 'Open',
                    'High': 'High',
                    'Low': 'Low',
                    'Close': 'Close',
                    'Volume': 'Volume',
                }
                
                # 列名を標準化（異なる言語や表記に対応）
                for orig_col in df.columns:
                    # 日付列の検出（例: Date, 日付, など）
                    if re.search(r'date|日付|日期', orig_col.lower()):
                        column_mapping['Date'] = orig_col
                    # 始値の検出
                    elif re.search(r'open|始値|開盤', orig_col.lower()):
                        column_mapping['Open'] = orig_col
                    # 高値の検出
                    elif re.search(r'high|高値|最高', orig_col.lower()):
                        column_mapping['High'] = orig_col
                    # 安値の検出
                    elif re.search(r'low|安値|最低', orig_col.lower()):
                        column_mapping['Low'] = orig_col
                    # 終値の検出
                    elif re.search(r'close|終値|收盤', orig_col.lower()):
                        column_mapping['Close'] = orig_col
                    # 出来高の検出
                    elif re.search(r'vol|出来高|成交量', orig_col.lower()):
                        column_mapping['Volume'] = orig_col
                
                # 列名を変更
                df_renamed = pd.DataFrame()
                for std_col, orig_col in column_mapping.items():
                    if orig_col in df.columns:
                        df_renamed[std_col] = df[orig_col]
                
                # 必須列の確認
                for col in ['Date', 'Open', 'High', 'Low', 'Close']:
                    if col not in df_renamed.columns:
                        logger.warning(f"必須列 '{col}' が見つかりません。列: {df.columns.tolist()}")
                        return pd.DataFrame()
                
                # データ型の変換
                df_renamed['Date'] = pd.to_datetime(df_renamed['Date'])
                
                for col in ['Open', 'High', 'Low', 'Close']:
                    df_renamed[col] = df_renamed[col].str.replace(',', '').astype(float)
                
                if 'Volume' in df_renamed.columns:
                    df_renamed['Volume'] = df_renamed['Volume'].str.replace(',', '').astype(float)
                else:
                    df_renamed['Volume'] = 0.0
                
                # ティッカー列の追加
                df_renamed['Ticker'] = ticker
                
                # 日付でソート
                df_renamed = df_renamed.sort_values('Date')
                
                # キャッシュに保存
                self._save_to_cache(ticker, start_date, end_date, df_renamed)
                
                logger.info(f"{ticker}のデータ取得が完了しました: {len(df_renamed)}行")
                
                # データのサンプルをログに出力
                if not df_renamed.empty:
                    logger.info(f"データサンプル: \n{df_renamed.head(1).to_string(index=False)}\n...\n{df_renamed.tail(1).to_string(index=False)}")
                
                return df_renamed
                
            except Exception as e:
                logger.error(f"{ticker}のデータ取得中にエラーが発生: {str(e)}")
                if attempt < retry - 1:
                    sleep_time_with_jitter = sleep_time + random.uniform(1, 5) * (attempt + 1)
                    logger.info(f"{sleep_time_with_jitter:.1f}秒待機してリトライします（{attempt+1}/{retry}）")
                    time.sleep(sleep_time_with_jitter)
        
        logger.error(f"{ticker}のデータ取得に{retry}回失敗しました")
        return pd.DataFrame()
    
    def get_annual_data(self, ticker, year, retry=3, sleep_time=2):
        """
        指定された年の株価データを取得
        
        Parameters:
        -----------
        ticker : str
            ティッカーシンボル（例: "7203.T"）
        year : int
            年（例: 2022）
        retry : int
            リトライ回数
        sleep_time : float
            リクエスト間の待機時間
        
        Returns:
        --------
        pd.DataFrame
            株価データ
        """
        start_date = datetime(year, 1, 1)
        
        # 現在の年の場合は終了日を今日にする
        if year == datetime.now().year:
            end_date = datetime.now()
        else:
            end_date = datetime(year, 12, 31)
        
        return self.get_stock_data(ticker, start_date, end_date, retry, sleep_time)
    
    def get_multi_year_data(self, ticker, start_year, end_year=None, retry=3, sleep_between_years=5):
        """
        複数年のデータを取得して結合
        
        Parameters:
        -----------
        ticker : str
            ティッカーシンボル（例: "7203.T"）
        start_year : int
            開始年
        end_year : int or None
            終了年（Noneの場合は現在の年）
        retry : int
            リトライ回数
        sleep_between_years : float
            年ごとのリクエスト間の待機時間
        
        Returns:
        --------
        pd.DataFrame
            結合された株価データ
        """
        if end_year is None:
            end_year = datetime.now().year
        
        all_data = []
        
        for year in range(start_year, end_year + 1):
            year_data = self.get_annual_data(ticker, year, retry)
            
            if not year_data.empty:
                all_data.append(year_data)
            
            # 最後の年でなければ待機
            if year < end_year:
                sleep_time = sleep_between_years + random.uniform(0, 2)
                logger.info(f"次の年のデータ取得まで{sleep_time:.1f}秒待機します")
                time.sleep(sleep_time)
        
        # データを結合
        if all_data:
            combined_data = pd.concat(all_data, ignore_index=True)
            # 重複を削除
            combined_data = combined_data.drop_duplicates(subset=['Date']).sort_values('Date')
            return combined_data
        else:
            return pd.DataFrame()

def main():
    """
    メイン関数
    """
    import argparse
    
    parser = argparse.ArgumentParser(description='Stooqウェブスクレイパー')
    parser.add_argument('--ticker', type=str, required=True, help='ティッカーシンボル（例: 7203.T）')
    parser.add_argument('--start-year', type=int, default=datetime.now().year-1, help='開始年')
    parser.add_argument('--end-year', type=int, default=None, help='終了年（指定しない場合は現在の年）')
    parser.add_argument('--output', type=str, default=None, help='出力ファイル名（CSVファイル）')
    parser.add_argument('--retry', type=int, default=3, help='リトライ回数')
    parser.add_argument('--sleep', type=int, default=5, help='リクエスト間の待機時間（秒）')
    
    args = parser.parse_args()
    
    # スクレイパーを初期化
    scraper = StooqScraper(cache_dir="data/scraper_cache")
    
    # データを取得
    df = scraper.get_multi_year_data(
        args.ticker,
        args.start_year,
        args.end_year,
        retry=args.retry,
        sleep_between_years=args.sleep
    )
    
    if df.empty:
        logger.error(f"データが取得できませんでした: {args.ticker}")
        return
    
    # 結果を表示
    print(f"\n{args.ticker}のデータ（{len(df)}行）\n")
    print(df.head().to_string(index=False))
    print("...\n")
    print(df.tail().to_string(index=False))
    
    # 出力ファイルに保存（指定されている場合）
    if args.output:
        df.to_csv(args.output, index=False)
        print(f"\nデータを保存しました: {args.output}")
    
    # 基本統計量
    print("\n基本統計量:")
    print(f"データ期間: {df['Date'].min()} ～ {df['Date'].max()}")
    print(f"営業日数: {len(df)}")
    
    # リターン計算
    df['Daily_Return'] = df['Close'].pct_change() * 100
    
    print(f"平均日次リターン: {df['Daily_Return'].mean():.2f}%")
    print(f"最大日次上昇: {df['Daily_Return'].max():.2f}%")
    print(f"最大日次下落: {df['Daily_Return'].min():.2f}%")
    print(f"日次リターン標準偏差: {df['Daily_Return'].std():.2f}%")
    
    total_return = (df['Close'].iloc[-1] / df['Close'].iloc[0] - 1) * 100
    print(f"期間トータルリターン: {total_return:.2f}%")

if __name__ == "__main__":
    main()
