import yfinance as yf
import pandas as pd
import time
import os

# ✅ 日経225のティッカーリスト（必要に応じて追加）
tickers = [
'1332.T', '1333.T', '1376.T', '1605.T', '1721.T', '1801.T', '1802.T', '1803.T', '1808.T', '1812.T',
    '1925.T', '1928.T', '1963.T', '2002.T', '2269.T', '2501.T', '2502.T', '2503.T', '2531.T', '2768.T',
    '2801.T', '2802.T', '2871.T', '2914.T', '3003.T', '3086.T', '3101.T', '3103.T', '3289.T', '3382.T',
    '3401.T', '3402.T', '3405.T', '3407.T', '3436.T', '3861.T', '3863.T', '4004.T', '4005.T', '4021.T',
    '4042.T', '4043.T', '4061.T', '4063.T', '4151.T', '4183.T', '4188.T', '4208.T', '4324.T', '4452.T',
    '4502.T', '4503.T', '4506.T', '4507.T', '4519.T', '4523.T', '4568.T', '4578.T', '4689.T', '4704.T',
    '4751.T', '4901.T', '4911.T', '5020.T', '5101.T', '5108.T', '5201.T', '5202.T', '5214.T', '5232.T',
    '5233.T', '5301.T', '5332.T', '5401.T', '5406.T', '5411.T', '5541.T', '5631.T', '5703.T', '5706.T',
    '5707.T', '5711.T', '5713.T', '5801.T', '5802.T', '5803.T', '5901.T', '5902.T', '6005.T', '6028.T',
    '6103.T', '6113.T', '6135.T', '6141.T', '6146.T', '6201.T', '6203.T', '6208.T', '6268.T', '6273.T',
    '6301.T', '6302.T', '6305.T', '6326.T', '6361.T', '6366.T', '6367.T', '6370.T', '6378.T', '6383.T',
    '6407.T', '6417.T', '6418.T', '6448.T', '6455.T', '6460.T', '6465.T', '6471.T', '6472.T', '6473.T',
    '6474.T', '6479.T', '6481.T', '6501.T', '6503.T', '6504.T', '6506.T', '6508.T', '6586.T', '6588.T',
    '6594.T', '6619.T', '6645.T', '6674.T', '6701.T', '6702.T', '6723.T', '6724.T', '6727.T', '6752.T',
    '6753.T', '6758.T', '6762.T', '6770.T', '6841.T', '6857.T', '6902.T', '6952.T', '6954.T', '6963.T',
    '6981.T', '6988.T', '7003.T', '7011.T', '7012.T', '7201.T', '7202.T', '7203.T', '7231.T', '7238.T'
]

# ✅ データ保存フォルダ
DATA_DIR = "nikkei225_data"
os.makedirs(DATA_DIR, exist_ok=True)

# ✅ すでに取得済みのティッカーをスキップ
def get_fetched_tickers():
    """取得済みのティッカーをリスト化"""
    existing_files = os.listdir(DATA_DIR)
    return [f.replace(".csv", "") for f in existing_files]

# ✅ 失敗したティッカーを記録
failed_tickers = []

# ✅ データ取得関数（レートリミット対策 & バックオフリトライ）
def fetch_stock_data(ticker_list, period="1mo", interval="1h", batch_size=5, max_retries=5):
    fetched_tickers = get_fetched_tickers()
    tickers_to_fetch = [t for t in ticker_list if t not in fetched_tickers]

    if not tickers_to_fetch:
        print("✅ すべてのデータがすでに取得済みです")
        return

    retries = 0
    while retries < max_retries:
        for i in range(0, len(tickers_to_fetch), batch_size):
            batch = tickers_to_fetch[i:i + batch_size]
            try:
                print(f"📡 Fetching {len(batch)} tickers: {batch}...")
                df = yf.download(batch, period=period, interval=interval, group_by='ticker', progress=False)

                if df.empty:
                    print("⚠️ Warning: No data fetched")
                    continue

                # ✅ 各ティッカーごとにデータを保存
                for ticker in batch:
                    if ticker in df.columns.levels[0]:  # データ取得できたか確認
                        df_ticker = df[ticker].dropna()
                        df_ticker.to_csv(os.path.join(DATA_DIR, f"{ticker}.csv"))
                        print(f"✅ Saved {ticker}.csv")
                    else:
                        failed_tickers.append(ticker)
                        print(f"❌ Failed to fetch {ticker}")

                # ✅ レートリミット対策：1リクエストごとに 3 秒スリープ
                time.sleep(3)

            except yf.YFRateLimitError:
                retries += 1
                wait_time = 10 * (2 ** retries)  # 10秒 → 20秒 → 40秒
                print(f"⚠️ Rate limit hit, retrying in {wait_time} seconds...")
                time.sleep(wait_time)
            except Exception as e:
                print(f"❌ Error fetching data: {e}")
                return

    print("🚨 Failed to fetch some tickers after multiple retries.")

# ✅ データ取得を実行（バッチサイズを小さくしてリクエスト負荷軽減）
fetch_stock_data(tickers, batch_size=3)

# ✅ 失敗したティッカーの再取得処理
if failed_tickers:
    print("🔄 Retrying failed tickers...")
    fetch_stock_data(failed_tickers, batch_size=2)

print("✅ データ取得完了！")
