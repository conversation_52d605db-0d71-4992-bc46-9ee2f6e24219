#!/usr/bin/env python3
"""
Stooqのウェブサイトから株価データを直接スクレイピングするモジュール

pandas_datareaderのget_data_stooqが動作しない場合の代替手段として使用します。
"""

import requests
from bs4 import BeautifulSoup
import pandas as pd
import time
import random
import logging
import os
from datetime import datetime, timedelta
import re

# ロギング設定
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class StooqScraper:
    """Stooqウェブサイトからのデータスクレイピングクラス"""

    def __init__(self, cache_dir="data/scraper_cache"):
        """
        初期化

        Parameters:
        -----------
        cache_dir : str
            キャッシュディレクトリのパス
        """
        self.cache_dir = cache_dir
        self.base_url = "https://stooq.com/q/d/"

        # ヘッダー設定
        self.headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
            "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8",
            "Accept-Language": "en-US,en;q=0.5",
            "Connection": "keep-alive",
            "Upgrade-Insecure-Requests": "1",
            "Pragma": "no-cache",
            "Cache-Control": "no-cache"
        }

        # キャッシュディレクトリの作成
        os.makedirs(cache_dir, exist_ok=True)

    def _get_cache_filename(self, ticker, start_date, end_date):
        """キャッシュファイル名を生成"""
        start_str = start_date.strftime('%Y%m%d')
        end_str = end_date.strftime('%Y%m%d')
        return os.path.join(self.cache_dir, f"{ticker}_{start_str}_{end_str}.csv")

    def _is_cached(self, ticker, start_date, end_date):
        """キャッシュが存在するかチェック"""
        cache_file = self._get_cache_filename(ticker, start_date, end_date)
        return os.path.exists(cache_file)

    def _load_from_cache(self, ticker, start_date, end_date):
        """キャッシュからデータを読み込み"""
        cache_file = self._get_cache_filename(ticker, start_date, end_date)
        try:
            df = pd.read_csv(cache_file, parse_dates=['Date'])
            logger.info(f"{ticker}のデータをキャッシュから読み込みました")
            return df
        except Exception as e:
            logger.error(f"キャッシュ読み込みエラー: {str(e)}")
            return None

    def _save_to_cache(self, ticker, start_date, end_date, df):
        """データをキャッシュに保存"""
        if df is None or df.empty:
            logger.warning(f"{ticker}のデータが空のため、キャッシュに保存しません")
            return

        cache_file = self._get_cache_filename(ticker, start_date, end_date)
        try:
            df.to_csv(cache_file, index=False)
            logger.info(f"{ticker}のデータをキャッシュに保存しました: {cache_file}")
        except Exception as e:
            logger.error(f"キャッシュ保存エラー: {str(e)}")

    def get_stock_data(self, ticker, start_date, end_date, retry=3, sleep_time=2):
        """
        指定された銘柄の株価データを取得

        Parameters:
        -----------
        ticker : str
            ティッカーシンボル（例: "7203.T"）
        start_date : datetime
            開始日
        end_date : datetime
            終了日
        retry : int
            リトライ回数
        sleep_time : float
            リクエスト間の待機時間

        Returns:
        --------
        pd.DataFrame
            株価データ（Date, Open, High, Low, Close, Volume）
        """
        # キャッシュがあれば読み込み
        if self._is_cached(ticker, start_date, end_date):
            return self._load_from_cache(ticker, start_date, end_date)

        # ティッカー形式を変換（必要な場合）
        stooq_ticker = ticker.replace('.T', '.JP')

        # パラメータの準備
        params = {
            's': stooq_ticker,
            'i': 'd',  # 日次データ
            'd1': start_date.strftime('%Y%m%d'),
            'd2': end_date.strftime('%Y%m%d'),
            'l': '0'  # 英語表示
        }

        logger.info(f"{ticker}のデータをStooqからスクレイピングします（期間: {start_date.strftime('%Y-%m-%d')}～{end_date.strftime('%Y-%m-%d')}）")

        # リトライループ
        for attempt in range(retry):
            try:
                response = requests.get(
                    self.base_url,
                    params=params,
                    headers=self.headers,
                    timeout=30
                )

                if response.status_code != 200:
                    logger.warning(f"HTTPエラー: {response.status_code} - {response.reason}")
                    time.sleep(sleep_time + random.uniform(1, 3))
                    continue

                # HTMLの解析
                soup = BeautifulSoup(response.text, 'html.parser')

                # テーブルを探す
                table = soup.find('table', {'id': 'fth1'})
                if not table:
                    logger.warning(f"データテーブルが見つかりません。ページ構造が変更された可能性があります。")
                    if attempt < retry - 1:
                        sleep_time_with_jitter = sleep_time + random.uniform(1, 5) * (attempt + 1)
                        logger.info(f"{sleep_time_with_jitter:.1f}秒待機してリトライします（{attempt+1}/{retry}）")
                        time.sleep(sleep_time_with_jitter)
                    continue

                # テーブルデータの抽出
                rows = table.find_all('tr')
                if len(rows) <= 1:
                    logger.warning(f"データが見つかりません。期間内にデータがない可能性があります。")
                    return pd.DataFrame()

                # 列名の取得
                headers = []
                header_row = rows[0]
                for th in header_row.find_all('th'):
                    headers.append(th.text.strip())

                # データの抽出
                data = []
                for row in rows[1:]:
                    cells = row.find_all('td')
                    if len(cells) >= len(headers):
                        row_data = {}
                        for i, cell in enumerate(cells):
                            if i < len(headers):
                                row_data[headers[i]] = cell.text.strip()
                        data.append(row_data)

                # DataFrameの作成
                df = pd.DataFrame(data)

                # 列名の標準化
                column_mapping = {
                    'Date': 'Date',
                    'Open': 'Open',
                    'High': 'High',
                    'Low': 'Low',
                    'Close': 'Close',
                    'Volume': 'Volume'
                }

                # 列名を標準化（異なる言語や表記に対応）
                for orig_col in df.columns:
                    # 日付列の検出（例: Date, 日付 など）
                    if re.search(r'date|日付|日期', orig_col.lower()):
                        column_mapping['Date'] = orig_col
                    # 始値の検出
                    elif re.search(r'open|始値|開盤', orig_col.lower()):
                        column_mapping['Open'] = orig_col
                    # 高値の検出
                    elif re.search(r'high|高値|最高', orig_col.lower()):
                        column_mapping['High'] = orig_col
                    # 安値の検出
                    elif re.search(r'low|安値|最低', orig_col.lower()):
                        column_mapping['Low'] = orig_col
                    # 終値の検出
                    elif re.search(r'close|終値|收盤', orig_col.lower()):
                        column_mapping['Close'] = orig_col
                    # 出来高の検出
                    elif re.search(r'vol|出来高|成交量', orig_col.lower()):
                        column_mapping['Volume'] = orig_col

                # 列名を変更
                df_renamed = pd.DataFrame()
                for std_col, orig_col in column_mapping.items():
                    if orig_col in df.columns:
                        df_renamed[std_col] = df[orig_col]

                # 必須列の確認
                for col in ['Date', 'Open', 'High', 'Low', 'Close']:
                    if col not in df_renamed.columns:
                        logger.warning(f"必須列 '{col}' が見つかりません。列: {df.columns.tolist()}")
                        return pd.DataFrame()

                # データ型の変換
                df_renamed['Date'] = pd.to_datetime(df_renamed['Date'])

                for col in ['Open', 'High', 'Low', 'Close']:
                    df_renamed[col] = df_renamed[col].str.replace(',', '').astype(float)

                if 'Volume' in df_renamed.columns:
                    df_renamed['Volume'] = df_renamed['Volume'].str.replace(',', '').astype(float)
                else:
                    df_renamed['Volume'] = 0.0

                # ティッカー列の追加
                df_renamed['Ticker'] = ticker

                # 日付でソート
                df_renamed = df_renamed.sort_values('Date')

                # キャッシュに保存
                self._save_to_cache(ticker, start_date, end_date, df_renamed)

                logger.info(f"{ticker}のデータ取得が完了しました: {len(df_renamed)}行")

                # データのサンプルをログに出力
                if not df_renamed.empty:
                    logger.info(f"データサンプル: \n{df_renamed.head(1).to_string(index=False)}\n...\n{df_renamed.tail(1).to_string(index=False)}")

                return df_renamed

            except Exception as e:
                logger.error(f"{ticker}のデータ取得中にエラーが発生: {str(e)}")
                if attempt < retry - 1:
                    sleep_time_with_jitter = sleep_time + random.uniform(1, 5) * (attempt + 1)
                    logger.info(f"{sleep_time_with_jitter:.1f}秒待機してリトライします（{attempt+1}/{retry}）")
                    time.sleep(sleep_time_with_jitter)

        logger.error(f"{ticker}のデータ取得に{retry}回失敗しました")
        return pd.DataFrame()

    def get_annual_data(self, ticker, year, retry=3, sleep_time=2):
        """
        指定された年の株価データを取得

        Parameters:
        -----------
        ticker : str
            ティッカーシンボル（例: "7203.T"）
        year : int
            年（例: 2022）
        retry : int
            リトライ回数
        sleep_time : float
            リクエスト間の待機時間

        Returns:
        --------
        pd.DataFrame
            株価データ
        """
        start_date = datetime(year, 1, 1)

        # 現在の年の場合は終了日を今日にする
        if year == datetime.now().year:
            end_date = datetime.now()
        else:
            end_date = datetime(year, 12, 31)

        return self.get_stock_data(ticker, start_date, end_date, retry, sleep_time)

    def get_multi_year_data(self, ticker, start_year, end_year=None, retry=3, sleep_between_years=5):
        """
        複数年のデータを取得して結合

        Parameters:
        -----------
        ticker : str
            ティッカーシンボル（例: "7203.T"）
        start_year : int
            開始年
        end_year : int or None
            終了年（Noneの場合は現在の年）
        retry : int
            リトライ回数
        sleep_between_years : float
            年ごとのリクエスト間の待機時間

        Returns:
        --------
        pd.DataFrame
            結合された株価データ
        """
        if end_year is None:
            end_year = datetime.now().year

        all_data = []

        for year in range(start_year, end_year + 1):
            year_data = self.get_annual_data(ticker, year, retry)

            if not year_data.empty:
                all_data.append(year_data)

            # 最後の年でなければ待機
            if year < end_year:
                sleep_time = sleep_between_years + random.uniform(0, 2)
                logger.info(f"次の年のデータ取得まで{sleep_time:.1f}秒待機します")
                time.sleep(sleep_time)

        # データを結合
        if all_data:
            combined_data = pd.concat(all_data, ignore_index=True)
            # 重複を削除
            combined_data = combined_data.drop_duplicates(subset=['Date']).sort_values('Date')
            return combined_data
        else:
            return pd.DataFrame()

    def save_to_nikkei225_structure(self, ticker, df, data_dir="nikkei225_full_data"):
        """
        データを日経225データ構造に保存する

        Parameters:
        -----------
        ticker : str
            ティッカーシンボル
        df : pd.DataFrame
            株価データ
        data_dir : str
            保存先ディレクトリ

        Returns:
        --------
        bool
            成功したかどうか
        """
        if df is None or df.empty:
            logger.warning(f"{ticker}のデータが空のため、保存しません")
            return False
        
        try:
            # ディレクトリ構造を作成
            daily_dir = os.path.join(data_dir, "daily")
            os.makedirs(daily_dir, exist_ok=True)
            
            # 年月ディレクトリの作成と保存
            for (year_val, month), group in df.groupby([df['Date'].dt.year, df['Date'].dt.month]):
                dir_path = os.path.join(daily_dir, str(year_val), f"{year_val}-{month:02d}")
                os.makedirs(dir_path, exist_ok=True)
                
                file_path = os.path.join(dir_path, f"{ticker}.csv")
                
                # 既存データがあれば統合
                if os.path.exists(file_path):
                    existing_df = pd.read_csv(file_path, parse_dates=["Date"])
                    group = pd.concat([existing_df, group])
                    group = group.drop_duplicates(subset=["Date"]).sort_values("Date")
                
                group.to_csv(file_path, index=False)
            
            logger.info(f"{ticker}のデータを日経225構造に保存しました")
            return True
            
        except Exception as e:
            logger.error(f"{ticker}のデータ保存中にエラー: {str(e)}")
            return False


def process_sector(scraper, sector_name, start_year=2020, end_year=2025, 
                   sleep_between_tickers=5, data_dir="nikkei225_full_data"):
    """
    セクター内の全銘柄を処理

    Parameters:
    -----------
    scraper : StooqScraper
        スクレイパーインスタンス
    sector_name : str
        セクター名
    start_year : int
        開始年
    end_year : int
        終了年
    sleep_between_tickers : float
        ティッカー間の待機時間
    data_dir : str
        保存先ディレクトリ

    Returns:
    --------
    dict
        処理結果
    """
    from nikkei225_tickers import get_tickers_by_sector
    
    # セクターの銘柄を取得
    tickers = get_tickers_by_sector(sector_name)
    if not tickers:
        logger.warning(f"セクター「{sector_name}」に銘柄が見つかりません")
        return {'success': [], 'failure': []}
    
    logger.info(f"セクター「{sector_name}」の{len(tickers)}銘柄のデータ処理を開始します")
    
    success_tickers = []
    failure_tickers = []
    
    # ディレクトリ構造を作成
    os.makedirs(data_dir, exist_ok=True)
    os.makedirs(os.path.join(data_dir, "daily"), exist_ok=True)
    os.makedirs(os.path.join(data_dir, "consolidated"), exist_ok=True)
    os.makedirs(os.path.join(data_dir, "metadata"), exist_ok=True)
    
    # 各銘柄を処理
    for i, ticker in enumerate(tickers):
        logger.info(f"銘柄 {i+1}/{len(tickers)}: {ticker} の処理を開始")
        
        # データを取得
        df = scraper.get_multi_year_data(
            ticker, 
            start_year, 
            end_year,
            retry=3,
            sleep_between_years=3
        )
        
        if df is not None and not df.empty:
            # データを日経225構造に保存
            if scraper.save_to_nikkei225_structure(ticker, df, data_dir):
                success_tickers.append(ticker)
            else:
                failure_tickers.append(ticker)
        else:
            failure_tickers.append(ticker)
        
        # 次の銘柄の前に待機（最後の銘柄以外）
        if i < len(tickers) - 1:
            sleep_time = sleep_between_tickers + random.uniform(0, 2)
            logger.info(f"次の銘柄の処理まで{sleep_time:.1f}秒待機します")
            time.sleep(sleep_time)
    
    # 結果をログに出力
    logger.info(f"セクター「{sector_name}」の処理結果:")
    logger.info(f"成功: {len(success_tickers)}/{len(tickers)} 銘柄")
    if failure_tickers:
        logger.info(f"失敗: {', '.join(failure_tickers)}")
    
    return {
        'success': success_tickers,
        'failure': failure_tickers
    }


def create_consolidated_file(data_dir="nikkei225_full_data", year=None):
    """
    統合ファイルの作成
    
    Parameters:
    -----------
    data_dir : str
        データディレクトリ
    year : int or None
        指定した年のデータのみを統合（Noneの場合は全期間）
    
    Returns:
    --------
    str or None
        作成された統合ファイルのパス
    """
    # 読み込むファイルを検索
    all_files = []
    if year:
        # 特定の年のみ
        search_dir = os.path.join(data_dir, "daily", str(year))
        if os.path.exists(search_dir):
            for root, _, files in os.walk(search_dir):
                for file in files:
                    if file.endswith(".csv"):
                        all_files.append(os.path.join(root, file))
        
        output_file = os.path.join(data_dir, "consolidated", f"nikkei225_daily_{year}.csv")
    else:
        # 全期間
        for root, _, files in os.walk(os.path.join(data_dir, "daily")):
            for file in files:
                if file.endswith(".csv"):
                    all_files.append(os.path.join(root, file))
        
        output_file = os.path.join(data_dir, "consolidated", "nikkei225_daily_all.csv")
    
    if not all_files:
        logger.warning("統合するファイルが見つかりません")
        return None
    
    logger.info(f"{len(all_files)}ファイルを統合します")
    
    # 全データを読み込み
    all_data = []
    from tqdm import tqdm
    for file in tqdm(all_files, desc="ファイル読み込み"):
        try:
            df = pd.read_csv(file, parse_dates=["Date"])
            all_data.append(df)
        except Exception as e:
            logger.error(f"{file}の読み込みエラー: {str(e)}")
    
    if not all_data:
        logger.warning("有効なデータがありません")
        return None
    
    # 結合
    combined_data = pd.concat(all_data, ignore_index=True)
    
    # 重複を削除
    combined_data = combined_data.drop_duplicates(subset=["Ticker", "Date"]).sort_values(["Ticker", "Date"])
    
    # 保存
    combined_data.to_csv(output_file, index=False)
    
    logger.info(f"統合ファイルを保存しました: {output_file}, {len(combined_data)}行, {combined_data['Ticker'].nunique()}銘柄")
    
    return output_file


def main():
    """メイン関数"""
    import argparse
    from nikkei225_tickers import get_sector_names, get_tickers_by_sector
    
    parser = argparse.ArgumentParser(description='Stooqウェブスクレイパー')
    parser.add_argument('--ticker', type=str, help='ティッカーシンボル（例: 7203.T）')
    parser.add_argument('--sector', type=str, help='セクター名')
    parser.add_argument('--start-year', type=int, default=2020, help='開始年')
    parser.add_argument('--end-year', type=int, default=2025, help='終了年')
    parser.add_argument('--output-dir', type=str, default="nikkei225_full_data", help='出力ディレクトリ')
    parser.add_argument('--sleep', type=int, default=5, help='リクエスト間の待機時間（秒）')
    parser.add_argument('--consolidate', action='store_true', help='統合ファイルを作成')
    parser.add_argument('--list-sectors', action='store_true', help='セクターの一覧を表示')

    args = parser.parse_args()
    
    # セクターの一覧表示
    if args.list_sectors:
        print("\n利用可能なセクター:")
        for sector in get_sector_names():
            tickers = get_tickers_by_sector(sector)
            print(f"  {sector}: {len(tickers)}銘柄")
        return
    
    # スクレイパーを初期化
    scraper = StooqScraper(cache_dir="data/scraper_cache")
    
    # 統合ファイルの作成
    if args.consolidate:
        create_consolidated_file(args.output_dir)
        return
    
    # 個別銘柄の処理
    if args.ticker:
        df = scraper.get_multi_year_data(args.ticker, args.start_year, args.end_year)
        if df is not None and not df.empty:
            scraper.save_to_nikkei225_structure(args.ticker, df, args.output_dir)
            print(f"\n{args.ticker}のデータを処理しました: {len(df)}行")
        return
    
    # セクターの処理
    if args.sector:
        result = process_sector(
            scraper, 
            args.sector, 
            args.start_year, 
            args.end_year, 
            args.sleep,
            args.output_dir
        )
        print(f"\nセクター「{args.sector}」の処理結果:")
        print(f"成功: {len(result['success'])}")
        print(f"失敗: {len(result['failure'])}")
        if result['failure']:
            print(f"失敗した銘柄: {', '.join(result['failure'])}")
        return
    
    # ヘルプの表示
    parser.print_help()
    print("\n使用例:")
    print("  # トヨタ自動車の2020年～2025年のデータを取得")
    print("  python stooq_web_scraper_fixed.py --ticker 7203.T --start-year 2020 --end-year 2025")
    print("\n  # 銀行業セクターの全銘柄を処理")
    print("  python stooq_web_scraper_fixed.py --sector 銀行業 --start-year 2020 --end-year 2025 --sleep 10")
    print("\n  # 全セクターを確認")
    print("  python stooq_web_scraper_fixed.py --list-sectors")
    print("\n  # 統合ファイルを作成")
    print("  python stooq_web_scraper_fixed.py --consolidate")


if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n処理を中断しました")
    except Exception as e:
        logger.error(f"エラーが発生しました: {str(e)}")
        import traceback
        traceback.print_exc()
