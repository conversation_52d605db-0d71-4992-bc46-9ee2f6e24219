#!/usr/bin/env python3
"""
日経225データ収集の改良版スクリプト

主な改良点:
1. 複数のデータソースを順次試行（yfinance, pandas-datareader, 直接API, stooq_web_scraper）
2. 詳細なエラーログと診断情報
3. 堅牢なエラーハンドリングと回復メカニズム
4. より効率的なキャッシング
5. スマートなリトライ機能
"""

import os
import logging
import json
import time
import argparse
import sys
from datetime import datetime, timedelta
import pandas as pd
import numpy as np
import random
import traceback
import requests
from tqdm import tqdm

# ロギングセットアップ
def setup_logging(debug=False):
    log_level = logging.DEBUG if debug else logging.INFO
    log_dir = "logs"
    os.makedirs(log_dir, exist_ok=True)
    
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    log_file = f"{log_dir}/nikkei_data_collector_{timestamp}.log"
    
    logging.basicConfig(
        level=log_level,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file),
            logging.StreamHandler()
        ]
    )
    
    logger = logging.getLogger(__name__)
    return logger

# 利用可能なデータソースを管理
class DataSourceManager:
    def __init__(self):
        self.sources = {}
        self.available_sources = []
        
        # Pandas DataReader
        try:
            import pandas_datareader as pdr
            self.sources["pandas_datareader"] = pdr
            self.available_sources.append("pandas_datareader")
        except ImportError:
            pass
        
        # yfinance
        try:
            import yfinance as yf
            self.sources["yfinance"] = yf
            self.available_sources.append("yfinance")
        except ImportError:
            pass
        
        # Stooq Web Scraper
        try:
            import stooq_web_scraper_fixed
            self.sources["stooq_scraper"] = stooq_web_scraper_fixed
            self.available_sources.append("stooq_scraper")
        except ImportError:
            try:
                import stooq_web_scraper
                self.sources["stooq_scraper"] = stooq_web_scraper
                self.available_sources.append("stooq_scraper")
            except ImportError:
                pass
        
        # ティッカー情報モジュール
        try:
            from nikkei225_tickers import (
                get_all_tickers,
                get_tickers_by_sector,
                get_sector_names,
                get_major_tickers,
                get_topix_core30
            )
            self.get_all_tickers = get_all_tickers
            self.get_tickers_by_sector = get_tickers_by_sector
            self.get_sector_names = get_sector_names
            self.get_major_tickers = get_major_tickers
            self.get_topix_core30 = get_topix_core30
        except ImportError:
            # デフォルトの簡易関数
            self.get_all_tickers = lambda: ["8306.T", "9432.T", "9984.T", "6758.T", "7203.T"]
            self.get_tickers_by_sector = lambda sector: self.get_all_tickers()
            self.get_sector_names = lambda: ["全銘柄"]
            self.get_major_tickers = lambda: self.get_all_tickers()
            self.get_topix_core30 = lambda: self.get_all_tickers()[:10]
    
    def get_source(self, source_name):
        """指定ソースのモジュールを取得"""
        return self.sources.get(source_name)
    
    def is_available(self, source_name):
        """ソースが利用可能かチェック"""
        return source_name in self.available_sources
    
    def get_available_sources(self):
        """利用可能なソース一覧を取得"""
        return self.available_sources

class ImprovedStockDataCollector:
    """改良版日経225株価データ収集クラス"""
    
    def __init__(self, base_dir="nikkei225_improved_data", cache_dir="data/improved_cache", debug=False):
        """初期化"""
        self.base_dir = base_dir
        self.cache_dir = cache_dir
        self.debug = debug
        
        # ロガー設定
        self.logger = setup_logging(debug=debug)
        
        # データソース管理
        self.source_manager = DataSourceManager()
        self.logger.info(f"利用可能なデータソース: {', '.join(self.source_manager.get_available_sources())}")
        
        # ディレクトリ構造
        self.daily_dir = os.path.join(base_dir, "daily")
        self.metadata_dir = os.path.join(base_dir, "metadata")
        self.consolidated_dir = os.path.join(base_dir, "consolidated")
        self.debug_dir = os.path.join(base_dir, "debug")
        
        # ディレクトリ作成
        for dir_path in [self.daily_dir, self.metadata_dir, self.consolidated_dir, self.cache_dir, self.debug_dir]:
            os.makedirs(dir_path, exist_ok=True)
        
        # ステータスとソース統計ファイル
        self.status_file = os.path.join(self.metadata_dir, "collection_status.json")
        self.source_stats_file = os.path.join(self.metadata_dir, "source_stats.json")
        
        # ステータスとソース統計の読み込み
        self.status = self._load_json_file(self.status_file, self._get_default_status())
        self.source_stats = self._load_json_file(self.source_stats_file, self._get_default_source_stats())
        
        # ソースの優先度（低いほど優先）
        self.source_weights = {
            "yfinance": 1.0,
            "pandas_datareader": 2.0,
            "stooq_scraper": 3.0,
            "direct_request": 4.0
        }
    
    def _load_json_file(self, filepath, default_value):
        """JSONファイルを読み込むユーティリティメソッド"""
        if os.path.exists(filepath):
            try:
                with open(filepath, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception as e:
                self.logger.error(f"ファイル読み込みエラー: {filepath} - {str(e)}")
        return default_value
    
    def _save_json_file(self, filepath, data):
        """JSONファイルを保存するユーティリティメソッド"""
        try:
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
            return True
        except Exception as e:
            self.logger.error(f"ファイル保存エラー: {filepath} - {str(e)}")
            return False
    
    def _get_default_status(self):
        """デフォルトステータスの取得"""
        return {
            "sectors": {},
            "years": {},
            "tickers": {},
            "last_update": None,
            "total_records": 0,
            "total_symbols": 0,
            "completed_years": []
        }
    
    def _get_default_source_stats(self):
        """デフォルトのデータソース統計情報の取得"""
        return {
            "success_rate": {
                "yfinance": {"success": 0, "failure": 0},
                "pandas_datareader": {"success": 0, "failure": 0},
                "stooq_scraper": {"success": 0, "failure": 0},
                "direct_request": {"success": 0, "failure": 0}
            },
            "average_time": {
                "yfinance": 0,
                "pandas_datareader": 0,
                "stooq_scraper": 0,
                "direct_request": 0
            },
            "tickers_by_source": {},
            "last_update": None
        }
    
    def _update_status(self):
        """ステータスの更新"""
        self.status["last_update"] = datetime.now().isoformat()
        return self._save_json_file(self.status_file, self.status)
    
    def _update_source_stats(self, ticker, source, success, elapsed_time=0):
        """データソース統計の更新"""
        # 成功/失敗カウントを更新
        if success:
            self.source_stats["success_rate"][source]["success"] += 1
        else:
            self.source_stats["success_rate"][source]["failure"] += 1
        
        # 平均時間の更新
        total_attempts = (self.source_stats["success_rate"][source]["success"] + 
                           self.source_stats["success_rate"][source]["failure"])
        
        current_avg = self.source_stats["average_time"][source]
        if total_attempts > 1:
            new_avg = current_avg + (elapsed_time - current_avg) / total_attempts
        else:
            new_avg = elapsed_time
        
        self.source_stats["average_time"][source] = new_avg
        
        # ティッカーごとの最適ソースを記録
        if success:
            self.source_stats["tickers_by_source"][ticker] = source
        
        # 統計情報を保存
        self.source_stats["last_update"] = datetime.now().isoformat()
        self._save_json_file(self.source_stats_file, self.source_stats)
        
        # ソースウェイトの調整
        self._adjust_source_weights()
    
    def _adjust_source_weights(self):
        """データソースの優先度を成功率に応じて動的に調整"""
        for source in self.source_weights.keys():
            success = self.source_stats["success_rate"][source]["success"]
            failure = self.source_stats["success_rate"][source]["failure"]
            
            if success + failure > 10:  # 十分なデータがある場合のみ調整
                success_rate = success / (success + failure)
                # 成功率が高いほど優先度を高く（ウェイト値を低く）
                adjusted_weight = 5.0 - 4.0 * success_rate
                self.source_weights[source] = max(0.5, min(5.0, adjusted_weight))
    
    def _get_prioritized_sources(self, ticker):
        """ティッカーに最適なデータソースの優先順位を取得"""
        # この銘柄で以前成功したソース
        previous_source = self.source_stats["tickers_by_source"].get(ticker)
        
        # 利用可能なソースをウェイトでソート
        sources = [(s, w) for s, w in self.source_weights.items() 
                   if self.source_manager.is_available(s)]
        sources.sort(key=lambda x: x[1])  # ウェイトの昇順（優先度の降順）
        
        prioritized = [s[0] for s in sources]
        
        # 以前成功したソースを最優先
        if previous_source and previous_source in prioritized:
            prioritized.remove(previous_source)
            prioritized.insert(0, previous_source)
        
        return prioritized
    
    def _fetch_data_with_yfinance(self, ticker, start_date, end_date):
        """yfinanceでデータ取得"""
        if not self.source_manager.is_available("yfinance"):
            return None, "yfinance not available"
        
        yf = self.source_manager.get_source("yfinance")
        
        try:
            start_time = time.time()
            self.logger.debug(f"yfinanceでの取得を試行: {ticker}, {start_date} - {end_date}")
            
            # Tickerオブジェクト取得
            stock = yf.Ticker(ticker)
            
            # データ取得
            df = stock.history(
                start=start_date.strftime('%Y-%m-%d'),
                end=end_date.strftime('%Y-%m-%d'),
                interval="1d"
            )
            
            if df.empty:
                elapsed_time = time.time() - start_time
                self._update_source_stats(ticker, "yfinance", False, elapsed_time)
                return None, "Empty dataframe returned"
            
            # インデックスリセットとカラム名標準化
            df = df.reset_index()
            
            elapsed_time = time.time() - start_time
            self._update_source_stats(ticker, "yfinance", True, elapsed_time)
            
            return df, None
            
        except Exception as e:
            elapsed_time = time.time() - start_time
            self._update_source_stats(ticker, "yfinance", False, elapsed_time)
            
            error_msg = str(e)
            self.logger.error(f"yfinanceでの取得エラー: {error_msg}")
            if self.debug:
                self.logger.debug(traceback.format_exc())
            
            return None, error_msg
    
    def _fetch_data_with_pandas_datareader(self, ticker, start_date, end_date):
        """pandas-datareaderでデータ取得"""
        if not self.source_manager.is_available("pandas_datareader"):
            return None, "pandas_datareader not available"
        
        pdr = self.source_manager.get_source("pandas_datareader")
        
        try:
            start_time = time.time()
            self.logger.debug(f"pandas-datareaderでの取得を試行: {ticker}, {start_date} - {end_date}")
            
            # Yahoo Financeからデータを取得
            df = pdr.data.get_data_yahoo(
                ticker,
                start=start_date,
                end=end_date
            )
            
            if df.empty:
                elapsed_time = time.time() - start_time
                self._update_source_stats(ticker, "pandas_datareader", False, elapsed_time)
                return None, "Empty dataframe returned"
            
            # インデックスリセット
            df = df.reset_index()
            
            elapsed_time = time.time() - start_time
            self._update_source_stats(ticker, "pandas_datareader", True, elapsed_time)
            
            return df, None
            
        except Exception as e:
            elapsed_time = time.time() - start_time
            self._update_source_stats(ticker, "pandas_datareader", False, elapsed_time)
            
            error_msg = str(e)
            self.logger.error(f"pandas-datareaderでの取得エラー: {error_msg}")
            if self.debug:
                self.logger.debug(traceback.format_exc())
            
            return None, error_msg
    
    def _fetch_data_with_stooq_scraper(self, ticker, start_date, end_date):
        """Stooq Webスクレイパーでデータ取得"""
        if not self.source_manager.is_available("stooq_scraper"):
            return None, "stooq_scraper not available"
        
        scraper = self.source_manager.get_source("stooq_scraper")
        
        try:
            start_time = time.time()
            self.logger.debug(f"stooq_scraperでの取得を試行: {ticker}, {start_date} - {end_date}")
            
            # Stooq用にティッカーを変換
            stooq_ticker = ticker.replace(".T", ".JP")
            
            # StooqScraper クラスのインスタンスを作成
            if hasattr(scraper, "StooqScraper"):
                scraper_instance = scraper.StooqScraper()
                
                # データ取得 (get_stock_data メソッドを使用)
                df = scraper_instance.get_stock_data(
                    stooq_ticker, 
                    start_date, 
                    end_date
                )
            # または直接関数を呼び出し
            elif hasattr(scraper, "get_stock_data"):
                df = scraper.get_stock_data(
                    stooq_ticker, 
                    start_date, 
                    end_date
                )
            else:
                return None, "stooq_scraper module does not have StooqScraper class or get_stock_data function"
            
            if df is None or df.empty:
                elapsed_time = time.time() - start_time
                self._update_source_stats(ticker, "stooq_scraper", False, elapsed_time)
                return None, "Empty dataframe returned"
            
            elapsed_time = time.time() - start_time
            self._update_source_stats(ticker, "stooq_scraper", True, elapsed_time)
            
            return df, None
            
        except Exception as e:
            elapsed_time = time.time() - start_time
            self._update_source_stats(ticker, "stooq_scraper", False, elapsed_time)
            
            error_msg = str(e)
            self.logger.error(f"stooq_scraperでの取得エラー: {error_msg}")
            if self.debug:
                self.logger.debug(traceback.format_exc())
            
            return None, error_msg
    
    def _fetch_data_with_direct_request(self, ticker, start_date, end_date):
        """直接HTTPリクエストでデータ取得"""
        try:
            start_time = time.time()
            self.logger.debug(f"直接リクエストでの取得を試行: {ticker}, {start_date} - {end_date}")
            
            # Yahoo Finance APIパラメータを計算
            period1 = int(start_date.timestamp())
            period2 = int(end_date.timestamp())
            interval = '1d'
            
            url = f"https://query1.finance.yahoo.com/v7/finance/download/{ticker}?period1={period1}&period2={period2}&interval={interval}&events=history"
            headers = {
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
            }
            
            # リクエスト送信
            response = requests.get(url, headers=headers)
            
            if response.status_code != 200:
                elapsed_time = time.time() - start_time
                self._update_source_stats(ticker, "direct_request", False, elapsed_time)
                return None, f"HTTP error: {response.status_code}"
            
            # CSVとしてパース
            df = pd.read_csv(pd.StringIO(response.text))
            
            if df.empty:
                elapsed_time = time.time() - start_time
                self._update_source_stats(ticker, "direct_request", False, elapsed_time)
                return None, "Empty dataframe returned"
            
            # 日付を変換
            df['Date'] = pd.to_datetime(df['Date'])
            
            elapsed_time = time.time() - start_time
            self._update_source_stats(ticker, "direct_request", True, elapsed_time)
            
            return df, None
            
        except Exception as e:
            try:
                elapsed_time = time.time() - start_time
            except:
                elapsed_time = 0
            
            self._update_source_stats(ticker, "direct_request", False, elapsed_time)
            
            error_msg = str(e)
            self.logger.error(f"直接リクエストでの取得エラー: {error_msg}")
            if self.debug:
                self.logger.debug(traceback.format_exc())
            
            return None, error_msg
    
    def collect_ticker_data(self, ticker, start_year, end_year=None, max_attempts=3):
        """単一銘柄の過去データを収集"""
        if end_year is None:
            end_year = datetime.now().year
        
        self.logger.info(f"{ticker}の{start_year}年～{end_year}年のデータ収集を開始（最大試行: {max_attempts}回）")
        
        # ティッカー情報の初期化
        if ticker not in self.status["tickers"]:
            self.status["tickers"][ticker] = {
                "completed_years": [],
                "total_records": 0,
                "last_update": None
            }
        
        all_year_data = []
        successful_years = 0
        failed_years = 0
        
        # 年ごとのデータを収集
        for year in range(start_year, end_year + 1):
            # すでに収集済みの場合はスキップ
            if year in self.status["tickers"][ticker]["completed_years"]:
                self.logger.info(f"{ticker}の{year}年データは既に収集済みです")
                successful_years += 1
                
                # キャッシュから読み込み
                year_start = datetime(year, 1, 1)
                year_end = datetime(year, 12, 31)
                if year == datetime.now().year:
                    year_end = datetime.now()
                
                cache_file = os.path.join(
                    self.cache_dir, 
                    f"{ticker}_{year_start.strftime('%Y%m%d')}_{year_end.strftime('%Y%m%d')}.csv"
                )
                
                if os.path.exists(cache_file):
                    try:
                        df = pd.read_csv(cache_file, parse_dates=["Date"])
                        all_year_data.append(df)
                    except Exception as e:
                        self.logger.error(f"{ticker}の{year}年キャッシュ読み込みエラー: {str(e)}")
                
                continue
            
            # 期間の設定
            start_date = datetime(year, 1, 1)
            end_date = datetime(year, 12, 31)
            if year == datetime.now().year:
                end_date = datetime.now()
            
            # キャッシュファイル名
            cache_file = os.path.join(
                self.cache_dir, 
                f"{ticker}_{start_date.strftime('%Y%m%d')}_{end_date.strftime('%Y%m%d')}.csv"
            )
            
            # キャッシュがあれば読み込み
            if os.path.exists(cache_file):
                try:
                    df = pd.read_csv(cache_file, parse_dates=["Date"])
                    self.logger.info(f"{ticker}の{year}年データをキャッシュから読み込みました")
                    
                    if not df.empty:
                        all_year_data.append(df)
                        successful_years += 1
                        
                        # ステータスを更新
                        if year not in self.status["tickers"][ticker]["completed_years"]:
                            self.status["tickers"][ticker]["completed_years"].append(year)
                            self.status["tickers"][ticker]["total_records"] += len(df)
                            self.status["tickers"][ticker]["last_update"] = datetime.now().isoformat()
                            self._update_status()
                        
                        continue
                except Exception as e:
                    self.logger.error(f"{ticker}の{year}年キャッシュ読み込みエラー: {str(e)}")
            
            # 複数のデータソースを試行
            df = None
            error_msgs = {}
            data_collected = False
            
            # このティッカーに最適なソースの優先順序を取得
            sources = self._get_prioritized_sources(ticker)
            
            # 各ソースを順番に試行
            for source in sources:
                for attempt in range(1, max_attempts + 1):
                    self.logger.info(f"{ticker}の{year}年データを{source}で取得（試行 {attempt}/{max_attempts}）")
                    
                    # 適切なソースメソッドを呼び出し
                    if source == "yfinance":
                        df, error = self._fetch_data_with_yfinance(ticker, start_date, end_date)
                    elif source == "pandas_datareader":
                        df, error = self._fetch_data_with_pandas_datareader(ticker, start_date, end_date)
                    elif source == "stooq_scraper":
                        df, error = self._fetch_data_with_stooq_scraper(ticker, start_date, end_date)
                    elif source == "direct_request":
                        df, error = self._fetch_data_with_direct_request(ticker, start_date, end_date)
                    else:
                        df, error = None, f"Unknown source: {source}"
                    
                    if df is not None and not df.empty:
                        data_collected = True
                        break
                    
                    error_msgs[source] = error
                    
                    # 待機してから再試行
                    if attempt < max_attempts:
                        wait_time = random.uniform(1.0, 3.0) * attempt  # 試行回数に応じて待機時間を増加
                        self.logger.info(f"再試行まで{wait_time:.1f}秒待機します...")
                        time.sleep(wait_time)
                
                if data_collected:
                    self.logger.info(f"{ticker}の{year}年データを{source}で取得しました")
                    break
            
            # データが取得できた場合
            if df is not None and not df.empty:
                # ティッカー情報を追加
                if "Ticker" not in df.columns:
                    df["Ticker"] = ticker
                
                # キャッシュに保存
                df.to_csv(cache_file, index=False)
                
                # 年月ディレクトリの作成と保存
                self._save_to_daily_structure(df, ticker, year)
                
                # 結果を保存
                all_year_data.append(df)
                successful_years += 1
                
                # ステータスを更新
                if year not in self.status["tickers"][ticker]["completed_years"]:
                    self.status["tickers"][ticker]["completed_years"].append(year)
                    self.status["tickers"][ticker]["total_records"] += len(df)
                    self.status["tickers"][ticker]["last_update"] = datetime.now().isoformat()
                    
                    # 年ステータスも更新
                    if str(year) not in self.status["years"]:
                        self.status["years"][str(year)] = {"completed": 0, "total": 0}
                    
                    self.status["years"][str(year)]["completed"] = self.status["years"][str(year)].get("completed", 0) + 1
                    self._update_status()
                
                self.logger.info(f"{ticker}の{year}年データ収集が完了しました: {len(df)}行")
            else:
                # すべてのソースが失敗した場合
                failed_years += 1
                self.logger.error(f"{ticker}の{year}年データを取得できませんでした")
                
                # 診断情報を保存
                self._save_error_diagnostics(ticker, year, error_msgs)
        
        # 成功と失敗の要約
        total_years = end_year - start_year + 1
        self.logger.info(f"{ticker}の{start_year}年～{end_year}年データ収集: 成功={successful_years}/{total_years}年, 失敗={failed_years}/{total_years}年")
        
        # 全年のデータを結合
        if all_year_data:
            all_df = pd.concat(all_year_data, ignore_index=True)
            all_df = all_df.drop_duplicates(subset=["Date"]).sort_values("Date")
            
            # 総レコード数を更新
            self.status["total_records"] += len(all_df)
            self._update_status()
            
            return len(all_df)
        else:
            return 0
    
    def _save_to_daily_structure(self, df, ticker, year):
        """データを日次ディレクトリ構造に保存"""
        if df.empty:
            return
        
        # 年月ごとにグループ化
        try:
            # DatetimeがDateという名前の場合もある
            date_col = "Datetime" if "Datetime" in df.columns else "Date"
            
            for (year_val, month), group in df.groupby([df[date_col].dt.year, df[date_col].dt.month]):
                dir_path = os.path.join(self.daily_dir, str(year_val), f"{year_val}-{month:02d}")
                os.makedirs(dir_path, exist_ok=True)
                
                file_path = os.path.join(dir_path, f"{ticker}.csv")
                
                # 既存データがあれば統合
                if os.path.exists(file_path):
                    try:
                        existing_df = pd.read_csv(file_path, parse_dates=[date_col])
                        group = pd.concat([existing_df, group])
                        group = group.drop_duplicates(subset=[date_col]).sort_values(date_col)
                    except Exception as e:
                        self.logger.error(f"既存データ読み込みエラー: {file_path} - {e}")
                
                # 保存
                group.to_csv(file_path, index=False)
                
        except Exception as e:
            self.logger.error(f"{ticker}の日次構造保存エラー: {e}")
            if self.debug:
                self.logger.debug(traceback.format_exc())
    
    def _save_error_diagnostics(self, ticker, year, error_msgs):
        """エラー診断情報をJSONファイルに保存"""
        try:
            diagnostics = {
                "ticker": ticker,
                "year": year,
                "timestamp": datetime.now().isoformat(),
                "errors": error_msgs
            }
            
            diag_file = os.path.join(self.debug_dir, f"{ticker}_{year}_error_diagnostics.json")
            
            with open(diag_file, 'w', encoding='utf-8') as f:
                json.dump(diagnostics, f, indent=2, ensure_ascii=False)
                
            self.logger.info(f"エラー診断情報を保存しました: {diag_file}")
        except Exception as e:
            self.logger.error(f"診断情報の保存エラー: {str(e)}")
    
    def collect_sector_data(self, sector_name, start_year, end_year=None, sleep_time=10):
        """セクター内の全銘柄のデータを収集"""
        if end_year is None:
            end_year = datetime.now().year
        
        # セクターに属する銘柄を取得
        tickers = self.source_manager.get_tickers_by_sector(sector_name)
        if not tickers:
            self.logger.warning(f"セクター「{sector_name}」に銘柄が見つかりません")
            return 0
        
        self.logger.info(f"セクター「{sector_name}」の{len(tickers)}銘柄の収集を開始: {start_year}年～{end_year}年")
        
        # セクター情報を初期化
        if sector_name not in self.status["sectors"]:
            self.status["sectors"][sector_name] = {
                "total_tickers": len(tickers),
                "completed_tickers": 0,
                "total_records": 0,
                "last_update": None
            }
        
        # 各銘柄のデータを収集
        total_records = 0
        completed = 0
        
        for i, ticker in enumerate(tqdm(tickers, desc=f"セクター「{sector_name}」")):
            # 銘柄間の待機時間をランダム化してAPI制限を回避
            if i > 0:  # 最初の銘柄の前には待機しない
                actual_sleep = sleep_time * (0.5 + random.random())  # 50%～150%のランダム化
                self.logger.debug(f"次の銘柄の処理まで{actual_sleep:.1f}秒待機します")
                time.sleep(actual_sleep)
            
            # データ収集
            records = self.collect_ticker_data(ticker, start_year, end_year)
            total_records += records
            
            if records > 0:
                completed += 1
        
        # ステータスを更新
        self.status["sectors"][sector_name]["completed_tickers"] = completed
        self.status["sectors"][sector_name]["total_records"] = total_records
        self.status["sectors"][sector_name]["last_update"] = datetime.now().isoformat()
        self._update_status()
        
        self.logger.info(f"セクター「{sector_name}」の収集が完了しました: {completed}/{len(tickers)}銘柄, {total_records}レコード")
        
        return total_records
    
    def collect_all_sectors(self, start_year, end_year=None, sleep_between_sectors=300):
        """全セクターのデータを収集"""
        if end_year is None:
            end_year = datetime.now().year
        
        # セクターのリストを取得
        sectors = self.source_manager.get_sector_names()
        self.logger.info(f"全{len(sectors)}セクターの収集を開始: {start_year}年～{end_year}年")
        
        total_records = 0
        
        # 各セクターの処理
        for i, sector in enumerate(sectors):
            # セクターのデータを収集
            records = self.collect_sector_data(sector, start_year, end_year)
            total_records += records
            
            # セクター間の待機
            if i < len(sectors) - 1:  # 最後のセクター以外
                self.logger.info(f"次のセクターの処理まで{sleep_between_sectors/60:.1f}分待機します")
                time.sleep(sleep_between_sectors)
        
        # 総銘柄数を更新
        self.status["total_symbols"] = len(self.source_manager.get_all_tickers())
        
        # 完了した年の記録
        for year in range(start_year, end_year + 1):
            year_str = str(year)
            if year_str not in self.status["completed_years"]:
                self.status["completed_years"].append(year_str)
        
        self._update_status()
        
        self.logger.info(f"全セクターの収集が完了しました: {start_year}年～{end_year}年, 合計{total_records}レコード")
        
        return total_records
    
    def collect_major_tickers(self, start_year, end_year=None, sleep_time=10):
        """主要銘柄のデータを収集"""
        if end_year is None:
            end_year = datetime.now().year
        
        # 主要銘柄を取得
        tickers = self.source_manager.get_major_tickers()
        self.logger.info(f"主要{len(tickers)}銘柄の収集を開始: {start_year}年～{end_year}年")
        
        total_records = 0
        completed = 0
        
        # 各銘柄のデータを収集
        for i, ticker in enumerate(tqdm(tickers, desc="主要銘柄")):
            # 銘柄間の待機
            if i > 0:
                time.sleep(sleep_time)
            
            records = self.collect_ticker_data(ticker, start_year, end_year)
            total_records += records
            
            if records > 0:
                completed += 1
        
        self.logger.info(f"主要銘柄の収集が完了しました: {completed}/{len(tickers)}銘柄, {total_records}レコード")
        
        return total_records
    
    def create_consolidated_file(self, year=None):
        """統合ファイルの作成"""
        # 読み込むファイルを検索
        all_files = []
        if year:
            # 特定の年のみ
            search_dir = os.path.join(self.daily_dir, str(year))
            if os.path.exists(search_dir):
                for root, _, files in os.walk(search_dir):
                    for file in files:
                        if file.endswith(".csv"):
                            all_files.append(os.path.join(root, file))
            
            output_file = os.path.join(self.consolidated_dir, f"nikkei225_daily_{year}.csv")
        else:
            # 全期間
            for root, _, files in os.walk(self.daily_dir):
                for file in files:
                    if file.endswith(".csv"):
                        all_files.append(os.path.join(root, file))
            
            output_file = os.path.join(self.consolidated_dir, "nikkei225_daily_all.csv")
        
        if not all_files:
            self.logger.warning("統合するファイルが見つかりません")
            return None
        
        self.logger.info(f"{len(all_files)}ファイルを統合します")
        
        # 全データを読み込み
        all_data = []
        read_errors = 0
        
        for file in tqdm(all_files, desc="ファイル読み込み"):
            try:
                df = pd.read_csv(file)
                
                # 日付カラムの特定
                date_column = None
                for col in ['Datetime', 'Date', 'date', 'datetime']:
                    if col in df.columns:
                        date_column = col
                        break
                
                if date_column:
                    df[date_column] = pd.to_datetime(df[date_column])
                
                all_data.append(df)
            except Exception as e:
                self.logger.error(f"{file}の読み込みエラー: {str(e)}")
                read_errors += 1
        
        if read_errors > 0:
            self.logger.warning(f"{read_errors}ファイルの読み込みに失敗しました")
        
        if not all_data:
            self.logger.warning("有効なデータがありません")
            return None
        
        # 結合
        combined_data = pd.concat(all_data, ignore_index=True)
        
        # ティッカーと日付カラムの特定
        ticker_col = "Ticker" if "Ticker" in combined_data.columns else None
        date_col = None
        for col in ['Datetime', 'Date', 'date', 'datetime']:
            if col in combined_data.columns:
                date_col = col
                break
        
        # 重複を削除
        if ticker_col and date_col:
            combined_data = combined_data.drop_duplicates(subset=[ticker_col, date_col]).sort_values([ticker_col, date_col])
        elif date_col:
            combined_data = combined_data.drop_duplicates(subset=[date_col]).sort_values(date_col)
        
        # 保存
        combined_data.to_csv(output_file, index=False)
        
        ticker_count = combined_data[ticker_col].nunique() if ticker_col else 0
        self.logger.info(f"統合ファイルを保存しました: {output_file}, {len(combined_data)}行, {ticker_count}銘柄")
        
        return output_file

    def get_collection_status(self):
        """収集状況のサマリーを取得"""
        all_tickers = self.source_manager.get_all_tickers()
        
        # 収集済み銘柄数
        collected_tickers = len(self.status["tickers"])
        
        # 完了した年
        completed_years = sorted(list(set([
            year for ticker_info in self.status["tickers"].values()
            for year in ticker_info["completed_years"]
        ])))
        
        # セクター別進捗
        sector_progress = {}
        for sector in self.source_manager.get_sector_names():
            sector_tickers = self.source_manager.get_tickers_by_sector(sector)
            collected = sum(1 for ticker in sector_tickers if ticker in self.status["tickers"])
            sector_progress[sector] = {
                "total": len(sector_tickers),
                "collected": collected,
                "percentage": round(collected / len(sector_tickers) * 100, 1) if sector_tickers else 0
            }
        
        # 年別進捗
        year_progress = {}
        current_year = datetime.now().year
        for year in range(current_year - 10, current_year + 1):
            year_str = str(year)
            if year_str in self.status["years"]:
                total_possible = len(all_tickers)
                collected = self.status["years"][year_str].get("completed", 0)
                year_progress[year_str] = {
                    "total_possible": total_possible,
                    "collected": collected,
                    "percentage": round(collected / total_possible * 100, 1) if total_possible else 0
                }
        
        # データソース統計
        source_stats = {}
        for source, stats in self.source_stats["success_rate"].items():
            total_attempts = stats["success"] + stats["failure"]
            if total_attempts > 0:
                success_rate = stats["success"] / total_attempts * 100
                source_stats[source] = {
                    "success_rate": round(success_rate, 1),
                    "total_attempts": total_attempts,
                    "avg_time": round(self.source_stats["average_time"][source], 2)
                }
        
        return {
            "total_tickers": len(all_tickers),
            "collected_tickers": collected_tickers,
            "completion_percentage": round(collected_tickers / len(all_tickers) * 100, 1) if all_tickers else 0,
            "total_records": self.status["total_records"],
            "completed_years": completed_years,
            "sector_progress": sector_progress,
            "year_progress": year_progress,
            "data_sources": source_stats,
            "last_update": self.status["last_update"]
        }
    
    def display_collection_status(self):
        """収集状況を表示"""
        status = self.get_collection_status()
        
        print("\n===== 日経225データ収集状況 =====")
        print(f"総銘柄数: {status['total_tickers']}")
        print(f"収集済み銘柄数: {status['collected_tickers']} ({status['completion_percentage']}%)")
        print(f"総レコード数: {status['total_records']}")
        
        if status["completed_years"]:
            print(f"完了した年: {', '.join(map(str, sorted(status['completed_years'])))}")
        
        print("\n-- セクター別進捗 --")
        for sector, progress in status["sector_progress"].items():
            print(f"{sector}: {progress['collected']}/{progress['total']} ({progress['percentage']}%)")
        
        print("\n-- データソース統計 --")
        for source, stats in status["data_sources"].items():
            print(f"{source}: 成功率 {stats['success_rate']}% ({stats['total_attempts']}回試行), 平均時間 {stats['avg_time']}秒")
        
        print("\n-- 最終更新 --")
        print(f"{status['last_update']}")
        print("=============================\n")


def main():
    """メイン関数"""
    parser = argparse.ArgumentParser(description='日経225株価データ収集ツール（改良版）')
    
    # 共通のオプション（サブコマンドの前に指定）
    parser.add_argument('--debug', action='store_true', help='デバッグモードを有効化')
    parser.add_argument('--base-dir', type=str, default='nikkei225_improved_data', help='データ保存ベースディレクトリ')
    parser.add_argument('--cache-dir', type=str, default='data/improved_cache', help='キャッシュディレクトリ')
    
    # サブコマンドの設定
    subparsers = parser.add_subparsers(dest='command', help='実行するコマンド')
    
    # ticker コマンド
    ticker_parser = subparsers.add_parser('ticker', help='単一銘柄のデータを収集')
    ticker_parser.add_argument('ticker', help='ティッカーシンボル (例: 8306.T)')
    ticker_parser.add_argument('--start-year', type=int, default=2020, help='開始年')
    ticker_parser.add_argument('--end-year', type=int, default=None, help='終了年')
    ticker_parser.add_argument('--debug', action='store_true', help='デバッグモードを有効化') # サブコマンド内でも指定可能に
    
    # sector コマンド
    sector_parser = subparsers.add_parser('sector', help='セクターのデータを収集')
    sector_parser.add_argument('sector', help='セクター名')
    sector_parser.add_argument('--start-year', type=int, default=2020, help='開始年')
    sector_parser.add_argument('--end-year', type=int, default=None, help='終了年')
    sector_parser.add_argument('--sleep', type=int, default=10, help='銘柄間の待機時間（秒）')
    sector_parser.add_argument('--debug', action='store_true', help='デバッグモードを有効化')
    
    # all コマンド
    all_parser = subparsers.add_parser('all', help='全セクターのデータを収集')
    all_parser.add_argument('--start-year', type=int, default=2020, help='開始年')
    all_parser.add_argument('--end-year', type=int, default=None, help='終了年')
    all_parser.add_argument('--sleep', type=int, default=300, help='セクター間の待機時間（秒）')
    all_parser.add_argument('--debug', action='store_true', help='デバッグモードを有効化')
    
    # major コマンド
    major_parser = subparsers.add_parser('major', help='主要銘柄のデータを収集')
    major_parser.add_argument('--start-year', type=int, default=2020, help='開始年')
    major_parser.add_argument('--end-year', type=int, default=None, help='終了年')
    major_parser.add_argument('--sleep', type=int, default=10, help='銘柄間の待機時間（秒）')
    major_parser.add_argument('--debug', action='store_true', help='デバッグモードを有効化')
    
    # consolidate コマンド
    consolidate_parser = subparsers.add_parser('consolidate', help='データを統合ファイルに結合')
    consolidate_parser.add_argument('--year', type=int, default=None, help='特定の年のみ統合（指定なしで全期間）')
    consolidate_parser.add_argument('--debug', action='store_true', help='デバッグモードを有効化')
    
    # status コマンド
    status_parser = subparsers.add_parser('status', help='収集状況を表示')
    status_parser.add_argument('--debug', action='store_true', help='デバッグモードを有効化')
    
    args = parser.parse_args()
    
    # デバッグフラグをサブコマンドとグローバルオプションの両方から取得
    debug_enabled = False
    if hasattr(args, 'debug'):
        debug_enabled = args.debug
    
    # データコレクターの初期化
    collector = ImprovedStockDataCollector(
        base_dir=args.base_dir,
        cache_dir=args.cache_dir,
        debug=debug_enabled
    )
    
    # コマンドに応じた処理
    if args.command == 'ticker':
        collector.collect_ticker_data(args.ticker, args.start_year, args.end_year)
    
    elif args.command == 'sector':
        collector.collect_sector_data(args.sector, args.start_year, args.end_year, args.sleep)
    
    elif args.command == 'all':
        collector.collect_all_sectors(args.start_year, args.end_year, args.sleep)
    
    elif args.command == 'major':
        collector.collect_major_tickers(args.start_year, args.end_year, args.sleep)
    
    elif args.command == 'consolidate':
        output_file = collector.create_consolidated_file(args.year)
        if output_file:
            print(f"統合ファイル作成: {output_file}")
    
    elif args.command == 'status':
        collector.display_collection_status()
    
    else:
        # コマンドが指定されていない場合はヘルプを表示
        parser.print_help()

if __name__ == "__main__":
    main()
