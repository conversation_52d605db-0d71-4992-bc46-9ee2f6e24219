import yfinance as yf
import pandas as pd
import time
import os

# データ保存用のディレクトリ
data_dir = "data"
os.makedirs(data_dir, exist_ok=True)

# 日経平均225のティッカーリスト
tickers = [
    '1332.T', '1333.T', '1376.T', '1605.T', '1721.T', '1801.T', '1802.T', '1803.T', '1808.T', '1812.T',
    '1925.T', '1928.T', '1963.T', '2002.T', '2269.T', '2501.T', '2502.T', '2503.T', '2531.T', '2768.T',
    '2801.T', '2802.T', '2871.T', '2914.T', '3003.T', '3086.T', '3101.T', '3103.T', '3289.T', '3382.T',
    '3401.T', '3402.T', '3405.T', '3407.T', '3436.T', '3861.T', '3863.T', '4004.T', '4005.T', '4021.T',
    '4042.T', '4043.T', '4061.T', '4063.T', '4151.T', '4183.T', '4188.T', '4208.T', '4324.T', '4452.T',
    '4502.T', '4503.T', '4506.T', '4507.T', '4519.T', '4523.T', '4568.T', '4578.T', '4689.T', '4704.T',
    '4751.T', '4901.T', '4911.T', '5020.T', '5101.T', '5108.T', '5201.T', '5202.T', '5214.T', '5232.T',
    '5233.T', '5301.T', '5332.T', '5401.T', '5406.T', '5411.T', '5541.T', '5631.T', '5703.T', '5706.T',
    '5707.T', '5711.T', '5713.T', '5801.T', '5802.T', '5803.T', '5901.T', '5902.T', '6005.T', '6028.T',
    '6103.T', '6113.T', '6135.T', '6141.T', '6146.T', '6201.T', '6203.T', '6208.T', '6268.T', '6273.T',
    '6301.T', '6302.T', '6305.T', '6326.T', '6361.T', '6366.T', '6367.T', '6370.T', '6378.T', '6383.T',
    '6407.T', '6417.T', '6418.T', '6448.T', '6455.T', '6460.T', '6465.T', '6471.T', '6472.T', '6473.T',
    '6474.T', '6479.T', '6481.T', '6501.T', '6503.T', '6504.T', '6506.T', '6508.T', '6586.T', '6588.T',
    '6594.T', '6619.T', '6645.T', '6674.T', '6701.T', '6702.T', '6723.T', '6724.T', '6727.T', '6752.T',
    '6753.T', '6758.T', '6762.T', '6770.T', '6841.T', '6857.T', '6902.T', '6952.T', '6954.T', '6963.T',
    '6981.T', '6988.T', '7003.T', '7011.T', '7012.T', '7201.T', '7202.T', '7203.T', '7231.T', '7238.T'
]

# 収集データの期間
period = "1y"  # 過去1年分
interval = "1h"  # 1時間足

# 全銘柄のデータを格納するリスト
all_data = []

# データ取得
for ticker in tickers:
    print(f"Downloading {ticker} data...")
    stock = yf.download(ticker, period=period, interval=interval)
    
    if not stock.empty:
        # 欠損値を前のデータで埋める（ffill: 前のデータで補完）
        stock.fillna(method="ffill", inplace=True)
        stock.dropna(inplace=True)  # それでも NaN が残る場合は削除
        
        # 不要なカラムを削除
        stock.reset_index(inplace=True)
        stock["Ticker"] = ticker  # 銘柄名のカラムを追加
        
        # 必要なカラムを選択
        stock = stock[["Datetime", "Ticker", "Open", "High", "Low", "Close", "Volume"]]
        
        # 統合リストに追加
        all_data.append(stock)
        
    time.sleep(1)  # API制限対策

# すべての銘柄データを1つのCSVに統合
merged_df = pd.concat(all_data, ignore_index=True)

# CSVに保存
merged_df.to_csv(os.path.join(data_dir, "nikkei225_1h_data.csv"), index=False)

print("データ収集 & 整形完了！")
