#!/usr/bin/env python3
"""
日経225全銘柄の過去データ収集スクリプト

セクターごとに銘柄をグループ化し、段階的に過去データを収集します。
"""

import os
import logging
import json
import time
import argparse
import sys
from datetime import datetime, timedelta
import pandas as pd
import random
import pandas_datareader as pdr
from tqdm import tqdm

# 自作モジュールをインポート
from nikkei225_tickers import (
    get_all_tickers, 
    get_tickers_by_sector, 
    get_sector_names,
    get_major_tickers,
    get_topix_core30
)

# ロギング設定
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f"logs/full_collection_{datetime.now().strftime('%Y%m%d')}.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class Nikkei225DataCollector:
    """日経225全銘柄のデータ収集クラス"""
    
    def __init__(self, base_dir="nikkei225_full_data", cache_dir="data/full_cache"):
        self.base_dir = base_dir
        self.cache_dir = cache_dir
        
        # ディレクトリ構造
        self.daily_dir = os.path.join(base_dir, "daily")
        self.metadata_dir = os.path.join(base_dir, "metadata")
        self.consolidated_dir = os.path.join(base_dir, "consolidated")
        
        # ディレクトリ作成
        for dir_path in [self.daily_dir, self.metadata_dir, self.consolidated_dir, self.cache_dir]:
            os.makedirs(dir_path, exist_ok=True)
        
        # ステータスファイル
        self.status_file = os.path.join(self.metadata_dir, "collection_status.json")
        self.status = self._load_status()
    
    def _load_status(self):
        """収集ステータスの読み込み"""
        if os.path.exists(self.status_file):
            try:
                with open(self.status_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception as e:
                logger.error(f"ステータス読み込みエラー: {str(e)}")
        
        # デフォルト値
        return {
            "sectors": {},
            "years": {},
            "tickers": {},
            "last_update": None,
            "total_records": 0,
            "total_symbols": 0,
            "completed_years": []
        }
    
    def _save_status(self):
        """収集ステータスの保存"""
        try:
            # 最終更新日時を更新
            self.status["last_update"] = datetime.now().isoformat()
            
            with open(self.status_file, 'w', encoding='utf-8') as f:
                json.dump(self.status, f, indent=2, ensure_ascii=False)
        except Exception as e:
            logger.error(f"ステータス保存エラー: {str(e)}")
    
    def collect_ticker_data(self, ticker, start_year, end_year=None):
        """単一銘柄の過去データを収集"""
        if end_year is None:
            end_year = datetime.now().year
        
        logger.info(f"{ticker}の{start_year}年～{end_year}年のデータ収集を開始")
        
        # ティッカー情報を初期化
        if ticker not in self.status["tickers"]:
            self.status["tickers"][ticker] = {
                "completed_years": [],
                "total_records": 0,
                "last_update": None
            }
        
        all_year_data = []
        
        # 年ごとのデータを収集
        for year in range(start_year, end_year + 1):
            # すでに収集済みの場合はスキップ
            if year in self.status["tickers"][ticker]["completed_years"]:
                logger.info(f"{ticker}の{year}年データは既に収集済みです")
                continue
            
            try:
                # 期間の設定
                start_date = datetime(year, 1, 1)
                end_date = datetime(year, 12, 31)
                if year == datetime.now().year:
                    end_date = datetime.now()
                
                # キャッシュファイル名
                cache_file = os.path.join(
                    self.cache_dir, 
                    f"{ticker}_{start_date.strftime('%Y%m%d')}_{end_date.strftime('%Y%m%d')}.csv"
                )
                
                if os.path.exists(cache_file):
                    # キャッシュから読み込み
                    df = pd.read_csv(cache_file, parse_dates=["Date"])
                    logger.info(f"{ticker}の{year}年データをキャッシュから読み込みました")
                else:
                    # Stooqからデータを取得
                    stooq_ticker = ticker.replace(".T", ".JP")
                    logger.info(f"{ticker}（{stooq_ticker}）の{year}年データをStooqから取得します")
                    
                    try:
                        df = pdr.data.get_data_stooq(
                            stooq_ticker,
                            start=start_date,
                            end=end_date
                        )
                        
                        # データがない場合はYahoo Financeを試す
                        if df.empty:
                            logger.warning(f"Stooqから{ticker}のデータを取得できませんでした。Yahoo Financeを試します...")
                            
                            # Yahoo Financeからデータを取得
                            df = pdr.data.get_data_yahoo(
                                ticker,
                                start=start_date,
                                end=end_date
                            )
                        
                        # インデックスをリセット
                        df = df.reset_index()
                        
                        # キャッシュに保存
                        df.to_csv(cache_file, index=False)
                        
                        # 少し待機してAPIリクエスト制限を避ける
                        time.sleep(random.uniform(1.0, 3.0))
                        
                    except Exception as e:
                        logger.error(f"{ticker}の{year}年データ取得エラー: {str(e)}")
                        continue
                
                if df.empty:
                    logger.warning(f"{ticker}の{year}年データは空です")
                    continue
                
                # ティッカー情報を追加
                df["Ticker"] = ticker
                
                # 年月ディレクトリの作成と保存
                for (year_val, month), group in df.groupby([df['Date'].dt.year, df['Date'].dt.month]):
                    dir_path = os.path.join(self.daily_dir, str(year_val), f"{year_val}-{month:02d}")
                    os.makedirs(dir_path, exist_ok=True)
                    
                    file_path = os.path.join(dir_path, f"{ticker}.csv")
                    
                    # 既存データがあれば統合
                    if os.path.exists(file_path):
                        existing_df = pd.read_csv(file_path, parse_dates=["Date"])
                        group = pd.concat([existing_df, group])
                        group = group.drop_duplicates(subset=["Date"]).sort_values("Date")
                    
                    group.to_csv(file_path, index=False)
                
                # 結果を保存
                all_year_data.append(df)
                
                # ステータスを更新
                self.status["tickers"][ticker]["completed_years"].append(year)
                self.status["tickers"][ticker]["total_records"] += len(df)
                self.status["tickers"][ticker]["last_update"] = datetime.now().isoformat()
                
                # 年ステータスも更新
                if str(year) not in self.status["years"]:
                    self.status["years"][str(year)] = {"completed": 0, "total": 0}
                
                self.status["years"][str(year)]["completed"] = self.status["years"][str(year)].get("completed", 0) + 1
                self._save_status()
                
                logger.info(f"{ticker}の{year}年データ収集が完了しました: {len(df)}行")
                
            except Exception as e:
                logger.error(f"{ticker}の{year}年データ処理中にエラーが発生: {str(e)}")
                continue
        
        # 全年のデータを結合
        if all_year_data:
            all_df = pd.concat(all_year_data, ignore_index=True)
            all_df = all_df.drop_duplicates(subset=["Date"]).sort_values("Date")
            
            # 総レコード数を更新
            self.status["total_records"] += len(all_df)
            self._save_status()
            
            return len(all_df)
        else:
            return 0
    
    def collect_sector_data(self, sector_name, start_year, end_year=None, sleep_time=10):
        """セクター内の全銘柄のデータを収集"""
        if end_year is None:
            end_year = datetime.now().year
        
        # セクターに属する銘柄を取得
        tickers = get_tickers_by_sector(sector_name)
        if not tickers:
            logger.warning(f"セクター「{sector_name}」に銘柄が見つかりません")
            return 0
        
        logger.info(f"セクター「{sector_name}」の{len(tickers)}銘柄の収集を開始: {start_year}年～{end_year}年")
        
        # セクター情報を初期化
        if sector_name not in self.status["sectors"]:
            self.status["sectors"][sector_name] = {
                "total_tickers": len(tickers),
                "completed_tickers": 0,
                "total_records": 0,
                "last_update": None
            }
        
        # 各銘柄のデータを収集
        total_records = 0
        completed = 0
        
        for ticker in tqdm(tickers, desc=f"セクター「{sector_name}」"):
            records = self.collect_ticker_data(ticker, start_year, end_year)
            total_records += records
            
            if records > 0:
                completed += 1
            
            # 銘柄間の待機
            if ticker != tickers[-1]:  # 最後の銘柄以外
                logger.info(f"次の銘柄の処理まで{sleep_time}秒待機します")
                time.sleep(sleep_time)
        
        # ステータスを更新
        self.status["sectors"][sector_name]["completed_tickers"] = completed
        self.status["sectors"][sector_name]["total_records"] = total_records
        self.status["sectors"][sector_name]["last_update"] = datetime.now().isoformat()
        self._save_status()
        
        logger.info(f"セクター「{sector_name}」の収集が完了しました: {completed}/{len(tickers)}銘柄, {total_records}レコード")
        
        return total_records
    
    def collect_all_sectors(self, start_year, end_year=None, sleep_between_sectors=300):
        """全セクターのデータを収集"""
        if end_year is None:
            end_year = datetime.now().year
        
        # セクターのリストを取得
        sectors = get_sector_names()
        logger.info(f"全{len(sectors)}セクターの収集を開始: {start_year}年～{end_year}年")
        
        # 各セクターの処理
        for i, sector in enumerate(sectors):
            # セクターのデータを収集
            self.collect_sector_data(sector, start_year, end_year)
            
            # セクター間の待機
            if i < len(sectors) - 1:  # 最後のセクター以外
                logger.info(f"次のセクターの処理まで{sleep_between_sectors/60:.1f}分待機します")
                time.sleep(sleep_between_sectors)
        
        # 総銘柄数を更新
        self.status["total_symbols"] = len(get_all_tickers())
        self.status["completed_years"].append(str(start_year))
        self._save_status()
        
        logger.info(f"全セクターの収集が完了しました: {start_year}年～{end_year}年")
    
    def collect_major_tickers(self, start_year, end_year=None, sleep_time=10):
        """主要銘柄のデータを収集"""
        if end_year is None:
            end_year = datetime.now().year
        
        # 主要銘柄を取得
        tickers = get_major_tickers()
        logger.info(f"主要{len(tickers)}銘柄の収集を開始: {start_year}年～{end_year}年")
        
        # 各銘柄のデータを収集
        for ticker in tqdm(tickers, desc="主要銘柄"):
            self.collect_ticker_data(ticker, start_year, end_year)
            
            # 銘柄間の待機
            if ticker != tickers[-1]:  # 最後の銘柄以外
                time.sleep(sleep_time)
        
        logger.info(f"主要銘柄の収集が完了しました: {start_year}年～{end_year}年")
    
    def create_consolidated_file(self, year=None):
        """統合ファイルの作成"""
        # 読み込むファイルを検索
        all_files = []
        if year:
            # 特定の年のみ
            search_dir = os.path.join(self.daily_dir, str(year))
            if os.path.exists(search_dir):
                for root, _, files in os.walk(search_dir):
                    for file in files:
                        if file.endswith(".csv"):
                            all_files.append(os.path.join(root, file))
            
            output_file = os.path.join(self.consolidated_dir, f"nikkei225_daily_{year}.csv")
        else:
            # 全期間
            for root, _, files in os.walk(self.daily_dir):
                for file in files:
                    if file.endswith(".csv"):
                        all_files.append(os.path.join(root, file))
            
            output_file = os.path.join(self.consolidated_dir, "nikkei225_daily_all.csv")
        
        if not all_files:
            logger.warning("統合するファイルが見つかりません")
            return None
        
        logger.info(f"{len(all_files)}ファイルを統合します")
        
        # 全データを読み込み
        all_data = []
        for file in tqdm(all_files, desc="ファイル読み込み"):
            try:
                df = pd.read_csv(file, parse_dates=["Date"])
                all_data.append(df)
            except Exception as e:
                logger.error(f"{file}の読み込みエラー: {str(e)}")
        
        if not all_data:
            logger.warning("有効なデータがありません")
            return None
        
        # 結合
        combined_data = pd.concat(all_data, ignore_index=True)
        
        # 重複を削除
        combined_data = combined_data.drop_duplicates(subset=["Ticker", "Date"]).sort_values(["Ticker", "Date"])
        
        # 保存
        combined_data.to_csv(output_file, index=False)
        
        logger.info(f"統合ファイルを保存しました: {output_file}, {len(combined_data)}行, {combined_data['Ticker'].nunique()}銘柄")
        
        return output_file
    
    def get_collection_status(self):
        """収集状況のサマリーを取得"""
        all_tickers = get_all_tickers()
        
        # 収集済み銘柄数
        collected_tickers = len(self.status["tickers"])
        
        # 完了した年
        completed_years = sorted(list(set([
            year for ticker_info in self.status["tickers"].values()
            for year in ticker_info["completed_years"]
        ])))
        
        # セクター別進捗
        sector_progress = {}
        for sector in get_sector_names():
            sector_tickers = get_tickers_by_sector(sector)
            collected = sum(1 for ticker in sector_tickers if ticker in self.status["tickers"])
            sector_progress[sector] = {
                "total": len(sector_tickers),
                "collected": collected,
                "percentage": round(collected / len(sector_tickers) * 100, 1) if sector_tickers else 0
            }
        
        # 年別進捗
        year_progress = {}
        current_year = datetime.now().year
        for year in range(current_year - 10, current_year + 1):
            year_str = str(year)
            if year_str in self.status["years"]:
                total_possible = len(all_tickers)
                collected = self.status["years"][year_str].get("completed", 0)
                year_progress[year_str] = {
                    "total_possible": total_possible,
                    "collected": collected,
                    "percentage": round(collected / total_possible * 100, 1) if total_possible else 0
                }
        
        return {
            "total_tickers": len(all_tickers),
            "collected_tickers": collected_tickers,
            "completion_percentage": round(collected_tickers / len(all_tickers) * 100, 1) if all_tickers else 0,
            "total_records": self.status["total_records"],
            "completed_years": completed_years,
            "sector_progress": sector_progress,
            "year_progress": year_progress,
            "last_update": self.status["last_update"]
        }
    
    def display_collection_status(self):
        """収集状況を表示"""
        status = self.get_collection_status()
        
        print("\n===== 日経225データ収集状況 =====")
        print(f"総銘柄数: {status['total_tickers']}")
        print(f"収集済み銘柄数: {status['collected_tickers']} ({status['completion_percentage']}%)")
        print(f"総レコード数: {status['total_records']}")
        
        if status["completed_years"]:
            print(f"完了した年: {', '.join(map(str, sorted(status['completed_years'])))}")
        else:
            print("完了した年: なし")
        
        # セクター別進捗
        print("\n--- セクター別進捗 ---")
        for sector, progress in sorted(status["sector_progress"].items(), key=lambda x: x[1]["percentage"], reverse=True):
            print(f"{sector}: {progress['collected']}/{progress['total']} ({progress['percentage']}%)")
        
        # 年別進捗
        print("\n--- 年別進捗 ---")
        for year, progress in sorted(status["year_progress"].items()):
            print(f"{year}年: {progress['collected']}/{progress['total_possible']} ({progress['percentage']}%)")
        
        if status["last_update"]:
            last_update = datetime.fromisoformat(status["last_update"])
            print(f"\n最終更新: {last_update.strftime('%Y-%m-%d %H:%M:%S')}")
    
    def is_ready_for_trading(self, required_years=5, required_symbols_percentage=90):
        """取引開始に十分なデータが収集されたか確認"""
        status = self.get_collection_status()
        
        # 必要な年数のデータが揃っているか
        current_year = datetime.now().year
        target_years = set(range(current_year - required_years, current_year + 1))
        completed_years = set(map(int, filter(lambda y: y.isdigit(), status["completed_years"])))
        
        years_completed = target_years.issubset(completed_years)
        
        # 必要な銘柄数のデータが揃っているか
        symbols_percentage = status["completion_percentage"]
        symbols_completed = symbols_percentage >= required_symbols_percentage
        
        return {
            "ready": years_completed and symbols_completed,
            "years_completed": years_completed,
            "symbols_completed": symbols_completed,
            "years_percentage": round(len(target_years.intersection(completed_years)) / len(target_years) * 100, 1),
            "symbols_percentage": symbols_percentage,
            "missing_years": sorted(list(target_years - completed_years)),
            "required_years": required_years,
            "required_symbols_percentage": required_symbols_percentage
        }
    
    def display_trading_readiness(self, required_years=5, required_symbols_percentage=90):
        """取引開始の準備状況を表示"""
        readiness = self.is_ready_for_trading(required_years, required_symbols_percentage)
        
        print("\n===== 仮想取引の準備状況 =====")
        
        if readiness["ready"]:
            print("✅ 取引開始の準備が整っています！")
        else:
            print("❌ 取引開始の準備が整っていません")
        
        print(f"\n--- 年次データの完備状況 ---")
        print(f"必要期間: 過去{required_years}年間")
        print(f"完了状況: {readiness['years_percentage']}% {'✅' if readiness['years_completed'] else '❌'}")
        
        if readiness["missing_years"]:
            print(f"不足している年: {', '.join(map(str, readiness['missing_years']))}")
        
        print(f"\n--- 銘柄カバレッジ ---")
        print(f"必要カバレッジ: {required_symbols_percentage}%")
        print(f"現在のカバレッジ: {readiness['symbols_percentage']}% {'✅' if readiness['symbols_completed'] else '❌'}")
        
        print("\n取引開始に向けた推奨アクション:")
        if not readiness["years_completed"]:
            print(f"- {', '.join(map(str, readiness['missing_years']))}年のデータを収集する")
        
        if not readiness["symbols_completed"]:
            print(f"- より多くの銘柄のデータを収集する（あと{required_symbols_percentage - readiness['symbols_percentage']:.1f}%必要）")

def setup_directory():
    """必要なディレクトリの作成"""
    dirs = ["logs", "data/full_cache", "nikkei225_full_data"]
    for d in dirs:
        os.makedirs(d, exist_ok=True)
    logger.info("ディレクトリ構造を確認しました")

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='日経225全銘柄の過去データ収集')
    parser.add_argument('--base-dir', type=str, default="nikkei225_full_data", help='ベースディレクトリ')
    parser.add_argument('--cache-dir', type=str, default="data/full_cache", help='キャッシュディレクトリ')
    
    # 収集オプション
    parser.add_argument('--sector', type=str, help='特定のセクターのみ収集')
    parser.add_argument('--all-sectors', action='store_true', help='全セクターを収集')
    parser.add_argument('--ticker', type=str, help='特定の銘柄のみ収集')
    parser.add_argument('--major', action='store_true', help='主要銘柄のみ収集')
    parser.add_argument('--topix', action='store_true', help='TOPIX Core30銘柄のみ収集')
    
    # 期間オプション
    parser.add_argument('--start-year', type=int, default=datetime.now().year-5, help='開始年（デフォルト: 5年前）')
    parser.add_argument('--end-year', type=int, default=datetime.now().year, help='終了年（デフォルト: 現在の年）')
    parser.add_argument('--year', type=int, help='特定の年のみ収集')
    
    # その他オプション
    parser.add_argument('--consolidate', action='store_true', help='統合ファイルを作成')
    parser.add_argument('--status', action='store_true', help='収集状況を表示')
    parser.add_argument('--readiness', action='store_true', help='取引開始の準備状況を表示')
    parser.add_argument('--sleep', type=int, default=10, help='銘柄間の待機時間（秒）')
    parser.add_argument('--sector-sleep', type=int, default=300, help='セクター間の待機時間（秒）')
    
    args = parser.parse_args()
    
    # ディレクトリ設定
    setup_directory()
    
    # データコレクター
    collector = Nikkei225DataCollector(base_dir=args.base_dir, cache_dir=args.cache_dir)
    
    try:
        # 収集状況の表示
        if args.status:
            collector.display_collection_status()
            sys.exit(0)
        
        # 取引準備状況の表示
        if args.readiness:
            collector.display_trading_readiness()
            sys.exit(0)
        
        # 統合ファイルの作成
        if args.consolidate:
            collector.create_consolidated_file(args.year)
            sys.exit(0)
        
        # 収集期間の設定
        start_year = args.year if args.year else args.start_year
        end_year = args.year if args.year else args.end_year
        
        # 指定されたオプションに基づいてデータ収集
        if args.ticker:
            collector.collect_ticker_data(args.ticker, start_year, end_year)
        
        elif args.sector:
            collector.collect_sector_data(args.sector, start_year, end_year, sleep_time=args.sleep)
        
        elif args.all_sectors:
            collector.collect_all_sectors(start_year, end_year, sleep_between_sectors=args.sector_sleep)
        
        elif args.major:
            collector.collect_major_tickers(start_year, end_year, sleep_time=args.sleep)
        
        elif args.topix:
            for ticker in get_topix_core30():
                collector.collect_ticker_data(ticker, start_year, end_year)
                time.sleep(args.sleep)
        
        else:
            # デフォルト: ステータス表示
            collector.display_collection_status()
            print("\n使用例:")
            print("  # 主要銘柄の過去5年分のデータを収集")
            print("  python collect_full_nikkei225_data.py --major")
            print("\n  # 銀行業セクターの2023年データを収集")
            print("  python collect_full_nikkei225_data.py --sector \"銀行業\" --year 2023")
            print("\n  # 全セクターの過去3年分のデータを段階的に収集")
            print("  python collect_full_nikkei225_data.py --all-sectors --start-year 2022")
            print("\n  # 統合ファイルの作成")
            print("  python collect_full_nikkei225_data.py --consolidate")
            print("\n  # 取引開始の準備状況を確認")
            print("  python collect_full_nikkei225_data.py --readiness")
    
    except KeyboardInterrupt:
        logger.warning("ユーザーによる中断")
        sys.exit(1)
    except Exception as e:
        logger.error(f"実行エラー: {str(e)}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
