#!/usr/bin/env python3
"""
日経225全銘柄の株価データをスクレイピングによって一括収集するスクリプト

pandas_datareaderが機能しない場合の代替手段として、Stooqウェブサイトから
直接スクレイピングしてデータを取得します。
"""

import os
import sys
import time
import random
import logging
import pandas as pd
import concurrent.futures
import json
from datetime import datetime, timedelta
import argparse
from tqdm import tqdm

# 自作モジュールをインポート
from stooq_web_scraper import StooqScraper
from nikkei225_tickers import (
    get_all_tickers, 
    get_tickers_by_sector, 
    get_sector_names,
    get_major_tickers,
    get_topix_core30
)

# ロギング設定
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f"logs/scraper_collection_{datetime.now().strftime('%Y%m%d')}.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class Nikkei225ScraperCollector:
    """日経225全銘柄をスクレイピングで収集するクラス"""
    
    def __init__(self, base_dir="nikkei225_scraped_data", cache_dir="data/scraper_cache"):
        """
        初期化
        
        Parameters:
        -----------
        base_dir : str
            ベースディレクトリ
        cache_dir : str
            スクレイパーキャッシュディレクトリ
        """
        self.base_dir = base_dir
        self.cache_dir = cache_dir
        
        # ディレクトリ構造
        self.daily_dir = os.path.join(base_dir, "daily")
        self.metadata_dir = os.path.join(base_dir, "metadata")
        self.consolidated_dir = os.path.join(base_dir, "consolidated")
        
        # スクレイパーの初期化
        self.scraper = StooqScraper(cache_dir=cache_dir)
        
        # ディレクトリ作成
        for dir_path in [self.daily_dir, self.metadata_dir, self.consolidated_dir, self.cache_dir]:
            os.makedirs(dir_path, exist_ok=True)
        
        # ステータスファイル
        self.status_file = os.path.join(self.metadata_dir, "collection_status.json")
        self.status = self._load_status()
        
        # 失敗したティッカーリスト
        self.failed_file = os.path.join(self.metadata_dir, "failed_tickers.json")
        self.failed_tickers = self._load_failed_tickers()
    
    def _load_status(self):
        """収集ステータスの読み込み"""
        if os.path.exists(self.status_file):
            try:
                with open(self.status_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception as e:
                logger.error(f"ステータス読み込みエラー: {str(e)}")
        
        # デフォルト値
        return {
            "sectors": {},
            "years": {},
            "tickers": {},
            "last_update": None,
            "total_records": 0,
            "total_symbols": 0,
            "completed_years": []
        }
    
    def _save_status(self):
        """収集ステータスの保存"""
        try:
            # 最終更新日時を更新
            self.status["last_update"] = datetime.now().isoformat()
            
            with open(self.status_file, 'w', encoding='utf-8') as f:
                json.dump(self.status, f, indent=2, ensure_ascii=False)
        except Exception as e:
            logger.error(f"ステータス保存エラー: {str(e)}")
    
    def _load_failed_tickers(self):
        """失敗したティッカーリストの読み込み"""
        if os.path.exists(self.failed_file):
            try:
                with open(self.failed_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception as e:
                logger.error(f"失敗リスト読み込みエラー: {str(e)}")
        
        # デフォルト値
        return {
            "tickers": [],
            "last_update": None
        }
    
    def _save_failed_tickers(self):
        """失敗したティッカーリストの保存"""
        try:
            # 最終更新日時を更新
            self.failed_tickers["last_update"] = datetime.now().isoformat()
            
            with open(self.failed_file, 'w', encoding='utf-8') as f:
                json.dump(self.failed_tickers, f, indent=2, ensure_ascii=False)
        except Exception as e:
            logger.error(f"失敗リスト保存エラー: {str(e)}")
    
    def collect_ticker_data(self, ticker, start_year, end_year=None, retry=3):
        """
        単一銘柄の過去データを収集
        
        Parameters:
        -----------
        ticker : str
            ティッカーシンボル
        start_year : int
            開始年
        end_year : int or None
            終了年（Noneの場合は現在の年）
        retry : int
            リトライ回数
        
        Returns:
        --------
        int
            収集したレコード数
        """
        if end_year is None:
            end_year = datetime.now().year
        
        logger.info(f"{ticker}の{start_year}年～{end_year}年のデータ収集を開始")
        
        # ティッカー情報を初期化
        if ticker not in self.status["tickers"]:
            self.status["tickers"][ticker] = {
                "completed_years": [],
                "total_records": 0,
                "last_update": None
            }
        
        try:
            # スクレイパーを使用してデータを取得
            df = self.scraper.get_multi_year_data(
                ticker,
                start_year,
                end_year,
                retry=retry,
                sleep_between_years=2
            )
            
            if df.empty:
                logger.warning(f"{ticker}のデータが空です")
                # 失敗リストに追加
                if ticker not in self.failed_tickers["tickers"]:
                    self.failed_tickers["tickers"].append(ticker)
                    self._save_failed_tickers()
                return 0
            
            # 年ごとにデータを分割して保存
            records_saved = 0
            for year, year_group in df.groupby(df['Date'].dt.year):
                year = int(year)
                
                # 年ディレクトリを作成
                year_dir = os.path.join(self.daily_dir, str(year))
                os.makedirs(year_dir, exist_ok=True)
                
                # 年月ごとに分割
                for (_, month), month_group in year_group.groupby([year_group['Date'].dt.year, year_group['Date'].dt.month]):
                    # 年月ディレクトリを作成
                    month_dir = os.path.join(year_dir, f"{year}-{month:02d}")
                    os.makedirs(month_dir, exist_ok=True)
                    
                    # ファイル保存
                    file_path = os.path.join(month_dir, f"{ticker}.csv")
                    month_group.to_csv(file_path, index=False)
                    
                    records_saved += len(month_group)
                
                # 年のステータスを更新
                if year not in self.status["tickers"][ticker]["completed_years"]:
                    self.status["tickers"][ticker]["completed_years"].append(year)
                
                # 年別統計の更新
                if str(year) not in self.status["years"]:
                    self.status["years"][str(year)] = {"completed": 0, "total": 0}
                
                self.status["years"][str(year)]["completed"] = self.status["years"][str(year)].get("completed", 0) + 1
            
            # ティッカーのステータスを更新
            self.status["tickers"][ticker]["total_records"] = records_saved
            self.status["tickers"][ticker]["last_update"] = datetime.now().isoformat()
            
            # 総レコード数を更新
            self.status["total_records"] += records_saved
            
            # 失敗リストから削除（成功した場合）
            if ticker in self.failed_tickers["tickers"]:
                self.failed_tickers["tickers"].remove(ticker)
                self._save_failed_tickers()
            
            # ステータスを保存
            self._save_status()
            
            logger.info(f"{ticker}のデータ収集が完了しました: {records_saved}行")
            return records_saved
            
        except Exception as e:
            logger.error(f"{ticker}のデータ収集エラー: {str(e)}")
            
            # 失敗リストに追加
            if ticker not in self.failed_tickers["tickers"]:
                self.failed_tickers["tickers"].append(ticker)
                self._save_failed_tickers()
            
            return 0
    
    def collect_sector_data(self, sector_name, start_year, end_year=None, sleep_time=10, retry=3):
        """
        セクター内の全銘柄のデータを収集
        
        Parameters:
        -----------
        sector_name : str
            セクター名
        start_year : int
            開始年
        end_year : int or None
            終了年（Noneの場合は現在の年）
        sleep_time : float
            銘柄間の待機時間
        retry : int
            リトライ回数
        
        Returns:
        --------
        int
            収集したレコード数
        """
        if end_year is None:
            end_year = datetime.now().year
        
        # セクターに属する銘柄を取得
        tickers = get_tickers_by_sector(sector_name)
        if not tickers:
            logger.warning(f"セクター「{sector_name}」に銘柄が見つかりません")
            return 0
        
        logger.info(f"セクター「{sector_name}」の{len(tickers)}銘柄の収集を開始: {start_year}年～{end_year}年")
        
        # セクター情報を初期化
        if sector_name not in self.status["sectors"]:
            self.status["sectors"][sector_name] = {
                "total_tickers": len(tickers),
                "completed_tickers": 0,
                "total_records": 0,
                "last_update": None
            }
        
        # 各銘柄のデータを収集
        total_records = 0
        completed = 0
        
        for i, ticker in enumerate(tqdm(tickers, desc=f"セクター「{sector_name}」")):
            records = self.collect_ticker_data(ticker, start_year, end_year, retry)
            total_records += records
            
            if records > 0:
                completed += 1
            
            # 銘柄間の待機
            if i < len(tickers) - 1:  # 最後の銘柄以外
                sleep_with_jitter = sleep_time + random.uniform(-2, 2)
                sleep_with_jitter = max(1, sleep_with_jitter)  # 最低1秒
                logger.info(f"次の銘柄の処理まで{sleep_with_jitter:.1f}秒待機します")
                time.sleep(sleep_with_jitter)
        
        # ステータスを更新
        self.status["sectors"][sector_name]["completed_tickers"] = completed
        self.status["sectors"][sector_name]["total_records"] = total_records
        self.status["sectors"][sector_name]["last_update"] = datetime.now().isoformat()
        self._save_status()
        
        logger.info(f"セクター「{sector_name}」の収集が完了しました: {completed}/{len(tickers)}銘柄, {total_records}レコード")
        
        return total_records
    
    def collect_all_sectors(self, start_year, end_year=None, sleep_between_sectors=300, sleep_between_tickers=10, retry=3):
        """
        全セクターのデータを収集
        
        Parameters:
        -----------
        start_year : int
            開始年
        end_year : int or None
            終了年（Noneの場合は現在の年）
        sleep_between_sectors : float
            セクター間の待機時間
        sleep_between_tickers : float
            銘柄間の待機時間
        retry : int
            リトライ回数
        
        Returns:
        --------
        int
            収集したレコード数
        """
        if end_year is None:
            end_year = datetime.now().year
        
        # セクターのリストを取得
        sectors = get_sector_names()
        logger.info(f"全{len(sectors)}セクターの収集を開始: {start_year}年～{end_year}年")
        
        # 各セクターの処理
        total_records = 0
        
        for i, sector in enumerate(sectors):
            sector_records = self.collect_sector_data(
                sector,
                start_year,
                end_year,
                sleep_time=sleep_between_tickers,
                retry=retry
            )
            
            total_records += sector_records
            
            # セクター間の待機
            if i < len(sectors) - 1:  # 最後のセクター以外
                logger.info(f"次のセクターの処理まで{sleep_between_sectors/60:.1f}分待機します")
                time.sleep(sleep_between_sectors)
        
        # 総銘柄数を更新
        self.status["total_symbols"] = len(get_all_tickers())
        self.status["completed_years"].append(str(start_year))
        self._save_status()
        
        logger.info(f"全セクターの収集が完了しました: {start_year}年～{end_year}年, {total_records}レコード")
        
        return total_records
    
    def collect_tickers(self, tickers, start_year, end_year=None, sleep_time=10, retry=3, desc="銘柄"):
        """
        指定された銘柄リストのデータを収集
        
        Parameters:
        -----------
        tickers : list
            ティッカーシンボルのリスト
        start_year : int
            開始年
        end_year : int or None
            終了年（Noneの場合は現在の年）
        sleep_time : float
            銘柄間の待機時間
        retry : int
            リトライ回数
        desc : str
            進捗表示の説明
        
        Returns:
        --------
        int
            収集したレコード数
        """
        if end_year is None:
            end_year = datetime.now().year
        
        if not tickers:
            logger.warning(f"銘柄リストが空です")
            return 0
        
        logger.info(f"{desc}（{len(tickers)}銘柄）の収集を開始: {start_year}年～{end_year}年")
        
        # 各銘柄のデータを収集
        total_records = 0
        completed = 0
        
        for i, ticker in enumerate(tqdm(tickers, desc=desc)):
            records = self.collect_ticker_data(ticker, start_year, end_year, retry)
            total_records += records
            
            if records > 0:
                completed += 1
            
            # 銘柄間の待機
            if i < len(tickers) - 1:  # 最後の銘柄以外
                sleep_with_jitter = sleep_time + random.uniform(-2, 2)
                sleep_with_jitter = max(1, sleep_with_jitter)  # 最低1秒
                logger.info(f"次の銘柄の処理まで{sleep_with_jitter:.1f}秒待機します")
                time.sleep(sleep_with_jitter)
        
        logger.info(f"{desc}の収集が完了しました: {completed}/{len(tickers)}銘柄, {total_records}レコード")
        
        return total_records
    
    def collect_major_tickers(self, start_year, end_year=None, sleep_time=10, retry=3):
        """
        主要銘柄のデータを収集
        
        Parameters:
        -----------
        start_year : int
            開始年
        end_year : int or None
            終了年（Noneの場合は現在の年）
        sleep_time : float
            銘柄間の待機時間
        retry : int
            リトライ回数
        
        Returns:
        --------
        int
            収集したレコード数
        """
        tickers = get_major_tickers()
        return self.collect_tickers(tickers, start_year, end_year, sleep_time, retry, desc="主要銘柄")
    
    def collect_topix_core30(self, start_year, end_year=None, sleep_time=10, retry=3):
        """
        TOPIX Core30のデータを収集
        
        Parameters:
        -----------
        start_year : int
            開始年
        end_year : int or None
            終了年（Noneの場合は現在の年）
        sleep_time : float
            銘柄間の待機時間
        retry : int
            リトライ回数
        
        Returns:
        --------
        int
            収集したレコード数
        """
        tickers = get_topix_core30()
        return self.collect_tickers(tickers, start_year, end_year, sleep_time, retry, desc="TOPIX Core30")
    
    def collect_failed_tickers(self, start_year, end_year=None, sleep_time=15, retry=5):
        """
        失敗した銘柄のデータを再収集
        
        Parameters:
        -----------
        start_year : int
            開始年
        end_year : int or None
            終了年（Noneの場合は現在の年）
        sleep_time : float
            銘柄間の待機時間
        retry : int
            リトライ回数
        
        Returns:
        --------
        int
            収集したレコード数
        """
        tickers = self.failed_tickers["tickers"]
        if not tickers:
            logger.info("失敗した銘柄はありません")
            return 0
        
        return self.collect_tickers(tickers, start_year, end_year, sleep_time, retry, desc="失敗銘柄")
    
    def create_consolidated_file(self, year=None):
        """
        統合ファイルの作成
        
        Parameters:
        -----------
        year : int or None
            年（Noneの場合は全期間）
        
        Returns:
        --------
        str or None
            作成されたファイルのパス、または失敗した場合はNone
        """
        # 読み込むファイルを検索
        all_files = []
        if year:
            # 特定の年のみ
            search_dir = os.path.join(self.daily_dir, str(year))
            if os.path.exists(search_dir):
                for root, _, files in os.walk(search_dir):
                    for file in files:
                        if file.endswith(".csv"):
                            all_files.append(os.path.join(root, file))
            
            output_file = os.path.join(self.consolidated_dir, f"nikkei225_daily_{year}.csv")
        else:
            # 全期間
            for root, _, files in os.walk(self.daily_dir):
                for file in files:
                    if file.endswith(".csv"):
                        all_files.append(os.path.join(root, file))
            
            output_file = os.path.join(self.consolidated_dir, "nikkei225_daily_all.csv")
        
        if not all_files:
            logger.warning("統合するファイルが見つかりません")
            return None
        
        logger.info(f"{len(all_files)}ファイルを統合します")
        
        # 全データを読み込み
        all_data = []
        for file in tqdm(all_files, desc="ファイル読み込み"):
            try:
                df = pd.read_csv(file, parse_dates=['Date'])
                all_data.append(df)
            except Exception as e:
                logger.error(f"{file}の読み込みエラー: {str(e)}")
        
        if not all_data:
            logger.warning("有効なデータがありません")
            return None
        
        # 結合
        combined_data = pd.concat(all_data, ignore_index=True)
        
        # 重複を削除
        combined_data = combined_data.drop_duplicates(subset=["Ticker", "Date"]).sort_values(["Ticker", "Date"])
        
        # 保存
        combined_data.to_csv(output_file, index=False)
        
        logger.info(f"統合ファイルを保存しました: {output_file}, {len(combined_data)}行, {combined_data['Ticker'].nunique()}銘柄")
        
        return output_file
    
    def get_collection_status(self):
        """
        収集状況のサマリーを取得
        
        Returns:
        --------
        dict
            収集状況のサマリー
        """
        all_tickers = get_all_tickers()
        
        # 収集済み銘柄数
        collected_tickers = len(self.status["tickers"])
        
        # 完了した年
        completed_years = sorted(list(set([
            year for ticker_info in self.status["tickers"].values()
            for year in ticker_info["completed_years"]
        ])))
        
        # セクター別進捗
        sector_progress = {}
        for sector in get_sector_names():
            sector_tickers = get_tickers_by_sector(sector)
            collected = sum(1 for ticker in sector_tickers if ticker in self.status["tickers"])
            sector_progress[sector] = {
                "total": len(sector_tickers),
                "collected": collected,
                "percentage": round(collected / len(sector_tickers) * 100, 1) if sector_tickers else 0
            }
        
        # 年別進捗
        year_progress = {}
        current_year = datetime.now().year
        for year in range(current_year - 10, current_year + 1):
            year_str = str(year)
            if year_str in self.status["years"]:
                total_possible = len(all_tickers)
                collected = self.status["years"][year_str].get("completed", 0)
                year_progress[year_str] = {
                    "total_possible": total_possible,
                    "collected": collected,
                    "percentage": round(collected / total_possible * 100, 1) if total_possible else 0
                }
        
        # 失敗した銘柄
        failed_count = len(self.failed_tickers["tickers"])
        
        return {
            "total_tickers": len(all_tickers),
            "collected_tickers": collected_tickers,
            "failed_tickers": failed_count,
            "completion_percentage": round(collected_tickers / len(all_tickers) * 100, 1) if all_tickers else 0,
            "total_records": self.status["total_records"],
            "completed_years": completed_years,
            "sector_progress": sector_progress,
            "year_progress": year_progress,
            "last_update": self.status["last_update"]
        }
    
    def display_collection_status(self):
        """収集状況を表示"""
        status = self.get_collection_status()
        
        print("\n===== 日経225データ収集状況 =====")
        print(f"総銘柄数: {status['total_tickers']}")
        print(f"収集済み銘柄数: {status['collected_tickers']} ({status['completion_percentage']}%)")
        print(f"失敗銘柄数: {status['failed_tickers']}")
        print(f"総レコード数: {status['total_records']}")
        
        if status["completed_years"]:
            print(f"完了した年: {', '.join(map(str, sorted(status['completed_years'])))}")
        else:
            print("完了した年: なし")
        
        # セクター別進捗
        print("\n--- セクター別進捗 ---")
        for sector, progress in sorted(status["sector_progress"].items(), key=lambda x: x[1]["percentage"], reverse=True):
            print(f"{sector}: {progress['collected']}/{progress['total']} ({progress['percentage']}%)")
        
        # 年別進捗
        print("\n--- 年別進捗 ---")
        for year, progress in sorted(status["year_progress"].items()):
            print(f"{year}年: {progress['collected']}/{progress['total_possible']} ({progress['percentage']}%)")
        
        if status["last_update"]:
            last_update = datetime.fromisoformat(status["last_update"])
            print(f"\n最終更新: {last_update.strftime('%Y-%m-%d %H:%M:%S')}")
        
        # 失敗した銘柄
        if self.failed_tickers["tickers"]:
            print(f"\n失敗した銘柄 ({len(self.failed_tickers['tickers'])}件):")
            for i, ticker in enumerate(sorted(self.failed_tickers["tickers"])):
                print(f"{ticker}", end=", ")
                if (i + 1) % 10 == 0:
                    print()
            print()
    
    def is_ready_for_trading(self, required_years=5, required_symbols_percentage=90):
        """
        取引開始に十分なデータが収集されたか確認
        
        Parameters:
        -----------
        required_years : int
            必要な年数
        required_symbols_percentage : float
            必要な銘柄カバレッジ率
        
        Returns:
        --------
        dict
            準備状況
        """
        status = self.get_collection_status()
        
        # 必要な年数のデータが揃っているか
        current_year = datetime.now().year
        target_years = set(range(current_year - required_years, current_year + 1))
        completed_years = set(map(int, filter(lambda y: y.isdigit(), status["completed_years"])))
        
        years_completed = target_years.issubset(completed_years)
        
        # 必要な銘柄数のデータが揃っているか
        symbols_percentage = status["completion_percentage"]
        symbols_completed = symbols_percentage >= required_symbols_percentage
        
        return {
            "ready": years_completed and symbols_completed,
            "years_completed": years_completed,
            "symbols_completed": symbols_completed,
            "years_percentage": round(len(target_years.intersection(completed_years)) / len(target_years) * 100, 1),
            "symbols_percentage": symbols_percentage,
            "missing_years": sorted(list(target_years - completed_years)),
            "required_years": required_years,
            "required_symbols_percentage": required_symbols_percentage
        }
    
    def display_trading_readiness(self, required_years=5, required_symbols_percentage=90):
        """
        取引開始の準備状況を表示
        
        Parameters:
        -----------
        required_years : int
            必要な年数
        required_symbols_percentage : float
            必要な銘柄カバレッジ率
        """
        readiness = self.is_ready_for_trading(required_years, required_symbols_percentage)
        
        print("\n===== 仮想取引の準備状況 =====")
        
        if readiness["ready"]:
            print("✅ 取引開始の準備が整っています！")
        else:
            print("❌ 取引開始の準備が整っていません")
        
        print(f"\n--- 年次データの完備状況 ---")
        print(f"必要期間: 過去{required_years}年間")
        print(f"完了状況: {readiness['years_percentage']}% {'✅' if readiness['years_completed'] else '❌'}")
        
        if readiness["missing_years"]:
            print(f"不足している年: {', '.join(map(str, readiness['missing_years']))}")
        
        print(f"\n--- 銘柄カバレッジ ---")
        print(f"必要カバレッジ: {required_symbols_percentage}%")
        print(f"現在のカバレッジ: {readiness['symbols_percentage']}% {'✅' if readiness['symbols_completed'] else '❌'}")
        
        print("\n取引開始に向けた推奨アクション:")
        if not readiness["years_completed"]:
            print(f"- {', '.join(map(str, readiness['missing_years']))}年のデータを収集する")
        
        if not readiness["symbols_completed"]:
            print(f"- より多くの銘柄のデータを収集する（あと{required_symbols_percentage - readiness['symbols_percentage']:.1f}%必要）")


def main():
    """メイン関数"""
    parser = argparse.ArgumentParser(description='日経225全銘柄のデータをスクレイピングで収集')
    
    # 銘柄指定
    group_ticker = parser.add_argument_group('銘柄選択オプション')
    group_ticker.add_argument('--ticker', type=str, help='単一銘柄のデータを収集')
    group_ticker.add_argument('--sector', type=str, help='特定のセクターの銘柄を収集')
    group_ticker.add_argument('--major', action='store_true', help='主要銘柄のデータを収集')
    group_ticker.add_argument('--topix', action='store_true', help='TOPIX Core30の銘柄を収集')
    group_ticker.add_argument('--all-sectors', action='store_true', help='全セクターの銘柄を収集')
    group_ticker.add_argument('--failed', action='store_true', help='以前失敗した銘柄を再収集')
    
    # 期間指定
    group_period = parser.add_argument_group('期間指定オプション')
    group_period.add_argument('--start-year', type=int, default=datetime.now().year-5, help='開始年（デフォルト: 5年前）')
    group_period.add_argument('--end-year', type=int, default=None, help='終了年（デフォルト: 現在年）')
    group_period.add_argument('--year', type=int, help='特定の年のデータのみ収集（--start-yearと--end-yearを上書き）')
    
    # その他オプション
    group_other = parser.add_argument_group('その他のオプション')
    group_other.add_argument('--base-dir', type=str, default='nikkei225_scraped_data', help='データ保存ディレクトリ')
    group_other.add_argument('--cache-dir', type=str, default='data/scraper_cache', help='スクレイパーキャッシュディレクトリ')
    group_other.add_argument('--sleep', type=int, default=10, help='銘柄間の待機時間（秒）')
    group_other.add_argument('--sector-sleep', type=int, default=300, help='セクター間の待機時間（秒）')
    group_other.add_argument('--retry', type=int, default=3, help='リトライ回数')
    group_other.add_argument('--consolidate', action='store_true', help='収集したデータを統合ファイルに結合')
    group_other.add_argument('--status', action='store_true', help='収集状況を表示')
    group_other.add_argument('--readiness', action='store_true', help='取引準備状況を表示')
    
    args = parser.parse_args()
    
    # コレクターの初期化
    collector = Nikkei225ScraperCollector(base_dir=args.base_dir, cache_dir=args.cache_dir)
    
    # ステータスの表示
    if args.status:
        collector.display_collection_status()
        return
    
    # 取引準備状況の表示
    if args.readiness:
        collector.display_trading_readiness()
        return
    
    # 期間の設定
    if args.year:
        start_year = args.year
        end_year = args.year
    else:
        start_year = args.start_year
        end_year = args.end_year
    
    # 統合ファイルの作成
    if args.consolidate:
        collector.create_consolidated_file(year=args.year)
        return
    
    # 銘柄データの収集
    if args.ticker:
        collector.collect_ticker_data(args.ticker, start_year, end_year, retry=args.retry)
    
    elif args.sector:
        collector.collect_sector_data(args.sector, start_year, end_year, sleep_time=args.sleep, retry=args.retry)
    
    elif args.major:
        collector.collect_major_tickers(start_year, end_year, sleep_time=args.sleep, retry=args.retry)
    
    elif args.topix:
        collector.collect_topix_core30(start_year, end_year, sleep_time=args.sleep, retry=args.retry)
    
    elif args.all_sectors:
        collector.collect_all_sectors(start_year, end_year, 
                                     sleep_between_sectors=args.sector_sleep, 
                                     sleep_between_tickers=args.sleep,
                                     retry=args.retry)
    
    elif args.failed:
        collector.collect_failed_tickers(start_year, end_year, sleep_time=args.sleep, retry=args.retry)
    
    else:
        # 何も指定されていない場合は使用方法を表示
        collector.display_collection_status()
        print("\n使用例:")
        print("  # 主要銘柄の過去5年分のデータを収集")
        print("  python collect_nikkei225_scraper.py --major")
        print("\n  # 銀行業セクターの2023年データを収集")
        print("  python collect_nikkei225_scraper.py --sector '銀行業' --year 2023")
        print("\n  # 失敗した銘柄を再収集")
        print("  python collect_nikkei225_scraper.py --failed --sleep 20 --retry 5")
        print("\n  # 統合ファイルの作成")
        print("  python collect_nikkei225_scraper.py --consolidate")
        print("\n  # 取引準備状況の確認")
        print("  python collect_nikkei225_scraper.py --readiness")


if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n処理を中断しました")
    except Exception as e:
        logger.error(f"エラーが発生しました: {str(e)}")
        import traceback
        traceback.print_exc()
