#!/usr/bin/env python3
"""
日経225全銘柄データ収集の改良版スクリプト

主な改良点:
1. 複数のデータソースを順次試行（yfinance, pandas-datareader, 直接API, stooq_web_scraper）
2. 詳細なエラーログと診断情報
3. 堅牢なエラーハンドリングと回復メカニズム
4. より効率的なキャッシング
5. スマートなリトライ機能
"""

import os
import logging
import json
import time
import argparse
import sys
from datetime import datetime, timedelta
import pandas as pd
import numpy as np
import random
import traceback
import requests
from tqdm import tqdm
from pathlib import Path

# 利用可能なデータソースを動的にインポート
available_sources = []
source_modules = {}

# Pandas DataReader
try:
    import pandas_datareader as pdr
    available_sources.append("pandas_datareader")
    source_modules["pandas_datareader"] = pdr
except ImportError:
    pass

# yfinance
try:
    import yfinance as yf
    available_sources.append("yfinance")
    source_modules["yfinance"] = yf
except ImportError:
    pass

# 既存のStooq Webスクレイパーを条件付きインポート
try:
    import stooq_web_scraper_fixed
    available_sources.append("stooq_scraper")
    source_modules["stooq_scraper"] = stooq_web_scraper_fixed
except ImportError:
    try:
        import stooq_web_scraper
        available_sources.append("stooq_scraper")
        source_modules["stooq_scraper"] = stooq_web_scraper
    except ImportError:
        pass

# 日経225銘柄情報
try:
    from src.utils.nikkei225_tickers import (
        get_all_tickers, 
        get_tickers_by_sector, 
        get_sector_names,
        get_major_tickers,
        get_topix_core30
    )
except ImportError:
    # ティッカー情報がなければ簡易的な関数を定義
    def get_all_tickers():
        return ["8306.T", "9432.T", "9984.T", "6758.T", "7203.T"]
    
    def get_tickers_by_sector(sector_name):
        return get_all_tickers()
    
    def get_sector_names():
        return ["全銘柄"]
    
    def get_major_tickers():
        return get_all_tickers()
    
    def get_topix_core30():
        return get_all_tickers()[:10]

# ロギング設定
def setup_logging(debug=False):
    """ロギングを設定"""
    log_level = logging.DEBUG if debug else logging.INFO
    log_dir = "logs"
    os.makedirs(log_dir, exist_ok=True)
    
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    log_file = f"{log_dir}/nikkei_data_collector_{timestamp}.log"
    
    logging.basicConfig(
        level=log_level,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file),
            logging.StreamHandler()
        ]
    )
    
    logger = logging.getLogger(__name__)
    logger.info(f"ログ出力先: {log_file}")
    logger.info(f"利用可能なデータソース: {', '.join(available_sources)}")
    
    return logger

class ImprovedDataCollector:
    """日経225全銘柄の株価データを収集する改良版クラス"""
    
    def __init__(self, base_dir="nikkei225_improved_data", cache_dir="data/improved_cache", 
                 max_retries=3, debug=False):
        """
        初期化
        
        Parameters:
        -----------
        base_dir : str
            データ保存先のベースディレクトリ
        cache_dir : str
            キャッシュディレクトリ
        max_retries : int
            取得失敗時の最大リトライ回数
        debug : bool
            デバッグモードの有効化
        """
        self.base_dir = base_dir
        self.cache_dir = cache_dir
        self.max_retries = max_retries
        self.debug = debug
        
        # ロギング設定
        self.logger = logging.getLogger(__name__)
        
        # ディレクトリ構造
        self.daily_dir = os.path.join(base_dir, "daily")
        self.metadata_dir = os.path.join(base_dir, "metadata")
        self.consolidated_dir = os.path.join(base_dir, "consolidated")
        self.debug_dir = os.path.join(base_dir, "debug")
        
        # ディレクトリ作成
        for dir_path in [self.daily_dir, self.metadata_dir, self.consolidated_dir, 
                          self.cache_dir, self.debug_dir]:
            os.makedirs(dir_path, exist_ok=True)
        
        # ステータスファイル
        self.status_file = os.path.join(self.metadata_dir, "collection_status.json")
        self.status = self._load_status()
        
        # データソース統計
        self.source_stats = self._load_source_stats()
        
        # リクエスト制限を避けるための調整可能なウェイト
        self.source_weights = {
            "yfinance": 1.0,        # ウェイト値が低いほど優先度が高い
            "pandas_datareader": 2.0,
            "direct_request": 3.0,
            "stooq_scraper": 4.0
        }
    
    def _load_status(self):
        """収集ステータスの読み込み"""
        if os.path.exists(self.status_file):
            try:
                with open(self.status_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception as e:
                self.logger.error(f"ステータス読み込みエラー: {str(e)}")
        
        # デフォルト値
        return {
            "sectors": {},
            "years": {},
            "tickers": {},
            "last_update": None,
            "total_records": 0,
            "total_symbols": 0,
            "completed_years": []
        }
    
    def _save_status(self):
        """収集ステータスの保存"""
        try:
            # 最終更新日時を更新
            self.status["last_update"] = datetime.now().isoformat()
            
            with open(self.status_file, 'w', encoding='utf-8') as f:
                json.dump(self.status, f, indent=2, ensure_ascii=False)
                
            self.logger.debug("ステータスファイルを保存しました")
        except Exception as e:
            self.logger.error(f"ステータス保存エラー: {str(e)}")
    
    def _load_source_stats(self):
        """データソース統計情報の読み込み"""
        stats_file = os.path.join(self.metadata_dir, "source_stats.json")
        
        if os.path.exists(stats_file):
            try:
                with open(stats_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception as e:
                self.logger.error(f"データソース統計の読み込みエラー: {str(e)}")
        
        # デフォルト値
        return {
            "success_rate": {
                "yfinance": {"success": 0, "failure": 0},
                "pandas_datareader": {"success": 0, "failure": 0},
                "direct_request": {"success": 0, "failure": 0},
                "stooq_scraper": {"success": 0, "failure": 0}
            },
            "average_time": {
                "yfinance": 0,
                "pandas_datareader": 0,
                "direct_request": 0,
                "stooq_scraper": 0
            },
            "tickers_by_source": {},
            "last_update": None
        }
    
    def _save_source_stats(self):
        """データソース統計情報の保存"""
        try:
            # 最終更新日時を更新
            self.source_stats["last_update"] = datetime.now().isoformat()
            
            stats_file = os.path.join(self.metadata_dir, "source_stats.json")
            with open(stats_file, 'w', encoding='utf-8') as f:
                json.dump(self.source_stats, f, indent=2, ensure_ascii=False)
                
            self.logger.debug("データソース統計を保存しました")
        except Exception as e:
            self.logger.error(f"データソース統計の保存エラー: {str(e)}")
    
    def _update_source_stats(self, ticker, source, success, elapsed_time=0):
        """データソース統計情報を更新"""
        # 成功/失敗カウントの更新
        if success:
            self.source_stats["success_rate"][source]["success"] += 1
        else:
            self.source_stats["success_rate"][source]["failure"] += 1
        
        # 平均時間の更新
        total_attempts = (self.source_stats["success_rate"][source]["success"] + 
                           self.source_stats["success_rate"][source]["failure"])
        
        current_avg = self.source_stats["average_time"][source]
        if total_attempts > 1:
            new_avg = current_avg + (elapsed_time - current_avg) / total_attempts
        else:
            new_avg = elapsed_time
            
        self.source_stats["average_time"][source] = new_avg
        
        # ティッカーごとの最適ソースを記録
        if success:
            self.source_stats["tickers_by_source"][ticker] = source
        
        # 統計情報を保存
        self._save_source_stats()
        
        # ソースウェイトの調整（成功率に基づく）
        self._adjust_source_weights()
    
    def _adjust_source_weights(self):
        """データソース優先度の調整"""
        for source in self.source_weights.keys():
            if source in self.source_stats["success_rate"]:
                success = self.source_stats["success_rate"][source]["success"]
                failure = self.source_stats["success_rate"][source]["failure"]
                
                if success + failure > 0:
                    success_rate = success / (success + failure)
                    # 成功率が高いほど重みを小さく（優先度を高く）
                    self.source_weights[source] = 5.0 - 4.0 * success_rate
    
    def _get_prioritized_sources(self, ticker):
        """ティッカーに適したデータソースの優先順序を取得"""
        # 以前成功したソースがあればそれを最優先
        previously_successful_source = self.source_stats["tickers_by_source"].get(ticker)
        
        # ソースをウェイトの昇順（優先度の降順）でソート
        sorted_sources = sorted(
            [(source, weight) for source, weight in self.source_weights.items()],
            key=lambda x: x[1]
        )
        
        # 利用可能なソースのみをフィルタリング
        prioritized_sources = [s[0] for s in sorted_sources if s[0] in available_sources]
        
        # 以前成功したソースを最優先（存在し、利用可能な場合）
        if previously_successful_source and previously_successful_source in available_sources:
            if previously_successful_source in prioritized_sources:
                prioritized_sources.remove(previously_successful_source)
            prioritized_sources.insert(0, previously_successful_source)
        
        return prioritized_sources
    
    def _fetch_with_yfinance(self, ticker, start_date, end_date):
        """yfinanceを使用してデータを取得"""
        if "yfinance" not in available_sources:
            return None, "yfinance not available"
        
        try:
            start_time = time.time()
            self.logger.debug(f"yfinanceでの取得を試行: {ticker}, {start_date} - {end_date}")
            
            # yfinance Tickerオブジェクトを使用
            stock = yf.Ticker(ticker)
            
            # データ取得
            df = stock.history(
                start=start_date.strftime('%Y-%m-%d'),
                end=end_date.strftime('%Y-%m-%d'),
                interval="1d"
            )
            
            if df.empty:
                elapsed_time = time.time() - start_time
                self._update_source_stats(ticker, "yfinance", False, elapsed_time)
                return None, "Empty dataframe returned"
            
            # インデックスをリセット
            df = df.reset_index()
            
            # カラム名の標準化
            df = df.rename(columns={
                'Date': 'Date',
                'Open': 'Open',
                'High': 'High',
                'Low': 'Low',
                'Close': 'Close',
                'Volume': 'Volume'
            })
            
            elapsed_time = time.time() - start_time
            self._update_source_stats(ticker, "yfinance", True, elapsed_time)
            
            return df, None
            
        except Exception as e:
            elapsed_time = time.time() - start_time
            self._update_source_stats(ticker, "yfinance", False, elapsed_time)
            
            error_msg = str(e)
            self.logger.error(f"yfinanceでの取得エラー: {error_msg}")
            if self.debug:
                self.logger.debug(traceback.format_exc())
                
            return None, error_msg
    
    def _fetch_with_pandas_datareader(self, ticker, start_date, end_date):
        """pandas-datareaderを使用してデータを取得"""
        if "pandas_datareader" not in available_sources:
            return None, "pandas_datareader not available"
        
        try:
            start_time = time.time()
            self.logger.debug(f"pandas-datareaderでの取得を試行: {ticker}, {start_date} - {end_date}")
            
            # Yahoo Financeからデータを取得
            df = pdr.data.get_data_yahoo(
                ticker,
                start=start_date,
                end=end_date
            )
            
            if df.empty:
                elapsed_time = time.time() - start_time
                self._update_source_stats(ticker, "pandas_datareader", False, elapsed_time)
                return None, "Empty dataframe returned"
            
            # インデックスをリセット
            df = df.reset_index()
            
            elapsed_time = time.time() - start_time
            self._update_source_stats(ticker, "pandas_datareader", True, elapsed_time)
            
            return df, None
            
        except Exception as e:
            elapsed_time = time.time() - start_time
            self._update_source_stats(ticker, "pandas_datareader", False, elapsed_time)
            
            error_msg = str(e)
            self.logger.error(f"pandas-datareaderでの取得エラー: {error_msg}")
            if self.debug:
                self.logger.debug(traceback.format_exc())
                
            return None, error_msg
    
    def _fetch_with_stooq_scraper(self, ticker, start_date, end_date):
        """Stooq Webスクレイパーを使用してデータを取得"""
        if "stooq_scraper" not in available_sources:
            return None, "stooq_scraper not available"
        
        try:
            start_time = time.time()
            self.logger.debug(f"stooq_scraperでの取得を試行: {ticker}, {start_date} - {end_date}")
            
            # Stooq用にティッカーを変換
            stooq_ticker = ticker.replace(".T", ".JP")
            
            # スクレイパーモジュールを取得
            scraper_module = source_modules.get("stooq_scraper")
            if not hasattr(scraper_module, "get_stooq_data"):
                return None, "stooq_scraper module does not have get_stooq_data function"
            
            # データ取得
            df = scraper_module.get_stooq_data(
                stooq_ticker, 
                start_date.strftime('%Y-%m-%d'), 
                end_date.strftime('%Y-%m-%d')
            )
            
            if df is None or df.empty:
                elapsed_time = time.time() - start_time
                self._update_source_stats(ticker, "stooq_scraper", False, elapsed_time)
                return None, "Empty dataframe returned"
            
            elapsed_time = time.time() - start_time
            self._update_source_stats(ticker, "stooq_scraper", True, elapsed_time)
            
            return df, None
            
        except Exception as e:
            elapsed_time = time.time() - start_time
            self._update_source_stats(ticker, "stooq_scraper", False, elapsed_time)
            
            error_msg = str(e)
            self.logger.error(f"stooq_scraperでの取得エラー: {error_msg}")
            if self.debug:
                self.logger.debug(traceback.format_exc())
                
            return None, error_msg
    
    def _fetch_with_direct_request(self, ticker, start_date, end_date):
        """直接HTTPリクエストでデータを取得（最終手段）"""
        try:
            start_time = time.time()
            self.logger.debug(f"直接リクエストでの取得を試行: {ticker}, {start_date} - {end_date}")
            
            # Yahoo Finance APIパラメータを計算
            period1 = int(start_date.timestamp())
            period2 = int(end_date.timestamp())
            interval = '1d'
            
            url = f"https://query1.finance.yahoo.com/v7/finance/download/{ticker}?period1={period1}&period2={period2}&interval={interval}&events=history"
            headers = {
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
            }
            
            # リクエストを送信
            response = requests.get(url, headers=headers)
            
            if response.status_code != 200:
                elapsed_time = time.time() - start_time
                self._update_source_stats(ticker, "direct_request", False, elapsed_time)
                return None, f"HTTP error: {response.status_code}"
            
            # CSVとしてパース
            df = pd.read_csv(pd.StringIO(response.text))
            
            if df.empty:
                elapsed_time = time.time() - start_time
                self._update_source_stats(ticker, "direct_request", False, elapsed_time)
                return None, "Empty dataframe returned"
            
            # 日付を変換
            df['Date'] = pd.to_datetime(df['Date'])
            
            elapsed_time = time.time() - start_time
            self._update_source_stats(ticker, "direct_request", True, elapsed_time)
            
            return df, None
            
        except Exception as e:
            try:
                elapsed_time = time.time() - start_time
            except:
                elapsed_time = 0
                
            self._update_source_stats(ticker, "direct_request", False, elapsed_time)
            
            error_msg = str(e)
            self.logger.error(f"直接リクエストでの取得エラー: {error_msg}")
            if self.debug:
                self.logger.debug(traceback.format_exc())
                
            return None, error_msg
            
    def collect_ticker_data(self, ticker, start_year, end_year=None, max_attempts=3):
        """単一銘柄の過去データを収集"""
        if end_year is None:
            end_year = datetime.now().year
        
        self.logger.info(f"{ticker}の{start_year}年～{end_year}年のデータ収集を開始（試行回数: {max_attempts}）")
        
        # ティッカー情報を初期化
        if ticker not in self.status["tickers"]:
            self.status["tickers"][ticker] = {
                "completed_years": [],
                "total_records": 0,
                "last_update": None
            }
        
        all_year_data = []
        successful_years = 0
        failed_years = 0
        
        # 年ごとのデータを収集
        for year in range(start_year, end_year + 1):
            # すでに収集済みの場合はスキップ
            if year in self.status["tickers"][ticker]["completed_years"]:
                self.logger.info(f"{ticker}の{year}年データは既に収集済みです")
                successful_years += 1
                
                # キャッシュから読み込み
                year_start = datetime(year, 1, 1)
                year_end = datetime(year, 12, 31)
                if year == datetime.now().year:
                    year_end = datetime.now()
                
                cache_file = os.path.join(
                    self.cache_dir, 
                    f"{ticker}_{year_start.strftime('%Y%m%d')}_{year_end.strftime('%Y%m%d')}.csv"
                )
                
                if os.path.exists(cache_file):
                    try:
                        df = pd.read_csv(cache_file, parse_dates=["Date"])
                        all_year_data.append(df)
                    except Exception as e:
                        self.logger.error(f"{ticker}の{year}年キャッシュ読み込みエラー: {str(e)}")
                
                continue
            
            # 期間の設定
            start_date = datetime(year, 1, 1)
            end_date = datetime(year, 12, 31)
            if year == datetime.now().year:
                end_date = datetime.now()
            
            # キャッシュファイル名
            cache_file = os.path.join(
                self.cache_dir, 
                f"{ticker}_{start_date.strftime('%Y%m%d')}_{end_date.strftime('%Y%m%d')}.csv"
            )
            
            # キャッシュがあれば読み込み
            if os.path.exists(cache_file):
                try:
                    df = pd.read_csv(cache_file, parse_dates=["Date"])
                    self.logger.info(f"{ticker}の{year}年データをキャッシュから読み込みました")
                    
                    if not df.empty:
                        all_year_data.append(df)
                        successful_years += 1
                        
                        # ステータスを更新
                        if year not in self.status["tickers"][ticker]["completed_years"]:
                            self.status["tickers"][ticker]["completed_years"].append(year)
                            self.status["tickers"][ticker]["total_records"] += len(df)
                            self.status["tickers"][ticker]["last_update"] = datetime.now().isoformat()
                            self._save_status()
                        
                        continue
                except Exception as e:
                    self.logger.error(f"{ticker}の{year}年キャッシュ読み込みエラー: {str(e)}")
            
            # 複数のデータソースを試行
            df = None
            error_msgs = {}
            data_collected = False
            
            # このティッカーに最適なソースの優先順序を取得
            sources = self._get_prioritized_sources(ticker)
            
            # 各ソースを順番に試行
            for source in sources:
                for attempt in range(1, max_attempts + 1):
                    self.logger.info(f"{ticker}の{year}年データを{source}で取得（試行 {attempt}/{max_attempts}）")
                    
                    if source == "yfinance":
                        df, error = self._fetch_with_yfinance(ticker, start_date, end_date)
                    elif source == "pandas_datareader":
                        df, error = self._fetch_with_pandas_datareader(ticker, start_date, end_date)
                    elif source == "stooq_scraper":
                        df, error = self._fetch_with_stooq_scraper(ticker, start_date, end_date)
                    elif source == "direct_request":
                        df, error = self._fetch_with_direct_request(ticker, start_date, end_date)
                    else:
                        df, error = None, f"Unknown source: {source}"
                    
                    if df is not None and not df.empty:
                        data_collected = True
                        break
                    
                    error_msgs[source] = error
                    
                    # 待機してから再試行
                    if attempt < max_attempts:
                        wait_time = random.uniform(1.0, 3.0) * attempt  # 試行回数に応じて待機時間を増加
                        self.logger.info(f"再試行まで{wait_time:.1f}秒待機します...")
                        time.sleep(wait_time)
                
                if data_collected:
                    self.logger.info(f"{ticker}の{year}年データを{source}で取得しました")
                    break
            
            # データが取得できた場合
            if df is not None and not df.empty:
                # ティッカー情報を追加
                if "Ticker" not in df.columns:
                    df["Ticker"] = ticker
                
                # キャッシュに保存
                df.to_csv(cache_file, index=False)
                
                # 年月ディレクトリの作成と保存
                self._save_to_daily_structure(df, ticker, year)
                
                # 結果を保存
                all_year_data.append(df)
                successful_years += 1
                
                # ステータスを更新
                if year not in self.status["tickers"][ticker]["completed_years"]:
                    self.status["tickers"][ticker]["completed_years"].append(year)
                    self.status["tickers"][ticker]["total_records"] += len(df)
                    self.status["tickers"][ticker]["last_update"] = datetime.now().isoformat()
                    
                    # 年ステータスも更新
                    if str(year) not in self.status["years"]:
                        self.status["years"][str(year)] = {"completed": 0, "total": 0}
                    
                    self.status["years"][str(year)]["completed"] = self.status["years"][str(year)].get("completed", 0) + 1
                    self._save_status()
                
                self.logger.info(f"{ticker}の{year}年データ収集が完了しました: {len(df)}行")
            else:
                # すべてのソースが失敗した場合
                failed_years += 1
                self.logger.error(f"{ticker}の{year}年データを取得できませんでした")
                
                # 診断情報を保存
                self._save_error_diagnostics(ticker, year, error_msgs)
        
        # 成功と失敗の要約
        total_years = end_year - start_year + 1
        self.logger.info(f"{ticker}の{start_year}年～{end_year}年データ収集: 成功={successful_years}/{total_years}年, 失敗={failed_years}/{total_years}年")
        
        # 全年のデータを結合
        if all_year_data:
            all_df = pd.concat(all_year_data, ignore_index=True)
            all_df = all_df.drop_duplicates(subset=["Date"]).sort_values("Date")
            
            # 総レコード数を更新
            self.status["total_records"] += len(all_df)
            self._save_status()
            
            return len(all_df)
        else:
            return 0
    
    def _save_to_daily_structure(self, df, ticker, year):
        """データを日次ディレクトリ構造に保存"""
        if df.empty:
            return
        
        # 年月ごとにグループ化
        try:
            # DatetimeがDateという名前の場合もある
            date_col = "Datetime" if "Datetime" in df.columns else "Date"
            
            for (year_val, month), group in df.groupby([df[date_col].dt.year, df[date_col].dt.month]):
                dir_path = os.path.join(self.daily_dir, str(year_val), f"{year_val}-{month:02d}")
                os.makedirs(dir_path, exist_ok=True)
                
                file_path = os.path.join(dir_path, f"{ticker}.csv")
                
                # 既存データがあれば統合
                if os.path.exists(file_path):
                    existing_df = pd.read_csv(file_path, parse_dates=[date_col])
                    group = pd.concat([existing_df, group])
                    group = group.drop_duplicates(subset=[date_col]).sort_values(date_col)
                
                group.to_csv(file_path, index=False)
        except Exception as e:
            self.logger.error(f"日次ディレクトリへの保存エラー: {str(e)}")
            if self.debug:
                self.logger.debug(traceback.format_exc())
    
    def _save_error_diagnostics(self, ticker, year, error_msgs):
        """エラー診断情報を保存"""
        diagnostics = {
            "ticker": ticker,
            "year": year,
            "timestamp": datetime.now().isoformat(),
            "errors": error_msgs
        }
        
        # 診断ファイル
        diag_file = os.path.join(
            self.debug_dir, 
            f"{ticker}_{year}_error_diagnostics.json"
        )
        
        try:
            with open(diag_file, 'w', encoding='utf-8') as f:
                json.dump(diagnostics, f, indent=2, ensure_ascii=False)
            self.logger.info(f"エラー診断情報を保存しました: {diag_file}")
        except Exception as e:
            self.logger.error(f"診断情報の保存エラー: {str(e)}")
    
    def collect_sector_data(self, sector_name, start_year, end_year=None, sleep_time=10):
        """セクター内の全銘柄のデータを収集"""
        if end_year is None:
            end_year = datetime.now().year
        
        # セクターに属する銘柄を取得
        tickers = get_tickers_by_sector(sector_name)
        if not tickers:
            self.logger.warning(f"セクター「{sector_name}」に銘柄が見つかりません")
            return 0
        
        self.logger.info(f"セクター「{sector_name}」の{len(tickers)}銘柄の収集を開始: {start_year}年～{end_year}年")
        
        # セクター情報を初期化
        if sector_name not in self.status["sectors"]:
            self.status["sectors"][sector_name] = {
                "total_tickers": len(tickers),
                "completed_tickers": 0,
                "total_records": 0,
                "last_update": None
            }
        
        # 各銘柄のデータを収集
        total_records = 0
        completed = 0
        
        for i, ticker in enumerate(tqdm(tickers, desc=f"セクター「{sector_name}」")):
            # 銘柄間の待機時間をランダム化してAPI制限を回避
            if i > 0:  # 最初の銘柄の前には待機しない
                actual_sleep = sleep_time * (0.5 + random.random())  # 50%～150%のランダム化
                self.logger.debug(f"次の銘柄の処理まで{actual_sleep:.1f}秒待機します")
                time.sleep(actual_sleep)
            
            # データ収集
            records = self.collect_ticker_data(ticker, start_year, end_year)
            total_records += records
            
            if records > 0:
                completed += 1
        
        # ステータスを更新
        self.status["sectors"][sector_name]["completed_tickers"] = completed
        self.status["sectors"][sector_name]["total_records"] = total_records
        self.status["sectors"][sector_name]["last_update"] = datetime.now().isoformat()
        self._save_status()
        
        self.logger.info(f"セクター「{sector_name}」の収集が完了しました: {completed}/{len(tickers)}銘柄, {total_records}レコード")
        
        return total_records
    
    def collect_all_sectors(self, start_year, end_year=None, sleep_between_sectors=300):
        """全セクターのデータを収集"""
        if end_year is None:
            end_year = datetime.now().year
        
        # セクターのリストを取得
        sectors = get_sector_names()
        self.logger.info(f"全{len(sectors)}セクターの収集を開始: {start_year}年～{end_year}年")
        
        total_records = 0
        
        # 各セクターの処理
        for i, sector in enumerate(sectors):
            # セクターのデータを収集
            records = self.collect_sector_data(sector, start_year, end_year)
            total_records += records
            
            # セクター間の待機
            if i < len(sectors) - 1:  # 最後のセクター以外
                self.logger.info(f"次のセクターの処理まで{sleep_between_sectors/60:.1f}分待機します")
                time.sleep(sleep_between_sectors)
        
        # 総銘柄数を更新
        self.status["total_symbols"] = len(get_all_tickers())
        
        # 完了した年の記録
        for year in range(start_year, end_year + 1):
            year_str = str(year)
            if year_str not in self.status["completed_years"]:
                self.status["completed_years"].append(year_str)
        
        self._save_status()
        
        self.logger.info(f"全セクターの収集が完了しました: {start_year}年～{end_year}年, 合計{total_records}レコード")
        
        return total_records
    
    def collect_major_tickers(self, start_year, end_year=None, sleep_time=10):
        """主要銘柄のデータを収集"""
        if end_year is None:
            end_year = datetime.now().year
        
        # 主要銘柄を取得
        tickers = get_major_tickers()
        self.logger.info(f"主要{len(tickers)}銘柄の収集を開始: {start_year}年～{end_year}年")
        
        total_records = 0
        completed = 0
        
        # 各銘柄のデータを収集
        for i, ticker in enumerate(tqdm(tickers, desc="主要銘柄")):
            # 銘柄間の待機
            if i > 0:
                time.sleep(sleep_time)
            
            records = self.collect_ticker_data(ticker, start_year, end_year)
            total_records += records
            
            if records > 0:
                completed += 1
        
        self.logger.info(f"主要銘柄の収集が完了しました: {completed}/{len(tickers)}銘柄, {total_records}レコード")
        
        return total_records
    
    def create_consolidated_file(self, year=None):
        """統合ファイルの作成"""
        # 読み込むファイルを検索
        all_files = []
        if year:
            # 特定の年のみ
            search_dir = os.path.join(self.daily_dir, str(year))
            if os.path.exists(search_dir):
                for root, _, files in os.walk(search_dir):
                    for file in files:
                        if file.endswith(".csv"):
                            all_files.append(os.path.join(root, file))
            
            output_file = os.path.join(self.consolidated_dir, f"nikkei225_daily_{year}.csv")
        else:
            # 全期間
            for root, _, files in os.walk(self.daily_dir):
                for file in files:
                    if file.endswith(".csv"):
                        all_files.append(os.path.join(root, file))
            
            output_file = os.path.join(self.consolidated_dir, "nikkei225_daily_all.csv")
        
        if not all_files:
            self.logger.warning("統合するファイルが見つかりません")
            return None
        
        self.logger.info(f"{len(all_files)}ファイルを統合します")
        
        # 全データを読み込み
        all_data = []
        read_errors = 0
        
        for file in tqdm(all_files, desc="ファイル読み込み"):
            try:
                df = pd.read_csv(file)
                
                # 日付カラムの特定
                date_column = None
                for col in ['Datetime', 'Date', 'date', 'datetime']:
                    if col in df.columns:
                        date_column = col
                        break
                
                if date_column:
                    df[date_column] = pd.to_datetime(df[date_column])
                
                all_data.append(df)
            except Exception as e:
                self.logger.error(f"{file}の読み込みエラー: {str(e)}")
                read_errors += 1
        
        if read_errors > 0:
            self.logger.warning(f"{read_errors}ファイルの読み込みに失敗しました")
        
        if not all_data:
            self.logger.warning("有効なデータがありません")
            return None
        
        # 結合
        combined_data = pd.concat(all_data, ignore_index=True)
        
        # ティッカーと日付カラムの特定
        ticker_col = "Ticker" if "Ticker" in combined_data.columns else None
        date_col = None
        for col in ['Datetime', 'Date', 'date', 'datetime']:
            if col in combined_data.columns:
                date_col = col
                break
        
        # 重複を削除
        if ticker_col and date_col:
            combined_data = combined_data.drop_duplicates(subset=[ticker_col, date_col]).sort_values([ticker_col, date_col])
        elif date_col:
            combined_data = combined_data.drop_duplicates(subset=[date_col]).sort_values(date_col)
        
        # 保存
        combined_data.to_csv(output_file, index=False)
        
        ticker_count = combined_data[ticker_col].nunique() if ticker_col else 0
        self.logger.info(f"統合ファイルを保存しました: {output_file}, {len(combined_data)}行, {ticker_count}銘柄")
        
        return output_file
    
    def get_collection_status(self):
        """収集状況のサマリーを取得"""
        all_tickers = get_all_tickers()
        
        # 収集済み銘柄数
        collected_tickers = len(self.status["tickers"])
        
        # 完了した年
        completed_years = sorted(list(set([
            year for ticker_info in self.status["tickers"].values()
            for year in ticker_info["completed_years"]
        ])))
        
        # セクター別進捗
        sector_progress = {}
        for sector in get_sector_names():
            sector_tickers = get_tickers_by_sector(sector)
            collected = sum(1 for ticker in sector_tickers if ticker in self.status["tickers"])
            sector_progress[sector] = {
                "total": len(sector_tickers),
                "collected": collected,
                "percentage": round(collected / len(sector_tickers) * 100, 1) if sector_tickers else 0
            }
        
        # 年別進捗
        year_progress = {}
        current_year = datetime.now().year
        for year in range(current_year - 10, current_year + 1):
            year_str = str(year)
            if year_str in self.status["years"]:
                total_possible = len(all_tickers)
                collected = self.status["years"][year_str].get("completed", 0)
                year_progress[year_str] = {
                    "total_possible": total_possible,
                    "collected": collected,
                    "percentage": round(collected / total_possible * 100, 1) if total_possible else 0
                }
        
        # データソース統計
        source_stats = {}
        for source, stats in self.source_stats["success_rate"].items():
            total_attempts = stats["success"] + stats["failure"]
            if total_attempts > 0:
                success_rate = stats["success"] / total_attempts * 100
                source_stats[source] = {
                    "success_rate": round(success_rate, 1),
                    "total_attempts": total_attempts,
                    "avg_time": round(self.source_stats["average_time"][source], 2)
                }
        
        return {
            "total_tickers": len(all_tickers),
            "collected_tickers": collected_tickers,
            "completion_percentage": round(collected_tickers / len(all_tickers) * 100, 1) if all_tickers else 0,
            "total_records": self.status["total_records"],
            "completed_years": completed_years,
            "sector_progress": sector_progress,
            "year_progress": year_progress,
            "data_sources": source_stats,
            "last_update": self.status["last_update"]
        }
    
    def display_collection_status(self):
        """収集状況を表示"""
        status = self.get_collection_status()
        
        print("\n===== 日経225データ収集状況 =====")
        print(f"総銘柄数: {status['total_tickers']}")
        print(f"収集済み銘柄数: {status['collected_tickers']} ({status['completion_percentage']}%)")
        print(f"総レコード数: {status['total_records']}")
        
        if status["completed_years"]:
            print(f"完了した年: {', '.join(map(str, sorted(status['
#!/usr/bin/env python3
"""
日経225全銘柄データ収集の改良版スクリプト

主な改良点:
1. 複数のデータソースを順次試行（yfinance, pandas-datareader, 直接API, stooq_web_scraper）
2. 詳細なエラーログと診断情報
3. 堅牢なエラーハンドリングと回復メカニズム
4. より効率的なキャッシング
5. スマートなリトライ機能
"""

import os
import logging
import json
import time
import argparse
import sys
from datetime import datetime, timedelta
import pandas as pd
import numpy as np
import random
import traceback
import requests
from tqdm import tqdm
from pathlib import Path

# 利用可能なデータソースを動的にインポート
available_sources = []
source_modules = {}

# Pandas DataReader
try:
    import pandas_datareader as pdr
    available_sources.append("pandas_datareader")
    source_modules["pandas_datareader"] = pdr
except ImportError:
    pass

# yfinance
try:
    import yfinance as yf
    available_sources.append("yfinance")
    source_modules["yfinance"] = yf
except ImportError:
    pass

# 既存のStooq Webスクレイパーを条件付きインポート
try:
    import stooq_web_scraper_fixed
    available_sources.append("stooq_scraper")
    source_modules["stooq_scraper"] = stooq_web_scraper_fixed
except ImportError:
    try:
        import stooq_web_scraper
        available_sources.append("stooq_scraper")
        source_modules["stooq_scraper"] = stooq_web_scraper
    except ImportError:
        pass

# 日経225銘柄情報
try:
    from src.utils.nikkei225_tickers import (
        get_all_tickers, 
        get_tickers_by_sector, 
        get_sector_names,
        get_major_tickers,
        get_topix_core30
    )
except ImportError:
    # ティッカー情報がなければ簡易的な関数を定義
    def get_all_tickers():
        return ["8306.T", "9432.T", "9984.T", "6758.T", "7203.T"]
    
    def get_tickers_by_sector(sector_name):
        return get_all_tickers()
    
    def get_sector_names():
        return ["全銘柄"]
    
    def get_major_tickers():
        return get_all_tickers()
    
    def get_topix_core30():
        return get_all_tickers()[:10]

# ロギング設定
def setup_logging(debug=False):
    """ロギングを設定"""
    log_level = logging.DEBUG if debug else logging.INFO
    log_dir = "logs"
    os.makedirs(log_dir, exist_ok=True)
    
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    log_file = f"{log_dir}/nikkei_data_collector_{timestamp}.log"
    
    logging.basicConfig(
        level=log_level,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file),
            logging.StreamHandler()
        ]
    )
    
    logger = logging.getLogger(__name__)
    logger.info(f"ログ出力先: {log_file}")
    logger.info(f"利用可能なデータソース: {', '.join(available_sources)}")
    
    return logger

class ImprovedDataCollector:
    """日経225全銘柄の株価データを収集する改良版クラス"""
    
    def __init__(self, base_dir="nikkei225_improved_data", cache_dir="data/improved_cache", 
                 max_retries=3, debug=False):
        """
        初期化
        
        Parameters:
        -----------
        base_dir : str
            データ保存先のベースディレクトリ
        cache_dir : str
            キャッシュディレクトリ
        max_retries : int
            取得失敗時の最大リトライ回数
        debug : bool
            デバッグモードの有効化
        """
        self.base_dir = base_dir
        self.cache_dir = cache_dir
        self.max_retries = max_retries
        self.debug = debug
        
        # ロギング設定
        self.logger = logging.getLogger(__name__)
        
        # ディレクトリ構造
        self.daily_dir = os.path.join(base_dir, "daily")
        self.metadata_dir = os.path.join(base_dir, "metadata")
        self.consolidated_dir = os.path.join(base_dir, "consolidated")
        self.debug_dir = os.path.join(base_dir, "debug")
        
        # ディレクトリ作成
        for dir_path in [self.daily_dir, self.metadata_dir, self.consolidated_dir, 
                          self.cache_dir, self.debug_dir]:
            os.makedirs(dir_path, exist_ok=True)
        
        # ステータスファイル
        self.status_file = os.path.join(self.metadata_dir, "collection_status.json")
        self.status = self._load_status()
        
        # データソース統計
        self.source_stats = self._load_source_stats()
        
        # リクエスト制限を避けるための調整可能なウェイト
        self.source_weights = {
            "yfinance": 1.0,        # ウェイト値が低いほど優先度が高い
            "pandas_datareader": 2.0,
            "direct_request": 3.0,
            "stooq_scraper": 4.0
        }
    
    def _load_status(self):
        """収集ステータスの読み込み"""
        if os.path.exists(self.status_file):
            try:
                with open(self.status_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception as e:
                self.logger.error(f"ステータス読み込みエラー: {str(e)}")
        
        # デフォルト値
        return {
            "sectors": {},
            "years": {},
            "tickers": {},
            "last_update": None,
            "total_records": 0,
            "total_symbols": 0,
            "completed_years": []
        }
    
    def _save_status(self):
        """収集ステータスの保存"""
        try:
            # 最終更新日時を更新
            self.status["last_update"] = datetime.now().isoformat()
            
            with open(self.status_file, 'w', encoding='utf-8') as f:
                json.dump(self.status, f, indent=2, ensure_ascii=False)
                
            self.logger.debug("ステータスファイルを保存しました")
        except Exception as e:
            self.logger.error(f"ステータス保存エラー: {str(e)}")
    
    def _load_source_stats(self):
        """データソース統計情報の読み込み"""
        stats_file = os.path.join(self.metadata_dir, "source_stats.json")
        
        if os.path.exists(stats_file):
            try:
                with open(stats_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception as e:
                self.logger.error(f"データソース統計の読み込みエラー: {str(e)}")
        
        # デフォルト値
        return {
            "success_rate": {
                "yfinance": {"success": 0, "failure": 0},
                "pandas_datareader": {"success": 0, "failure": 0},
                "direct_request": {"success": 0, "failure": 0},
                "stooq_scraper": {"success": 0, "failure": 0}
            },
            "average_time": {
                "yfinance": 0,
                "pandas_datareader": 0,
                "direct_request": 0,
                "stooq_scraper": 0
            },
            "tickers_by_source": {},
            "last_update": None
        }
    
    def _save_source_stats(self):
        """データソース統計情報の保存"""
        try:
            # 最終更新日時を更新
            self.source_stats["last_update"] = datetime.now().isoformat()
            
            stats_file = os.path.join(self.metadata_dir, "source_stats.json")
            with open(stats_file, 'w', encoding='utf-8') as f:
                json.dump(self.source_stats, f, indent=2, ensure_ascii=False)
                
            self.logger.debug("データソース統計を保存しました")
        except Exception as e:
            self.logger.error(f"データソース統計の保存エラー: {str(e)}")
    
    def _update_source_stats(self, ticker, source, success, elapsed_time=0):
        """データソース統計情報を更新"""
        # 成功/失敗カウントの更新
        if success:
            self.source_stats["success_rate"][source]["success"] += 1
        else:
            self.source_stats["success_rate"][source]["failure"] += 1
        
        # 平均時間の更新
        total_attempts = (self.source_stats["success_rate"][source]["success"] + 
                           self.source_stats["success_rate"][source]["failure"])
        
        current_avg = self.source_stats["average_time"][source]
        if total_attempts > 1:
            new_avg = current_avg + (elapsed_time - current_avg) / total_attempts
        else:
            new_avg = elapsed_time
            
        self.source_stats["average_time"][source] = new_avg
        
        # ティッカーごとの最適ソースを記録
        if success:
            self.source_stats["tickers_by_source"][ticker] = source
        
        # 統計情報を保存
        self._save_source_stats()
        
        # ソースウェイトの調整（成功率に基づく）
        self._adjust_source_weights()
    
    def _adjust_source_weights(self):
        """データソース優先度の調整"""
        for source in self.source_weights.keys():
            if source in self.source_stats["success_rate"]:
                success = self.source_stats["success_rate"][source]["success"]
                failure = self.source_stats["success_rate"][source]["failure"]
                
                if success + failure > 0:
                    success_rate = success / (success + failure)
                    # 成功率が高いほど重みを小さく（優先度を高く）
                    self.source_weights[source] = 5.0 - 4.0 * success_rate
    
    def _get_prioritized_sources(self, ticker):
        """ティッカーに適したデータソースの優先順序を取得"""
        # 以前成功したソースがあればそれを最優先
        previously_successful_source = self.source_stats["tickers_by_source"].get(ticker)
        
        # ソースをウェイトの昇順（優先度の降順）でソート
        sorted_sources = sorted(
            [(source, weight) for source, weight in self.source_weights.items()],
            key=lambda x: x[1]
        )
        
        # 利用可能なソースのみをフィルタリング
        prioritized_sources = [s[0] for s in sorted_sources if s[0] in available_sources]
        
        # 以前成功したソースを最優先（存在し、利用可能な場合）
        if previously_successful_source and previously_successful_source in available_sources:
            if previously_successful_source in prioritized_sources:
                prioritized_sources.remove(previously_successful_source)
            prioritized_sources.insert(0, previously_successful_source)
        
        return prioritized_sources
    
    def _fetch_with_yfinance(self, ticker, start_date, end_date):
        """yfinanceを使用してデータを取得"""
        if "yfinance" not in available_sources:
            return None, "yfinance not available"
        
        try:
            start_time = time.time()
            self.logger.debug(f"yfinanceでの取得を試行: {ticker}, {start_date} - {end_date}")
            
            # yfinance Tickerオブジェクトを使用
            stock = yf.Ticker(ticker)
            
            # データ取得
            df = stock.history(
                start=start_date.strftime('%Y-%m-%d'),
                end=end_date.strftime('%Y-%m-%d'),
                interval="1d"
            )
            
            if df.empty:
                elapsed_time = time.time() - start_time
                self._update_source_stats(ticker, "yfinance", False, elapsed_time)
                return None, "Empty dataframe returned"
            
            # インデックスをリセット
            df = df.reset_index()
            
            # カラム名の標準化
            df = df.rename(columns={
                'Date': 'Date',
                'Open': 'Open',
                'High': 'High',
                'Low': 'Low',
                'Close': 'Close',
                'Volume': 'Volume'
            })
            
            elapsed_time = time.time() - start_time
            self._update_source_stats(ticker, "yfinance", True, elapsed_time)
            
            return df, None
            
        except Exception as e:
            elapsed_time = time.time() - start_time
            self._update_source_stats(ticker, "yfinance", False, elapsed_time)
            
            error_msg = str(e)
            self.logger.error(f"yfinanceでの取得エラー: {error_msg}")
            if self.debug:
                self.logger.debug(traceback.format_exc())
                
            return None, error_msg
    
    def _fetch_with_pandas_datareader(self, ticker, start_date, end_date):
        """pandas-datareaderを使用してデータを取得"""
        if "pandas_datareader" not in available_sources:
            return None, "pandas_datareader not available"
        
        try:
            start_time
