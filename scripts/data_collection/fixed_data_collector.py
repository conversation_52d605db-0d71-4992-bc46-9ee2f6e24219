#!/usr/bin/env python3
"""
日経225データ収集の問題を診断・修正するスクリプト

主な修正点:
1. 詳細なデバッグ情報を記録
2. より堅牢なエラーハンドリング
3. 複数のデータソースを順番に試行
4. APIリクエスト制限への適切な対応
"""

import os
import logging
import json
import time
import argparse
import sys
from datetime import datetime, timedelta
import pandas as pd
import numpy as np
import random
import pandas_datareader as pdr
import requests
import yfinance as yf
from tqdm import tqdm
import traceback

# ロギング設定
log_dir = "logs/debug"
os.makedirs(log_dir, exist_ok=True)
logging.basicConfig(
    level=logging.DEBUG,  # DEBUGレベルに設定して詳細情報を取得
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f"{log_dir}/data_collection_debug_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class DebugDataCollector:
    """日経225データ収集の問題を診断するクラス"""
    
    def __init__(self, ticker, start_year=2020, end_year=2025, output_dir="debug_data"):
        """初期化"""
        self.ticker = ticker
        self.start_year = start_year
        self.end_year = end_year
        self.output_dir = output_dir
        self.debug_info = {
            "ticker": ticker,
            "attempts": [],
            "success": False,
            "final_method": None,
            "error_details": {}
        }
        
        # 出力ディレクトリの作成
        os.makedirs(output_dir, exist_ok=True)
    
    def _fetch_with_yfinance(self, start_date, end_date):
        """yfinanceライブラリを使用してデータを取得"""
        logger.debug(f"yfinanceでの取得を試行: {self.ticker}, {start_date} - {end_date}")
        try:
            # yfinance独自のTickerオブジェクトを使用
            yf_ticker = yf.Ticker(self.ticker)
            
            # 詳細なデバッグ情報
            logger.debug(f"yfinance Tickerオブジェクト: {yf_ticker}")
            
            # ティッカー情報の取得を試行
            try:
                info = yf_ticker.info
                logger.debug(f"ティッカー情報取得成功: {list(info.keys()) if info else 'No info'}")
            except Exception as e:
                logger.debug(f"ティッカー情報取得エラー: {str(e)}")
            
            # データ取得
            df = yf_ticker.history(
                start=start_date.strftime('%Y-%m-%d'),
                end=end_date.strftime('%Y-%m-%d'),
                interval="1d"
            )
            
            if df.empty:
                logger.warning(f"yfinanceからの取得結果が空です: {self.ticker}")
                return None
            
            # カラム名の標準化
            df = df.rename(columns={
                'Open': 'Open', 
                'High': 'High',
                'Low': 'Low',
                'Close': 'Close',
                'Volume': 'Volume',
                'Dividends': 'Dividends',
                'Stock Splits': 'Stock_Splits'
            })
            
            # インデックスをリセット
            df = df.reset_index()
            df = df.rename(columns={'Date': 'Date'})
            
            logger.info(f"yfinanceでの取得成功: {self.ticker}, 行数: {len(df)}")
            
            self.debug_info["attempts"].append({
                "method": "yfinance",
                "success": True,
                "records": len(df),
                "timestamp": datetime.now().isoformat()
            })
            
            self.debug_info["success"] = True
            self.debug_info["final_method"] = "yfinance"
            
            return df
        
        except Exception as e:
            error_msg = str(e)
            trace = traceback.format_exc()
            logger.error(f"yfinanceでの取得エラー: {error_msg}\n{trace}")
            
            self.debug_info["attempts"].append({
                "method": "yfinance",
                "success": False,
                "error": error_msg,
                "traceback": trace,
                "timestamp": datetime.now().isoformat()
            })
            
            self.debug_info["error_details"]["yfinance"] = {
                "message": error_msg,
                "traceback": trace
            }
            
            return None
    
    def _fetch_with_pandas_datareader_yahoo(self, start_date, end_date):
        """pandas-datareaderを使用してYahoo Financeからデータを取得"""
        logger.debug(f"pandas-datareader(Yahoo)での取得を試行: {self.ticker}, {start_date} - {end_date}")
        try:
            df = pdr.data.get_data_yahoo(
                self.ticker,
                start=start_date,
                end=end_date
            )
            
            if df.empty:
                logger.warning(f"pandas-datareaderからの取得結果が空です: {self.ticker}")
                return None
            
            # インデックスをリセット
            df = df.reset_index()
            
            logger.info(f"pandas-datareader(Yahoo)での取得成功: {self.ticker}, 行数: {len(df)}")
            
            self.debug_info["attempts"].append({
                "method": "pandas_datareader_yahoo",
                "success": True,
                "records": len(df),
                "timestamp": datetime.now().isoformat()
            })
            
            self.debug_info["success"] = True
            self.debug_info["final_method"] = "pandas_datareader_yahoo"
            
            return df
        
        except Exception as e:
            error_msg = str(e)
            trace = traceback.format_exc()
            logger.error(f"pandas-datareader(Yahoo)での取得エラー: {error_msg}\n{trace}")
            
            self.debug_info["attempts"].append({
                "method": "pandas_datareader_yahoo",
                "success": False,
                "error": error_msg,
                "traceback": trace,
                "timestamp": datetime.now().isoformat()
            })
            
            self.debug_info["error_details"]["pandas_datareader_yahoo"] = {
                "message": error_msg,
                "traceback": trace
            }
            
            return None
    
    def _fetch_with_pandas_datareader_stooq(self, start_date, end_date):
        """pandas-datareaderを使用してStooqからデータを取得"""
        logger.debug(f"pandas-datareader(Stooq)での取得を試行: {self.ticker}, {start_date} - {end_date}")
        try:
            # Stooq用にティッカーを変換
            stooq_ticker = self.ticker.replace(".T", ".JP")
            
            df = pdr.data.get_data_stooq(
                stooq_ticker,
                start=start_date,
                end=end_date
            )
            
            if df.empty:
                logger.warning(f"pandas-datareader(Stooq)からの取得結果が空です: {stooq_ticker}")
                return None
            
            # インデックスをリセット
            df = df.reset_index()
            
            logger.info(f"pandas-datareader(Stooq)での取得成功: {stooq_ticker}, 行数: {len(df)}")
            
            self.debug_info["attempts"].append({
                "method": "pandas_datareader_stooq",
                "success": True,
                "records": len(df),
                "timestamp": datetime.now().isoformat()
            })
            
            self.debug_info["success"] = True
            self.debug_info["final_method"] = "pandas_datareader_stooq"
            
            return df
        
        except Exception as e:
            error_msg = str(e)
            trace = traceback.format_exc()
            logger.error(f"pandas-datareader(Stooq)での取得エラー: {error_msg}\n{trace}")
            
            self.debug_info["attempts"].append({
                "method": "pandas_datareader_stooq",
                "success": False,
                "error": error_msg,
                "traceback": trace,
                "timestamp": datetime.now().isoformat()
            })
            
            self.debug_info["error_details"]["pandas_datareader_stooq"] = {
                "message": error_msg,
                "traceback": trace
            }
            
            return None
    
    def _fetch_with_direct_request(self, start_date, end_date):
        """直接HTTPリクエストによるデータ取得（最終手段）"""
        logger.debug(f"直接リクエストでの取得を試行: {self.ticker}, {start_date} - {end_date}")
        try:
            # Yahoo Finance APIパラメータを計算
            ticker = self.ticker
            period1 = int(start_date.timestamp())
            period2 = int(end_date.timestamp())
            interval = '1d'
            
            url = f"https://query1.finance.yahoo.com/v7/finance/download/{ticker}?period1={period1}&period2={period2}&interval={interval}&events=history"
            headers = {
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
            }
            
            # リクエストを送信
            response = requests.get(url, headers=headers)
            
            if response.status_code != 200:
                logger.error(f"直接リクエストのステータスコードが不正: {response.status_code}")
                logger.debug(f"レスポンス内容: {response.text[:1000]}")  # 最初の1000文字だけログに記録
                return None
            
            # CSVとしてパース
            try:
                df = pd.read_csv(pd.StringIO(response.text))
                
                if df.empty:
                    logger.warning(f"直接リクエストからの取得結果が空です: {self.ticker}")
                    return None
                
                # 日付を変換
                df['Date'] = pd.to_datetime(df['Date'])
                
                logger.info(f"直接リクエストでの取得成功: {self.ticker}, 行数: {len(df)}")
                
                self.debug_info["attempts"].append({
                    "method": "direct_request",
                    "success": True,
                    "records": len(df),
                    "timestamp": datetime.now().isoformat()
                })
                
                self.debug_info["success"] = True
                self.debug_info["final_method"] = "direct_request"
                
                return df
            except Exception as e:
                logger.error(f"レスポンスのCSVパースエラー: {str(e)}")
                return None
            
        except Exception as e:
            error_msg = str(e)
            trace = traceback.format_exc()
            logger.error(f"直接リクエストでの取得エラー: {error_msg}\n{trace}")
            
            self.debug_info["attempts"].append({
                "method": "direct_request",
                "success": False,
                "error": error_msg,
                "traceback": trace,
                "timestamp": datetime.now().isoformat()
            })
            
            self.debug_info["error_details"]["direct_request"] = {
                "message": error_msg,
                "traceback": trace
            }
            
            return None
    
    def collect_all_years(self):
        """指定された期間の全年のデータを収集"""
        all_data = []
        
        for year in range(self.start_year, self.end_year + 1):
            logger.info(f"{self.ticker}の{year}年データ収集を開始")
            
            # 期間の設定
            start_date = datetime(year, 1, 1)
            end_date = datetime(year, 12, 31)
            if year == datetime.now().year:
                end_date = datetime.now()
            
            # キャッシュパスの設定
            cache_dir = os.path.join(self.output_dir, "cache")
            os.makedirs(cache_dir, exist_ok=True)
            cache_file = os.path.join(
                cache_dir, 
                f"{self.ticker}_{start_date.strftime('%Y%m%d')}_{end_date.strftime('%Y%m%d')}.csv"
            )
            
            # キャッシュがあれば読み込み
            if os.path.exists(cache_file):
                df = pd.read_csv(cache_file, parse_dates=["Date"])
                logger.info(f"{self.ticker}の{year}年データをキャッシュから読み込みました")
                
                if not df.empty:
                    all_data.append(df)
                    continue
            
            # 複数の方法を順番に試行
            df = None
            
            # 1. yfinanceを試行
            df = self._fetch_with_yfinance(start_date, end_date)
            if df is not None and not df.empty:
                df.to_csv(cache_file, index=False)
                all_data.append(df)
                time.sleep(random.uniform(0.5, 1.5))  # APIレート制限を回避
                continue
            
            # 短い待機
            time.sleep(random.uniform(1.0, 2.0))
            
            # 2. pandas-datareader(Yahoo)を試行
            df = self._fetch_with_pandas_datareader_yahoo(start_date, end_date)
            if df is not None and not df.empty:
                df.to_csv(cache_file, index=False)
                all_data.append(df)
                time.sleep(random.uniform(0.5, 1.5))  # APIレート制限を回避
                continue
            
            # 短い待機
            time.sleep(random.uniform(1.0, 2.0))
            
            # 3. pandas-datareader(Stooq)を試行
            df = self._fetch_with_pandas_datareader_stooq(start_date, end_date)
            if df is not None and not df.empty:
                df.to_csv(cache_file, index=False)
                all_data.append(df)
                time.sleep(random.uniform(0.5, 1.5))  # APIレート制限を回避
                continue
            
            # 短い待機
            time.sleep(random.uniform(2.0, 3.0))
            
            # 4. 直接HTTPリクエストを試行（最終手段）
            df = self._fetch_with_direct_request(start_date, end_date)
            if df is not None and not df.empty:
                df.to_csv(cache_file, index=False)
                all_data.append(df)
                time.sleep(random.uniform(1.0, 2.0))  # APIレート制限を回避
                continue
            
            # すべての方法が失敗した場合
            logger.error(f"{self.ticker}の{year}年データをどの方法でも取得できませんでした")
        
        # 診断情報の保存
        self._save_debug_info()
        
        # 全年のデータを結合
        if all_data:
            combined_df = pd.concat(all_data, ignore_index=True)
            combined_df = combined_df.drop_duplicates(subset=["Date"]).sort_values("Date")
            
            # 結合データを保存
            output_file = os.path.join(self.output_dir, f"{self.ticker}_all_data.csv")
            combined_df.to_csv(output_file, index=False)
            
            logger.info(f"{self.ticker}の全期間データを保存しました: {output_file}, {len(combined_df)}行")
            return combined_df
        else:
            logger.error(f"{self.ticker}のデータが取得できませんでした")
            return None
    
    def _save_debug_info(self):
        """診断情報をJSONファイルに保存"""
        debug_file = os.path.join(self.output_dir, f"{self.ticker}_debug_info.json")
        
        try:
            with open(debug_file, 'w', encoding='utf-8') as f:
                json.dump(self.debug_info, f, indent=2, ensure_ascii=False)
            logger.info(f"診断情報を保存しました: {debug_file}")
        except Exception as e:
            logger.error(f"診断情報の保存エラー: {str(e)}")

def parse_args():
    """コマンドライン引数を解析"""
    parser = argparse.ArgumentParser(description='日経225データ収集の問題を診断')
    parser.add_argument('--ticker', type=str, required=True, help='ティッカーシンボル（例: 8306.T）')
    parser.add_argument('--start-year', type=int, default=2020, help='開始年')
    parser.add_argument('--end-year', type=int, default=2025, help='終了年')
    parser.add_argument('--output-dir', type=str, default="debug_data", help='出力ディレクトリ')
    
    return parser.parse_args()

def main():
    """メイン関数"""
    # 引数の解析
    args = parse_args()
    
    logger.info(f"==== {args.ticker}のデータ収集診断を開始 ====")
    
    # データ収集の実行
    collector = DebugDataCollector(
        ticker=args.ticker,
        start_year=args.start_year,
        end_year=args.end_year,
        output_dir=args.output_dir
    )
    
    df = collector.collect_all_years()
    
    if df is not None:
        print(f"収集成功: {args.ticker}, {len(df)}行のデータを取得しました")
    else:
        print(f"収集失敗: {args.ticker}のデータを取得できませんでした")
        sys.exit(1)

if __name__ == "__main__":
    main()
