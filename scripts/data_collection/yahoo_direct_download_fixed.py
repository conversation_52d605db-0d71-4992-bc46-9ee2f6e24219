#!/usr/bin/env python3
"""
Yahoo Financeから直接CSVファイルをダウンロードするシンプルなスクリプト
"""

import os
import sys
import time
import random
import logging
import argparse
import requests
import pandas as pd
from datetime import datetime, timedelta
from tqdm import tqdm

# ロギング設定
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f"logs/yahoo_download_{datetime.now().strftime('%Y%m%d')}.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# 保存ディレクトリの設定
DATA_DIR = "nikkei225_full_data"
CACHE_DIR = os.path.join(DATA_DIR, "cache")

# 日経225銘柄を取得
sys.path.append('.')
from nikkei225_tickers import get_all_tickers, get_tickers_by_sector, get_sector_names

def ensure_dirs():
    """必要なディレクトリを作成"""
    os.makedirs(DATA_DIR, exist_ok=True)
    os.makedirs(CACHE_DIR, exist_ok=True)
    os.makedirs("logs", exist_ok=True)
    os.makedirs(os.path.join(DATA_DIR, "daily"), exist_ok=True)
    os.makedirs(os.path.join(DATA_DIR, "consolidated"), exist_ok=True)
    os.makedirs(os.path.join(DATA_DIR, "metadata"), exist_ok=True)


def get_unix_timestamp(dt):
    """日時をUnixタイムスタンプに変換"""
    return int(dt.timestamp())


def download_historical_data(ticker, start_date, end_date, sleep_time=2, retries=3):
    """
    Yahoo FinanceからCSVを直接ダウンロード

    Parameters:
    -----------
    ticker : str
        ティッカーシンボル (例: "7203.T")
    start_date : datetime
        開始日
    end_date : datetime
        終了日
    sleep_time : float
        リクエスト間の待機時間
    retries : int
        リトライ回数

    Returns:
    --------
    pd.DataFrame or None
        成功した場合はデータフレーム、失敗した場合はNone
    """
    # URLパラメータを構築
    period1 = get_unix_timestamp(start_date)
    period2 = get_unix_timestamp(end_date)

    # Yahoo FinanceのCSVダウンロードURL
    url = f"https://query1.finance.yahoo.com/v7/finance/download/{ticker}"
    params = {
        "period1": period1,
        "period2": period2,
        "interval": "1d",
        "events": "history",
        "includeAdjustedClose": "true"
    }

    # キャッシュファイル名
    cache_file = os.path.join(CACHE_DIR, f"{ticker}_{start_date.strftime('%Y%m%d')}_{end_date.strftime('%Y%m%d')}.csv")

    # キャッシュが存在する場合はそれを返す
    if os.path.exists(cache_file):
        try:
            df = pd.read_csv(cache_file, parse_dates=["Date"])
            logger.info(f"{ticker}のデータをキャッシュから読み込みました（{len(df)}行）")
            return df
        except Exception as e:
            logger.error(f"キャッシュ読み込みエラー: {str(e)}")

    # ヘッダー（一般的なブラウザのように振る舞う）
    headers = {
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
        "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8",
        "Accept-Language": "en-US,en;q=0.5",
        "Cache-Control": "no-cache"
    }

    logger.info(f"{ticker}の{start_date.strftime('%Y-%m-%d')}～{end_date.strftime('%Y-%m-%d')}のデータをダウンロード開始")

    for attempt in range(retries):
        try:
            response = requests.get(url, params=params, headers=headers, timeout=30)

            if response.status_code == 200:
                # CSVデータを解析
                try:
                    df = pd.read_csv(pd.io.common.StringIO(response.text), parse_dates=["Date"])

                    # 空のデータフレームをチェック
                    if df.empty:
                        logger.warning(f"{ticker}のデータは空です")
                        return None

                    # ティッカー列を追加
                    df["Ticker"] = ticker

                    # キャッシュに保存
                    df.to_csv(cache_file, index=False)

                    logger.info(f"{ticker}のデータを取得しました（{len(df)}行）")
                    time.sleep(sleep_time + random.uniform(0, 1))
                    return df
                except Exception as e:
                    logger.error(f"CSVパースエラー: {str(e)}")
                    logger.debug(f"レスポンス内容: {response.text[:200]}...")
            else:
                logger.warning(f"HTTP エラー: {response.status_code} - {response.reason}")

            # リトライ前の待機
            if attempt < retries - 1:
                wait_time = sleep_time * (2 ** attempt) + random.uniform(0, 2)
                logger.info(f"リトライ {attempt+1}/{retries} - {wait_time:.1f}秒待機...")
                time.sleep(wait_time)

        except Exception as e:
            logger.error(f"{ticker}のダウンロード中にエラー: {str(e)}")

            if attempt < retries - 1:
                wait_time = sleep_time * (2 ** attempt) + random.uniform(0, 2)
                logger.info(f"リトライ {attempt+1}/{retries} - {wait_time:.1f}秒待機...")
                time.sleep(wait_time)

    logger.error(f"{ticker}のダウンロードに{retries}回失敗しました")
    return None


def download_ticker_data(ticker, years=5, sleep_time=2, retries=3):
    """
    指定の銘柄の過去数年のデータをダウンロード

    Parameters:
    -----------
    ticker : str
        ティッカーシンボル
    years : int
        取得する年数
    sleep_time : float
        リクエスト間の待機時間
    retries : int
        リトライ回数

    Returns:
    --------
    pd.DataFrame or None
        成功した場合はデータフレーム、失敗した場合はNone
    """
    end_date = datetime.now()
    start_date = end_date - timedelta(days=365 * years)

    df = download_historical_data(ticker, start_date, end_date, sleep_time, retries)
    
    if df is not None and not df.empty:
        # ティッカー情報を追加
        df["Ticker"] = ticker
        
        # 年月ディレクトリの作成と保存
        for (year_val, month), group in df.groupby([df['Date'].dt.year, df['Date'].dt.month]):
            dir_path = os.path.join(DATA_DIR, "daily", str(year_val), f"{year_val}-{month:02d}")
            os.makedirs(dir_path, exist_ok=True)
            
            file_path = os.path.join(dir_path, f"{ticker}.csv")
            
            # 既存データがあれば統合
            if os.path.exists(file_path):
                existing_df = pd.read_csv(file_path, parse_dates=["Date"])
                group = pd.concat([existing_df, group])
                group = group.drop_duplicates(subset=["Date"]).sort_values("Date")
            
            group.to_csv(file_path, index=False)
            
        logger.info(f"{ticker}のデータを年月別ディレクトリに保存しました")
    
    return df


def download_sector_data(sector_name, years=5, sleep_time=5, retries=3):
    """
    セクター内の全銘柄のデータをダウンロード
    
    Parameters:
    -----------
    sector_name : str
        セクター名
    years : int
        取得する年数
    sleep_time : float
        リクエスト間の待機時間
    retries : int
        リトライ回数
    
    Returns:
    --------
    dict
        成功した銘柄と失敗した銘柄のリスト
    """
    # セクターに属する銘柄を取得
    tickers = get_tickers_by_sector(sector_name)
    if not tickers:
        logger.warning(f"セクター「{sector_name}」に銘柄が見つかりません")
        return {"success": [], "failure": []}
    
    logger.info(f"セクター「{sector_name}」の{len(tickers)}銘柄のデータをダウンロード開始")
    
    success_tickers = []
    failure_tickers = []
    
    for ticker in tqdm(tickers, desc=f"セクター「{sector_name}」"):
        df = download_ticker_data(ticker, years, sleep_time, retries)
        
        if df is not None and not df.empty:
            success_tickers.append(ticker)
        else:
            failure_tickers.append(ticker)
        
        # 銘柄間の待機
        if ticker != tickers[-1]:  # 最後の銘柄以外
            logger.info(f"次の銘柄の処理まで{sleep_time}秒待機します")
            time.sleep(sleep_time)
    
    logger.info(f"セクター「{sector_name}」のダウンロード完了: 成功={len(success_tickers)}, 失敗={len(failure_tickers)}")
    
    return {
        "success": success_tickers,
        "failure": failure_tickers
    }


def download_tickers(tickers, years=5, sleep_time=5, retries=3, output_file=None):
    """
    指定された銘柄リストのデータを一括ダウンロード

    Parameters:
    -----------
    tickers : list
        ティッカーのリスト
    years : int
        取得する年数
    sleep_time : float
        リクエスト間の待機時間
    retries : int
        リトライ回数
    output_file : str or None
        出力CSVファイル名（Noneの場合は保存しない）

    Returns:
    --------
    tuple
        (成功数, 失敗数, 結合データフレーム)
    """
    logger.info(f"{len(tickers)}銘柄の過去{years}年分のデータをダウンロード開始")

    all_data = []
    success_count = 0
    failure_count = 0
    failed_tickers = []

    for ticker in tqdm(tickers, desc="銘柄ダウンロード"):
        df = download_ticker_data(ticker, years, sleep_time, retries)

        if df is not None and not df.empty:
            all_data.append(df)
            success_count += 1
        else:
            failure_count += 1
            failed_tickers.append(ticker)
        
        # 待機時間を入れる
        if ticker != tickers[-1]:  # 最後の銘柄以外
            time.sleep(sleep_time)

    if not all_data:
        logger.error("有効なデータがありません")
        return success_count, failure_count, pd.DataFrame()

    # 全データを結合
    combined_df = pd.concat(all_data, ignore_index=True)

    logger.info(f"ダウンロード完了: 成功={success_count}, 失敗={failure_count}, 総レコード数={len(combined_df)}")

    if failed_tickers:
        logger.warning(f"失敗した銘柄: {' '.join(failed_tickers)}")

    # 指定があれば結合データを保存
    if output_file:
        os.makedirs(os.path.dirname(os.path.abspath(output_file)), exist_ok=True)
        combined_df.to_csv(output_file, index=False)
        logger.info(f"結合データを保存しました: {output_file}")

    return success_count, failure_count, combined_df


def create_consolidated_file(year=None):
    """
    統合ファイルの作成
    
    Parameters:
    -----------
    year : int or None
        指定した年のデータのみを統合（Noneの場合は全期間）
    
    Returns:
    --------
    str or None
        作成された統合ファイルのパス
    """
    # 読み込むファイルを検索
    all_files = []
    if year:
        # 特定の年のみ
        search_dir = os.path.join(DATA_DIR, "daily", str(year))
        if os.path.exists(search_dir):
            for root, _, files in os.walk(search_dir):
                for file in files:
                    if file.endswith(".csv"):
                        all_files.append(os.path.join(root, file))
        
        output_file = os.path.join(DATA_DIR, "consolidated", f"nikkei225_daily_{year}.csv")
    else:
        # 全期間
        for root, _, files in os.walk(os.path.join(DATA_DIR, "daily")):
            for file in files:
                if file.endswith(".csv"):
                    all_files.append(os.path.join(root, file))
        
        output_file = os.path.join(DATA_DIR, "consolidated", "nikkei225_daily_all.csv")
    
    if not all_files:
        logger.warning("統合するファイルが見つかりません")
        return None
    
    logger.info(f"{len(all_files)}ファイルを統合します")
    
    # 全データを読み込み
    all_data = []
    for file in tqdm(all_files, desc="ファイル読み込み"):
        try:
            df = pd.read_csv(file, parse_dates=["Date"])
            all_data.append(df)
        except Exception as e:
            logger.error(f"{file}の読み込みエラー: {str(e)}")
    
    if not all_data:
        logger.warning("有効なデータがありません")
        return None
    
    # 結合
    combined_data = pd.concat(all_data, ignore_index=True)
    
    # 重複を削除
    combined_data = combined_data.drop_duplicates(subset=["Ticker", "Date"]).sort_values(["Ticker", "Date"])
    
    # 保存
    combined_data.to_csv(output_file, index=False)
    
    logger.info(f"統合ファイルを保存しました: {output_file}, {len(combined_data)}行, {combined_data['Ticker'].nunique()}銘柄")
    
    return output_file


def main():
    """メイン関数"""
    # コマンドライン引数の解析
    parser = argparse.ArgumentParser(description='Yahoo FinanceからCSVを直接ダウンロード')
    parser.add_argument('--ticker', type=str, help='個別銘柄をダウンロード')
    parser.add_argument('--sector', type=str, help='特定のセクターのデータをダウンロード')
    parser.add_argument('--years', type=int, default=5, help='取得する年数（デフォルト: 5）')
    parser.add_argument('--sleep', type=float, default=5.0, help='リクエスト間の待機時間（デフォルト: 5秒）')
    parser.add_argument('--retries', type=int, default=3, help='リトライ回数（デフォルト: 3）')
    parser.add_argument('--output', type=str, help='結合データの出力ファイル（デフォルト: なし）')
    parser.add_argument('--consolidate', action='store_true', help='統合ファイルを作成する')
    parser.add_argument('--year', type=int, help='統合対象の特定の年（--consolidateと一緒に使用）')
    parser.add_argument('--list-sectors', action='store_true', help='利用可能なセクターを表示')

    args = parser.parse_args()

    # 必要なディレクトリを作成
    ensure_dirs()
    
    # セクターの一覧表示
    if args.list_sectors:
        print("\n利用可能なセクター:")
        for sector in get_sector_names():
            tickers = get_tickers_by_sector(sector)
            print(f"  {sector}: {len(tickers)}銘柄")
        return

    # 統合ファイルの作成
    if args.consolidate:
        create_consolidated_file(args.year)
        return

    # 個別銘柄のダウンロード
    if args.ticker:
        df = download_ticker_data(args.ticker, args.years, args.sleep, args.retries)
        if df is not None and not df.empty:
            print(f"\n{args.ticker}のデータを取得しました")
            print(f"取得期間: {df['Date'].min()} ～ {df['Date'].max()}")
            print(f"データ件数: {len(df)}行")
        return
    
    # セクター別ダウンロード
    if args.sector:
        results = download_sector_data(args.sector, args.years, args.sleep, args.retries)
        print(f"\nセクター「{args.sector}」のダウンロード結果:")
        print(f"成功: {len(results['success'])}銘柄")
        print(f"失敗: {len(results['failure'])}銘柄")
        if results['failure']:
            print(f"失敗した銘柄: {', '.join(results['failure'])}")
        return
    
    # ヘルプの表示
    parser.print_help()
    print("\n使用例:")
    print("  # トヨタ自動車の5年分のデータをダウンロード")
    print("  python yahoo_direct_download_fixed.py --ticker 7203.T")
    print("\n  # 銀行業セクターの3年分のデータをダウンロード")
    print("  python yahoo_direct_download_fixed.py --sector 銀行業 --years 3 --sleep 10")
    print("\n  # 統合ファイルの作成")
    print("  python yahoo_direct_download_fixed.py --consolidate")
    print("\n  # 2023年のデータのみ統合")
    print("  python yahoo_direct_download_fixed.py --consolidate --year 2023")


if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n処理を中断しました")
    except Exception as e:
        logger.error(f"エラーが発生しました: {str(e)}")
        import traceback
        traceback.print_exc()
