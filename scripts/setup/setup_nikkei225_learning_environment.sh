#!/bin/bash
# 日経225 AI取引システム - 学習環境セットアップスクリプト
#
# 5年分の日経225データを収集し、継続学習環境をセットアップします

set -e  # エラー発生時に停止

# カラー出力の設定
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# ディレクトリ設定
DATA_DIR="nikkei225_full_data"
LOG_DIR="logs"
CONFIG_DIR="config"

# 年の設定
START_YEAR=2020
END_YEAR=2025

# ログファイル
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
LOG_FILE="${LOG_DIR}/setup_environment_${TIMESTAMP}.log"

# ディレクトリの作成
mkdir -p $LOG_DIR
mkdir -p $CONFIG_DIR

# ヘルプメッセージ表示関数
show_help() {
    echo "日経225 AI取引システム - 学習環境セットアップスクリプト"
    echo ""
    echo "使用方法: $0 [オプション]"
    echo ""
    echo "オプション:"
    echo "  --help           このヘルプメッセージを表示します"
    echo "  --start-year N   開始年を指定します (デフォルト: 2020)"
    echo "  --end-year N     終了年を指定します (デフォルト: 2025)"
    echo "  --no-parallel    並列処理を無効化します"
    echo "  --workers N      並列ワーカー数を指定します (デフォルト: 4)"
    echo "  --data-only      データ収集のみを行い、継続学習設定をスキップします"
    echo "  --setup-only     データ収集をスキップし、継続学習設定のみを行います"
    echo ""
    echo "例:"
    echo "  $0 --start-year 2019 --end-year 2024 --workers 8"
    echo ""
}

# コマンドライン引数の解析
PARALLEL_OPTS=""
DATA_ONLY=false
SETUP_ONLY=false

while [[ $# -gt 0 ]]; do
    case $1 in
        --help)
            show_help
            exit 0
            ;;
        --start-year)
            START_YEAR="$2"
            shift 2
            ;;
        --end-year)
            END_YEAR="$2"
            shift 2
            ;;
        --no-parallel)
            PARALLEL_OPTS="--sequential"
            shift
            ;;
        --workers)
            PARALLEL_OPTS="--workers $2"
            shift 2
            ;;
        --data-only)
            DATA_ONLY=true
            shift
            ;;
        --setup-only)
            SETUP_ONLY=true
            shift
            ;;
        *)
            echo -e "${RED}エラー: 不明なオプション $1${NC}"
            show_help
            exit 1
            ;;
    esac
done

# ログ出力関数
log() {
    local message="$1"
    local level="${2:-INFO}"
    local timestamp=$(date +"%Y-%m-%d %H:%M:%S")
    echo -e "[$timestamp] [$level] $message"
    echo "[$timestamp] [$level] $message" >> "$LOG_FILE"
}

# ステップ実行関数
run_step() {
    local step_name="$1"
    local command="$2"
    
    log "--- $step_name 開始 ---" "STEP"
    echo -e "${BLUE}実行中: $step_name...${NC}"
    
    if eval "$command"; then
        log "$step_name 完了" "SUCCESS"
        echo -e "${GREEN}完了: $step_name${NC}"
        return 0
    else
        local exit_code=$?
        log "$step_name 失敗 (コード: $exit_code)" "ERROR"
        echo -e "${RED}失敗: $step_name (コード: $exit_code)${NC}"
        return $exit_code
    fi
}

# メイン処理
log "日経225 AI取引システム 学習環境セットアップ開始" "START"
echo -e "${BLUE}=== 日経225 AI取引システム - 学習環境セットアップ ===${NC}"
echo -e "${BLUE}開始年: $START_YEAR${NC}"
echo -e "${BLUE}終了年: $END_YEAR${NC}"
echo -e "${BLUE}ログファイル: $LOG_FILE${NC}"
echo ""

# 1. 5年分のデータセット収集
if [ "$SETUP_ONLY" != "true" ]; then
    run_step "5年分のデータセット収集" "python prepare_full_nikkei225_dataset.py --start-year $START_YEAR --end-year $END_YEAR --output-dir $DATA_DIR $PARALLEL_OPTS"
    
    if [ $? -ne 0 ]; then
        log "データセット収集に失敗したため、処理を中止します" "ERROR"
        echo -e "${RED}エラー: データセット収集に失敗しました。ログを確認してください。${NC}"
        exit 1
    fi
else
    log "データ収集をスキップします" "INFO"
    echo -e "${YELLOW}データ収集をスキップします${NC}"
fi

# 継続学習設定をスキップする場合
if [ "$DATA_ONLY" == "true" ]; then
    log "継続学習設定をスキップします" "INFO"
    echo -e "${YELLOW}継続学習設定をスキップします${NC}"
    
    log "セットアップ完了" "END"
    echo -e "${GREEN}=== セットアップ完了 ===${NC}"
    echo -e "${GREEN}データディレクトリ: $DATA_DIR${NC}"
    echo -e "${GREEN}ログファイル: $LOG_FILE${NC}"
    exit 0
fi

# 2. 継続学習設定ファイルの作成
if [ "$SETUP_ONLY" == "true" ] || [ -d "$DATA_DIR" ]; then
    # 継続学習設定ファイルの作成
    log "継続学習設定ファイルを作成" "STEP"
    echo -e "${BLUE}継続学習設定ファイルを作成中...${NC}"
    
    # 設定ファイル作成
    cat > "${CONFIG_DIR}/continuous_learning_config.json" << EOF
{
  "base_dir": ".",
  "data_dir": "${DATA_DIR}",
  "models_dir": "models",
  "results_dir": "results",
  "schedules": {
    "daily_data_collection": {
      "time": "17:00",
      "days": ["monday", "tuesday", "wednesday", "thursday", "friday"]
    },
    "weekly_model_retraining": {
      "time": "22:00",
      "days": ["sunday"]
    },
    "monthly_full_cycle": {
      "day": 1,
      "time": "01:00"
    }
  },
  "notification": {
    "enabled": false,
    "email": null,
    "telegram": null
  }
}
EOF
    
    log "継続学習設定ファイルを作成: ${CONFIG_DIR}/continuous_learning_config.json" "SUCCESS"
    echo -e "${GREEN}継続学習設定ファイルを作成しました${NC}"
    
    # 設定ファイルを指定位置にコピー
    cp "${CONFIG_DIR}/continuous_learning_config.json" continuous_learning_config.json
    log "設定ファイルをカレントディレクトリにコピーしました" "INFO"
    
    # 継続学習スクリプトの実行権限設定
    chmod +x continuous_learning_batch_processor.py
    chmod +x start_continuous_learning.sh
    log "継続学習スクリプトの実行権限を設定しました" "INFO"
    
    # 継続学習開始方法の案内
    echo -e "${YELLOW}=== 継続学習の開始方法 ===${NC}"
    echo -e "${YELLOW}以下のコマンドで継続学習を開始できます:${NC}"
    echo -e "  ./start_continuous_learning.sh"
    echo -e "${YELLOW}手動でプロセスを実行する場合:${NC}"
    echo -e "  ./start_continuous_learning.sh --manual daily   # 日次データ収集"
    echo -e "  ./start_continuous_learning.sh --manual weekly  # 週次モデル再学習"
    echo -e "  ./start_continuous_learning.sh --manual monthly # 月次完全サイクル"
    echo ""
else
    log "データディレクトリが見つからないため、継続学習設定をスキップします" "WARNING"
    echo -e "${YELLOW}警告: データディレクトリ $DATA_DIR が見つかりません。継続学習設定をスキップします。${NC}"
fi

# 完了メッセージ
log "セットアップ完了" "END"
echo -e "${GREEN}=== セットアップ完了 ===${NC}"
echo -e "${GREEN}データディレクトリ: $DATA_DIR${NC}"
echo -e "${GREEN}設定ディレクトリ: $CONFIG_DIR${NC}"
echo -e "${GREEN}ログファイル: $LOG_FILE${NC}"
