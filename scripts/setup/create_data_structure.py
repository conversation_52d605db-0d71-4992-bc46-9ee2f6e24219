#!/usr/bin/env python3
import os
import json
from datetime import datetime, timedelta
import logging

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f"logs/data_structure_{datetime.now().strftime('%Y%m%d')}.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def create_directory_structure(base_dir="nikkei225_historical_data"):
    """
    データ保存のためのディレクトリ構造を作成
    
    Parameters:
    -----------
    base_dir : str
        ベースディレクトリ
    """
    logger.info(f"ディレクトリ構造の作成を開始: {base_dir}")
    
    # ベースディレクトリ
    os.makedirs(base_dir, exist_ok=True)
    
    # 日次データ用ディレクトリ
    daily_dir = os.path.join(base_dir, "daily")
    os.makedirs(daily_dir, exist_ok=True)
    
    # 時間足データ用ディレクトリ
    hourly_dir = os.path.join(base_dir, "hourly")
    os.makedirs(hourly_dir, exist_ok=True)
    
    # メタデータ用ディレクトリ
    metadata_dir = os.path.join(base_dir, "metadata")
    os.makedirs(metadata_dir, exist_ok=True)
    
    # 統合データ用ディレクトリ
    consolidated_dir = os.path.join(base_dir, "consolidated")
    os.makedirs(consolidated_dir, exist_ok=True)
    
    # AI評価データ用ディレクトリ
    ai_evaluation_dir = os.path.join(base_dir, "ai_evaluation")
    os.makedirs(ai_evaluation_dir, exist_ok=True)
    os.makedirs(os.path.join(ai_evaluation_dir, "predictions"), exist_ok=True)
    os.makedirs(os.path.join(ai_evaluation_dir, "performance"), exist_ok=True)
    os.makedirs(os.path.join(ai_evaluation_dir, "model_improvements"), exist_ok=True)
    
    # 過去5年分の年別・月別ディレクトリを作成
    current_date = datetime.now()
    for year_offset in range(5):
        year = current_date.year - year_offset
        year_dir_daily = os.path.join(daily_dir, str(year))
        year_dir_hourly = os.path.join(hourly_dir, str(year))
        
        os.makedirs(year_dir_daily, exist_ok=True)
        os.makedirs(year_dir_hourly, exist_ok=True)
        
        for month in range(1, 13):
            month_str = f"{year}-{month:02d}"
            os.makedirs(os.path.join(year_dir_daily, month_str), exist_ok=True)
            os.makedirs(os.path.join(year_dir_hourly, month_str), exist_ok=True)
    
    # 初期メタデータファイルの作成
    create_initial_metadata_files(metadata_dir)
    
    # 評価データ用の日付ディレクトリを作成（直近30日分）
    predictions_dir = os.path.join(ai_evaluation_dir, "predictions")
    for day_offset in range(30):
        date = (current_date - timedelta(days=day_offset)).strftime('%Y-%m-%d')
        os.makedirs(os.path.join(predictions_dir, date), exist_ok=True)
    
    logger.info(f"ディレクトリ構造の作成が完了しました")
    return base_dir

def create_initial_metadata_files(metadata_dir):
    """
    初期メタデータファイルの作成
    
    Parameters:
    -----------
    metadata_dir : str
        メタデータディレクトリ
    """
    # ティッカー情報ファイル
    tickers_info = {
        "last_updated": datetime.now().isoformat(),
        "source": "initial_creation",
        "tickers": {}
    }
    
    # ティッカー履歴ファイル
    ticker_history = {
        "last_updated": datetime.now().isoformat(),
        "changes": []
    }
    
    # 最終更新情報ファイル
    last_update = {
        "daily": {
            "last_full_update": None,
            "last_incremental_update": None,
            "tickers_status": {}
        },
        "hourly": {
            "last_full_update": None,
            "last_incremental_update": None,
            "tickers_status": {}
        }
    }
    
    # セクター分類情報ファイル
    sector_classification = {
        "last_updated": datetime.now().isoformat(),
        "sectors": {
            "technology": {"description": "情報技術", "tickers": []},
            "financials": {"description": "金融", "tickers": []},
            "industrials": {"description": "資本財・サービス", "tickers": []},
            "consumer_discretionary": {"description": "一般消費財・サービス", "tickers": []},
            "consumer_staples": {"description": "生活必需品", "tickers": []},
            "healthcare": {"description": "ヘルスケア", "tickers": []},
            "communication_services": {"description": "コミュニケーション・サービス", "tickers": []},
            "utilities": {"description": "公共事業", "tickers": []},
            "real_estate": {"description": "不動産", "tickers": []},
            "materials": {"description": "素材", "tickers": []},
            "energy": {"description": "エネルギー", "tickers": []}
        }
    }
    
    # ファイルに書き込み
    with open(os.path.join(metadata_dir, "tickers_info.json"), 'w', encoding='utf-8') as f:
        json.dump(tickers_info, f, indent=2, ensure_ascii=False)
    
    with open(os.path.join(metadata_dir, "ticker_history.json"), 'w', encoding='utf-8') as f:
        json.dump(ticker_history, f, indent=2, ensure_ascii=False)
    
    with open(os.path.join(metadata_dir, "last_update.json"), 'w', encoding='utf-8') as f:
        json.dump(last_update, f, indent=2, ensure_ascii=False)
    
    with open(os.path.join(metadata_dir, "sector_classification.json"), 'w', encoding='utf-8') as f:
        json.dump(sector_classification, f, indent=2, ensure_ascii=False)
    
    logger.info(f"初期メタデータファイルの作成が完了しました")

if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description='Create directory structure for Nikkei 225 historical data')
    parser.add_argument('--base-dir', type=str, default="nikkei225_historical_data", help='Base directory')
    
    args = parser.parse_args()
    
    # logs ディレクトリがなければ作成
    os.makedirs("logs", exist_ok=True)
    
    create_directory_structure(args.base_dir)
