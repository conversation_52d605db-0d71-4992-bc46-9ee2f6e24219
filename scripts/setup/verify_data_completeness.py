#!/usr/bin/env python3
"""
日経225データ収集 - データ完全性検証スクリプト

データ収集の完全性を検証し、欠損や不完全なデータを特定して再収集します。
"""

import os
import sys
import argparse
import logging
import json
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import matplotlib.pyplot as plt
from tqdm import tqdm
import multiprocessing

# 自作モジュールをインポート
from src.data_collector.utils import NikkeiUtils, FileUtils
from src.data_collector.collector import StockDataCollector

# ロギング設定
log_dir = 'logs'
os.makedirs(log_dir, exist_ok=True)

log_file = os.path.join(log_dir, f"verify_data_{datetime.now().strftime('%Y%m%d')}.log")
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_file),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("データ検証")

def check_data_directory(base_dir):
    """データディレクトリの存在と構造を確認"""
    if not os.path.exists(base_dir):
        logger.error(f"ディレクトリが存在しません: {base_dir}")
        return False
    
    # サブディレクトリの確認
    required_subdirs = ["yearly", "consolidated", "metadata"]
    missing_dirs = []
    
    for subdir in required_subdirs:
        path = os.path.join(base_dir, subdir)
        if not os.path.exists(path):
            missing_dirs.append(subdir)
    
    if missing_dirs:
        logger.warning(f"不足しているサブディレクトリ: {', '.join(missing_dirs)}")
    
    # メタデータファイルの確認
    metadata_file = os.path.join(base_dir, "metadata", "dataset_metadata.json")
    if not os.path.exists(metadata_file):
        logger.warning(f"メタデータファイルが見つかりません: {metadata_file}")
        return True  # 致命的ではないので処理継続
    
    # 統合ファイルの確認
    consolidated_file = os.path.join(base_dir, "consolidated", "nikkei225_full_dataset.csv")
    if not os.path.exists(consolidated_file):
        logger.warning(f"統合データファイルが見つかりません: {consolidated_file}")
        return True  # 致命的ではないので処理継続
    
    return True

def get_data_statistics(base_dir):
    """データディレクトリ内のCSVファイルの統計情報を取得"""
    stats = {
        "total_files": 0,
        "yearly_files": {},
        "ticker_counts": {},
        "date_ranges": {},
        "empty_files": [],
        "consolidated_file": None,
        "total_records": 0
    }
    
    # 年別ファイルの確認
    yearly_dir = os.path.join(base_dir, "yearly")
    if os.path.exists(yearly_dir):
        for year_dir in os.listdir(yearly_dir):
            year_path = os.path.join(yearly_dir, year_dir)
            if os.path.isdir(year_path):
                year_stats = {"file_count": 0, "record_count": 0, "ticker_count": 0}
                
                # 統合ファイルを探す
                year_files = [f for f in os.listdir(year_path) if f.endswith('.csv')]
                consolidate_files = [f for f in year_files if "consolidate" in f.lower() or "all" in f.lower()]
                
                if consolidate_files:
                    for cfile in consolidate_files:
                        file_path = os.path.join(year_path, cfile)
                        try:
                            df = pd.read_csv(file_path)
                            year_stats["record_count"] += len(df)
                            
                            # ティッカー数を計算
                            if 'Ticker' in df.columns:
                                year_stats["ticker_count"] = df['Ticker'].nunique()
                            
                            # 日付範囲を取得
                            if 'Date' in df.columns:
                                df['Date'] = pd.to_datetime(df['Date'])
                                stats["date_ranges"][year_dir] = {
                                    "min": df['Date'].min().strftime('%Y-%m-%d'),
                                    "max": df['Date'].max().strftime('%Y-%m-%d')
                                }
                            
                            year_stats["file_count"] += 1
                            stats["total_files"] += 1
                            
                            # 空ファイルチェック
                            if len(df) == 0:
                                stats["empty_files"].append(file_path)
                                
                        except Exception as e:
                            logger.error(f"ファイル読み込みエラー: {file_path}, {str(e)}")
                
                stats["yearly_files"][year_dir] = year_stats
    
    # 統合ファイルの確認
    consolidated_file = os.path.join(base_dir, "consolidated", "nikkei225_full_dataset.csv")
    if os.path.exists(consolidated_file):
        try:
            df = pd.read_csv(consolidated_file)
            stats["consolidated_file"] = {
                "record_count": len(df),
                "ticker_count": df['Ticker'].nunique() if 'Ticker' in df.columns else 0
            }
            
            # 日付範囲を取得
            if 'Date' in df.columns:
                df['Date'] = pd.to_datetime(df['Date'])
                stats["consolidated_file"]["date_range"] = {
                    "min": df['Date'].min().strftime('%Y-%m-%d'),
                    "max": df['Date'].max().strftime('%Y-%m-%d')
                }
            
            stats["total_records"] = len(df)
            
            # ティッカーごとのレコード数を取得
            if 'Ticker' in df.columns:
                ticker_counts = df['Ticker'].value_counts().to_dict()
                stats["ticker_counts"] = ticker_counts
        except Exception as e:
            logger.error(f"統合ファイル読み込みエラー: {consolidated_file}, {str(e)}")
    
    return stats

def find_incomplete_tickers(data_stats, min_records_threshold=200):
    """不完全なデータを持つティッカーを検出"""
    incomplete_tickers = []
    
    # Nikkei225の全ティッカーを取得
    all_tickers = NikkeiUtils.get_all_nikkei225_tickers()
    
    # データに存在するティッカー
    existing_tickers = set(data_stats["ticker_counts"].keys())
    
    # 存在しないティッカーを検出
    missing_tickers = set(all_tickers) - existing_tickers
    if missing_tickers:
        logger.warning(f"データに存在しないティッカー: {len(missing_tickers)}件")
        for ticker in missing_tickers:
            incomplete_tickers.append({
                "ticker": ticker,
                "records": 0,
                "reason": "データなし",
                "sector": NikkeiUtils.get_sector_for_ticker(ticker)
            })
    
    # レコード数が少ないティッカーを検出
    for ticker, count in data_stats["ticker_counts"].items():
        if count < min_records_threshold:
            incomplete_tickers.append({
                "ticker": ticker,
                "records": count,
                "reason": f"レコード数不足 ({count}件)",
                "sector": NikkeiUtils.get_sector_for_ticker(ticker)
            })
    
    return incomplete_tickers

def recover_missing_data(incomplete_tickers, base_dir, start_year=2020, end_year=2025, parallel=True, workers=4):
    """不足しているデータを再収集"""
    logger.info(f"不足データの再収集を開始: {len(incomplete_tickers)}銘柄")
    
    # データコレクターの初期化
    collector = StockDataCollector(
        base_dir=os.path.join(base_dir, "recovery"),
        cache_dir=os.path.join(base_dir, "recovery", "cache"),
        debug=True
    )
    
    # 日付範囲の設定
    start_date = datetime(start_year, 1, 1)
    current_year = datetime.now().year
    if end_year > current_year:
        end_year = current_year
    end_date = datetime(end_year, 12, 31)
    if end_date > datetime.now():
        end_date = datetime.now()
    
    # 並列処理用の関数
    def process_ticker(ticker_info):
        ticker = ticker_info["ticker"]
        try:
            logger.info(f"銘柄データを再収集: {ticker}, 理由: {ticker_info['reason']}")
            df = collector.get_stock_data(ticker, start_date, end_date, use_cache=False)
            if df is not None and not df.empty:
                return ticker, len(df), True, None
            else:
                return ticker, 0, False, "データなし"
        except Exception as e:
            logger.error(f"再収集エラー: {ticker}, {str(e)}")
            return ticker, 0, False, str(e)
    
    # 並列処理で銘柄データを収集
    results = []
    
    if parallel and workers > 1 and len(incomplete_tickers) > 1:
        # 並列処理
        logger.info(f"並列処理モード: {workers}ワーカー")
        with multiprocessing.Pool(workers) as pool:
            results = list(tqdm(
                pool.imap(process_ticker, incomplete_tickers),
                total=len(incomplete_tickers),
                desc="データ再収集"
            ))
    else:
        # 直列処理
        logger.info("直列処理モード")
        for ticker_info in tqdm(incomplete_tickers, desc="データ再収集"):
            results.append(process_ticker(ticker_info))
    
    # 結果の集計
    success_count = sum(1 for _, _, success, _ in results if success)
    failure_count = len(results) - success_count
    
    logger.info(f"再収集完了: 成功={success_count}銘柄, 失敗={failure_count}銘柄")
    
    # データの統合
    recovery_data = os.path.join(base_dir, "recovery", "recovered_data.csv")
    recovery_data = collector.consolidate_data(output_file=recovery_data)
    logger.info(f"再収集データの統合ファイル: {recovery_data}")
    
    return recovery_data, success_count, failure_count

def update_consolidated_data(base_dir, recovery_data):
    """再収集したデータを統合ファイルに追加・更新"""
    # 既存の統合ファイル
    consolidated_file = os.path.join(base_dir, "consolidated", "nikkei225_full_dataset.csv")
    
    if not os.path.exists(recovery_data):
        logger.error(f"再収集データファイルが見つかりません: {recovery_data}")
        return False
    
    if not os.path.exists(consolidated_file):
        logger.warning(f"既存の統合ファイルが見つかりません: {consolidated_file}")
        # 単純にコピー
        os.makedirs(os.path.dirname(consolidated_file), exist_ok=True)
        import shutil
        shutil.copy2(recovery_data, consolidated_file)
        logger.info(f"再収集データをそのまま統合ファイルとして使用: {consolidated_file}")
        return True
    
    # 両方のデータを読み込む
    try:
        df_orig = pd.read_csv(consolidated_file)
        df_recovery = pd.read_csv(recovery_data)
        
        logger.info(f"既存データ: {len(df_orig)}行, 再収集データ: {len(df_recovery)}行")
        
        # 日付カラムをdatetime型に変換
        df_orig['Date'] = pd.to_datetime(df_orig['Date'])
        df_recovery['Date'] = pd.to_datetime(df_recovery['Date'])
        
        # データを結合（再収集データを優先）
        df_combined = pd.concat([df_orig, df_recovery])
        
        # 重複を除去（同じTicker,Dateの組み合わせは再収集データを優先）
        df_combined = df_combined.drop_duplicates(subset=['Ticker', 'Date'], keep='last')
        
        # 日付でソート
        df_combined = df_combined.sort_values(['Ticker', 'Date'])
        
        # バックアップを作成
        backup_file = f"{consolidated_file}.bak.{datetime.now().strftime('%Y%m%d%H%M%S')}"
        df_orig.to_csv(backup_file, index=False)
        logger.info(f"既存データのバックアップを作成: {backup_file}")
        
        # 新しいデータを保存
        df_combined.to_csv(consolidated_file, index=False)
        logger.info(f"更新された統合データを保存: {consolidated_file}, {len(df_combined)}行")
        
        return True
    except Exception as e:
        logger.error(f"データ更新エラー: {str(e)}")
        return False

def generate_completeness_report(data_stats, incomplete_tickers, output_dir, recovered=None):
    """データ完全性のレポートを生成"""
    report_dir = os.path.join(output_dir, "reports")
    os.makedirs(report_dir, exist_ok=True)
    
    # レポートファイル
    report_file = os.path.join(report_dir, f"data_completeness_report_{datetime.now().strftime('%Y%m%d')}.html")
    
    # HTML レポートを生成
    with open(report_file, 'w', encoding='utf-8') as f:
        f.write("""
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>日経225データ収集 - 完全性レポート</title>
            <style>
                body { font-family: Arial, sans-serif; line-height: 1.6; margin: 0; padding: 20px; color: #333; }
                h1, h2, h3 { color: #0066cc; }
                table { border-collapse: collapse; width: 100%; margin-bottom: 20px; }
                th, td { padding: 12px 15px; border: 1px solid #ddd; text-align: left; }
                th { background-color: #f2f2f2; }
                tr:nth-child(even) { background-color: #f9f9f9; }
                .container { max-width: 1200px; margin: 0 auto; }
                .summary { background-color: #f0f8ff; padding: 15px; border-radius: 5px; margin-bottom: 20px; }
                .warning { color: #e65100; }
                .error { color: #d32f2f; }
                .success { color: #2e7d32; }
            </style>
        </head>
        <body>
            <div class="container">
                <h1>日経225データ収集 - 完全性レポート</h1>
                <p>生成日時: """ + datetime.now().strftime('%Y年%m月%d日 %H:%M:%S') + """</p>
                
                <div class="summary">
                    <h2>要約</h2>
        """)
        
        # 要約セクション
        consolidated_stats = data_stats.get("consolidated_file", {})
        ticker_count = consolidated_stats.get("ticker_count", 0)
        record_count = consolidated_stats.get("record_count", 0)
        missing_count = len(incomplete_tickers)
        
        f.write(f"""
                    <p>総銘柄数: <strong>{ticker_count}</strong> / {len(NikkeiUtils.get_all_nikkei225_tickers())}</p>
                    <p>総レコード数: <strong>{record_count}</strong></p>
                    <p>不完全なデータを持つ銘柄: <strong class="{'warning' if missing_count > 0 else 'success'}">{missing_count}</strong></p>
        """)
        
        if recovered:
            f.write(f"""
                    <p>再収集: <strong class="{'success' if recovered[1] > 0 else 'error'}">{recovered[1]}</strong> 成功, <strong class="{'error' if recovered[2] > 0 else ''}">{recovered[2]}</strong> 失敗</p>
            """)
        
        f.write("""
                </div>
                
                <h2>詳細統計</h2>
        """)
        
        # 年別セクション
        f.write("""
                <h3>年別データ</h3>
                <table>
                    <tr>
                        <th>年</th>
                        <th>ファイル数</th>
                        <th>レコード数</th>
                        <th>銘柄数</th>
                        <th>日付範囲</th>
                    </tr>
        """)
        
        for year, stats in sorted(data_stats.get("yearly_files", {}).items()):
            date_range = data_stats.get("date_ranges", {}).get(year, {})
            date_str = f"{date_range.get('min', 'N/A')} - {date_range.get('max', 'N/A')}" if date_range else "N/A"
            
            f.write(f"""
                    <tr>
                        <td>{year}</td>
                        <td>{stats.get("file_count", 0)}</td>
                        <td>{stats.get("record_count", 0)}</td>
                        <td>{stats.get("ticker_count", 0)}</td>
                        <td>{date_str}</td>
                    </tr>
            """)
        
        f.write("""
                </table>
        """)
        
        # 不完全なティッカーセクション
        if incomplete_tickers:
            f.write("""
                <h3>不完全なデータを持つ銘柄</h3>
                <table>
                    <tr>
                        <th>ティッカー</th>
                        <th>セクター</th>
                        <th>レコード数</th>
                        <th>理由</th>
                    </tr>
            """)
            
            # セクター別にソート
            sorted_tickers = sorted(incomplete_tickers, key=lambda x: (x.get("sector", ""), x.get("ticker", "")))
            
            for ticker_info in sorted_tickers:
                ticker = ticker_info.get("ticker", "")
                records = ticker_info.get("records", 0)
                reason = ticker_info.get("reason", "不明")
                sector = ticker_info.get("sector", "不明")
                
                f.write(f"""
                        <tr>
                            <td>{ticker}</td>
                            <td>{sector}</td>
                            <td>{records}</td>
                            <td>{reason}</td>
                        </tr>
                """)
            
            f.write("""
                </table>
            """)
        
        # 空ファイルセクション
        empty_files = data_stats.get("empty_files", [])
        if empty_files:
            f.write("""
                <h3>空のファイル</h3>
                <ul>
            """)
            
            for file_path in empty_files:
                f.write(f"""
                    <li>{os.path.basename(file_path)} ({file_path})</li>
                """)
            
            f.write("""
                </ul>
            """)
        
        f.write("""
            </div>
        </body>
        </html>
        """)
    
    logger.info(f"完全性レポートを生成: {report_file}")
    return report_file

def main():
    """メイン関数"""
    parser = argparse.ArgumentParser(description='日経225データの完全性を検証')
    
    parser.add_argument('--data-dir', type=str, default='nikkei225_full_data', help='データディレクトリ')
    parser.add_argument('--min-records', type=int, default=200, help='最小レコード数閾値')
    parser.add_argument('--start-year', type=int, default=2020, help='再収集の開始年')
    parser.add_argument('--end-year', type=int, default=2025, help='再収集の終了年')
    parser.add_argument('--recover', action='store_true', help='不足データを自動的に再収集')
    parser.add_argument('--no-parallel', dest='parallel', action='store_false', help='並列処理を無効化')
    parser.add_argument('--workers', type=int, default=4, help='並列ワーカー数')
    parser.add_argument('--report-only', action='store_true', help='レポート生成のみを行う')
    
    args = parser.parse_args()
    
    # データディレクトリの確認
    if not check_data_directory(args.data_dir):
        logger.error("データディレクトリの検証に失敗しました")
        return 1
    
    # データ統計情報の取得
    logger.info("データ統計情報を収集中...")
    data_stats = get_data_statistics(args.data_dir)
    
    # 統計情報の表示
    total_tickers = len(NikkeiUtils.get_all_nikkei225_tickers())
    existing_tickers = len(data_stats["ticker_counts"])
    logger.info(f"統計情報: {existing_tickers}/{total_tickers}銘柄, {data_stats['total_records']}レコード")
    
    # 不完全なデータを持つティッカーを検出
    logger.info("不完全なデータを検出中...")
    incomplete_tickers = find_incomplete_tickers(data_stats, args.min_records)
    
    logger.info(f"不完全なデータを持つ銘柄: {len(incomplete_tickers)}件")
    
    # データ再収集
    recovered_data = None
    if args.recover and incomplete_tickers and not args.report_only:
        logger.info("不足データの再収集を開始...")
        recovered_data, success_count, failure_count = recover_missing_data(
            incomplete_tickers, 
            args.data_dir,
            args.start_year,
            args.end_year,
            args.parallel,
            args.workers
        )
        
        if success_count > 0 and recovered_data:
            # 統合データの更新
            logger.info("統合データを更新中...")
            if update_consolidated_data(args.data_dir, recovered_data):
                logger.info("統合データの更新が完了しました")
            else:
                logger.error("統合データの更新に失敗しました")
        else:
            logger.warning("再収集に成功したデータがないため、統合データは更新しません")
        
        recovered_info = (recovered_data, success_count, failure_count)
    else:
        recovered_info = None
    
    # レポート生成
    report_file = generate_completeness_report(data_stats, incomplete_tickers, args.data_dir, recovered_info)
    
    # 結果表示
    print(f"\n--- データ完全性検証完了 ---")
    print(f"データディレクトリ: {args.data_dir}")
    print(f"分析された銘柄数: {existing_tickers}/{total_tickers}")
    print(f"不完全なデータを持つ銘柄: {len(incomplete_tickers)}件")
    
    if recovered_info:
        print(f"再収集: {recovered_info[1]}件成功, {recovered_info[2]}件失敗")
    
    print(f"レポートファイル: {report_file}")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
