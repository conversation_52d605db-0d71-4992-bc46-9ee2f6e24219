#!/usr/bin/env python3
import os
import json
import requests
from bs4 import BeautifulSoup
import pandas as pd
import yfinance as yf
import time
from datetime import datetime
import logging
import random

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f"logs/ticker_manager_{datetime.now().strftime('%Y%m%d')}.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class TickerManager:
    """
    日経225構成銘柄の管理と最新情報の取得
    """
    def __init__(self, metadata_dir="nikkei225_historical_data/metadata"):
        """
        初期化
        
        Parameters:
        -----------
        metadata_dir : str
            メタデータディレクトリ
        """
        self.metadata_dir = metadata_dir
        self.tickers_file = os.path.join(metadata_dir, "tickers_info.json")
        self.history_file = os.path.join(metadata_dir, "ticker_history.json")
        self.sector_file = os.path.join(metadata_dir, "sector_classification.json")
        
        # ディレクトリがなければ作成
        os.makedirs(metadata_dir, exist_ok=True)
        
        # メタデータの読み込み
        self.tickers_info = self._load_json(self.tickers_file, {"last_updated": None, "tickers": {}})
        self.ticker_history = self._load_json(self.history_file, {"last_updated": None, "changes": []})
        self.sector_info = self._load_json(self.sector_file, {"last_updated": None, "sectors": {}})
        
        # APIリクエスト制限管理
        self.request_count = 0
        self.last_request_time = 0
    
    def _load_json(self, file_path, default_value):
        """
        JSONファイルの読み込み
        
        Parameters:
        -----------
        file_path : str
            ファイルパス
        default_value : dict
            デフォルト値
            
        Returns:
        --------
        dict
            読み込んだJSONデータ
        """
        if os.path.exists(file_path):
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception as e:
                logger.error(f"JSONファイルの読み込みエラー ({file_path}): {str(e)}")
        
        return default_value
    
    def _save_json(self, file_path, data):
        """
        JSONファイルの保存
        
        Parameters:
        -----------
        file_path : str
            ファイルパス
        data : dict
            保存するデータ
            
        Returns:
        --------
        bool
            保存成功の場合はTrue
        """
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
            return True
        except Exception as e:
            logger.error(f"JSONファイルの保存エラー ({file_path}): {str(e)}")
            return False
    
    def _respect_rate_limit(self):
        """APIレート制限を尊重"""
        # 1分間に最大300リクエスト
        MAX_REQUESTS_PER_MINUTE = 300
        
        # 前回のリクエストからの経過時間
        elapsed = time.time() - self.last_request_time
        
        # リクエストカウントをリセット（1分経過）
        if elapsed > 60:
            self.request_count = 0
        
        # リクエスト数が制限に近づいたら待機
        if self.request_count > MAX_REQUESTS_PER_MINUTE * 0.8:  # 80%以上で待機
            wait_time = max(60 - elapsed, 0) + 1  # 残り時間 + 1秒
            logger.info(f"レート制限に近づいています。{wait_time:.1f}秒待機...")
            time.sleep(wait_time)
            self.request_count = 0
        
        # リクエストカウント更新
        self.request_count += 1
        self.last_request_time = time.time()
    
    def update_index_composition(self, force_update=False):
        """
        最新の日経225構成銘柄を取得し更新
        
        Parameters:
        -----------
        force_update : bool
            強制更新フラグ
            
        Returns:
        --------
        dict
            更新結果
        """
        # 前回の更新から1日以内であれば更新しない（強制更新でない場合）
        if not force_update and self.tickers_info["last_updated"]:
            last_updated = datetime.fromisoformat(self.tickers_info["last_updated"])
            if (datetime.now() - last_updated).days < 1:
                logger.info("前回の更新から1日以内のため、更新をスキップします")
                return {"status": "skipped", "reason": "recently_updated"}
        
        logger.info("日経225構成銘柄の取得を開始")
        
        try:
            # 日経225の構成銘柄を取得
            new_tickers = self.fetch_nikkei225_tickers()
            
            if not new_tickers:
                logger.error("日経225構成銘柄の取得に失敗しました")
                return {"status": "error", "reason": "fetch_failed"}
            
            # 現在の銘柄リスト
            current_tickers = set(self.tickers_info["tickers"].keys())
            
            # 新しい銘柄リスト
            new_ticker_set = set(new_tickers)
            
            # 追加された銘柄
            added_tickers = new_ticker_set - current_tickers
            
            # 削除された銘柄
            removed_tickers = current_tickers - new_ticker_set
            
            # 変更があれば履歴を更新
            if added_tickers or removed_tickers:
                change_record = {
                    "timestamp": datetime.now().isoformat(),
                    "added": list(added_tickers),
                    "removed": list(removed_tickers)
                }
                
                self.ticker_history["changes"].append(change_record)
                self.ticker_history["last_updated"] = datetime.now().isoformat()
                
                # 履歴を保存
                self._save_json(self.history_file, self.ticker_history)
                
                logger.info(f"銘柄変更を検出: 追加={len(added_tickers)}, 削除={len(removed_tickers)}")
            
            # 新しい銘柄のメタデータを取得
            for ticker in added_tickers:
                ticker_data = self.get_ticker_metadata(ticker)
                if ticker_data:
                    self.tickers_info["tickers"][ticker] = ticker_data
            
            # 削除された銘柄の処理
            for ticker in removed_tickers:
                if ticker in self.tickers_info["tickers"]:
                    del self.tickers_info["tickers"][ticker]
            
            # 日経225の公式サイトから分類情報を取得
            self.update_sector_classification()
            
            # 最終更新日時を更新
            self.tickers_info["last_updated"] = datetime.now().isoformat()
            self.tickers_info["source"] = "web_scraping"
            
            # ティッカー情報を保存
            self._save_json(self.tickers_file, self.tickers_info)
            
            logger.info(f"日経225構成銘柄の更新が完了しました。銘柄数: {len(new_tickers)}")
            
            return {
                "status": "success",
                "added": list(added_tickers),
                "removed": list(removed_tickers),
                "total": len(new_tickers)
            }
            
        except Exception as e:
            logger.error(f"日経225構成銘柄の更新エラー: {str(e)}")
            return {"status": "error", "reason": str(e)}
    
    def fetch_nikkei225_tickers(self):
        """
        日経225の構成銘柄リストを取得
        
        Returns:
        --------
        list
            ティッカーシンボルのリスト
        """
        # すでに保存されているティッカーのバックアップリスト
        # Web取得に失敗した場合に使用
        backup_tickers = [
            '1332.T', '1333.T', '1376.T', '1605.T', '1721.T', '1801.T', '1802.T', '1803.T', '1808.T', '1812.T',
            '1925.T', '1928.T', '1963.T', '2002.T', '2269.T', '2501.T', '2502.T', '2503.T', '2531.T', '2768.T',
            '2801.T', '2802.T', '2871.T', '2914.T', '3003.T', '3086.T', '3101.T', '3103.T', '3289.T', '3382.T',
            '3401.T', '3402.T', '3405.T', '3407.T', '3436.T', '3861.T', '3863.T', '4004.T', '4005.T', '4021.T',
            '4042.T', '4043.T', '4061.T', '4063.T', '4151.T', '4183.T', '4188.T', '4208.T', '4324.T', '4452.T',
            '4502.T', '4503.T', '4506.T', '4507.T', '4519.T', '4523.T', '4568.T', '4578.T', '4689.T', '4704.T',
            '4751.T', '4901.T', '4911.T', '5020.T', '5101.T', '5108.T', '5201.T', '5202.T', '5214.T', '5232.T',
            '5233.T', '5301.T', '5332.T', '5401.T', '5406.T', '5411.T', '5541.T', '5631.T', '5703.T', '5706.T',
            '5707.T', '5711.T', '5713.T', '5801.T', '5802.T', '5803.T', '5901.T', '5902.T', '6005.T', '6028.T',
            '6103.T', '6113.T', '6135.T', '6141.T', '6146.T', '6201.T', '6203.T', '6208.T', '6268.T', '6273.T',
            '6301.T', '6302.T', '6305.T', '6326.T', '6361.T', '6366.T', '6367.T', '6370.T', '6378.T', '6383.T',
            '6407.T', '6417.T', '6418.T', '6448.T', '6455.T', '6460.T', '6465.T', '6471.T', '6472.T', '6473.T',
            '6474.T', '6479.T', '6481.T', '6501.T', '6503.T', '6504.T', '6506.T', '6508.T', '6586.T', '6588.T',
            '6594.T', '6619.T', '6645.T', '6674.T', '6701.T', '6702.T', '6723.T', '6724.T', '6727.T', '6752.T',
            '6753.T', '6758.T', '6762.T', '6770.T', '6841.T', '6857.T', '6902.T', '6952.T', '6954.T', '6963.T',
            '6981.T', '6988.T', '7003.T', '7011.T', '7012.T', '7201.T', '7202.T', '7203.T', '7231.T', '7238.T',
            '7240.T', '7261.T', '7267.T', '7269.T', '7270.T', '7272.T', '7731.T', '7733.T', '7735.T', '7751.T',
            '7752.T', '7762.T', '7832.T', '7911.T', '7912.T', '7951.T', '7974.T', '8001.T', '8002.T', '8015.T',
            '8031.T', '8035.T', '8053.T', '8058.T', '8233.T', '8252.T', '8253.T', '8267.T', '8303.T', '8304.T',
            '8306.T', '8308.T', '8309.T', '8316.T', '8331.T', '8354.T', '8355.T', '8411.T', '8601.T', '8604.T',
            '8628.T', '8630.T', '8725.T', '8750.T', '8766.T', '8795.T', '8801.T', '8802.T', '8804.T', '8830.T',
            '9001.T', '9005.T', '9007.T', '9008.T', '9009.T', '9020.T', '9021.T', '9022.T', '9062.T', '9064.T',
            '9101.T', '9104.T', '9107.T', '9202.T', '9301.T', '9432.T', '9433.T', '9434.T', '9501.T', '9502.T',
            '9503.T', '9531.T', '9532.T', '9602.T', '9613.T', '9735.T', '9766.T', '9983.T', '9984.T'
        ]
        
        try:
            # 方法1: 日経225公式サイトから取得（本来はここを実装）
            # 現在はダミー実装として、バックアップリストを返す
            logger.info("日経225公式サイトからティッカー情報を取得しようとしましたが、現在はバックアップリストを使用します")
            
            # TODO: 実際のWebスクレイピングを実装
            # 実際には公式サイトからスクレイピングして取得する
            # 例:
            # url = "https://www.jpx.co.jp/markets/indices/nikkei225/"
            # response = requests.get(url)
            # soup = BeautifulSoup(response.content, 'html.parser')
            # ...
            
            # バックアップリストを返す
            return backup_tickers
            
        except Exception as e:
            logger.error(f"日経225構成銘柄の取得エラー: {str(e)}")
            logger.info("バックアップリストを使用します")
            return backup_tickers
    
    def get_ticker_metadata(self, ticker, retry_count=3):
        """
        銘柄の詳細情報を取得
        
        Parameters:
        -----------
        ticker : str
            ティッカーシンボル
        retry_count : int
            リトライ回数
            
        Returns:
        --------
        dict
            銘柄情報
        """
        logger.info(f"銘柄情報の取得: {ticker}")
        
        for attempt in range(retry_count):
            try:
                # APIリクエスト制限を考慮
                self._respect_rate_limit()
                
                # yfinanceで詳細情報を取得
                ticker_obj = yf.Ticker(ticker)
                info = ticker_obj.info
                
                # 基本情報を抽出
                metadata = {
                    "name": info.get("longName", info.get("shortName", "")),
                    "sector": info.get("sector", ""),
                    "industry": info.get("industry", ""),
                    "market_cap": info.get("marketCap", None),
                    "currency": info.get("currency", "JPY"),
                    "country": info.get("country", "Japan"),
                    "website": info.get("website", ""),
                    "business_summary": info.get("longBusinessSummary", ""),
                    "last_updated": datetime.now().isoformat()
                }
                
                return metadata
                
            except Exception as e:
                logger.warning(f"銘柄情報の取得エラー ({ticker}, 試行 {attempt+1}/{retry_count}): {str(e)}")
                time.sleep(2 ** attempt)  # エクスポネンシャルバックオフ
        
        logger.error(f"銘柄情報の取得に失敗しました: {ticker}")
        
        # 最低限の情報を返す
        return {
            "name": ticker,
            "sector": "",
            "industry": "",
            "last_updated": datetime.now().isoformat()
        }
    
    def update_sector_classification(self):
        """
        セクター分類情報を更新
        
        Returns:
        --------
        bool
            更新成功の場合はTrue
        """
        logger.info("セクター分類情報の更新を開始")
        
        try:
            # 全ティッカーからセクター情報を収集
            sectors = {}
            
            for ticker, info in self.tickers_info["tickers"].items():
                sector = info.get("sector", "")
                if not sector:
                    continue
                
                if sector not in sectors:
                    sectors[sector] = {
                        "description": sector,
                        "tickers": []
                    }
                
                sectors[sector]["tickers"].append(ticker)
            
            # セクター情報を更新
            self.sector_info["sectors"] = sectors
            self.sector_info["last_updated"] = datetime.now().isoformat()
            
            # 保存
            self._save_json(self.sector_file, self.sector_info)
            
            logger.info(f"セクター分類情報の更新が完了しました。セクター数: {len(sectors)}")
            return True
            
        except Exception as e:
            logger.error(f"セクター分類情報の更新エラー: {str(e)}")
            return False
    
    def get_tickers_by_sector(self, sector):
        """
        セクターごとのティッカーリストを取得
        
        Parameters:
        -----------
        sector : str
            セクター名
            
        Returns:
        --------
        list
            ティッカーリスト
        """
        if sector in self.sector_info["sectors"]:
            return self.sector_info["sectors"][sector]["tickers"]
        
        return []
    
    def get_all_sectors(self):
        """
        全セクターのリストを取得
        
        Returns:
        --------
        list
            セクターリスト
        """
        return list(self.sector_info["sectors"].keys())
    
    def get_all_tickers(self):
        """
        全ティッカーのリストを取得
        
        Returns:
        --------
        list
            ティッカーリスト
        """
        return list(self.tickers_info["tickers"].keys())
    
    def get_ticker_groups(self, group_size=25):
        """
        ティッカーをグループに分割
        
        Parameters:
        -----------
        group_size : int
            グループサイズ
            
        Returns:
        --------
        list
            ティッカーグループのリスト
        """
        all_tickers = self.get_all_tickers()
        
        # ランダムに並び替え
        random.shuffle(all_tickers)
        
        # グループに分割
        return [all_tickers[i:i+group_size] for i in range(0, len(all_tickers), group_size)]
    
    def export_ticker_list(self, output_file):
        """
        ティッカーリストをCSVファイルにエクスポート
        
        Parameters:
        -----------
        output_file : str
            出力ファイルパス
            
        Returns:
        --------
        bool
            エクスポート成功の場合はTrue
        """
        try:
            # ティッカー情報をDataFrameに変換
            data = []
            
            for ticker, info in self.tickers_info["tickers"].items():
                data.append({
                    "ticker": ticker,
                    "name": info.get("name", ""),
                    "sector": info.get("sector", ""),
                    "industry": info.get("industry", ""),
                    "market_cap": info.get("market_cap", ""),
                    "last_updated": info.get("last_updated", "")
                })
            
            # DataFrameに変換
            df = pd.DataFrame(data)
            
            # CSVに保存
            df.to_csv(output_file, index=False)
            
            logger.info(f"ティッカーリストをエクスポートしました: {output_file}")
            return True
            
        except Exception as e:
            logger.error(f"ティッカーリストのエクスポートエラー: {str(e)}")
            return False

if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description='Manage Nikkei 225 tickers')
    parser.add_argument('--update', action='store_true', help='Update index composition')
    parser.add_argument('--force', action='store_true', help='Force update even if recently updated')
    parser.add_argument('--export', type=str, help='Export ticker list to CSV file')
    parser.add_argument('--metadata-dir', type=str, default="nikkei225_historical_data/metadata", help='Metadata directory')
    
    args = parser.parse_args()
    
    # logs ディレクトリがなければ作成
    os.makedirs("logs", exist_ok=True)
    
    manager = TickerManager(metadata_dir=args.metadata_dir)
    
    if args.update:
        result = manager.update_index_composition(force_update=args.force)
        print(json.dumps(result, indent=2, ensure_ascii=False))
    
    if args.export:
        manager.export_ticker_list(args.export)
