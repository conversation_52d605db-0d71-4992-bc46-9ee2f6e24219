#!/usr/bin/env python3
import os
import argparse
import logging
from datetime import datetime, timedelta
import time

# 自作モジュールをインポート
from create_data_structure import create_directory_structure
from ticker_manager import TickerManager
from historical_data_collector import HistoricalDataCollector
from ai_evaluator import AIEvaluator

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f"logs/setup_data_{datetime.now().strftime('%Y%m%d')}.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def setup_environment(base_dir="nikkei225_historical_data"):
    """初期環境のセットアップ"""
    logger.info("環境のセットアップを開始します")
    
    # ディレクトリ作成
    create_directory_structure(base_dir)
    
    # ティッカー管理
    ticker_manager = TickerManager(metadata_dir=os.path.join(base_dir, "metadata"))
    
    # ティッカー情報を更新
    ticker_manager.update_index_composition(force_update=True)
    
    # データコレクター
    collector = HistoricalDataCollector(base_dir=base_dir, ticker_manager=ticker_manager)
    
    # AI評価システム
    evaluator = AIEvaluator(evaluation_dir=os.path.join(base_dir, "ai_evaluation"), ticker_manager=ticker_manager)
    
    logger.info("環境のセットアップが完了しました")
    
    return {
        "base_dir": base_dir,
        "ticker_manager": ticker_manager,
        "collector": collector,
        "evaluator": evaluator
    }

def initial_data_collection(env, days_back=30, group_size=25):
    """初期データ収集（小規模）"""
    logger.info(f"初期データ収集を開始します（過去{days_back}日分）")
    
    collector = env["collector"]
    ticker_manager = env["ticker_manager"]
    
    # 全ティッカーの一部だけを使用
    all_tickers = ticker_manager.get_all_tickers()
    sample_tickers = all_tickers[:min(50, len(all_tickers))]  # 最初の50銘柄だけ使用
    
    # 日次データ収集
    logger.info("日次データの収集を開始します")
    collector.collect_daily_data(
        tickers=sample_tickers,
        start_date=(datetime.now() - timedelta(days=days_back)).strftime('%Y-%m-%d'),
        end_date=datetime.now().strftime('%Y-%m-%d'),
        batch_size=5,
        sleep_time=2
    )
    
    # 時間足データ収集
    logger.info("時間足データの収集を開始します")
    collector.collect_hourly_data(
        tickers=sample_tickers,
        days_back=min(days_back, 7),  # 時間足は最大7日に制限
        batch_size=2,
        sleep_time=5
    )
    
    # 統合ファイル作成
    collector.create_consolidated_files(data_type="daily", force_update=True)
    collector.create_consolidated_files(data_type="hourly", force_update=True)
    
    logger.info("初期データ収集が完了しました")

def record_sample_predictions(env):
    """サンプル予測の記録（AI評価システムのデモ用）"""
    logger.info("サンプル予測の記録を開始します")
    
    evaluator = env["evaluator"]
    ticker_manager = env["ticker_manager"]
    
    # サンプル用ティッカー
    sample_tickers = ticker_manager.get_all_tickers()[:10]
    
    # 現在時刻
    now = datetime.now()
    
    # サンプル予測を記録
    for ticker in sample_tickers:
        # サンプル予測データ
        prediction_data = {
            "timestamp": now.isoformat(),
            "ticker": ticker,
            "current_price": 1000.0,
            "predicted_price": 1050.0,
            "predicted_change_pct": 5.0,
            "confidence": 0.8,
            "features_used": ["price_ma5", "rsi", "macd"],
            "model_version": "v1.0.0",
            "prediction_horizon": 24  # 時間単位
        }
        
        # 予測を記録
        prediction_file = evaluator.record_prediction(prediction_data)
        logger.info(f"予測を記録しました: {ticker}")
        
        # 少し待機
        time.sleep(0.5)
    
    logger.info("サンプル予測の記録が完了しました")
    
    # サンプル結果を記録
    for ticker in sample_tickers:
        prediction_id = f"{ticker}_{now.strftime('%Y%m%d%H%M%S')}_0"
        
        # サンプル取引結果
        trade_result = {
            "executed": True,
            "buy_price": 1000.0,
            "sell_price": 1080.0 if ticker in sample_tickers[:5] else 950.0,
            "profit": 80.0 if ticker in sample_tickers[:5] else -50.0,
            "profit_pct": 8.0 if ticker in sample_tickers[:5] else -5.0,
            "fees": 1.0
        }
        
        # 実際の結果を記録
        actual_price = 1080.0 if ticker in sample_tickers[:5] else 950.0
        evaluator.record_actual_result(prediction_id, actual_price, trade_result)
        logger.info(f"結果を記録しました: {ticker}")
        
        # 少し待機
        time.sleep(0.5)
    
    logger.info("サンプル結果の記録が完了しました")

def generate_sample_report(env):
    """サンプルレポートの生成"""
    logger.info("サンプルレポートの生成を開始します")
    
    evaluator = env["evaluator"]
    
    # パフォーマンス評価
    metrics = evaluator.evaluate_performance()
    
    # レポート生成
    report_file = "ai_evaluation_report.html"
    evaluator.generate_report(output_file=report_file)
    
    logger.info(f"サンプルレポートを生成しました: {report_file}")
    
    return report_file

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='日経225 AIトレーダー用データ環境のセットアップ')
    parser.add_argument('--base-dir', type=str, default="nikkei225_historical_data", help='ベースディレクトリ')
    parser.add_argument('--setup-only', action='store_true', help='ディレクトリ構造のみをセットアップ')
    parser.add_argument('--collect-data', action='store_true', help='初期データ収集を実行')
    parser.add_argument('--demo-evaluation', action='store_true', help='AI評価システムのデモを実行')
    parser.add_argument('--days-back', type=int, default=30, help='過去何日分のデータを取得するか')
    
    args = parser.parse_args()
    
    # logs ディレクトリがなければ作成
    os.makedirs("logs", exist_ok=True)
    
    try:
        # 環境のセットアップ
        env = setup_environment(args.base_dir)
        
        # データ収集
        if args.collect_data:
            initial_data_collection(env, days_back=args.days_back)
        
        # AI評価システムのデモ
        if args.demo_evaluation:
            record_sample_predictions(env)
            report_file = generate_sample_report(env)
            print(f"\nAI評価レポートが生成されました: {report_file}")
            print(f"このレポートをブラウザで開いて確認してください。")
    
    except Exception as e:
        logger.error(f"エラーが発生しました: {str(e)}")
        import traceback
        traceback.print_exc()
