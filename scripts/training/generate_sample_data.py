べつにべつに#!/usr/bin/env python3
"""
テスト用の日経225サンプルデータを生成するスクリプト

API制限に引っかかる場合でも、仮想取引システムのテストが行えるように
合成データを生成します。
"""

import os
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
import random
import argparse

# ティッカーリスト（簡略のため代表的な大型株30銘柄を定義）
MAJOR_TICKERS = [
    "7203.T",  # トヨタ自動車
    "9432.T",  # NTT
    "9984.T",  # ソフトバンク
    "8306.T",  # 三菱UFJ
    "6758.T",  # ソニー
    "6861.T",  # キーエンス
    "9433.T",  # KDDI
    "4063.T",  # 信越化学
    "8316.T",  # 三井住友FG
    "7974.T",  # 任天堂
    "4661.T",  # オリエンタルランド
    "9983.T",  # ファーストリテ
    "6367.T",  # ダイキン工業
    "6501.T",  # 日立製作所
    "6594.T",  # 日本電産
    "6902.T",  # デンソー
    "6981.T",  # 村田製作所
    "4519.T",  # 中外製薬
    "8035.T",  # 東京エレクトロン
    "7267.T",  # ホンダ
    "7741.T",  # HOYA
    "8766.T",  # 東京海上HD
    "8031.T",  # 三井物産
    "2914.T",  # JT
    "5401.T",  # 日本製鉄
    "6954.T",  # ファナック
    "4507.T",  # 塩野義製薬
    "4543.T",  # テルモ
    "4568.T",  # 第一三共
    "3382.T",  # セブン＆アイ
]

# 業種
SECTORS = {
    "7203.T": "自動車",
    "9432.T": "情報通信",
    "9984.T": "情報通信",
    "8306.T": "銀行業",
    "6758.T": "電気機器",
    "6861.T": "電気機器",
    "9433.T": "情報通信",
    "4063.T": "化学",
    "8316.T": "銀行業",
    "7974.T": "その他製品",
    "4661.T": "サービス業",
    "9983.T": "小売業",
    "6367.T": "機械",
    "6501.T": "電気機器",
    "6594.T": "電気機器",
    "6902.T": "輸送用機器",
    "6981.T": "電気機器",
    "4519.T": "医薬品",
    "8035.T": "電気機器",
    "7267.T": "輸送用機器",
    "7741.T": "精密機器",
    "8766.T": "保険業",
    "8031.T": "卸売業",
    "2914.T": "食料品",
    "5401.T": "鉄鋼",
    "6954.T": "電気機器",
    "4507.T": "医薬品",
    "4543.T": "精密機器",
    "4568.T": "医薬品",
    "3382.T": "小売業",
}

# 平均株価データ（実際のデータに近い値）
AVG_PRICES = {
    "7203.T": 2500,    # トヨタ自動車
    "9432.T": 3800,    # NTT
    "9984.T": 6500,    # ソフトバンク
    "8306.T": 1200,    # 三菱UFJ
    "6758.T": 12000,   # ソニー
    "6861.T": 65000,   # キーエンス
    "9433.T": 4200,    # KDDI
    "4063.T": 17000,   # 信越化学
    "8316.T": 5500,    # 三井住友FG
    "7974.T": 7000,    # 任天堂
    "4661.T": 20000,   # オリエンタルランド
    "9983.T": 35000,   # ファーストリテ
    "6367.T": 25000,   # ダイキン工業
    "6501.T": 8500,    # 日立製作所
    "6594.T": 18000,   # 日本電産
    "6902.T": 2000,    # デンソー
    "6981.T": 9500,    # 村田製作所
    "4519.T": 3500,    # 中外製薬
    "8035.T": 24000,   # 東京エレクトロン
    "7267.T": 1500,    # ホンダ
    "7741.T": 15000,   # HOYA
    "8766.T": 3000,    # 東京海上HD
    "8031.T": 4500,    # 三井物産
    "2914.T": 3000,    # JT
    "5401.T": 2800,    # 日本製鉄
    "6954.T": 6000,    # ファナック
    "4507.T": 7500,    # 塩野義製薬
    "4543.T": 5000,    # テルモ
    "4568.T": 4000,    # 第一三共
    "3382.T": 5500,    # セブン＆アイ
}

def generate_ticker_data(ticker, start_date, end_date, volatility=0.015, trend=0.0001):
    """
    単一銘柄の株価データを生成
    
    Parameters:
    -----------
    ticker : str
        ティッカーシンボル
    start_date : datetime
        開始日
    end_date : datetime
        終了日
    volatility : float
        価格変動の大きさ
    trend : float
        上昇/下降トレンドの強さ
    
    Returns:
    --------
    pd.DataFrame
        生成された株価データ
    """
    # 営業日の生成（土日を除く）
    dates = []
    current_date = start_date
    while current_date <= end_date:
        if current_date.weekday() < 5:  # 月～金のみ
            dates.append(current_date)
        current_date += timedelta(days=1)
    
    # ランダムウォークで株価を生成
    n_days = len(dates)
    avg_price = AVG_PRICES.get(ticker, 5000)  # デフォルト価格は5000円
    
    # ボラティリティとトレンドにランダム性を加える
    specific_vol = volatility * (0.8 + 0.4 * random.random())
    specific_trend = trend * (-1 if random.random() < 0.3 else 1) * (0.5 + 1.5 * random.random())
    
    # 業種ごとの特性を反映
    sector = SECTORS.get(ticker, "その他")
    if sector == "情報通信" or sector == "電気機器":
        specific_vol *= 1.2  # ハイテク銘柄は変動が大きい
        specific_trend *= 1.1  # 成長傾向
    elif sector == "銀行業" or sector == "保険業":
        specific_vol *= 0.9  # 金融株は比較的安定
    elif sector == "医薬品":
        specific_vol *= 1.3  # 医薬品は変動大
    
    # 初期株価の設定
    base_price = avg_price * (0.9 + 0.2 * random.random())
    
    # 株価変動のシミュレーション
    returns = np.random.normal(specific_trend, specific_vol, n_days)
    
    # 株価の計算
    price_path = np.cumprod(1 + returns) * base_price
    
    # 出来高の生成
    avg_volume = int(avg_price * 500 * (0.5 + random.random()))  # 平均出来高
    volumes = np.random.lognormal(mean=np.log(avg_volume), sigma=0.5, size=n_days).astype(int)
    
    # データフレームの作成
    df = pd.DataFrame({
        'Date': dates,
        'Open': price_path * (1 - 0.005 * np.random.random(n_days)),
        'High': price_path * (1 + 0.01 * np.random.random(n_days)),
        'Low': price_path * (1 - 0.01 * np.random.random(n_days)),
        'Close': price_path,
        'Volume': volumes,
        'Ticker': ticker
    })
    
    # 整合性の確保（High > Open > Low など）
    for i in range(len(df)):
        high = max(df.loc[i, 'Open'], df.loc[i, 'Close']) * (1 + 0.005 * random.random())
        low = min(df.loc[i, 'Open'], df.loc[i, 'Close']) * (1 - 0.005 * random.random())
        df.loc[i, 'High'] = high
        df.loc[i, 'Low'] = low
    
    return df

def generate_all_ticker_data(tickers, start_date, end_date, output_dir="data", single_file=True):
    """
    複数銘柄のデータを生成して保存
    
    Parameters:
    -----------
    tickers : list
        ティッカーリスト
    start_date : datetime
        開始日
    end_date : datetime
        終了日
    output_dir : str
        出力ディレクトリ
    single_file : bool
        True: 全銘柄を1つのファイルに保存
        False: 銘柄ごとに個別ファイルに保存
    
    Returns:
    --------
    str
        保存したファイルパス
    """
    # 出力ディレクトリの作成
    os.makedirs(output_dir, exist_ok=True)
    
    all_data = []
    
    # 各銘柄のデータを生成
    for ticker in tickers:
        print(f"{ticker}のデータを生成中...")
        df = generate_ticker_data(ticker, start_date, end_date)
        all_data.append(df)
        
        # 個別ファイルに保存する場合
        if not single_file:
            ticker_file = os.path.join(output_dir, f"{ticker.replace('.', '_')}_data.csv")
            df.to_csv(ticker_file, index=False)
            print(f"- {ticker_file}に保存しました ({len(df)}行)")
    
    # 全銘柄を1つのファイルに保存
    if single_file:
        combined_df = pd.concat(all_data, ignore_index=True)
        combined_file = os.path.join(output_dir, "nikkei225_sample_data.csv")
        combined_df.to_csv(combined_file, index=False)
        print(f"すべてのデータを{combined_file}に保存しました ({len(combined_df)}行)")
        
        # 特徴量計算用のファイルも生成
        feature_file = os.path.join(output_dir, "nikkei225_with_features.csv")
        feature_df = generate_features(combined_df)
        feature_df.to_csv(feature_file, index=False)
        print(f"特徴量データを{feature_file}に保存しました ({len(feature_df)}行)")
        
        return combined_file
    else:
        return output_dir

def generate_features(df):
    """
    株価データに特徴量を追加
    
    Parameters:
    -----------
    df : pd.DataFrame
        株価データ
    
    Returns:
    --------
    pd.DataFrame
        特徴量を追加したデータ
    """
    # 日付でソート
    df = df.sort_values(['Ticker', 'Date'])
    
    # 前日比を計算
    df['PrevClose'] = df.groupby('Ticker')['Close'].shift(1)
    df['Change'] = df['Close'] - df['PrevClose']
    df['ChangePercent'] = (df['Change'] / df['PrevClose']) * 100
    
    # 5日、20日移動平均を計算
    df['MA5'] = df.groupby('Ticker')['Close'].transform(lambda x: x.rolling(window=5).mean())
    df['MA20'] = df.groupby('Ticker')['Close'].transform(lambda x: x.rolling(window=20).mean())
    
    # ボリンジャーバンド（20日）
    df['StdDev20'] = df.groupby('Ticker')['Close'].transform(lambda x: x.rolling(window=20).std())
    df['UpperBand'] = df['MA20'] + (df['StdDev20'] * 2)
    df['LowerBand'] = df['MA20'] - (df['StdDev20'] * 2)
    
    # 相対ストレングス指標（RSI）の計算（14日）
    df['Gain'] = df.groupby('Ticker')['Change'].transform(lambda x: x.clip(lower=0))
    df['Loss'] = df.groupby('Ticker')['Change'].transform(lambda x: -x.clip(upper=0))
    df['AvgGain'] = df.groupby('Ticker')['Gain'].transform(lambda x: x.rolling(window=14).mean())
    df['AvgLoss'] = df.groupby('Ticker')['Loss'].transform(lambda x: x.rolling(window=14).mean())
    df['RS'] = df['AvgGain'] / df['AvgLoss'].replace(0, 0.00001)  # ゼロ除算を防止
    df['RSI'] = 100 - (100 / (1 + df['RS']))
    
    # MACD
    df['EMA12'] = df.groupby('Ticker')['Close'].transform(lambda x: x.ewm(span=12, adjust=False).mean())
    df['EMA26'] = df.groupby('Ticker')['Close'].transform(lambda x: x.ewm(span=26, adjust=False).mean())
    df['MACD'] = df['EMA12'] - df['EMA26']
    df['SignalLine'] = df.groupby('Ticker')['MACD'].transform(lambda x: x.ewm(span=9, adjust=False).mean())
    df['MACDHist'] = df['MACD'] - df['SignalLine']
    
    # 出来高変化率
    df['VolumeChange'] = df.groupby('Ticker')['Volume'].transform(lambda x: x.pct_change())
    
    # 価格帯別出来高
    df['PriceVolume'] = df['Close'] * df['Volume']
    
    # 不要な中間列を削除
    df = df.drop(['Gain', 'Loss', 'AvgGain', 'AvgLoss', 'RS'], axis=1)
    
    # NaNを削除
    df = df.dropna()
    
    return df

def main():
    """メイン関数"""
    parser = argparse.ArgumentParser(description='日経225サンプルデータを生成')
    parser.add_argument('--years', type=int, default=5, help='生成する年数（デフォルト: 5年）')
    parser.add_argument('--output-dir', type=str, default='data', help='出力ディレクトリ（デフォルト: data）')
    parser.add_argument('--separate-files', action='store_true', help='銘柄ごとに個別ファイルに保存')
    parser.add_argument('--tickers', type=int, default=30, help='生成する銘柄数（デフォルト: 30銘柄）')
    
    args = parser.parse_args()
    
    # 期間の設定
    end_date = datetime.now()
    start_date = end_date - timedelta(days=365 * args.years)
    
    # ティッカーリストの準備
    if args.tickers <= len(MAJOR_TICKERS):
        selected_tickers = MAJOR_TICKERS[:args.tickers]
    else:
        selected_tickers = MAJOR_TICKERS
    
    print(f"{start_date.strftime('%Y-%m-%d')}～{end_date.strftime('%Y-%m-%d')}の期間で")
    print(f"{len(selected_tickers)}銘柄のサンプルデータを生成します")
    
    # データ生成と保存
    output_path = generate_all_ticker_data(
        selected_tickers,
        start_date,
        end_date,
        output_dir=args.output_dir,
        single_file=not args.separate_files
    )
    
    print(f"\nデータ生成が完了しました")
    print(f"出力: {output_path}")
    
    # データの使用方法
    print("\n利用方法:")
    print("1. バッチプロセッサーをテストデータで実行する場合:")
    print("   python start_enhanced_batch_processor.py --test-data")
    print("\n2. 特定の銘柄のデータを確認する場合:")
    print("   head -n 10 data/nikkei225_sample_data.csv | grep 7203.T")


if __name__ == "__main__":
    main()
