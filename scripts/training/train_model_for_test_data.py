#!/usr/bin/env python3
import os
# Force TensorFlow to use CPU only
os.environ["CUDA_VISIBLE_DEVICES"] = "-1"

import pandas as pd
import numpy as np
from datetime import datetime
from src.model import NikkeiAIModel
from src.feature_engineering import FeatureEngineer

def main():
    """テストデータを使用してモデルをトレーニングし、予測を行う"""
    print("テストデータを使用してモデルをトレーニングします")
    
    # データディレクトリが存在することを確認
    os.makedirs("data", exist_ok=True)
    os.makedirs("models", exist_ok=True)
    
    # 特徴量が含まれたデータを読み込む
    data_file = os.path.join("data", "nikkei225_with_features.csv")
    
    if not os.path.exists(data_file):
        print(f"データファイルが見つかりません: {data_file}")
        print("まず特徴量エンジニアリングを実行してください: python src/feature_engineering.py")
        return False
    
    print(f"データファイルを読み込み: {data_file}")
    df = pd.read_csv(data_file, parse_dates=['Datetime'])
    
    # モデルのインスタンスを作成
    model = NikkeiAIModel(model_dir="models", data_dir="data")
    
    # 特徴量を準備
    prepared_data = model.prepare_features(
        df,
        ticker=None,  # すべての銘柄でトレーニング
        target_col='Close',
        sequence_length=24,
        prediction_horizon=1,  # 1時間先を予測
        train_ratio=0.8
    )
    
    # モデルのトレーニング
    results = model.train_models(
        prepared_data,
        ticker="nikkei_all",
        epochs=20,  # テスト用に短く
        batch_size=32
    )
    
    # モデルのメタデータを保存
    model.save_model_metadata("nikkei_all", results)
    
    # 株式を評価して上位5銘柄を選択
    top_stocks = model.evaluate_and_select_stocks(
        df,
        prediction_horizon=1,  # 1時間先を予測
        top_n=5  # 上位5銘柄を選択
    )
    
    # 上位銘柄の保存
    if not top_stocks.empty:
        top_stocks_file = os.path.join("results", f"top_stocks_{datetime.now().strftime('%Y%m%d_%H%M')}.csv")
        os.makedirs("results", exist_ok=True)
        top_stocks.to_csv(top_stocks_file, index=False)
        print(f"上位銘柄を保存しました: {top_stocks_file}")
        
        # 上位銘柄を表示
        print("\n選択された上位5銘柄:")
        print(top_stocks[['Ticker', 'Current_Price', 'Predicted_Price', 'Predicted_Change_Pct']])
    else:
        print("上位銘柄が見つかりませんでした")
    
    print("完了！")
    return True

if __name__ == "__main__":
    main()
