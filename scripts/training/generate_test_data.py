#!/usr/bin/env python3
import os
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

def generate_synthetic_stock_data(tickers, days=90, interval_hours=1):
    """
    Generate synthetic stock price data for testing
    
    Parameters:
    -----------
    tickers : list
        List of ticker symbols
    days : int
        Number of days of data to generate
    interval_hours : int
        Interval between data points in hours
        
    Returns:
    --------
    pd.DataFrame
        DataFrame with synthetic stock data
    """
    # Calculate number of data points
    hours_per_day = 6  # Typical market hours are about 6 hours
    points_per_ticker = days * hours_per_day // interval_hours
    
    all_data = []
    
    for ticker in tickers:
        # Start price between 1000 and 5000
        base_price = np.random.uniform(1000, 5000)
        
        # Time series date range
        end_date = datetime.now().replace(hour=15, minute=0, second=0, microsecond=0)
        start_date = end_date - timedelta(days=days)
        
        # Create date range with market hours (9 AM to 3 PM)
        market_hours = []
        current_date = start_date
        while current_date <= end_date:
            if current_date.weekday() < 5:  # Weekdays only
                for hour in range(9, 15, interval_hours):
                    market_hours.append(current_date.replace(hour=hour))
            current_date += timedelta(days=1)
        
        market_hours = market_hours[-points_per_ticker:]  # Keep only required number of points
        
        # Generate price movements (random walk with momentum)
        price_changes = np.random.normal(0, 0.01, len(market_hours))
        
        # Add some momentum and mean reversion
        for i in range(3, len(price_changes)):
            price_changes[i] += 0.2 * np.mean(price_changes[i-3:i]) - 0.1 * np.sum(price_changes[i-3:i])
        
        # Calculate actual prices
        closes = base_price * np.cumprod(1 + price_changes)
        
        # Generate high, low, open
        volatility = np.random.uniform(0.005, 0.02, len(closes))
        highs = closes * (1 + volatility)
        lows = closes * (1 - volatility)
        opens = lows + np.random.rand(len(lows)) * (highs - lows)
        
        # Generate volume (higher for more volatile days)
        volumes = np.random.normal(100000, 50000, len(closes)) * (1 + 5 * np.abs(price_changes))
        volumes = np.maximum(volumes, 10000)  # Ensure minimum volume
        
        # Create DataFrame
        ticker_data = pd.DataFrame({
            'Datetime': market_hours,
            'Ticker': ticker,
            'Open': opens,
            'High': highs,
            'Low': lows,
            'Close': closes,
            'Volume': volumes.astype(int)
        })
        
        all_data.append(ticker_data)
    
    # Combine all tickers
    result = pd.concat(all_data, ignore_index=True)
    result = result.sort_values(['Ticker', 'Datetime'])
    
    return result

def main():
    """Generate and save synthetic test data"""
    print("Generating synthetic test data for Nikkei 225 stocks")
    
    # Create directories
    os.makedirs("data", exist_ok=True)
    
    # Generate data for 10 tickers to keep file size manageable
    tickers = [
        '1332.T', '1333.T', '1376.T', '1605.T', '1721.T', 
        '1801.T', '1802.T', '1803.T', '1808.T', '1812.T'
    ]
    
    # Generate data
    data = generate_synthetic_stock_data(tickers, days=90, interval_hours=1)
    
    # Save raw data
    raw_data_file = os.path.join("data", "nikkei225_data.csv")
    data.to_csv(raw_data_file, index=False)
    print(f"Raw data saved to {raw_data_file}")
    
    # Save cleaned data (same as raw in this case since it's already clean)
    cleaned_file = os.path.join("data", "nikkei225_cleaned.csv")
    data.to_csv(cleaned_file, index=False)
    print(f"Cleaned data saved to {cleaned_file}")
    
    print(f"Generated data for {data['Ticker'].nunique()} tickers with {len(data)} rows")
    
if __name__ == "__main__":
    main()
