#!/usr/bin/env python3
"""
日経225 AI取引システム - 継続学習バッチプロセッサー

継続学習パイプラインを定期的に実行し、自動的にモデルを改善するバッチ処理スクリプト
"""

import os
import sys
import time
import logging
import argparse
import json
import schedule
from datetime import datetime, timedelta
import traceback

# 自作モジュールをインポート
from src.continuous_learning_pipeline import ContinuousLearningPipeline

# ロギング設定
log_dir = 'logs'
os.makedirs(log_dir, exist_ok=True)

log_file = os.path.join(log_dir, f"continuous_learning_batch_{datetime.now().strftime('%Y%m%d')}.log")
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_file),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("継続学習バッチ")

class ContinuousLearningBatchProcessor:
    """継続学習プロセスを自動化するバッチ処理クラス"""
    
    def __init__(self, config=None):
        """初期化"""
        self.config = config or self._load_default_config()
        self.status_file = os.path.join(log_dir, "continuous_learning_status.json")
        self.status = self._load_status()
        self.pipeline = ContinuousLearningPipeline(
            base_dir=self.config.get("base_dir", "."),
            data_dir=self.config.get("data_dir", "data"),
            models_dir=self.config.get("models_dir", "models"),
            results_dir=self.config.get("results_dir", "results")
        )
    
    def _load_default_config(self):
        """デフォルト設定の読み込み"""
        return {
            "base_dir": ".",
            "data_dir": "data",
            "models_dir": "models",
            "results_dir": "results",
            "schedules": {
                "daily_data_collection": {
                    "time": "17:00",
                    "days": ["monday", "tuesday", "wednesday", "thursday", "friday"]
                },
                "weekly_model_retraining": {
                    "time": "23:00",
                    "days": ["sunday"]
                },
                "monthly_full_cycle": {
                    "day": 1,
                    "time": "02:00"
                }
            },
            "notification": {
                "enabled": False,
                "email": None,
                "telegram": None
            }
        }
    
    def _load_status(self):
        """ステータスの読み込み"""
        if os.path.exists(self.status_file):
            try:
                with open(self.status_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception as e:
                logger.error(f"ステータスファイルの読み込みエラー: {str(e)}")
        
        # デフォルトのステータス
        return {
            "last_run": {
                "daily_data_collection": None,
                "weekly_model_retraining": None,
                "monthly_full_cycle": None
            },
            "success_counts": {
                "daily_data_collection": 0,
                "weekly_model_retraining": 0,
                "monthly_full_cycle": 0
            },
            "error_counts": {
                "daily_data_collection": 0,
                "weekly_model_retraining": 0,
                "monthly_full_cycle": 0
            },
            "model_improvements": 0,
            "last_model_version": None,
            "start_time": datetime.now().isoformat()
        }
    
    def _save_status(self):
        """ステータスの保存"""
        try:
            with open(self.status_file, 'w', encoding='utf-8') as f:
                json.dump(self.status, f, indent=2, ensure_ascii=False)
            logger.debug("ステータスファイルを保存しました")
        except Exception as e:
            logger.error(f"ステータスファイルの保存エラー: {str(e)}")
    
    def _update_status(self, job_type, success, result=None):
        """ステータスの更新"""
        self.status["last_run"][job_type] = datetime.now().isoformat()
        
        if success:
            self.status["success_counts"][job_type] += 1
            
            # モデル改善の場合
            if result and result.get("improved_model", False):
                self.status["model_improvements"] += 1
                self.status["last_model_version"] = result.get("adopted_model_version")
        else:
            self.status["error_counts"][job_type] += 1
        
        self._save_status()
    
    def _send_notification(self, message, level="info"):
        """通知の送信"""
        if not self.config.get("notification", {}).get("enabled", False):
            return
        
        logger.info(f"通知を送信: {message}")
        # ここに通知処理を実装（メール、Telegram等）
    
    def run_daily_data_collection(self):
        """日次データ収集ジョブ"""
        logger.info("日次データ収集ジョブを開始")
        
        try:
            # 取引時間後の時間帯にデータを収集
            data = self.pipeline.collect_latest_data()
            
            if data is not None:
                # 特徴量エンジニアリング
                processed_data = self.pipeline.engineer_features(data)
                
                if processed_data is not None:
                    logger.info(f"日次データ収集が完了しました: {len(processed_data)}行")
                    self._update_status("daily_data_collection", True)
                    self._send_notification("日次データ収集が完了しました")
                    return True
            
            logger.error("日次データ収集に失敗しました")
            self._update_status("daily_data_collection", False)
            self._send_notification("日次データ収集に失敗しました", level="error")
            return False
            
        except Exception as e:
            logger.error(f"日次データ収集エラー: {str(e)}")
            logger.debug(traceback.format_exc())
            self._update_status("daily_data_collection", False)
            self._send_notification(f"日次データ収集エラー: {str(e)}", level="error")
            return False
    
    def run_weekly_model_retraining(self):
        """週次モデル再学習ジョブ"""
        logger.info("週次モデル再学習ジョブを開始")
        
        try:
            # モデル評価
            evaluation_results = self.pipeline.evaluate_model()
            
            if evaluation_results is None:
                logger.error("モデル評価に失敗しました")
                self._update_status("weekly_model_retraining", False)
                return False
            
            # パラメータ最適化
            optimized_model = self.pipeline.optimize_model(evaluation_results)
            
            if optimized_model is None:
                logger.error("モデル最適化に失敗しました")
                self._update_status("weekly_model_retraining", False)
                return False
            
            # モデル再学習
            training_result = self.pipeline.retrain_model(optimized_model)
            
            if training_result is None or not training_result.get("completed", False):
                logger.error("モデル再学習に失敗しました")
                self._update_status("weekly_model_retraining", False)
                return False
            
            # 比較と採用判断
            old_version = self.pipeline.model_versions["current_version"]
            new_version = optimized_model["version"]
            
            adoption_decision = self.pipeline.compare_and_adopt(old_version, new_version, training_result)
            
            if adoption_decision:
                logger.info(f"新モデル ({new_version}) を採用しました")
                self._send_notification(f"新モデル ({new_version}) を採用しました")
            else:
                logger.info(f"現行モデル ({old_version}) を維持します")
            
            result = {
                "improved_model": adoption_decision,
                "adopted_model_version": new_version if adoption_decision else None
            }
            
            self._update_status("weekly_model_retraining", True, result)
            return True
            
        except Exception as e:
            logger.error(f"週次モデル再学習エラー: {str(e)}")
            logger.debug(traceback.format_exc())
            self._update_status("weekly_model_retraining", False)
            self._send_notification(f"週次モデル再学習エラー: {str(e)}", level="error")
            return False
    
    def run_monthly_full_cycle(self):
        """月次完全サイクルジョブ"""
        logger.info("月次完全サイクルジョブを開始")
        
        try:
            # 完全な継続学習サイクルを実行
            result = self.pipeline.run_continuous_learning_cycle(collect_new_data=True)
            
            if "error" in result:
                logger.error(f"月次完全サイクルエラー: {result['error']}")
                self._update_status("monthly_full_cycle", False)
                self._send_notification(f"月次完全サイクルエラー: {result['error']}", level="error")
                return False
            
            if result.get("improved_model", False):
                message = f"モデルが改善されました！新バージョン: {result.get('adopted_model_version')}"
                logger.info(message)
                self._send_notification(message)
            else:
                logger.info("現行モデルが維持されました（十分な改善が見られなかったため）")
            
            self._update_status("monthly_full_cycle", True, result)
            return True
            
        except Exception as e:
            logger.error(f"月次完全サイクルエラー: {str(e)}")
            logger.debug(traceback.format_exc())
            self._update_status("monthly_full_cycle", False)
            self._send_notification(f"月次完全サイクルエラー: {str(e)}", level="error")
            return False
    
    def setup_schedules(self):
        """スケジュールの設定"""
        logger.info("スケジュールを設定")
        
        # 日次データ収集のスケジュール
        daily_schedule = self.config.get("schedules", {}).get("daily_data_collection", {})
        
        if daily_schedule:
            time_str = daily_schedule.get("time", "17:00")
            days = daily_schedule.get("days", ["monday", "tuesday", "wednesday", "thursday", "friday"])
            
            for day in days:
                if day.lower() == "monday":
                    schedule.every().monday.at(time_str).do(self.run_daily_data_collection)
                elif day.lower() == "tuesday":
                    schedule.every().tuesday.at(time_str).do(self.run_daily_data_collection)
                elif day.lower() == "wednesday":
                    schedule.every().wednesday.at(time_str).do(self.run_daily_data_collection)
                elif day.lower() == "thursday":
                    schedule.every().thursday.at(time_str).do(self.run_daily_data_collection)
                elif day.lower() == "friday":
                    schedule.every().friday.at(time_str).do(self.run_daily_data_collection)
                elif day.lower() == "saturday":
                    schedule.every().saturday.at(time_str).do(self.run_daily_data_collection)
                elif day.lower() == "sunday":
                    schedule.every().sunday.at(time_str).do(self.run_daily_data_collection)
            
            logger.info(f"日次データ収集スケジュール: {', '.join(days)} at {time_str}")
        
        # 週次モデル再学習のスケジュール
        weekly_schedule = self.config.get("schedules", {}).get("weekly_model_retraining", {})
        
        if weekly_schedule:
            time_str = weekly_schedule.get("time", "23:00")
            days = weekly_schedule.get("days", ["friday"])
            
            for day in days:
                if day.lower() == "monday":
                    schedule.every().monday.at(time_str).do(self.run_weekly_model_retraining)
                elif day.lower() == "tuesday":
                    schedule.every().tuesday.at(time_str).do(self.run_weekly_model_retraining)
                elif day.lower() == "wednesday":
                    schedule.every().wednesday.at(time_str).do(self.run_weekly_model_retraining)
                elif day.lower() == "thursday":
                    schedule.every().thursday.at(time_str).do(self.run_weekly_model_retraining)
                elif day.lower() == "friday":
                    schedule.every().friday.at(time_str).do(self.run_weekly_model_retraining)
                elif day.lower() == "saturday":
                    schedule.every().saturday.at(time_str).do(self.run_weekly_model_retraining)
                elif day.lower() == "sunday":
                    schedule.every().sunday.at(time_str).do(self.run_weekly_model_retraining)
            
            logger.info(f"週次モデル再学習スケジュール: {', '.join(days)} at {time_str}")
        
        # 月次完全サイクルのスケジュール
        monthly_schedule = self.config.get("schedules", {}).get("monthly_full_cycle", {})
        
        if monthly_schedule:
            day = monthly_schedule.get("day", 1)
            time_str = monthly_schedule.get("time", "02:00")
            
            schedule.every().month.at(f"{day:02d} {time_str}").do(self.run_monthly_full_cycle)
            logger.info(f"月次完全サイクルスケジュール: 毎月 {day}日 {time_str}")
    
    def run_manually(self, job_type):
        """ジョブを手動実行"""
        if job_type == "daily":
            return self.run_daily_data_collection()
        elif job_type == "weekly":
            return self.run_weekly_model_retraining()
        elif job_type == "monthly":
            return self.run_monthly_full_cycle()
        else:
            logger.error(f"不明なジョブタイプ: {job_type}")
            return False
    
    def run_scheduler(self):
        """スケジューラーを実行"""
        logger.info("スケジューラーを開始")
        
        self.setup_schedules()
        
        try:
            while True:
                schedule.run_pending()
                time.sleep(60)  # 1分ごとにスケジュールをチェック
        except KeyboardInterrupt:
            logger.info("スケジューラーを停止します（Ctrl+Cが押されました）")
        except Exception as e:
            logger.error(f"スケジューラー実行エラー: {str(e)}")
            logger.debug(traceback.format_exc())
            self._send_notification(f"スケジューラーエラー: {str(e)}", level="error")
            return False
        
        return True

def load_config(config_file=None):
    """設定ファイルを読み込む"""
    default_config_file = "continuous_learning_config.json"
    
    if config_file is None and os.path.exists(default_config_file):
        config_file = default_config_file
    
    if config_file and os.path.exists(config_file):
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
            logger.info(f"設定ファイルを読み込みました: {config_file}")
            return config
        except Exception as e:
            logger.error(f"設定ファイルの読み込みエラー: {str(e)}")
    
    return None

def parse_args():
    """コマンドライン引数を解析"""
    parser = argparse.ArgumentParser(description='日経225 AI取引システム - 継続学習バッチプロセッサー')
    
    # 動作モード
    parser.add_argument('--mode', type=str, choices=['scheduler', 'manual'], default='scheduler',
                      help='実行モード（scheduler=スケジューラー実行, manual=手動実行）')
    
    # 手動実行用の引数
    parser.add_argument('--job', type=str, choices=['daily', 'weekly', 'monthly'],
                      help='手動実行するジョブタイプ（daily=データ収集, weekly=モデル再学習, monthly=完全サイクル）')
    
    # 設定ファイル
    parser.add_argument('--config', type=str, help='設定ファイルのパス')
    
    # その他のオプション
    parser.add_argument('--verbose', action='store_true', help='詳細なログを出力')
    
    return parser.parse_args()

def main():
    """メイン関数"""
    # 引数の解析
    args = parse_args()
    
    # ログレベルの設定
    if args.verbose:
        logger.setLevel(logging.DEBUG)
    
    # 設定の読み込み
    config = load_config(args.config)
    
    # バッチプロセッサーの初期化
    processor = ContinuousLearningBatchProcessor(config)
    
    # 動作モードに応じた処理
    if args.mode == 'manual':
        if not args.job:
            logger.error("手動実行にはジョブタイプ（--job）が必要です")
            return 1
        
        logger.info(f"手動実行モード: {args.job}")
        success = processor.run_manually(args.job)
        
        if success:
            logger.info(f"{args.job}ジョブが正常に完了しました")
            return 0
        else:
            logger.error(f"{args.job}ジョブの実行に失敗しました")
            return 1
    
    else:  # 'scheduler'
        logger.info("スケジューラーモード")
        return 0 if processor.run_scheduler() else 1

if __name__ == "__main__":
    sys.exit(main())
