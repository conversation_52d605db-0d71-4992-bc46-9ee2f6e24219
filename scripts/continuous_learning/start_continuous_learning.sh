#!/bin/bash
# 日経225 AI取引システム - 継続学習パイプライン起動スクリプト
#
# 継続学習バッチプロセッサーをバックグラウンドで起動し、ログを記録します

# ディレクトリ設定
LOG_DIR="logs"
RUN_DIR="."
CONFIG_FILE="continuous_learning_config.json"

# 現在の日時
CURRENT_DATE=$(date +"%Y%m%d")
CURRENT_TIME=$(date +"%H%M%S")

# ログファイル
LOG_FILE="${LOG_DIR}/continuous_learning_${CURRENT_DATE}_${CURRENT_TIME}.log"

# 必要なディレクトリを作成
mkdir -p $LOG_DIR

# 設定ファイルの確認
if [ ! -f "$CONFIG_FILE" ]; then
    echo "警告: 設定ファイル ($CONFIG_FILE) が見つかりません。デフォルト設定で実行します。"
    
    # デフォルト設定ファイルの作成
    cat > $CONFIG_FILE << EOF
{
  "base_dir": ".",
  "data_dir": "data",
  "models_dir": "models",
  "results_dir": "results",
  "schedules": {
    "daily_data_collection": {
      "time": "17:00",
      "days": ["monday", "tuesday", "wednesday", "thursday", "friday"]
    },
    "weekly_model_retraining": {
      "time": "23:00",
      "days": ["sunday"]
    },
    "monthly_full_cycle": {
      "day": 1,
      "time": "02:00"
    }
  },
  "notification": {
    "enabled": false,
    "email": null,
    "telegram": null
  }
}
EOF
    echo "デフォルト設定ファイルを作成しました: $CONFIG_FILE"
fi

# 既に実行中かチェック
if pgrep -f "python continuous_learning_batch_processor.py" > /dev/null; then
    echo "警告: 継続学習プロセスが既に実行中です。"
    echo "実行中のプロセスを終了するには: pkill -f \"python continuous_learning_batch_processor.py\""
    exit 1
fi

# 手動実行モード
if [ "$1" = "--manual" ]; then
    if [ -z "$2" ]; then
        echo "エラー: 手動実行モードではジョブタイプを指定してください。"
        echo "使用法: $0 --manual [daily|weekly|monthly]"
        exit 1
    fi
    
    echo "手動実行モード: $2"
    python continuous_learning_batch_processor.py --mode manual --job $2 --verbose
    exit $?
fi

# スケジューラーモードでバックグラウンド実行
echo "継続学習プロセスを開始します..."
echo "ログは次のファイルに記録されます: $LOG_FILE"

# バックグラウンドで実行
nohup python continuous_learning_batch_processor.py --mode scheduler --config $CONFIG_FILE --verbose > "$LOG_FILE" 2>&1 &

# プロセスID
PID=$!
echo "プロセスID: $PID"
echo $PID > "${LOG_DIR}/continuous_learning.pid"

echo "継続学習プロセスがバックグラウンドで起動しました。"
echo "プロセスを停止するには: kill $PID または bash stop_continuous_learning.sh"

# 停止用スクリプトを作成
cat > stop_continuous_learning.sh << EOF
#!/bin/bash
# 継続学習プロセスを停止するスクリプト
if [ -f "${LOG_DIR}/continuous_learning.pid" ]; then
    PID=\$(cat "${LOG_DIR}/continuous_learning.pid")
    if ps -p \$PID > /dev/null; then
        echo "継続学習プロセス (PID: \$PID) を停止します..."
        kill \$PID
        echo "プロセスを停止しました。"
    else
        echo "プロセス (PID: \$PID) は既に実行されていません。"
    fi
    rm "${LOG_DIR}/continuous_learning.pid"
else
    echo "PIDファイルが見つかりません。"
    echo "手動で停止するには: pkill -f \"python continuous_learning_batch_processor.py\""
fi
EOF

chmod +x stop_continuous_learning.sh
echo "停止用スクリプトを作成しました: stop_continuous_learning.sh"

# ログの表示（オプション）
if [ "$1" = "--tail" ]; then
    echo "ログを表示しています (Ctrl+Cで終了)..."
    tail -f "$LOG_FILE"
fi
