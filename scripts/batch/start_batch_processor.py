#!/usr/bin/env python3
import os
import sys
import time
import logging
from datetime import datetime, timedelta
import pytz
import subprocess

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f"startup_{datetime.now().strftime('%Y%m%d')}.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def wait_until_market_open():
    """
    Wait until the next market open time (9:00 AM JST on a weekday)
    """
    jst = pytz.timezone('Asia/Tokyo')
    now = datetime.now(jst)
    
    # Target time: 9:00 AM JST
    target_hour, target_minute = 9, 0
    
    # Create target datetime for today
    target = now.replace(hour=target_hour, minute=target_minute, second=0, microsecond=0)
    
    # If target time has already passed today, set target to tomorrow
    if now > target:
        target = target + timedelta(days=1)
    
    # Adjust for weekends
    while target.weekday() >= 5:  # 5=Saturday, 6=Sunday
        target = target + timedelta(days=1)
    
    # Calculate wait time
    wait_seconds = (target - now).total_seconds()
    
    if wait_seconds > 0:
        logger.info(f"Waiting until market open at {target.strftime('%Y-%m-%d %H:%M:%S %Z')}")
        logger.info(f"Sleeping for {wait_seconds:.0f} seconds ({wait_seconds/3600:.1f} hours)")
        time.sleep(wait_seconds)
    else:
        logger.info("Market is already open")

def start_batch_processor():
    """
    Start the batch processor
    """
    logger.info("Starting batch processor")
    
    # Check if src/batch_processor.py exists
    if not os.path.exists("src/batch_processor.py"):
        logger.error("batch_processor.py not found in src directory")
        sys.exit(1)
    
    # Make sure it's executable
    os.chmod("src/batch_processor.py", 0o755)
    
    # Start the process
    try:
        process = subprocess.Popen(
            ["python", "src/batch_processor.py"],
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            universal_newlines=True
        )
        
        logger.info(f"Started batch processor with PID: {process.pid}")
        
        # Monitor the process
        while True:
            output = process.stdout.readline()
            if output == '' and process.poll() is not None:
                break
            if output:
                logger.info(output.strip())
        
        # Process exited
        return_code = process.poll()
        logger.info(f"Batch processor exited with return code: {return_code}")
        
        if return_code != 0:
            logger.error("Batch processor failed. Restarting...")
            start_batch_processor()
            
    except Exception as e:
        logger.error(f"Error starting batch processor: {str(e)}")
        sys.exit(1)

def main():
    """
    Main function
    """
    logger.info("Starting script")
    
    # Parse command line arguments
    import argparse
    parser = argparse.ArgumentParser(description='Start batch processor for Nikkei 225 AI Trading')
    parser.add_argument('--wait', action='store_true', help='Wait until market open')
    args = parser.parse_args()
    
    # Wait until market open if requested
    if args.wait:
        wait_until_market_open()
    
    # Start batch processor
    start_batch_processor()

if __name__ == "__main__":
    main()
