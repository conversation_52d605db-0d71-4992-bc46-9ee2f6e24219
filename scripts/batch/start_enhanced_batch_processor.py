#!/usr/bin/env python3
import os
import sys
import time
import logging
from datetime import datetime, timedelta
import pytz
import subprocess
import argparse

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f"startup_{datetime.now().strftime('%Y%m%d')}.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def wait_until_market_open():
    """
    Wait until the next market open time (9:00 AM JST on a weekday)
    """
    jst = pytz.timezone('Asia/Tokyo')
    now = datetime.now(jst)
    
    # Target time: 9:00 AM JST
    target_hour, target_minute = 9, 0
    
    # Create target datetime for today
    target = now.replace(hour=target_hour, minute=target_minute, second=0, microsecond=0)
    
    # If target time has already passed today, set target to tomorrow
    if now > target:
        target = target + timedelta(days=1)
    
    # Adjust for weekends
    while target.weekday() >= 5:  # 5=Saturday, 6=Sunday
        target = target + timedelta(days=1)
    
    # Calculate wait time
    wait_seconds = (target - now).total_seconds()
    
    if wait_seconds > 0:
        logger.info(f"市場開始時刻まで待機します: {target.strftime('%Y-%m-%d %H:%M:%S %Z')}")
        logger.info(f"{wait_seconds:.0f}秒（{wait_seconds/3600:.1f}時間）待機します")
        time.sleep(wait_seconds)
    else:
        logger.info("市場はすでに開いています")

def start_batch_processor(args):
    """
    Start the enhanced batch processor
    
    Parameters:
    -----------
    args : argparse.Namespace
        Command line arguments
    """
    logger.info("拡張バッチプロセッサーを開始します")
    
    # Check if src/trading/batch_processor.py exists
    if not os.path.exists("src/trading/batch_processor.py"):
        logger.error("src/trading/batch_processor.py が見つかりません")
        sys.exit(1)
    
    # Make sure it's executable
    os.chmod("src/trading/batch_processor.py", 0o755)
    
    # スクリプトとして直接実行
    cmd = ["python", "src/trading/batch_processor.py"]
    
    if args.trading_mode:
        cmd.append("--trading-mode")
    
    if args.initial_balance:
        cmd.extend(["--initial-balance", str(args.initial_balance)])
    
    if args.max_stocks:
        cmd.extend(["--max-stocks", str(args.max_stocks)])
    
    if args.test_data:
        cmd.append("--test-data")
        logger.info("テストデータモードで実行します")
    
    # Start the process
    try:
        process = subprocess.Popen(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            universal_newlines=True
        )
        
        logger.info(f"バッチプロセッサーを開始しました（PID: {process.pid}）")
        
        # Monitor the process
        while True:
            output = process.stdout.readline()
            if output == '' and process.poll() is not None:
                break
            if output:
                logger.info(output.strip())
        
        # Process exited
        return_code = process.poll()
        logger.info(f"バッチプロセッサーが終了しました（リターンコード: {return_code}）")
        
        if return_code != 0:
            logger.error("バッチプロセッサーが失敗しました。再起動します...")
            start_batch_processor(args)
            
    except Exception as e:
        logger.error(f"バッチプロセッサー起動エラー: {str(e)}")
        sys.exit(1)

def main():
    """
    Main function
    """
    logger.info("スクリプトを開始します")
    
    # Parse command line arguments
    parser = argparse.ArgumentParser(description='Nikkei 225 AI Trading Enhanced Batch Processor')
    parser.add_argument('--wait', action='store_true', help='市場開始時刻まで待機')
    parser.add_argument('--trading-mode', action='store_true', help='実際の取引を行う（デフォルトは仮想取引）')
    parser.add_argument('--initial-balance', type=float, default=1000000, help='初期資金')
    parser.add_argument('--max-stocks', type=int, default=5, help='最大銘柄数')
    parser.add_argument('--test-data', action='store_true', help='テストデータを使用（APIデータ取得をスキップ）')
    args = parser.parse_args()
    
    # Wait until market open if requested
    if args.wait:
        wait_until_market_open()
    
    # Start batch processor
    start_batch_processor(args)

if __name__ == "__main__":
    main()
