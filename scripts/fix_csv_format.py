#!/usr/bin/env python3
import pandas as pd
import re

# データファイルのパス
input_file = "data/nikkei225_sample_data.csv"
output_file = "data/nikkei225_sample_data_fixed.csv"

# ヘッダー行を定義
headers = ["Datetime", "Open", "High", "Low", "Close", "Volume", "Ticker"]

# データを読み込む（区切り文字なし）
with open(input_file, 'r') as f:
    lines = f.readlines()

# ヘッダー行をスキップして処理
data_lines = lines[1:]
processed_data = []

for line in data_lines:
    # 文字列から日付部分を抽出
    datetime_match = re.match(r'([\d\-]+ [\d\:\.]+)', line)
    if datetime_match:
        datetime_str = datetime_match.group(1)
        # 日付の後の数値データを抽出
        rest = line[len(datetime_str):]
        # 数値を抽出（7つのフィールドを想定）
        numbers = re.findall(r'[\d\.]+', rest)
        if len(numbers) >= 6:  # Datetime + 5つの数値フィールド + Ticker
            # Open, High, Low, Close, Volume, Ticker を抽出
            open_price = numbers[0]
            high_price = numbers[1]
            low_price = numbers[2]
            close_price = numbers[3]
            volume = numbers[4]
            ticker = line.strip()[-5:]  # 行末の5文字がティッカー（例：7203.T）
            if not ticker.endswith('.T'):
                ticker = ticker[:4] + '.T'
            
            # CSVの行を作成
            row = [datetime_str, open_price, high_price, low_price, close_price, volume, ticker]
            processed_data.append(row)

# DataFrameに変換
df = pd.DataFrame(processed_data, columns=headers)

# CSVとして保存
df.to_csv(output_file, index=False)

print(f"Fix completed. Saved to {output_file}")
