#!/usr/bin/env python3
import os
import numpy as np
import pandas as pd
import json
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime, timedelta
import logging
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
import joblib
import time

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f"logs/ai_evaluator_{datetime.now().strftime('%Y%m%d')}.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class AIEvaluator:
    """
    AI予測の評価と改善提案を行う
    """
    def __init__(self, evaluation_dir="nikkei225_historical_data/ai_evaluation", 
                ticker_manager=None, lookback_days=30):
        """
        初期化
        
        Parameters:
        -----------
        evaluation_dir : str
            評価データディレクトリ
        ticker_manager : TickerManager or None
            ティッカー管理オブジェクト
        lookback_days : int
            評価に使用する過去の日数
        """
        self.evaluation_dir = evaluation_dir
        self.predictions_dir = os.path.join(evaluation_dir, "predictions")
        self.performance_dir = os.path.join(evaluation_dir, "performance")
        self.improvements_dir = os.path.join(evaluation_dir, "model_improvements")
        
        # ディレクトリがなければ作成
        os.makedirs(self.predictions_dir, exist_ok=True)
        os.makedirs(self.performance_dir, exist_ok=True)
        os.makedirs(self.improvements_dir, exist_ok=True)
        
        # ティッカー管理
        self.ticker_manager = ticker_manager
        
        # 評価期間
        self.lookback_days = lookback_days
        
        # 評価データの読み込み
        self.predictions = self._load_predictions()
        self.performance_metrics = self._load_performance_metrics()
    
    def record_prediction(self, prediction_data):
        """
        予測を記録
        
        Parameters:
        -----------
        prediction_data : dict
            予測データ
            {
                "timestamp": "2025-04-01T10:00:00",
                "ticker": "1332.T",
                "current_price": 1000.0,
                "predicted_price": 1050.0,
                "predicted_change_pct": 5.0,
                "confidence": 0.8,
                "features_used": ["price_ma5", "rsi", ...],
                "model_version": "v1.2.3",
                "prediction_horizon": 24  # 時間単位
            }
            
        Returns:
        --------
        str
            保存されたファイルパス
        """
        # 必須フィールドの確認
        required_fields = ["timestamp", "ticker", "current_price", "predicted_price"]
        for field in required_fields:
            if field not in prediction_data:
                raise ValueError(f"予測データに必須フィールドがありません: {field}")
        
        # タイムスタンプを解析
        if isinstance(prediction_data["timestamp"], str):
            timestamp = datetime.fromisoformat(prediction_data["timestamp"])
        else:
            timestamp = prediction_data["timestamp"]
        
        # 日付ディレクトリ
        date_str = timestamp.strftime('%Y-%m-%d')
        date_dir = os.path.join(self.predictions_dir, date_str)
        os.makedirs(date_dir, exist_ok=True)
        
        # 銘柄別予測ファイル
        ticker = prediction_data["ticker"]
        prediction_file = os.path.join(date_dir, f"{ticker}_predictions.json")
        
        # 既存の予測を読み込む
        predictions = []
        if os.path.exists(prediction_file):
            try:
                with open(prediction_file, 'r', encoding='utf-8') as f:
                    predictions = json.load(f)
            except Exception as e:
                logger.error(f"予測ファイルの読み込みエラー ({prediction_file}): {str(e)}")
        
        # 予測IDを生成
        prediction_id = f"{ticker}_{timestamp.strftime('%Y%m%d%H%M%S')}_{len(predictions)}"
        prediction_data["prediction_id"] = prediction_id
        
        # 予測変化率を計算（なければ）
        if "predicted_change_pct" not in prediction_data:
            current_price = prediction_data["current_price"]
            predicted_price = prediction_data["predicted_price"]
            prediction_data["predicted_change_pct"] = (predicted_price - current_price) / current_price * 100
        
        # タイムスタンプを文字列に変換
        if isinstance(prediction_data["timestamp"], datetime):
            prediction_data["timestamp"] = prediction_data["timestamp"].isoformat()
        
        # 予測を追加
        predictions.append(prediction_data)
        
        # 保存
        try:
            with open(prediction_file, 'w', encoding='utf-8') as f:
                json.dump(predictions, f, indent=2, ensure_ascii=False)
            logger.info(f"予測を記録しました: {prediction_file}, ID: {prediction_id}")
            
            # メモリ内のデータを更新
            self.predictions = self._load_predictions()
            
            return prediction_file
        except Exception as e:
            logger.error(f"予測の記録エラー: {str(e)}")
            return None
    
    def record_actual_result(self, prediction_id, actual_price, trade_result=None):
        """
        実際の結果を記録
        
        Parameters:
        -----------
        prediction_id : str
            予測ID
        actual_price : float
            実際の価格
        trade_result : dict or None
            取引結果
            {
                "executed": True,
                "buy_price": 1000.0,
                "sell_price": 1050.0,
                "profit": 50.0,
                "profit_pct": 5.0,
                "fees": 1.0
            }
            
        Returns:
        --------
        bool
            成功の場合はTrue
        """
        # 予測IDの解析
        parts = prediction_id.split('_')
        if len(parts) < 2:
            logger.error(f"不正な予測ID: {prediction_id}")
            return False
        
        ticker = parts[0]
        date_str = parts[1][:8]  # YYYYMMDD
        
        # 日付フォーマットを変換
        try:
            date = datetime.strptime(date_str, '%Y%m%d')
            date_str_formatted = date.strftime('%Y-%m-%d')
        except ValueError:
            logger.error(f"予測IDから日付を解析できません: {prediction_id}")
            return False
        
        # 予測ファイル
        prediction_file = os.path.join(self.predictions_dir, date_str_formatted, f"{ticker}_predictions.json")
        
        if not os.path.exists(prediction_file):
            logger.error(f"予測ファイルが見つかりません: {prediction_file}")
            return False
        
        try:
            # 予測を読み込む
            with open(prediction_file, 'r', encoding='utf-8') as f:
                predictions = json.load(f)
            
            # 該当する予測を検索
            found = False
            for pred in predictions:
                if pred.get("prediction_id") == prediction_id:
                    # 実際の価格を記録
                    pred["actual_price"] = actual_price
                    
                    # 実際の変化率を計算
                    current_price = pred["current_price"]
                    pred["actual_change_pct"] = (actual_price - current_price) / current_price * 100
                    
                    # 予測誤差を計算
                    pred["prediction_error"] = actual_price - pred["predicted_price"]
                    pred["prediction_error_pct"] = abs(pred["prediction_error"] / actual_price * 100)
                    
                    # 方向性が合っていたか
                    pred_direction = np.sign(pred["predicted_change_pct"])
                    actual_direction = np.sign(pred["actual_change_pct"])
                    pred["direction_correct"] = pred_direction == actual_direction
                    
                    # 取引結果の記録
                    if trade_result:
                        pred["trade_result"] = trade_result
                    
                    found = True
                    break
            
            if not found:
                logger.error(f"予測IDに一致する予測が見つかりません: {prediction_id}")
                return False
            
            # 保存
            with open(prediction_file, 'w', encoding='utf-8') as f:
                json.dump(predictions, f, indent=2, ensure_ascii=False)
            
            logger.info(f"実際の結果を記録しました: {prediction_file}, ID: {prediction_id}")
            
            # メモリ内のデータを更新
            self.predictions = self._load_predictions()
            
            return True
        except Exception as e:
            logger.error(f"実際の結果の記録エラー: {str(e)}")
            return False
    
    def _load_predictions(self):
        """
        過去の予測を読み込む
        
        Returns:
        --------
        list
            予測リスト
        """
        all_predictions = []
        
        # 現在の日付
        current_date = datetime.now()
        
        # lookback_days日前からの予測を読み込む
        for day in range(self.lookback_days):
            date = current_date - timedelta(days=day)
            date_str = date.strftime('%Y-%m-%d')
            date_dir = os.path.join(self.predictions_dir, date_str)
            
            if not os.path.exists(date_dir):
                continue
            
            # 日付ディレクトリ内の全予測ファイルを読み込む
            for file in os.listdir(date_dir):
                if file.endswith("_predictions.json"):
                    file_path = os.path.join(date_dir, file)
                    try:
                        with open(file_path, 'r', encoding='utf-8') as f:
                            predictions = json.load(f)
                        all_predictions.extend(predictions)
                    except Exception as e:
                        logger.error(f"予測ファイルの読み込みエラー ({file_path}): {str(e)}")
        
        return all_predictions
    
    def _load_performance_metrics(self):
        """
        過去のパフォーマンス指標を読み込む
        
        Returns:
        --------
        list
            パフォーマンス指標リスト
        """
        metrics_file = os.path.join(self.performance_dir, "model_metrics.csv")
        
        if os.path.exists(metrics_file):
            try:
                return pd.read_csv(metrics_file)
            except Exception as e:
                logger.error(f"パフォーマンス指標ファイルの読み込みエラー: {str(e)}")
        
        return pd.DataFrame()
    
    def evaluate_performance(self, save_metrics=True):
        """
        モデルパフォーマンスの評価
        
        Parameters:
        -----------
        save_metrics : bool
            指標を保存するかどうか
            
        Returns:
        --------
        dict
            評価指標
        """
        logger.info("モデルパフォーマンスの評価を開始")
        
        # 結果が記録されている予測のみを対象とする
        valid_predictions = [p for p in self.predictions if "actual_price" in p]
        
        if not valid_predictions:
            logger.warning("評価対象の予測がありません")
            return {}
        
        # 結果を抽出
        y_true = np.array([p["actual_price"] for p in valid_predictions])
        y_pred = np.array([p["predicted_price"] for p in valid_predictions])
        
        # 回帰指標
        regression_metrics = self._calculate_regression_metrics(y_true, y_pred)
        
        # 方向性予測指標
        directional_metrics = self._calculate_directional_metrics(valid_predictions)
        
        # 取引指標
        trading_metrics = self._calculate_trading_metrics(valid_predictions)
        
        # 銘柄別・セクター別の分析
        dimensional_analysis = self._analyze_by_dimensions(valid_predictions)
        
        # 総合評価
        overall_metrics = {
            "timestamp": datetime.now().isoformat(),
            "prediction_count": len(valid_predictions),
            "regression_metrics": regression_metrics,
            "directional_metrics": directional_metrics,
            "trading_metrics": trading_metrics,
            "dimensional_analysis": dimensional_analysis
        }
        
        # 強みと弱み
        strengths_weaknesses = self._identify_strengths_weaknesses(overall_metrics)
        overall_metrics["strengths"] = strengths_weaknesses["strengths"]
        overall_metrics["weaknesses"] = strengths_weaknesses["weaknesses"]
        
        # 改善提案
        overall_metrics["improvement_suggestions"] = self._generate_improvement_suggestions(overall_metrics)
        
        # 指標の保存
        if save_metrics:
            self._save_performance_metrics(overall_metrics)
        
        logger.info(f"モデルパフォーマンスの評価が完了しました。予測数: {len(valid_predictions)}")
        
        return overall_metrics
    
    def _calculate_regression_metrics(self, y_true, y_pred):
        """
        回帰指標の計算
        
        Parameters:
        -----------
        y_true : np.array
            実際の値
        y_pred : np.array
            予測値
            
        Returns:
        --------
        dict
            回帰指標
        """
        # 平均二乗誤差の平方根（予測値と実際値の差の二乗の平均の平方根）
        rmse = np.sqrt(mean_squared_error(y_true, y_pred))
        
        # 平均絶対誤差（予測値と実際値の差の絶対値の平均）
        mae = mean_absolute_error(y_true, y_pred)
        
        # 平均絶対パーセント誤差（相対的な誤差の大きさ）
        mape = np.mean(np.abs((y_true - y_pred) / y_true)) * 100
        
        # 決定係数（分散の説明率、1に近いほど良いモデル）
        r2 = r2_score(y_true, y_pred)
        
        return {
            "rmse": float(rmse),
            "mae": float(mae),
            "mape": float(mape),
            "r2": float(r2)
        }
    
    def _calculate_directional_metrics(self, predictions):
        """
        方向性予測指標の計算
        
        Parameters:
        -----------
        predictions : list
            予測リスト
            
        Returns:
        --------
        dict
            方向性予測指標
        """
        # 方向性の抽出
        true_direction = [np.sign(p.get("actual_change_pct", 0)) for p in predictions]
        pred_direction = [np.sign(p.get("predicted_change_pct", 0)) for p in predictions]
        
        # 方向性一致率（何%の方向予測が合っていたか）
        directional_accuracy = np.mean([t == p for t, p in zip(true_direction, pred_direction)]) * 100
        
        # 混同行列の要素
        true_positive = sum((t > 0) and (p > 0) for t, p in zip(true_direction, pred_direction))  # 上昇を上昇と予測
        false_positive = sum((t <= 0) and (p > 0) for t, p in zip(true_direction, pred_direction))  # 下落を上昇と予測
        true_negative = sum((t <= 0) and (p <= 0) for t, p in zip(true_direction, pred_direction))  # 下落を下落と予測
        false_negative = sum((t > 0) and (p <= 0) for t, p in zip(true_direction, pred_direction))  # 上昇を下落と予測
        
        # 精度
        accuracy = (true_positive + true_negative) / len(true_direction) if len(true_direction) > 0 else 0
        
        # 適合率（上昇と予測したうち実際に上昇した割合）
        precision = true_positive / (true_positive + false_positive) if (true_positive + false_positive) > 0 else 0
        
        # 再現率（実際の上昇のうち予測できた割合）
        recall = true_positive / (true_positive + false_negative) if (true_positive + false_negative) > 0 else 0
        
        # F1スコア（適合率と再現率の調和平均）
        f1 = 2 * (precision * recall) / (precision + recall) if (precision + recall) > 0 else 0
        
        return {
            "directional_accuracy": float(directional_accuracy),
            "accuracy": float(accuracy * 100),
            "precision": float(precision * 100),
            "recall": float(recall * 100),
            "f1": float(f1 * 100),
            "confusion_matrix": {
                "true_positive": int(true_positive),
                "false_positive": int(false_positive),
                "true_negative": int(true_negative),
                "false_negative": int(false_negative)
            }
        }
    
    def _calculate_trading_metrics(self, predictions):
        """
        取引ベースのパフォーマンス指標の計算
        
        Parameters:
        -----------
        predictions : list
            予測リスト
            
        Returns:
        --------
        dict
            取引指標
        """
        # 取引結果がある予測のみを抽出
        trades = [p for p in predictions if "trade_result" in p]
        
        if not trades:
            return {"warning": "No trades available for analysis"}
        
        # 初期資金
        initial_capital = 1000000  # 仮の値
        
        # 利益の計算
        total_profit = sum(trade.get("trade_result", {}).get("profit", 0) for trade in trades)
        total_profit_pct = (total_profit / initial_capital) * 100 if initial_capital > 0 else 0
        
        # 勝率の計算
        winning_trades = [trade for trade in trades if trade.get("trade_result", {}).get("profit", 0) > 0]
        win_rate = (len(winning_trades) / len(trades)) * 100 if len(trades) > 0 else 0
        
        # 平均リターン
        avg_return = np.mean([trade.get("trade_result", {}).get("profit_pct", 0) for trade in trades])
        
        # 最大ドローダウン計算
        equity_curve = [initial_capital]
        for trade in trades:
            profit = trade.get("trade_result", {}).get("profit", 0)
            equity_curve.append(equity_curve[-1] + profit)
        
        running_max = np.maximum.accumulate(equity_curve)
        drawdown = (equity_curve - running_max) / running_max * 100
        max_drawdown = abs(min(drawdown)) if len(drawdown) > 0 else 0
        
        # リスク調整後リターン
        returns = [trade.get("trade_result", {}).get("profit_pct", 0) for trade in trades]
        sharpe_ratio = np.mean(returns) / np.std(returns) if len(returns) > 1 and np.std(returns) > 0 else 0
        
        # プロフィットファクター（総利益÷総損失）
        total_gain = sum(max(trade.get("trade_result", {}).get("profit", 0), 0) for trade in trades)
        total_loss = abs(sum(min(trade.get("trade_result", {}).get("profit", 0), 0) for trade in trades))
        profit_factor = total_gain / total_loss if total_loss > 0 else float('inf')
        
        return {
            "total_trades": len(trades),
            "winning_trades": len(winning_trades),
            "win_rate": float(win_rate),
            "total_profit": float(total_profit),
            "total_profit_pct": float(total_profit_pct),
            "avg_return": float(avg_return),
            "max_drawdown": float(max_drawdown),
            "sharpe_ratio": float(sharpe_ratio),
            "profit_factor": float(profit_factor) if profit_factor != float('inf') else "inf"
        }
    
    def _analyze_by_dimensions(self, predictions):
        """
        多次元分析（銘柄/セクター別）
        
        Parameters:
        -----------
        predictions : list
            予測リスト
            
        Returns:
        --------
        dict
            多次元分析結果
        """
        # 銘柄別分析
        ticker_analysis = {}
        
        # 全銘柄
        all_tickers = set(p["ticker"] for p in predictions)
        
        for ticker in all_tickers:
            ticker_predictions = [p for p in predictions if p["ticker"] == ticker]
            
            if len(ticker_predictions) < 5:  # 最低5件の予測が必要
                continue
            
            # 実際の値と予測値
            y_true = [p["actual_price"] for p in ticker_predictions]
            y_pred = [p["predicted_price"] for p in ticker_predictions]
            
            # 回帰指標
            regression_metrics = self._calculate_regression_metrics(np.array(y_true), np.array(y_pred))
            
            # 方向性予測指標
            directional_metrics = self._calculate_directional_metrics(ticker_predictions)
            
            ticker_analysis[ticker] = {
                "count": len(ticker_predictions),
                "regression_metrics": regression_metrics,
                "directional_metrics": directional_metrics
            }
        
        # セクター別分析
        sector_analysis = {}
        
        # ティッカー管理がある場合
        if self.ticker_manager:
            for sector in self.ticker_manager.get_all_sectors():
                sector_tickers = self.ticker_manager.get_tickers_by_sector(sector)
                sector_predictions = [p for p in predictions if p["ticker"] in sector_tickers]
                
                if len(sector_predictions) < 10:  # 最低10件の予測が必要
                    continue
                
                # 実際の値と予測値
                y_true = [p["actual_price"] for p in sector_predictions]
                y_pred = [p["predicted_price"] for p in sector_predictions]
                
                # 回帰指標
                regression_metrics = self._calculate_regression_metrics(np.array(y_true), np.array(y_pred))
                
                # 方向性予測指標
                directional_metrics = self._calculate_directional_metrics(sector_predictions)
                
                sector_analysis[sector] = {
                    "ticker_count": len(set(p["ticker"] for p in sector_predictions)),
                    "prediction_count": len(sector_predictions),
                    "regression_metrics": regression_metrics,
                    "directional_metrics": directional_metrics
                }
        
        # 時間軸別分析
        time_horizon_analysis = {}
        
        # 予測期間のタイプ分け
        horizons = {
            "short_term": [],   # 1時間〜1日
            "medium_term": [],  # 1日〜1週間
            "long_term": []     # 1週間以上
        }
        
        for p in predictions:
            horizon_hours = p.get("prediction_horizon", 24)  # デフォルト24時間
            
            if horizon_hours <= 24:
                horizons["short_term"].append(p)
            elif horizon_hours <= 168:  # 7日 * 24時間
                horizons["medium_term"].append(p)
            else:
                horizons["long_term"].append(p)
        
        for horizon_type, horizon_preds in horizons.items():
            if len(horizon_preds) < 10:  # 最低10件の予測が必要
                continue
            
            # 実際の値と予測値
            y_true = [p["actual_price"] for p in horizon_preds]
            y_pred = [p["predicted_price"] for p in horizon_preds]
            
            # 回帰指標
            regression_metrics = self._calculate_regression_metrics(np.array(y_true), np.array(y_pred))
            
            # 方向性予測指標
            directional_metrics = self._calculate_directional_metrics(horizon_preds)
            
            time_horizon_analysis[horizon_type] = {
                "count": len(horizon_preds),
                "regression_metrics": regression_metrics,
                "directional_metrics": directional_metrics
            }
        
        return {
            "ticker_analysis": ticker_analysis,
            "sector_analysis": sector_analysis,
            "time_horizon_analysis": time_horizon_analysis
        }
    
    def _identify_strengths_weaknesses(self, metrics):
        """
        強みと弱みを特定
        
        Parameters:
        -----------
        metrics : dict
            評価指標
            
        Returns:
        --------
        dict
            強みと弱み
        """
        strengths = []
        weaknesses = []
        
        # 方向性予測が70%以上なら強み
        directional_accuracy = metrics.get("directional_metrics", {}).get("directional_accuracy", 0)
        if directional_accuracy >= 70:
            strengths.append(f"方向性予測の精度が高い ({directional_accuracy:.1f}%)")
        elif directional_accuracy < 50:
            weaknesses.append(f"方向性予測の精度が低い ({directional_accuracy:.1f}%)")
        
        # 取引指標の分析
        win_rate = metrics.get("trading_metrics", {}).get("win_rate", 0)
        if win_rate >= 60:
            strengths.append(f"取引勝率が高い ({win_rate:.1f}%)")
        elif win_rate < 45:
            weaknesses.append(f"取引勝率が低い ({win_rate:.1f}%)")
        
        # プロフィットファクター
        profit_factor = metrics.get("trading_metrics", {}).get("profit_factor", 0)
        if profit_factor != "inf" and isinstance(profit_factor, (int, float)):
            if profit_factor >= 1.5:
                strengths.append(f"リスクリワード比が良好 (プロフィットファクター: {profit_factor:.2f})")
            elif profit_factor < 1.0:
                weaknesses.append(f"リスクリワード比が不十分 (プロフィットファクター: {profit_factor:.2f})")
        
        # 銘柄別分析
        ticker_analysis = metrics.get("dimensional_analysis", {}).get("ticker_analysis", {})
        
        # 最もパフォーマンスの良い銘柄
        best_tickers = sorted(
            ticker_analysis.keys(),
            key=lambda t: ticker_analysis[t]["directional_metrics"]["directional_accuracy"],
            reverse=True
        )[:3]
        
        if best_tickers:
            ticker_strengths = []
            for ticker in best_tickers:
                accuracy = ticker_analysis[ticker]["directional_metrics"]["directional_accuracy"]
                if accuracy >= 65:
                    ticker_strengths.append(f"{ticker} ({accuracy:.1f}%)")
            
            if ticker_strengths:
                strengths.append(f"以下の銘柄で高い予測精度: {', '.join(ticker_strengths)}")
        
        # 最もパフォーマンスの悪い銘柄
        worst_tickers = sorted(
            ticker_analysis.keys(),
            key=lambda t: ticker_analysis[t]["directional_metrics"]["directional_accuracy"]
        )[:3]
        
        if worst_tickers:
            ticker_weaknesses = []
            for ticker in worst_tickers:
                accuracy = ticker_analysis[ticker]["directional_metrics"]["directional_accuracy"]
                if accuracy <= 40:
                    ticker_weaknesses.append(f"{ticker} ({accuracy:.1f}%)")
            
            if ticker_weaknesses:
                weaknesses.append(f"以下の銘柄で低い予測精度: {', '.join(ticker_weaknesses)}")
        
        # セクター分析
        sector_analysis = metrics.get("dimensional_analysis", {}).get("sector_analysis", {})
        
        # 最もパフォーマンスの良いセクター
        if sector_analysis:
            best_sectors = sorted(
                sector_analysis.keys(),
                key=lambda s: sector_analysis[s]["directional_metrics"]["directional_accuracy"],
                reverse=True
            )[:2]
            
            sector_strengths = []
            for sector in best_sectors:
                accuracy = sector_analysis[sector]["directional_metrics"]["directional_accuracy"]
                if accuracy >= 60:
                    sector_strengths.append(f"{sector} ({accuracy:.1f}%)")
            
            if sector_strengths:
                strengths.append(f"以下のセクターで高い予測精度: {', '.join(sector_strengths)}")
            
            # 最もパフォーマンスの悪いセクター
            worst_sectors = sorted(
                sector_analysis.keys(),
                key=lambda s: sector_analysis[s]["directional_metrics"]["directional_accuracy"]
            )[:2]
            
            sector_weaknesses = []
            for sector in worst_sectors:
                accuracy = sector_analysis[sector]["directional_metrics"]["directional_accuracy"]
                if accuracy <= 45:
                    sector_weaknesses.append(f"{sector} ({accuracy:.1f}%)")
            
            if sector_weaknesses:
                weaknesses.append(f"以下のセクターで低い予測精度: {', '.join(sector_weaknesses)}")
        
        return {
            "strengths": strengths,
            "weaknesses": weaknesses
        }
    
    def _generate_improvement_suggestions(self, metrics):
        """
        改善提案を生成
        
        Parameters:
        -----------
        metrics : dict
            評価指標
            
        Returns:
        --------
        list
            改善提案リスト
        """
        suggestions = []
        
        # 方向性精度が低い場合
        directional_accuracy = metrics.get("directional_metrics", {}).get("directional_accuracy", 0)
        if directional_accuracy < 60:
            suggestions.append(
                "方向性予測を改善するため、テクニカル指標の重みを調整し、"
                "トレンド関連指標（移動平均、MACD、RSIなど）をより重視してください"
            )
        
        # 特定の銘柄で弱い場合
        ticker_analysis = metrics.get("dimensional_analysis", {}).get("ticker_analysis", {})
        weak_tickers = []
        
        for ticker, analysis in ticker_analysis.items():
            accuracy = analysis.get("directional_metrics", {}).get("directional_accuracy", 0)
            if accuracy < 45 and analysis.get("count", 0) >= 10:  # 予測が10件以上ある場合のみ
                weak_tickers.append(ticker)
        
        if len(weak_tickers) >= 3:
            suggestions.append(
                f"以下の銘柄群({', '.join(weak_tickers[:5])}{', ...' if len(weak_tickers) > 5 else ''})の予測能力が低いため、"
                "これらの銘柄に特化した特徴量を追加するか、個別モデルを検討してください"
            )
        
        # セクター別の弱点
        sector_analysis = metrics.get("dimensional_analysis", {}).get("sector_analysis", {})
        weak_sectors = []
        
        for sector, analysis in sector_analysis.items():
            accuracy = analysis.get("directional_metrics", {}).get("directional_accuracy", 0)
            if accuracy < 45 and analysis.get("prediction_count", 0) >= 20:  # 予測が20件以上ある場合のみ
                weak_sectors.append(sector)
        
        if weak_sectors:
            suggestions.append(
                f"{', '.join(weak_sectors)}セクターでの予測精度が低いため、"
                "このセクター特有の指標（例: セクター指数との相関、業界固有の指標）を追加してください"
            )
        
        # 予測期間による弱点
        horizon_analysis = metrics.get("dimensional_analysis", {}).get("time_horizon_analysis", {})
        
        for horizon, analysis in horizon_analysis.items():
            accuracy = analysis.get("directional_metrics", {}).get("directional_accuracy", 0)
            if accuracy < 45 and analysis.get("count", 0) >= 15:  # 予測が15件以上ある場合のみ
                if horizon == "short_term":
                    suggestions.append(
                        "短期予測の精度が低いため、より細かい時間足のデータやVolume Profile、高頻度の取引指標を追加してください"
                    )
                elif horizon == "medium_term":
                    suggestions.append(
                        "中期予測の精度が低いため、日足データとの組み合わせやより長い期間の移動平均を検討してください"
                    )
                elif horizon == "long_term":
                    suggestions.append(
                        "長期予測の精度が低いため、ファンダメンタル指標や、マクロ経済データとの相関を検討してください"
                    )
        
        # 一般的な改善提案
        if len(suggestions) == 0:
            suggestions.append("特徴量間の相関分析を行い、冗長な特徴量を削除して予測モデルを単純化してください")
            suggestions.append("異なるモデルアルゴリズム（LSTM、Transformer、XGBoost、LightGBM）の重み付けを見直してください")
        
        return suggestions
    
    def _save_performance_metrics(self, metrics):
        """
        パフォーマンス指標を保存
        
        Parameters:
        -----------
        metrics : dict
            評価指標
            
        Returns:
        --------
        bool
            保存成功の場合はTrue
        """
        try:
            # JSON形式で保存
            metrics_file = os.path.join(
                self.performance_dir, 
                f"metrics_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            )
            
            with open(metrics_file, 'w', encoding='utf-8') as f:
                json.dump(metrics, f, indent=2, ensure_ascii=False)
            
            logger.info(f"パフォーマンス指標を保存しました: {metrics_file}")
            
            # CSVにも保存（時系列での比較用）
            csv_file = os.path.join(self.performance_dir, "model_metrics.csv")
            
            # フラット化した指標を作成
            flat_metrics = {
                "timestamp": metrics["timestamp"],
                "prediction_count": metrics["prediction_count"],
                "rmse": metrics["regression_metrics"]["rmse"],
                "mae": metrics["regression_metrics"]["mae"],
                "mape": metrics["regression_metrics"]["mape"],
                "r2": metrics["regression_metrics"]["r2"],
                "directional_accuracy": metrics["directional_metrics"]["directional_accuracy"],
                "win_rate": metrics["trading_metrics"].get("win_rate", 0),
                "total_profit": metrics["trading_metrics"].get("total_profit", 0),
                "profit_factor": metrics["trading_metrics"].get("profit_factor", 0)
            }
            
            # DataFrameに変換
            df = pd.DataFrame([flat_metrics])
            
            # 既存ファイルがあれば読み込み、なければ新規作成
            if os.path.exists(csv_file):
                existing_df = pd.read_csv(csv_file)
                df = pd.concat([existing_df, df], ignore_index=True)
            
            # CSVに保存
            df.to_csv(csv_file, index=False)
            
            return True
        except Exception as e:
            logger.error(f"パフォーマンス指標の保存エラー: {str(e)}")
            return False
    
    def generate_report(self, output_file=None):
        """
        評価レポートを生成
        
        Parameters:
        -----------
        output_file : str
            出力ファイルパス
            
        Returns:
        --------
        str
            レポートファイルパスまたはレポート内容
        """
        # パフォーマンス評価
        metrics = self.evaluate_performance(save_metrics=True)
        
        if not metrics:
            logger.warning("評価対象のデータがありません")
            return "評価対象のデータがありません"
        
        # HTMLレポートを生成
        html = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>AI評価レポート - {datetime.now().strftime('%Y-%m-%d')}</title>
            <style>
                body {{ font-family: Arial, sans-serif; margin: 0; padding: 20px; }}
                h1, h2, h3 {{ color: #333; }}
                .section {{ margin-bottom: 30px; }}
                .metrics {{ display: flex; flex-wrap: wrap; }}
                .metric-card {{ background-color: #f5f5f5; border-radius: 5px; padding: 15px; margin: 10px; flex: 1; min-width: 200px; }}
                .metric-value {{ font-size: 24px; font-weight: bold; margin: 10px 0; }}
                .metric-title {{ color: #666; }}
                .strength {{ color: green; }}
                .weakness {{ color: red; }}
                .suggestion {{ color: blue; }}
            </style>
        </head>
        <body>
            <h1>AI評価レポート</h1>
            <p>評価日時: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
            <p>評価対象予測数: {metrics['prediction_count']}</p>
            
            <div class="section">
                <h2>総合評価指標</h2>
                <div class="metrics">
                    <div class="metric-card">
                        <div class="metric-title">方向性予測精度</div>
                        <div class="metric-value">{metrics['directional_metrics']['directional_accuracy']:.1f}%</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-title">平均二乗誤差の平方根</div>
                        <div class="metric-value">{metrics['regression_metrics']['rmse']:.2f}</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-title">平均絶対誤差</div>
                        <div class="metric-value">{metrics['regression_metrics']['mae']:.2f}</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-title">平均絶対パーセント誤差</div>
                        <div class="metric-value">{metrics['regression_metrics']['mape']:.1f}%</div>
                    </div>
                </div>
            </div>
            
            <div class="section">
                <h2>強み</h2>
                <ul>
                    {''.join([f'<li class="strength">{s}</li>' for s in metrics['strengths']])}
                </ul>
            </div>
            
            <div class="section">
                <h2>弱み</h2>
                <ul>
                    {''.join([f'<li class="weakness">{w}</li>' for w in metrics['weaknesses']])}
                </ul>
            </div>
            
            <div class="section">
                <h2>改善提案</h2>
                <ul>
                    {''.join([f'<li class="suggestion">{s}</li>' for s in metrics['improvement_suggestions']])}
                </ul>
            </div>
        </body>
        </html>
        """
        
        if output_file:
            # HTMLファイルに保存
            try:
                with open(output_file, 'w', encoding='utf-8') as f:
                    f.write(html)
                logger.info(f"評価レポートを保存しました: {output_file}")
                return output_file
            except Exception as e:
                logger.error(f"評価レポートの保存エラー: {str(e)}")
                return html
        else:
            return html
    
    def suggest_model_improvements(self, model_config, save_to_file=True):
        """
        モデル改善案を提案
        
        Parameters:
        -----------
        model_config : dict
            現在のモデル設定
        save_to_file : bool
            ファイルに保存するかどうか
            
        Returns:
        --------
        dict
            改善されたモデル設定
        """
        # モデルパフォーマンスの評価
        metrics = self.evaluate_performance(save_metrics=False)
        
        if not metrics:
            logger.warning("評価対象のデータがありません")
            return model_config
        
        # ディープコピーを作成
        import copy
        improved_config = copy.deepcopy(model_config)
        
        # 改善案の適用
        changes_made = []
        
        # 1. 方向性精度が低い場合、テクニカル指標の重みを強化
        directional_accuracy = metrics.get("directional_metrics", {}).get("directional_accuracy", 0)
        if directional_accuracy < 55:
            # テクニカル指標の重みを増やす
            if "feature_weights" in improved_config:
                technical_indicators = ["rsi", "macd", "bollinger", "ma", "momentum", "trend"]
                for indicator in technical_indicators:
                    for feature, weight in improved_config["feature_weights"].items():
                        if indicator in feature.lower():
                            improved_config["feature_weights"][feature] = weight * 1.5
                            changes_made.append(f"{feature}の重みを1.5倍に増加")
        
        # 2. 過学習の兆候があれば正則化を強化
        mape = metrics.get("regression_metrics", {}).get("mape", 0)
        r2 = metrics.get("regression_metrics", {}).get("r2", 0)
        
        if mape > 10 and r2 < 0.5:
            if "regularization" in improved_config:
                improved_config["regularization"] *= 1.5
                changes_made.append(f"正則化パラメータを{improved_config['regularization']}に増加")
            
            if "dropout_rate" in improved_config:
                improved_config["dropout_rate"] = min(0.5, improved_config["dropout_rate"] * 1.2)
                changes_made.append(f"ドロップアウト率を{improved_config['dropout_rate']:.2f}に増加")
        
        # 3. 特定のセクターで弱い場合、セクター特有の特徴量を追加
        sector_analysis = metrics.get("dimensional_analysis", {}).get("sector_analysis", {})
        
        for sector, analysis in sector_analysis.items():
            accuracy = analysis.get("directional_metrics", {}).get("directional_accuracy", 0)
            if accuracy < 45 and analysis.get("prediction_count", 0) >= 20:
                # セクター特有の特徴量を提案
                if "sector_features" not in improved_config:
                    improved_config["sector_features"] = {}
                
                if sector not in improved_config["sector_features"]:
                    improved_config["sector_features"][sector] = []
                
                # セクター特有の特徴を追加
                if sector == "technology":
                    new_features = ["tech_momentum", "innovation_index", "r&d_spending"]
                elif sector == "financials":
                    new_features = ["interest_rate_sensitivity", "yield_curve", "banking_index"]
                elif sector == "healthcare":
                    new_features = ["healthcare_index", "drug_approval_cycles", "pandemic_impact"]
                else:
                    new_features = ["sector_relative_strength", f"{sector}_index_correlation"]
                
                # 既存の特徴量と重複を避ける
                for feature in new_features:
                    if feature not in improved_config["sector_features"][sector]:
                        improved_config["sector_features"][sector].append(feature)
                        changes_made.append(f"{sector}セクター用に{feature}特徴量を追加")
        
        # 4. モデルの複雑さを調整
        if "layers" in improved_config and isinstance(improved_config["layers"], list):
            accuracy = metrics.get("directional_metrics", {}).get("directional_accuracy", 0)
            
            if accuracy < 50 and len(improved_config["layers"]) < 4:
                # モデルが単純すぎる可能性がある
                improved_config["layers"].append(improved_config["layers"][-1] // 2)
                changes_made.append(f"モデル層を追加: {improved_config['layers'][-1]}ユニット")
            elif accuracy < 50 and len(improved_config["layers"]) >= 4:
                # モデルが複雑すぎる可能性がある
                improved_config["layers"] = improved_config["layers"][:-1]
                changes_made.append(f"モデル層を簡略化: {improved_config['layers']}")
        
        # 改善内容を記録
        improvements = {
            "timestamp": datetime.now().isoformat(),
            "metrics": {
                "directional_accuracy": directional_accuracy,
                "mape": mape,
                "r2": r2
            },
            "original_config": model_config,
            "improved_config": improved_config,
            "changes": changes_made
        }
        
        # ファイルに保存
        if save_to_file:
            improvements_file = os.path.join(
                self.improvements_dir, 
                f"model_improvements_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            )
            
            try:
                with open(improvements_file, 'w', encoding='utf-8') as f:
                    json.dump(improvements, f, indent=2, ensure_ascii=False)
                logger.info(f"モデル改善案を保存しました: {improvements_file}")
            except Exception as e:
                logger.error(f"モデル改善案の保存エラー: {str(e)}")
        
        return improved_config

if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description='AI予測の評価と改善提案')
    parser.add_argument('--dir', type=str, default="nikkei225_historical_data/ai_evaluation", help='評価データディレクトリ')
    parser.add_argument('--report', type=str, help='レポート出力先ファイル')
    parser.add_argument('--lookback', type=int, default=30, help='評価に使用する過去の日数')
    
    args = parser.parse_args()
    
    # logs ディレクトリがなければ作成
    os.makedirs("logs", exist_ok=True)
    
    evaluator = AIEvaluator(evaluation_dir=args.dir, lookback_days=args.lookback)
    
    if args.report:
        evaluator.generate_report(output_file=args.report)
    else:
        metrics = evaluator.evaluate_performance()
        print(json.dumps(metrics, indent=2, ensure_ascii=False))
