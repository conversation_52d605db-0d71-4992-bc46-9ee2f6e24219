import pandas as pd
import numpy as np
import os
import pickle
import joblib
from datetime import datetime, timedelta
from sklearn.preprocessing import StandardScaler, MinMaxScaler
from sklearn.model_selection import TimeSeriesSplit
from sklearn.metrics import mean_squared_error, mean_absolute_percentage_error
import xgboost as xgb
import lightgbm as lgb
import tensorflow as tf
from tensorflow.keras.models import Sequential, Model, load_model
from tensorflow.keras.layers import Dense, LSTM, Dropout, GRU, Input, Concatenate
from tensorflow.keras.optimizers import Adam
from tensorflow.keras.callbacks import EarlyStopping, ModelCheckpoint
import warnings

# Suppress warnings
warnings.filterwarnings('ignore')

class NikkeiAIModel:
    def __init__(self, model_dir="models", data_dir="data"):
        """
        Initialize the AI model for Nikkei 225 prediction
        
        Parameters:
        -----------
        model_dir : str
            Directory to save trained models
        data_dir : str
            Directory containing processed data
        """
        self.model_dir = model_dir
        self.data_dir = data_dir
        
        # Create directories
        os.makedirs(model_dir, exist_ok=True)
        
        # Initialize models
        self.models = {
            'lstm': None,
            'xgboost': None,
            'lightgbm': None,
            'ensemble': None
        }
        
        # Scalers for data preprocessing
        self.scalers = {}
        
        # Feature importance
        self.feature_importance = {}
        
    def load_data(self, file_path=None, target_tickers=None):
        """
        Load and prepare data for model training and testing
        
        Parameters:
        -----------
        file_path : str
            Path to the processed data file with features
        target_tickers : list
            List of ticker symbols to focus on (default: all tickers in the data)
            
        Returns:
        --------
        pd.DataFrame
            Processed data ready for modeling
        """
        if file_path is None:
            file_path = os.path.join(self.data_dir, "nikkei225_with_features.csv")
            
        if not os.path.exists(file_path):
            raise ValueError(f"Data file not found: {file_path}")
            
        # Load data
        df = pd.read_csv(file_path, parse_dates=['Datetime'])
        
        # Filter data for specific tickers if provided
        if target_tickers is not None:
            df = df[df['Ticker'].isin(target_tickers)]
            
        # Ensure there's enough data for each ticker
        ticker_counts = df['Ticker'].value_counts()
        valid_tickers = ticker_counts[ticker_counts > 100].index
        df = df[df['Ticker'].isin(valid_tickers)]
        
        # Drop columns with too many missing values
        missing_threshold = 0.3
        missing_percentage = df.isna().mean()
        columns_to_drop = missing_percentage[missing_percentage > missing_threshold].index
        df = df.drop(columns=columns_to_drop)
        
        # Fill remaining missing values
        df = df.groupby('Ticker').apply(
            lambda group: group.fillna(method='ffill').fillna(method='bfill')
        ).reset_index(drop=True)
        
        # Convert categorical features to numeric
        for col in df.select_dtypes(include=['object']).columns:
            if col != 'Ticker' and col != 'Datetime':
                df[col] = pd.factorize(df[col])[0]
        
        return df
    
    def prepare_features(self, df, ticker=None, target_col='Close', 
                         sequence_length=24, prediction_horizon=1, 
                         train_ratio=0.8):
        """
        Prepare features and target for a specific ticker
        
        Parameters:
        -----------
        df : pd.DataFrame
            Input data
        ticker : str
            Ticker symbol to prepare data for (or None for all tickers)
        target_col : str
            Column to predict (default: 'Close')
        sequence_length : int
            Length of sequence for LSTM (how many past hours to look at)
        prediction_horizon : int
            How many hours ahead to predict (default: 1 hour)
        train_ratio : float
            Ratio of data to use for training (0.0-1.0)
            
        Returns:
        --------
        dict
            Dictionary containing prepared data for model training and testing
        """
        # Filter data for specific ticker if provided
        if ticker is not None:
            data = df[df['Ticker'] == ticker].copy()
        else:
            data = df.copy()
            
        # Sort by datetime
        data = data.sort_values('Datetime')
        
        # Create target variable (future price)
        data[f'future_{target_col}'] = data.groupby('Ticker')[target_col].shift(-prediction_horizon)
        
        # Calculate target percentage change
        data[f'future_{target_col}_pct'] = data.groupby('Ticker')[target_col].pct_change(-prediction_horizon)
        
        # Drop rows with NaN in target
        data = data.dropna(subset=[f'future_{target_col}', f'future_{target_col}_pct'])
        
        # Define feature columns (excluding datetime, ticker, and target columns)
        feature_cols = [col for col in data.columns if col not in 
                        ['Datetime', 'Ticker', f'future_{target_col}', f'future_{target_col}_pct']]
        
        # Split data into train and test sets by time
        train_size = int(len(data) * train_ratio)
        train_data = data.iloc[:train_size]
        test_data = data.iloc[train_size:]
        
        # Scale features
        scaler_key = f"{ticker}_{target_col}" if ticker else "all_tickers"
        self.scalers[scaler_key] = {}
        
        # Feature scaler
        feature_scaler = StandardScaler()
        train_features = feature_scaler.fit_transform(train_data[feature_cols])
        test_features = feature_scaler.transform(test_data[feature_cols])
        self.scalers[scaler_key]['features'] = feature_scaler
        
        # Target scaler
        target_scaler = StandardScaler()
        train_target = target_scaler.fit_transform(train_data[[f'future_{target_col}']])
        test_target = target_scaler.transform(test_data[[f'future_{target_col}']])
        self.scalers[scaler_key]['target'] = target_scaler
        
        # For LSTM: Create sequences
        X_train_lstm, y_train_lstm = self._create_sequences(
            train_features, train_target, sequence_length
        )
        X_test_lstm, y_test_lstm = self._create_sequences(
            test_features, test_target, sequence_length
        )
        
        # For tree-based models: Use raw features
        X_train_tree = train_features
        y_train_tree = train_target.reshape(-1)
        X_test_tree = test_features
        y_test_tree = test_target.reshape(-1)
        
        # Save feature and target column names for later use
        self.feature_cols = feature_cols
        self.target_col = target_col
        
        return {
            'lstm': {
                'X_train': X_train_lstm,
                'y_train': y_train_lstm,
                'X_test': X_test_lstm,
                'y_test': y_test_lstm
            },
            'tree': {
                'X_train': X_train_tree,
                'y_train': y_train_tree,
                'X_test': X_test_tree,
                'y_test': y_test_tree
            },
            'feature_cols': feature_cols,
            'train_data': train_data,
            'test_data': test_data
        }
    
    def _create_sequences(self, features, target, sequence_length):
        """
        Create sequences for LSTM training
        
        Parameters:
        -----------
        features : np.array
            Feature array
        target : np.array
            Target array
        sequence_length : int
            Length of sequences
            
        Returns:
        --------
        tuple
            (X, y) where X is sequence data and y is targets
        """
        X, y = [], []
        
        for i in range(len(features) - sequence_length):
            X.append(features[i:i+sequence_length])
            y.append(target[i+sequence_length])
            
        return np.array(X), np.array(y)
    
    def build_lstm_model(self, input_shape, output_shape=1):
        """
        Build LSTM model for time series prediction
        
        Parameters:
        -----------
        input_shape : tuple
            Shape of input data (sequence_length, n_features)
        output_shape : int
            Number of output units
            
        Returns:
        --------
        tf.keras.Model
            Compiled LSTM model
        """
        model = Sequential([
            LSTM(128, return_sequences=True, input_shape=input_shape),
            Dropout(0.2),
            LSTM(64, return_sequences=False),
            Dropout(0.2),
            Dense(32, activation='relu'),
            Dense(output_shape)
        ])
        
        model.compile(
            optimizer=Adam(learning_rate=0.001),
            loss=tf.keras.losses.MeanSquaredError(),
            metrics=[tf.keras.metrics.MeanAbsoluteError()]
        )
        
        return model
    
    def build_transformer_model(self, input_shape, output_shape=1):
        """
        Build a Time Series Transformer model
        
        Parameters:
        -----------
        input_shape : tuple
            Shape of input data (sequence_length, n_features)
        output_shape : int
            Number of output units
            
        Returns:
        --------
        tf.keras.Model
            Compiled Transformer model
        """
        # This is a simplified implementation of a Transformer for time series
        # A full implementation would include attention mechanisms, etc.
        
        inputs = Input(shape=input_shape)
        
        # Add positional encoding
        x = tf.keras.layers.Conv1D(filters=128, kernel_size=1, activation='relu')(inputs)
        
        # Two Transformer blocks
        for _ in range(2):
            # Self-attention
            attention = tf.keras.layers.MultiHeadAttention(
                num_heads=4, key_dim=32
            )(x, x, x)
            x = tf.keras.layers.LayerNormalization()(attention + x)
            
            # Feed-forward network
            ffn = tf.keras.Sequential([
                Dense(256, activation='relu'),
                Dense(128)
            ])(x)
            x = tf.keras.layers.LayerNormalization()(x + ffn)
        
        # Global average pooling
        x = tf.keras.layers.GlobalAveragePooling1D()(x)
        
        # Output layers
        x = Dense(64, activation='relu')(x)
        x = Dropout(0.2)(x)
        outputs = Dense(output_shape)(x)
        
        model = Model(inputs=inputs, outputs=outputs)
        
        model.compile(
            optimizer=Adam(learning_rate=0.0005),
            loss=tf.keras.losses.MeanSquaredError(),
            metrics=[tf.keras.metrics.MeanAbsoluteError()]
        )
        
        return model
    
    def train_models(self, prepared_data, ticker=None, epochs=50, batch_size=32):
        """
        Train all models (LSTM, XGBoost, LightGBM, and Transformer)
        
        Parameters:
        -----------
        prepared_data : dict
            Dictionary containing prepared training and testing data
        ticker : str
            Ticker symbol being trained on
        epochs : int
            Number of epochs for neural network training
        batch_size : int
            Batch size for neural network training
            
        Returns:
        --------
        dict
            Dictionary containing trained models and performance metrics
        """
        print(f"Training models for {'all tickers' if ticker is None else ticker}...")
        results = {}
        
        # Model paths
        model_key = ticker if ticker else "all_tickers"
        lstm_path = os.path.join(self.model_dir, f"{model_key}_lstm.h5")
        transformer_path = os.path.join(self.model_dir, f"{model_key}_transformer.h5")
        xgboost_path = os.path.join(self.model_dir, f"{model_key}_xgboost.json")
        lightgbm_path = os.path.join(self.model_dir, f"{model_key}_lightgbm.txt")
        
        # 1. Train LSTM model
        print("Training LSTM model...")
        lstm_model = self.build_lstm_model(
            input_shape=(prepared_data['lstm']['X_train'].shape[1], prepared_data['lstm']['X_train'].shape[2]),
            output_shape=prepared_data['lstm']['y_train'].shape[1]
        )
        
        # Set up callbacks
        early_stopping = EarlyStopping(
            monitor='val_loss', patience=10, restore_best_weights=True
        )
        checkpoint = ModelCheckpoint(
            lstm_path, save_best_only=True, monitor='val_loss'
        )
        
        # Train LSTM
        lstm_history = lstm_model.fit(
            prepared_data['lstm']['X_train'], prepared_data['lstm']['y_train'],
            epochs=epochs,
            batch_size=batch_size,
            validation_split=0.2,
            callbacks=[early_stopping, checkpoint],
            verbose=1
        )
        
        # Load best model
        lstm_model = load_model(lstm_path)
        
        # Evaluate LSTM
        lstm_preds = lstm_model.predict(prepared_data['lstm']['X_test'])
        lstm_rmse = np.sqrt(mean_squared_error(
            prepared_data['lstm']['y_test'], lstm_preds
        ))
        
        # Store results
        results['lstm'] = {
            'model': lstm_model,
            'rmse': lstm_rmse,
            'history': lstm_history.history
        }
        self.models['lstm'] = lstm_model
        
        # 2. Train Transformer model
        print("Training Transformer model...")
        transformer_model = self.build_transformer_model(
            input_shape=(prepared_data['lstm']['X_train'].shape[1], prepared_data['lstm']['X_train'].shape[2]),
            output_shape=prepared_data['lstm']['y_train'].shape[1]
        )
        
        # Set up callbacks for transformer
        transformer_checkpoint = ModelCheckpoint(
            transformer_path, save_best_only=True, monitor='val_loss'
        )
        
        # Train Transformer
        transformer_history = transformer_model.fit(
            prepared_data['lstm']['X_train'], prepared_data['lstm']['y_train'],
            epochs=epochs,
            batch_size=batch_size,
            validation_split=0.2,
            callbacks=[early_stopping, transformer_checkpoint],
            verbose=1
        )
        
        # Load best model
        try:
            transformer_model = load_model(transformer_path, compile=False)
            transformer_model.compile(
                optimizer=Adam(learning_rate=0.0005),
                loss=tf.keras.losses.MeanSquaredError(),
                metrics=[tf.keras.metrics.MeanAbsoluteError()]
            )
        except Exception as e:
            print(f"Failed to load transformer model: {e}")
            # 最後に保存されたTransformerモデルを使用
            print("Using the last trained transformer model without loading from disk")
        
        # Evaluate Transformer
        transformer_preds = transformer_model.predict(prepared_data['lstm']['X_test'])
        transformer_rmse = np.sqrt(mean_squared_error(
            prepared_data['lstm']['y_test'], transformer_preds
        ))
        
        # Store results
        results['transformer'] = {
            'model': transformer_model,
            'rmse': transformer_rmse,
            'history': transformer_history.history
        }
        self.models['transformer'] = transformer_model
        
        # 3. Train XGBoost model
        print("Training XGBoost model...")
        xgb_model = xgb.XGBRegressor(
            n_estimators=200,
            learning_rate=0.05,
            max_depth=6,
            subsample=0.8,
            colsample_bytree=0.8,
            random_state=42
        )
        
        # Train XGBoost
        xgb_model.fit(
            prepared_data['tree']['X_train'], prepared_data['tree']['y_train'],
            eval_set=[(prepared_data['tree']['X_test'], prepared_data['tree']['y_test'])],
            early_stopping_rounds=20,
            verbose=1
        )
        
        # Save model
        xgb_model.save_model(xgboost_path)
        
        # Evaluate XGBoost
        xgb_preds = xgb_model.predict(prepared_data['tree']['X_test'])
        xgb_rmse = np.sqrt(mean_squared_error(
            prepared_data['tree']['y_test'], xgb_preds
        ))
        
        # Feature importance
        self.feature_importance['xgboost'] = dict(
            zip(prepared_data['feature_cols'], xgb_model.feature_importances_)
        )
        
        # Store results
        results['xgboost'] = {
            'model': xgb_model,
            'rmse': xgb_rmse,
            'feature_importance': self.feature_importance['xgboost']
        }
        self.models['xgboost'] = xgb_model
        
        # 4. Train LightGBM model
        print("Training LightGBM model...")
        lgb_model = lgb.LGBMRegressor(
            n_estimators=200,
            learning_rate=0.05,
            max_depth=6,
            subsample=0.8,
            colsample_bytree=0.8,
            random_state=42
        )
        
        # Train LightGBM
        lgb_model.fit(
            prepared_data['tree']['X_train'], prepared_data['tree']['y_train'],
            eval_set=[(prepared_data['tree']['X_test'], prepared_data['tree']['y_test'])],
            early_stopping_rounds=20,
            verbose=1
        )
        
        # Save model
        lgb_model.booster_.save_model(lightgbm_path)
        
        # Evaluate LightGBM
        lgb_preds = lgb_model.predict(prepared_data['tree']['X_test'])
        lgb_rmse = np.sqrt(mean_squared_error(
            prepared_data['tree']['y_test'], lgb_preds
        ))
        
        # Feature importance
        self.feature_importance['lightgbm'] = dict(
            zip(prepared_data['feature_cols'], lgb_model.feature_importances_)
        )
        
        # Store results
        results['lightgbm'] = {
            'model': lgb_model,
            'rmse': lgb_rmse,
            'feature_importance': self.feature_importance['lightgbm']
        }
        self.models['lightgbm'] = lgb_model
        
        # Print results
        print("\nModel Results:")
        print(f"LSTM RMSE: {lstm_rmse:.4f}")
        print(f"Transformer RMSE: {transformer_rmse:.4f}")
        print(f"XGBoost RMSE: {xgb_rmse:.4f}")
        print(f"LightGBM RMSE: {lgb_rmse:.4f}")
        
        return results
    
    def ensemble_predict(self, X_lstm, X_tree, weights=None):
        """
        Make predictions using ensemble of trained models
        
        Parameters:
        -----------
        X_lstm : np.array
            Input data for LSTM and Transformer models
        X_tree : np.array
            Input data for tree-based models
        weights : dict
            Weights for each model (if None, weights will be determined by inverse RMSE)
            
        Returns:
        --------
        np.array
            Ensemble predictions
        """
        # Check if models are trained
        for model_name, model in self.models.items():
            if model is None and model_name != 'ensemble':
                raise ValueError(f"Model '{model_name}' is not trained yet")
        
        # Make predictions
        lstm_preds = self.models['lstm'].predict(X_lstm)
        transformer_preds = self.models['transformer'].predict(X_lstm)
        xgb_preds = self.models['xgboost'].predict(X_tree)
        lgb_preds = self.models['lightgbm'].predict(X_tree)
        
        # Reshape tree model predictions to match LSTM output shape if needed
        if len(lstm_preds.shape) > 1 and len(xgb_preds.shape) == 1:
            xgb_preds = xgb_preds.reshape(-1, 1)
            lgb_preds = lgb_preds.reshape(-1, 1)
        
        # Determine weights if not provided
        if weights is None:
            # Assuming RMSE values are in self.models
            total_inverse_rmse = (
                1/self.models['lstm']['rmse'] +
                1/self.models['transformer']['rmse'] +
                1/self.models['xgboost']['rmse'] +
                1/self.models['lightgbm']['rmse']
            )
            
            weights = {
                'lstm': (1/self.models['lstm']['rmse']) / total_inverse_rmse,
                'transformer': (1/self.models['transformer']['rmse']) / total_inverse_rmse,
                'xgboost': (1/self.models['xgboost']['rmse']) / total_inverse_rmse,
                'lightgbm': (1/self.models['lightgbm']['rmse']) / total_inverse_rmse
            }
        
        # Create ensemble prediction
        ensemble_preds = (
            weights['lstm'] * lstm_preds +
            weights['transformer'] * transformer_preds +
            weights['xgboost'] * xgb_preds +
            weights['lightgbm'] * lgb_preds
        )
        
        return ensemble_preds
    
    def walk_forward_optimization(self, df, ticker, window_size=30, step_size=7, 
                                  prediction_horizon=1, sequence_length=24):
        """
        Perform walk-forward optimization for a specific ticker
        
        Parameters:
        -----------
        df : pd.DataFrame
            Input data
        ticker : str
            Ticker symbol to analyze
        window_size : int
            Size of the training window (in days)
        step_size : int
            Number of days to move forward after each iteration
        prediction_horizon : int
            How many hours ahead to predict
        sequence_length : int
            Length of sequence for LSTM
            
        Returns:
        --------
        pd.DataFrame
            DataFrame with actual and predicted values
        """
        print(f"Performing walk-forward optimization for {ticker}...")
        
        # Filter data for the specific ticker
        ticker_data = df[df['Ticker'] == ticker].copy()
        ticker_data = ticker_data.sort_values('Datetime')
        
        # Convert to hours since start for easier windowing
        start_date = ticker_data['Datetime'].min()
        ticker_data['hours_since_start'] = (ticker_data['Datetime'] - start_date).dt.total_seconds() / 3600
        
        # List to store results
        results = []
        
        # Convert window_size and step_size from days to hours
        window_hours = window_size * 24
        step_hours = step_size * 24
        
        # Calculate total number of iterations
        max_hours = ticker_data['hours_since_start'].max()
        iterations = int((max_hours - window_hours) / step_hours) + 1
        
        # Iterate through time windows
        for i in range(iterations):
            # Define window
            start_hour = i * step_hours
            end_hour = start_hour + window_hours
            test_end_hour = end_hour + step_hours
            
            # Get data for this window
            train_data = ticker_data[(ticker_data['hours_since_start'] >= start_hour) & 
                                   (ticker_data['hours_since_start'] < end_hour)]
            test_data = ticker_data[(ticker_data['hours_since_start'] >= end_hour) & 
                                  (ticker_data['hours_since_start'] < test_end_hour)]
            
            # Skip if not enough data
            if len(train_data) < 100 or len(test_data) < 1:
                continue
                
            # Get actual start/end dates for logging
            window_start = start_date + timedelta(hours=start_hour)
            window_end = start_date + timedelta(hours=end_hour)
            test_end = start_date + timedelta(hours=test_end_hour)
            
            print(f"\nIteration {i+1}/{iterations}")
            print(f"Train: {window_start.date()} to {window_end.date()}")
            print(f"Test: {window_end.date()} to {test_end.date()}")
            
            # Prepare features
            prepared_data = self.prepare_features(
                pd.concat([train_data, test_data]), 
                ticker=None,  # No need to filter by ticker again
                target_col='Close', 
                sequence_length=sequence_length,
                prediction_horizon=prediction_horizon,
                train_ratio=len(train_data) / (len(train_data) + len(test_data))
            )
            
            # Train models
            try:
                self.train_models(
                    prepared_data, 
                    ticker=f"{ticker}_window_{i}",
                    epochs=30,  # Reduce epochs for faster training
                    batch_size=32
                )
                
                # Make predictions
                test_predictions = self.ensemble_predict(
                    prepared_data['lstm']['X_test'],
                    prepared_data['tree']['X_test']
                )
                
                # Inverse transform predictions
                scaler_key = f"{ticker}_window_{i}_Close"
                target_scaler = self.scalers[scaler_key]['target']
                test_predictions_inv = target_scaler.inverse_transform(test_predictions)
                
                # Create results DataFrame
                test_indices = prepared_data['test_data'].index[-len(test_predictions):]
                preds_df = pd.DataFrame({
                    'Datetime': prepared_data['test_data'].loc[test_indices, 'Datetime'].values,
                    'Actual': prepared_data['test_data'].loc[test_indices, 'Close'].values,
                    'Predicted': test_predictions_inv.flatten(),
                    'Window': i
                })
                
                results.append(preds_df)
                
            except Exception as e:
                print(f"Error in window {i}: {str(e)}")
                continue
        
        # Combine all results
        if results:
            all_results = pd.concat(results, ignore_index=True)
            
            # Save results
            results_file = os.path.join(self.model_dir, f"{ticker}_walk_forward_results.csv")
            all_results.to_csv(results_file, index=False)
            
            return all_results
        else:
            print("No valid results were generated")
            return None
    
    def save_model_metadata(self, ticker, results):
        """
        Save model metadata and performance metrics
        
        Parameters:
        -----------
        ticker : str
            Ticker symbol
        results : dict
            Results dictionary from training
        """
        metadata = {
            'ticker': ticker,
            'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'feature_cols': self.feature_cols,
            'target_col': self.target_col,
            'performance': {
                'lstm_rmse': results['lstm']['rmse'],
                'transformer_rmse': results['transformer']['rmse'],
                'xgboost_rmse': results['xgboost']['rmse'],
                'lightgbm_rmse': results['lightgbm']['rmse']
            },
            'feature_importance': {
                'xgboost': self.feature_importance.get('xgboost', {}),
                'lightgbm': self.feature_importance.get('lightgbm', {})
            }
        }
        
        # Save metadata
        metadata_file = os.path.join(self.model_dir, f"{ticker}_metadata.json")
        with open(metadata_file, 'w') as f:
            import json
            json.dump(metadata, f, indent=2)
    
    def evaluate_and_select_stocks(self, df, prediction_horizon=1, top_n=5, force_all_tickers=True):
        """
        Evaluate all stocks and select the top N for trading
        
        Parameters:
        -----------
        df : pd.DataFrame
            Input data with all stocks
        prediction_horizon : int
            How many hours ahead to predict
        top_n : int
            Number of top stocks to select
        force_all_tickers : bool
            If True, force evaluation of all tickers, even those with less data
            
        Returns:
        --------
        pd.DataFrame
            DataFrame with top stocks and predicted price changes
        """
        print(f"Evaluating stocks and selecting top {top_n} for trading...")
        
        # Get latest data for each ticker
        latest_data = df.sort_values('Datetime').groupby('Ticker').tail(50)
        
        # Get unique tickers
        tickers = latest_data['Ticker'].unique()
        
        # Predictions for each ticker
        predictions = []
        skipped_tickers = []
        
        for ticker in tickers:
            try:
                # Prepare features
                ticker_data = latest_data[latest_data['Ticker'] == ticker]
                
                # Skip if not enough data and not forcing all tickers
                if len(ticker_data) < 30:
                    if not force_all_tickers:
                        skipped_tickers.append(ticker)
                        continue
                    else:
                        # If forcing all tickers, pad with the available data
                        while len(ticker_data) < 30:
                            ticker_data = pd.concat([ticker_data.iloc[0:1], ticker_data])
                
                # Scale features
                feature_cols = [col for col in ticker_data.columns if col not in 
                              ['Datetime', 'Ticker', 'future_Close', 'future_Close_pct']]
                
                # Check if we have a scaler for this ticker
                scaler_key = f"{ticker}_Close"
                if scaler_key not in self.scalers:
                    # Create a new scaler
                    feature_scaler = StandardScaler()
                    features = feature_scaler.fit_transform(ticker_data[feature_cols])
                    
                    # Store the scaler
                    self.scalers[scaler_key] = {
                        'features': feature_scaler
                    }
                else:
                    # Use existing scaler
                    feature_scaler = self.scalers[scaler_key]['features']
                    features = feature_scaler.transform(ticker_data[feature_cols])
                
                # Create sequences for LSTM
                sequence_length = 24
                X_lstm = np.array([features[-sequence_length:]])
                X_tree = np.array([features[-1]])
                
                # Make prediction
                prediction = self.ensemble_predict(X_lstm, X_tree)
                
                # Calculate predicted change
                current_price = ticker_data['Close'].iloc[-1]
                predicted_price = prediction[0][0]
                predicted_change_pct = ((predicted_price - current_price) / current_price) * 100
                
                # Store prediction
                predictions.append({
                    'Ticker': ticker,
                    'Current_Price': current_price,
                    'Predicted_Price': predicted_price,
                    'Predicted_Change_Pct': predicted_change_pct,
                    'Datetime': ticker_data['Datetime'].iloc[-1]
                })
                
            except Exception as e:
                print(f"Error predicting {ticker}: {str(e)}")
                skipped_tickers.append(ticker)
                continue
        
        # Convert to DataFrame
        if not predictions:
            print("No valid predictions were generated")
            return pd.DataFrame()
            
        predictions_df = pd.DataFrame(predictions)
        
        # Sort by predicted change percentage (descending)
        predictions_df = predictions_df.sort_values('Predicted_Change_Pct', ascending=False)
        
        # Select top N stocks
        top_stocks = predictions_df.head(top_n)
        
        # Create separate dataframe for top stocks that are predicted to be profitable
        profitable_stocks = predictions_df[predictions_df['Predicted_Change_Pct'] > 0]
        
        # Save predictions
        predictions_file = os.path.join(self.model_dir, f"stock_predictions_{datetime.now().strftime('%Y%m%d_%H%M')}.csv")
        predictions_df.to_csv(predictions_file, index=False)
        
        top_stocks_file = os.path.join(self.model_dir, f"top_stocks_{datetime.now().strftime('%Y%m%d_%H%M')}.csv")
        top_stocks.to_csv(top_stocks_file, index=False)
        
        # Log skipped tickers if any
        if skipped_tickers:
            print(f"Skipped {len(skipped_tickers)} tickers due to insufficient data or errors")
            
        print(f"Selected top {top_n} stocks for trading:")
        print(top_stocks[['Ticker', 'Current_Price', 'Predicted_Price', 'Predicted_Change_Pct']])
        
        return top_stocks
        
    def calculate_performance_metrics(self, y_true, y_pred):
        """
        Calculate various performance metrics for model evaluation
        
        Parameters:
        -----------
        y_true : np.array
            True values
        y_pred : np.array
            Predicted values
            
        Returns:
        --------
        dict
            Dictionary of performance metrics
        """
        # RMSE (Root Mean Squared Error)
        rmse = np.sqrt(mean_squared_error(y_true, y_pred))
        
        # MAPE (Mean Absolute Percentage Error)
        mape = mean_absolute_percentage_error(y_true, y_pred) * 100
        
        # Calculate daily returns
        returns_true = np.diff(y_true) / y_true[:-1]
        returns_pred = np.diff(y_pred) / y_pred[:-1]
        
        # Sharpe Ratio (assuming 252 trading days per year)
        # Note: This is a simplified version without risk-free rate
        sharpe_ratio = np.mean(returns_pred) / np.std(returns_pred) * np.sqrt(252)
        
        # Maximum Drawdown
        def calculate_max_drawdown(returns):
            # Calculate cumulative returns
            cum_returns = np.cumprod(1 + returns)
            # Calculate running maximum
            running_max = np.maximum.accumulate(cum_returns)
            # Calculate drawdown
            drawdown = (cum_returns - running_max) / running_max
            # Return maximum drawdown
            return np.min(drawdown)
        
        max_drawdown = calculate_max_drawdown(returns_pred)
        
        # Directional Accuracy
        direction_true = np.sign(np.diff(y_true))
        direction_pred = np.sign(np.diff(y_pred))
        directional_accuracy = np.mean(direction_true == direction_pred) * 100
        
        return {
            'rmse': rmse,
            'mape': mape,
            'sharpe_ratio': sharpe_ratio,
            'max_drawdown': max_drawdown,
            'directional_accuracy': directional_accuracy
        }
