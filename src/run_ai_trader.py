#!/usr/bin/env python3
import os
import argparse
import logging
import pandas as pd
import numpy as np
from datetime import datetime
import matplotlib.pyplot as plt

from .data_collection import DataCollector
from .feature_engineering import FeatureEngineer
from .model import NikkeiAIModel
from .reinforcement_learning import StockTradingEnv, RLStockTrader

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("nikkei_ai_trader.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def parse_arguments():
    """Parse command line arguments"""
    parser = argparse.ArgumentParser(description='Nikkei 225 AI Stock Trading System')
    
    # Data collection args
    parser.add_argument('--collect-data', action='store_true', help='Collect new data')
    parser.add_argument('--interval', type=str, default='1h', help='Data interval (e.g., 1h, 1d)')
    parser.add_argument('--period', type=str, default='3mo', help='Data period (e.g., 1mo, 3mo, 1y)')
    
    # Feature engineering args
    parser.add_argument('--rebuild-features', action='store_true', help='Rebuild features from scratch')
    
    # Model selection args
    parser.add_argument('--model-type', type=str, default='hybrid', 
                        choices=['hybrid', 'lstm', 'xgboost', 'lightgbm', 'transformer', 'rl'],
                        help='Type of model to use')
    
    # Ticker selection args
    parser.add_argument('--tickers', type=str, nargs='+', help='Specific tickers to analyze')
    parser.add_argument('--num-tickers', type=int, default=5, help='Number of tickers to select')
    parser.add_argument('--select-method', type=str, default='volatility',
                        choices=['volatility', 'volume', 'market_cap', 'random'],
                        help='Method to select tickers')
    
    # Training args
    parser.add_argument('--train', action='store_true', help='Train new models')
    parser.add_argument('--epochs', type=int, default=50, help='Number of epochs for neural network training')
    parser.add_argument('--batch-size', type=int, default=32, help='Batch size for neural network training')
    parser.add_argument('--sequence-length', type=int, default=24, help='Sequence length for LSTM')
    parser.add_argument('--prediction-horizon', type=int, default=1, help='How many hours ahead to predict')
    
    # RL args
    parser.add_argument('--rl-timesteps', type=int, default=10000, help='Number of timesteps for RL training')
    parser.add_argument('--rl-eval-episodes', type=int, default=5, help='Number of episodes for RL evaluation')
    parser.add_argument('--rl-window-size', type=int, default=50, help='Window size for RL observation')
    
    # Evaluation args
    parser.add_argument('--backtest', action='store_true', help='Run backtesting')
    parser.add_argument('--walk-forward', action='store_true', help='Perform walk-forward optimization')
    
    # Output args
    parser.add_argument('--output-dir', type=str, default='results', help='Directory to save results')
    parser.add_argument('--save-plots', action='store_true', help='Save visualizations')
    parser.add_argument('--verbose', action='store_true', help='Print detailed information')
    
    return parser.parse_args()

def setup_directories(args):
    """Create necessary directories"""
    directories = ['data', 'models', args.output_dir, 'logs']
    for directory in directories:
        os.makedirs(directory, exist_ok=True)
        if args.verbose:
            logger.info(f"Created directory: {directory}")

def collect_and_prepare_data(args):
    """Collect and prepare data for modeling"""
    # Collect data if requested
    if args.collect_data:
        logger.info(f"Collecting {args.period} of {args.interval} data...")
        collector = DataCollector(output_dir="data", interval=args.interval, period=args.period)
        data = collector.fetch_data(batch_size=5)
        cleaned_data = collector.clean_data()
        logger.info(f"Collected data for {cleaned_data['Ticker'].nunique()} tickers")
    else:
        # Use existing data
        cleaned_file = os.path.join("data", "nikkei225_cleaned.csv")
        if os.path.exists(cleaned_file):
            logger.info("Loading existing clean data")
            cleaned_data = pd.read_csv(cleaned_file, parse_dates=['Datetime'])
        else:
            logger.error("No existing data found. Please use --collect-data to collect new data.")
            return None
    
    # Engineer features
    features_file = os.path.join("data", "nikkei225_with_features.csv")
    if args.rebuild_features or not os.path.exists(features_file):
        logger.info("Engineering features...")
        feature_engineer = FeatureEngineer(
            input_file=os.path.join("data", "nikkei225_cleaned.csv"),
            output_dir="data"
        )
        processed_data = feature_engineer.process_and_save(include_market_features=True)
        logger.info(f"Added features. New data shape: {processed_data.shape}")
    else:
        logger.info("Loading existing features")
        processed_data = pd.read_csv(features_file, parse_dates=['Datetime'])
    
    return processed_data

def select_tickers(data, args):
    """Select tickers for analysis"""
    if args.tickers:
        # Use specific tickers provided by the user
        selected_tickers = args.tickers
        if len(selected_tickers) > args.num_tickers:
            logger.warning(f"Too many tickers specified. Using only the first {args.num_tickers}.")
            selected_tickers = selected_tickers[:args.num_tickers]
    else:
        # Select tickers based on specified method
        all_tickers = data['Ticker'].unique()
        
        if args.select_method == 'volatility':
            # Calculate volatility (standard deviation of returns)
            volatility = data.groupby('Ticker')['Close'].pct_change().groupby(data['Ticker']).std().sort_values(ascending=False)
            selected_tickers = volatility.head(args.num_tickers).index.tolist()
            
        elif args.select_method == 'volume':
            # Select tickers with highest average volume
            volume = data.groupby('Ticker')['Volume'].mean().sort_values(ascending=False)
            selected_tickers = volume.head(args.num_tickers).index.tolist()
            
        elif args.select_method == 'market_cap':
            # This is a placeholder - in a real implementation, you would
            # need additional data about market capitalization
            logger.warning("Market cap selection not implemented. Using volatility instead.")
            volatility = data.groupby('Ticker')['Close'].pct_change().groupby(data['Ticker']).std().sort_values(ascending=False)
            selected_tickers = volatility.head(args.num_tickers).index.tolist()
            
        elif args.select_method == 'random':
            # Select random tickers
            selected_tickers = np.random.choice(all_tickers, size=min(args.num_tickers, len(all_tickers)), replace=False).tolist()
            
        else:
            logger.error(f"Unknown selection method: {args.select_method}")
            return None
    
    logger.info(f"Selected tickers: {selected_tickers}")
    return selected_tickers

def train_models(data, selected_tickers, args):
    """Train AI models based on selected model type"""
    # Filter data for selected tickers
    filtered_data = data[data['Ticker'].isin(selected_tickers)]
    
    if args.model_type == 'hybrid' or args.model_type in ['lstm', 'xgboost', 'lightgbm', 'transformer']:
        # Initialize model
        model = NikkeiAIModel(model_dir="models", data_dir="data")
        
        # Prepare features
        logger.info("Preparing features for model training...")
        prepared_data = model.prepare_features(
            filtered_data,
            ticker=None,  # Train on all selected tickers
            target_col='Close',
            sequence_length=args.sequence_length,
            prediction_horizon=args.prediction_horizon,
            train_ratio=0.8
        )
        
        # Train models
        logger.info("Training models...")
        results = model.train_models(
            prepared_data,
            ticker="nikkei_selected",
            epochs=args.epochs,
            batch_size=args.batch_size
        )
        
        # Save model metadata
        model.save_model_metadata("nikkei_selected", results)
        
        # Perform walk-forward optimization if requested
        if args.walk_forward:
            logger.info("Performing walk-forward optimization...")
            for ticker in selected_tickers:
                logger.info(f"Walk-forward optimization for {ticker}...")
                wf_results = model.walk_forward_optimization(
                    data,
                    ticker=ticker,
                    window_size=30,  # 30 days
                    step_size=7,     # 7 days
                    prediction_horizon=args.prediction_horizon,
                    sequence_length=args.sequence_length
                )
                
                if wf_results is not None and args.save_plots:
                    visualize_predictions(wf_results, ticker, args.output_dir)
        
        # Select top stocks for trading
        logger.info(f"Selecting top stocks for trading...")
        top_stocks = model.evaluate_and_select_stocks(
            data,
            prediction_horizon=args.prediction_horizon,
            top_n=args.num_tickers
        )
        
        # Save and visualize results
        results_file = os.path.join(args.output_dir, f"top_stocks_{datetime.now().strftime('%Y%m%d')}.csv")
        top_stocks.to_csv(results_file, index=False)
        logger.info(f"Saved top stocks to {results_file}")
        
        if args.save_plots:
            visualize_stock_selection(top_stocks, args.output_dir)
        
        return model, top_stocks
    
    elif args.model_type == 'rl':
        # Initialize RL trader
        rl_trader = RLStockTrader(model_dir="models")
        
        # Create ensemble of RL models
        logger.info("Creating RL ensemble...")
        env = StockTradingEnv(filtered_data, window_size=args.rl_window_size)
        
        # Train DQN model
        logger.info("Training DQN model...")
        dqn_model = rl_trader.train_dqn(env, total_timesteps=args.rl_timesteps)
        
        # Train PPO model
        logger.info("Training PPO model...")
        ppo_model = rl_trader.train_ppo(env, total_timesteps=args.rl_timesteps * 2)
        
        # Evaluate models
        logger.info("Evaluating models...")
        dqn_mean, dqn_std = rl_trader.evaluate_model(dqn_model, env, n_eval_episodes=args.rl_eval_episodes)
        ppo_mean, ppo_std = rl_trader.evaluate_model(ppo_model, env, n_eval_episodes=args.rl_eval_episodes)
        
        # Create ensemble
        ensemble = {
            'dqn_model': dqn_model,
            'ppo_model': ppo_model,
            'weights': {
                'dqn': dqn_mean / (dqn_mean + ppo_mean),
                'ppo': ppo_mean / (dqn_mean + ppo_mean)
            },
            'performance': {
                'dqn': dqn_mean,
                'ppo': ppo_mean
            }
        }
        
        logger.info(f"DQN mean reward: {dqn_mean:.2f} +/- {dqn_std:.2f}")
        logger.info(f"PPO mean reward: {ppo_mean:.2f} +/- {ppo_std:.2f}")
        logger.info(f"Ensemble weights: DQN={ensemble['weights']['dqn']:.2f}, PPO={ensemble['weights']['ppo']:.2f}")
        
        # Backtest if requested
        if args.backtest:
            logger.info("Backtesting RL model...")
            results = rl_trader.backtest(dqn_model, env, verbose=args.verbose)
            
            # Save backtest results
            backtest_file = os.path.join(args.output_dir, f"rl_backtest_{datetime.now().strftime('%Y%m%d')}.csv")
            pd.DataFrame({
                'Step': results['steps'],
                'NetWorth': results['net_worths']
            }).to_csv(backtest_file, index=False)
            logger.info(f"Saved backtest results to {backtest_file}")
        
        return rl_trader, ensemble

def visualize_predictions(results, ticker, output_dir):
    """Visualize prediction results"""
    plt.figure(figsize=(12, 6))
    
    # Plot actual vs. predicted values
    plt.plot(results['Datetime'], results['Actual'], label='Actual', marker='o', alpha=0.7)
    plt.plot(results['Datetime'], results['Predicted'], label='Predicted', marker='x', alpha=0.7)
    
    # Add labels and title
    plt.title(f'Actual vs. Predicted Prices for {ticker}')
    plt.xlabel('Date')
    plt.ylabel('Price')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    # Rotate x-axis labels for better readability
    plt.xticks(rotation=45)
    plt.tight_layout()
    
    # Save plot
    plt.savefig(os.path.join(output_dir, f"{ticker}_predictions.png"))
    logger.info(f"Saved prediction visualization to {output_dir}/{ticker}_predictions.png")
    plt.close()

def visualize_stock_selection(top_stocks, output_dir):
    """Visualize stock selection results"""
    plt.figure(figsize=(10, 6))
    
    # Create a bar chart of predicted changes
    ax = plt.bar(top_stocks['Ticker'], top_stocks['Predicted_Change_Pct'])
    
    # Add labels and title
    plt.title('Predicted Price Change % for Selected Stocks')
    plt.xlabel('Ticker')
    plt.ylabel('Predicted Change %')
    plt.grid(True, alpha=0.3, axis='y')
    
    # Annotate bars with values
    for i, v in enumerate(top_stocks['Predicted_Change_Pct']):
        plt.text(i, v, f"{v:.2f}%", ha='center', va='bottom', fontweight='bold')
    
    plt.tight_layout()
    
    # Save plot
    plt.savefig(os.path.join(output_dir, "top_stocks_prediction.png"))
    logger.info(f"Saved stock selection visualization to {output_dir}/top_stocks_prediction.png")
    plt.close()

def main():
    """Main function"""
    # Parse arguments
    args = parse_arguments()
    
    # Setup directories
    setup_directories(args)
    
    # Collect and prepare data
    data = collect_and_prepare_data(args)
    if data is None:
        return
    
    # Select tickers
    selected_tickers = select_tickers(data, args)
    if selected_tickers is None:
        return
    
    # Train models if requested
    if args.train:
        model_result = train_models(data, selected_tickers, args)
        if model_result is None:
            return
    
    logger.info("Done!")

if __name__ == "__main__":
    main()
