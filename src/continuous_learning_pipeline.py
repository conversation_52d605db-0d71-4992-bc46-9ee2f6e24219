#!/usr/bin/env python3
"""
日経225 AI取引システム - 継続学習パイプライン

取引結果を評価し、モデルを自動的に改善・再学習するシステム
"""

import os
import logging
import argparse
from datetime import datetime, timedelta
import pandas as pd
import numpy as np
import json
import matplotlib.pyplot as plt
import time
from pathlib import Path

# 自作モジュールのインポート
from .data_collection import DataCollector
from .feature_engineering import FeatureEngineer
from .model import NikkeiAIModel
from .reinforcement_learning import RLStockTrader, StockTradingEnv
from .ai_evaluator import AIEvaluator

# ロギング設定
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f"logs/continuous_learning_{datetime.now().strftime('%Y%m%d')}.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class AutoModelOptimizer:
    """
    取引結果に基づいてモデルを自動最適化するクラス
    """
    
    def __init__(self, base_model_config, models_dir="models"):
        """初期化"""
        self.base_config = base_model_config
        self.models_dir = models_dir
        self.optimization_history = []
        
        # 履歴ファイル
        self.history_file = os.path.join(models_dir, "optimization_history.json")
        self._load_history()
    
    def _load_history(self):
        """最適化履歴を読み込む"""
        if os.path.exists(self.history_file):
            try:
                with open(self.history_file, 'r', encoding='utf-8') as f:
                    self.optimization_history = json.load(f)
                logger.info(f"最適化履歴を読み込みました: {len(self.optimization_history)}件")
            except Exception as e:
                logger.error(f"最適化履歴の読み込みエラー: {str(e)}")
    
    def _save_history(self):
        """最適化履歴を保存する"""
        try:
            with open(self.history_file, 'w', encoding='utf-8') as f:
                json.dump(self.optimization_history, f, indent=2, ensure_ascii=False)
            logger.info(f"最適化履歴を保存しました: {len(self.optimization_history)}件")
        except Exception as e:
            logger.error(f"最適化履歴の保存エラー: {str(e)}")
    
    def optimize_based_on_results(self, evaluation_results):
        """
        評価結果に基づいてパラメータを最適化
        
        Parameters:
        -----------
        evaluation_results : dict
            AIEvaluatorによる評価結果
            
        Returns:
        --------
        dict
            最適化されたモデル設定
        """
        logger.info("評価結果に基づくモデル最適化を開始")
        
        # ベース設定をコピー
        optimized_config = self.base_config.copy()
        
        # 変更点のリスト
        changes = []
        
        # 1. 方向性精度に基づく調整
        directional_accuracy = evaluation_results.get("directional_metrics", {}).get("directional_accuracy", 0)
        
        if directional_accuracy < 55:
            # 方向性予測が弱い場合、テクニカル指標の重みを増加
            if 'feature_weights' in optimized_config:
                for feature in optimized_config['feature_weights']:
                    if any(tech in feature.lower() for tech in ['rsi', 'macd', 'bollinger', 'trend', 'momentum']):
                        optimized_config['feature_weights'][feature] *= 1.2
                        changes.append(f"{feature}の重みを20%増加")
            
            # 方向性特化のアンサンブル重みを調整
            if 'ensemble_weights' in optimized_config:
                if 'trend_model' in optimized_config['ensemble_weights']:
                    optimized_config['ensemble_weights']['trend_model'] *= 1.2
                    changes.append("トレンドモデルの重みを20%増加")
        
        # 2. 特定のセクターの問題に対応
        sector_analysis = evaluation_results.get("dimensional_analysis", {}).get("sector_analysis", {})
        weak_sectors = []
        
        for sector, sector_data in sector_analysis.items():
            sector_accuracy = sector_data.get("directional_metrics", {}).get("directional_accuracy", 0)
            if sector_accuracy < 45:
                weak_sectors.append(sector)
                
                # セクター特有の特徴量やモデルパラメータを調整
                if 'sector_specific_params' not in optimized_config:
                    optimized_config['sector_specific_params'] = {}
                
                if sector not in optimized_config['sector_specific_params']:
                    optimized_config['sector_specific_params'][sector] = {}
                
                # セクター特有の強化学習
                optimized_config['sector_specific_params'][sector]['learning_rate'] = 0.001
                optimized_config['sector_specific_params'][sector]['train_epochs'] = 100
                changes.append(f"{sector}セクター用の特別パラメータを設定")
        
        # 3. 時間軸別の調整
        time_horizon_analysis = evaluation_results.get("dimensional_analysis", {}).get("time_horizon_analysis", {})
        
        for horizon, horizon_data in time_horizon_analysis.items():
            horizon_accuracy = horizon_data.get("directional_metrics", {}).get("directional_accuracy", 0)
            
            if horizon_accuracy < 45:
                if 'time_horizon_params' not in optimized_config:
                    optimized_config['time_horizon_params'] = {}
                
                if horizon == "short_term":
                    # 短期予測の改善
                    optimized_config['time_horizon_params']['short_term'] = {
                        'features': ['price_change_1h', 'price_change_3h', 'RSI_6', 'BB_Upper_1sigma', 'BB_Lower_1sigma', 'ATR_7'],
                        'weight': 1.2
                    }
                    changes.append("短期予測の特徴量と重みを調整")
                
                elif horizon == "medium_term":
                    # 中期予測の改善
                    optimized_config['time_horizon_params']['medium_term'] = {
                        'features': ['SMA_10', 'SMA_20', 'EMA_10', 'MACD', 'RSI_14'],
                        'weight': 1.1
                    }
                    changes.append("中期予測の特徴量と重みを調整")
                
                elif horizon == "long_term":
                    # 長期予測の改善
                    optimized_config['time_horizon_params']['long_term'] = {
                        'features': ['SMA_50', 'EMA_50', 'ROC_20', 'Market_SMA_50_Ratio'],
                        'weight': 1.0
                    }
                    changes.append("長期予測の特徴量と重みを調整")
        
        # 4. 取引メトリクスに基づく調整
        win_rate = evaluation_results.get("trading_metrics", {}).get("win_rate", 0)
        
        if win_rate < 50:
            # 勝率が低い場合、リスク回避パラメータを調整
            if 'trading_params' not in optimized_config:
                optimized_config['trading_params'] = {}
            
            optimized_config['trading_params']['confidence_threshold'] = 0.65  # 信頼度閾値を上げる
            optimized_config['trading_params']['stop_loss_pct'] = 1.5  # ストップロス設定
            changes.append("取引パラメータを保守的に調整")
        
        # 5. モデルアーキテクチャの調整
        model_performance = evaluation_results.get("regression_metrics", {})
        mape = model_performance.get("mape", 0)
        r2 = model_performance.get("r2", 0)
        
        if mape > 5 or r2 < 0.5:
            # 予測精度が低い場合、モデル複雑性を調整
            if 'model_architecture' not in optimized_config:
                optimized_config['model_architecture'] = {}
            
            # LSTMユニット数の調整
            if 'lstm_units' in optimized_config['model_architecture']:
                optimized_config['model_architecture']['lstm_units'] = [
                    int(unit * 1.2) for unit in optimized_config['model_architecture']['lstm_units']
                ]
                changes.append("LSTMユニット数を20%増加")
            
            # ドロップアウト率の調整
            if 'dropout_rate' in optimized_config['model_architecture']:
                optimized_config['model_architecture']['dropout_rate'] += 0.05
                changes.append(f"ドロップアウト率を{optimized_config['model_architecture']['dropout_rate']}に増加")
        
        # 6. 強化学習特有の調整
        if 'rl_params' in optimized_config:
            # 探索率の調整
            if directional_accuracy < 50:
                optimized_config['rl_params']['exploration_rate'] = 0.2
                changes.append("探索率を0.2に設定")
            
            # 割引率の調整
            if win_rate < 45:
                optimized_config['rl_params']['gamma'] = 0.95
                changes.append("割引率γを0.95に調整")
        
        # 最適化記録を保存
        optimization_record = {
            "timestamp": datetime.now().isoformat(),
            "evaluation_metrics": {
                "directional_accuracy": directional_accuracy,
                "win_rate": win_rate,
                "mape": mape,
                "r2": r2
            },
            "changes": changes,
            "config_before": self.base_config,
            "config_after": optimized_config
        }
        
        self.optimization_history.append(optimization_record)
        self._save_history()
        
        logger.info(f"モデル最適化が完了しました。変更点: {len(changes)}項目")
        for change in changes:
            logger.info(f"- {change}")
        
        return optimized_config

class PerformanceTracker:
    """モデル改善の追跡とパフォーマンス測定"""
    
    def __init__(self, history_dir="performance_history"):
        """初期化"""
        self.history_dir = history_dir
        os.makedirs(history_dir, exist_ok=True)
        
        # パフォーマンス履歴ファイル
        self.history_file = os.path.join(history_dir, "model_performance_history.csv")
        self.performance_history = self._load_history()
    
    def _load_history(self):
        """パフォーマンス履歴を読み込む"""
        if os.path.exists(self.history_file):
            try:
                history = pd.read_csv(self.history_file)
                logger.info(f"パフォーマンス履歴を読み込みました: {len(history)}件")
                return history
            except Exception as e:
                logger.error(f"パフォーマンス履歴の読み込みエラー: {str(e)}")
        
        # 新規ファイルの場合は空のDataFrameを作成
        return pd.DataFrame(columns=[
            'timestamp', 'model_version', 'directional_accuracy', 
            'win_rate', 'profit_pct', 'sharpe_ratio', 'max_drawdown',
            'rmse', 'mae', 'mape', 'r2'
        ])
    
    def record_model_performance(self, model_version, metrics):
        """
        モデルパフォーマンスを記録
        
        Parameters:
        -----------
        model_version : str
            モデルバージョン
        metrics : dict
            パフォーマンス指標
            
        Returns:
        --------
        pd.DataFrame
            更新されたパフォーマンス履歴
        """
        # 新しいレコードを作成
        new_record = {
            'timestamp': datetime.now().isoformat(),
            'model_version': model_version
        }
        
        # 方向性指標
        directional_metrics = metrics.get('directional_metrics', {})
        new_record['directional_accuracy'] = directional_metrics.get('directional_accuracy', 0)
        
        # 取引指標
        trading_metrics = metrics.get('trading_metrics', {})
        new_record['win_rate'] = trading_metrics.get('win_rate', 0)
        new_record['profit_pct'] = trading_metrics.get('total_profit_pct', 0)
        new_record['sharpe_ratio'] = trading_metrics.get('sharpe_ratio', 0)
        new_record['max_drawdown'] = trading_metrics.get('max_drawdown', 0)
        
        # 回帰指標
        regression_metrics = metrics.get('regression_metrics', {})
        new_record['rmse'] = regression_metrics.get('rmse', 0)
        new_record['mae'] = regression_metrics.get('mae', 0)
        new_record['mape'] = regression_metrics.get('mape', 0)
        new_record['r2'] = regression_metrics.get('r2', 0)
        
        # DataFrameに追加
        self.performance_history = pd.concat([
            self.performance_history, 
            pd.DataFrame([new_record])
        ], ignore_index=True)
        
        # CSVに保存
        self.performance_history.to_csv(self.history_file, index=False)
        logger.info(f"モデル '{model_version}' のパフォーマンスを記録しました")
        
        return self.performance_history
    
    def compare_models(self, old_version, new_version):
        """
        モデルバージョン間の比較
        
        Parameters:
        -----------
        old_version : str
            旧モデルバージョン
        new_version : str
            新モデルバージョン
            
        Returns:
        --------
        dict
            比較結果
        """
        history = self.performance_history
        
        # 各バージョンの最新レコードを取得
        old_model = history[history['model_version'] == old_version].iloc[-1] if any(history['model_version'] == old_version) else None
        new_model = history[history['model_version'] == new_version].iloc[-1] if any(history['model_version'] == new_version) else None
        
        if old_model is None or new_model is None:
            logger.warning(f"比較できません: 旧モデル={old_version}{'(なし)' if old_model is None else ''}, 新モデル={new_version}{'(なし)' if new_model is None else ''}")
            return {"error": "モデルが見つかりません"}
        
        # 改善率を計算
        improvements = {}
        key_metrics = [
            'directional_accuracy', 'win_rate', 'profit_pct', 
            'sharpe_ratio', 'max_drawdown', 'rmse', 'mae', 'mape', 'r2'
        ]
        
        for metric in key_metrics:
            old_value = float(old_model[metric])
            new_value = float(new_model[metric])
            
            # 改善率（正の値が改善）
            if metric in ['max_drawdown', 'rmse', 'mae', 'mape']:
                # これらは小さいほど良い指標
                improvement_pct = (old_value - new_value) / max(0.1, abs(old_value)) * 100
            else:
                # これらは大きいほど良い指標
                improvement_pct = (new_value - old_value) / max(0.1, abs(old_value)) * 100
            
            improvements[metric] = {
                'old_value': old_value,
                'new_value': new_value,
                'improvement_pct': improvement_pct,
                'improved': improvement_pct > 0
            }
        
        # 総合的な改善を評価
        key_improvements = [improvements[m]['improvement_pct'] for m in ['directional_accuracy', 'profit_pct', 'sharpe_ratio']]
        overall_improvement = sum(key_improvements) / len(key_improvements)
        
        # 決定：新モデルを採用するか
        adoption_threshold = 3.0  # 3%以上の改善で採用
        
        return {
            'improvements': improvements,
            'overall_improvement': overall_improvement,
            'recommend_adoption': overall_improvement > adoption_threshold,
            'old_version': old_version,
            'new_version': new_version,
            'comparison_time': datetime.now().isoformat()
        }
    
    def plot_performance_history(self, metrics=None, save_dir=None):
        """
        パフォーマンス履歴をプロット
        
        Parameters:
        -----------
        metrics : list
            プロットする指標のリスト
        save_dir : str
            保存先ディレクトリ
            
        Returns:
        --------
        list
            生成されたプロットのファイルパスリスト
        """
        if len(self.performance_history) < 2:
            logger.warning("プロット用のデータが不足しています")
            return []
        
        if metrics is None:
            metrics = [
                'directional_accuracy', 'win_rate', 'profit_pct', 
                'sharpe_ratio', 'rmse', 'mape'
            ]
        
        if save_dir is None:
            save_dir = self.history_dir
        
        # 日時をパース
        history = self.performance_history.copy()
        history['timestamp'] = pd.to_datetime(history['timestamp'])
        
        # 生成されたファイルのリスト
        plot_files = []
        
        # メトリクスごとにプロット
        for metric in metrics:
            if metric not in history.columns:
                continue
                
            plt.figure(figsize=(12, 6))
            
            # 各モデルバージョンを別の色でプロット
            versions = history['model_version'].unique()
            
            for version in versions:
                version_data = history[history['model_version'] == version]
                plt.plot(version_data['timestamp'], version_data[metric], marker='o', linestyle='-', label=f'Model {version}')
            
            plt.title(f'{metric} Over Time')
            plt.xlabel('Date')
            plt.ylabel(metric)
            plt.grid(True, alpha=0.3)
            plt.legend()
            
            # 日付フォーマットを調整
            plt.gcf().autofmt_xdate()
            
            # 保存
            os.makedirs(save_dir, exist_ok=True)
            file_path = os.path.join(save_dir, f'{metric}_history.png')
            plt.savefig(file_path)
            plt.close()
            
            plot_files.append(file_path)
            logger.info(f"パフォーマンスプロットを保存しました: {file_path}")
        
        # 総合的なパフォーマンスダッシュボード
        if len(metrics) >= 4:
            plt.figure(figsize=(15, 10))
            
            # サブプロットの配置を計算
            n_plots = min(6, len(metrics))
            n_cols = 2
            n_rows = (n_plots + 1) // n_cols
            
            for i, metric in enumerate(metrics[:n_plots]):
                if metric not in history.columns:
                    continue
                    
                plt.subplot(n_rows, n_cols, i+1)
                
                for version in versions:
                    version_data = history[history['model_version'] == version]
                    plt.plot(version_data['timestamp'], version_data[metric], marker='o', linestyle='-', label=f'Model {version}')
                
                plt.title(metric)
                plt.grid(True, alpha=0.3)
                
                # 最初のプロットにだけ凡例を表示
                if i == 0:
                    plt.legend()
            
            plt.tight_layout()
            
            # 保存
            dashboard_path = os.path.join(save_dir, 'performance_dashboard.png')
            plt.savefig(dashboard_path)
            plt.close()
            
            plot_files.append(dashboard_path)
            logger.info(f"パフォーマンスダッシュボードを保存しました: {dashboard_path}")
        
        return plot_files

class ContinuousLearningPipeline:
    """
    継続学習パイプライン
    """
    
    def __init__(self, base_dir='.', data_dir='data', models_dir='models', results_dir='results'):
        """初期化"""
        self.base_dir = base_dir
        self.data_dir = os.path.join(base_dir, data_dir)
        self.models_dir = os.path.join(base_dir, models_dir)
        self.results_dir = os.path.join(base_dir, results_dir)
        
        # ディレクトリを作成
        for d in [self.data_dir, self.models_dir, self.results_dir]:
            os.makedirs(d, exist_ok=True)
        
        # コンポーネントの初期化
        self.data_collector = None  # 必要時に初期化
        self.feature_engineer = None  # 必要時に初期化
        self.model = NikkeiAIModel(model_dir=self.models_dir, data_dir=self.data_dir)
        self.evaluator = AIEvaluator(evaluation_dir=os.path.join(self.results_dir, "ai_evaluation"))
        
        # モデル最適化用のベース設定
        self.base_model_config = self._load_base_model_config()
        self.model_optimizer = AutoModelOptimizer(self.base_model_config, models_dir=self.models_dir)
        
        # パフォーマンス追跡
        self.performance_tracker = PerformanceTracker(
            history_dir=os.path.join(self.results_dir, "performance_history")
        )
        
        # モデルバージョン管理
        self.model_versions_file = os.path.join(self.models_dir, "model_versions.json")
        self.model_versions = self._load_model_versions()
    
    def _load_base_model_config(self):
        """ベースモデル設定を読み込む"""
        config_file = os.path.join(self.models_dir, "base_model_config.json")
        
        if os.path.exists(config_file):
            try:
                with open(config_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception as e:
                logger.error(f"ベースモデル設定の読み込みエラー: {str(e)}")
        
        # デフォルト設定
        default_config = {
            "version": "1.0.0",
            "model_type": "hybrid",
            "feature_weights": {
                "price": 1.0,
                "volume": 0.8,
                "trend": 1.2,
                "momentum": 1.0,
                "volatility": 0.9
            },
            "model_architecture": {
                "lstm_units": [64, 32],
                "dropout_rate": 0.2,
                "dense_units": [32, 16]
            },
            "training_params": {
                "batch_size": 32,
                "epochs": 50,
                "learning_rate": 0.001
            },
            "ensemble_weights": {
                "lstm_model": 0.4,
                "transformer_model": 0.3,
                "xgboost_model": 0.2,
                "lightgbm_model": 0.1
            },
            "rl_params": {
                "window_size": 50,
                "exploration_rate": 0.1,
                "gamma": 0.99
            },
            "trading_params": {
                "confidence_threshold": 0.6,
                "position_size_pct": 5.0,
                "max_positions": 10
            }
        }
        
        # デフォルト設定を保存
        try:
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(default_config, f, indent=2, ensure_ascii=False)
            logger.info(f"デフォルトのベースモデル設定を作成しました: {config_file}")
        except Exception as e:
            logger.error(f"ベースモデル設定の保存エラー: {str(e)}")
        
        return default_config
    
    def _load_model_versions(self):
        """モデルバージョン情報を読み込む"""
        if os.path.exists(self.model_versions_file):
            try:
                with open(self.model_versions_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception as e:
                logger.error(f"モデルバージョン情報の読み込みエラー: {str(e)}")
        
        # デフォルト
        return {
            "current_version": "1.0.0",
            "previous_version": None,
            "versions": [
                {
                    "version": "1.0.0",
                    "created_at": datetime.now().isoformat(),
                    "metrics": {},
                    "config_file": "base_model_config.json"
                }
            ]
        }
    
    def _save_model_versions(self):
        """モデルバージョン情報を保存"""
        try:
            with open(self.model_versions_file, 'w', encoding='utf-8') as f:
                json.dump(self.model_versions, f, indent=2, ensure_ascii=False)
            logger.info("モデルバージョン情報を保存しました")
        except Exception as e:
            logger.error(f"モデルバージョン情報の保存エラー: {str(e)}")
    
    def _create_new_version(self, old_version):
        """新しいバージョン番号を生成"""
        major, minor, patch = map(int, old_version.split('.'))
        patch += 1
        return f"{major}.{minor}.{patch}"
    
    def collect_latest_data(self, start_date=None, end_date=None, tickers=None):
        """
        最新のデータを収集
        
        Parameters:
        -----------
        start_date : str
            開始日 (YYYY-MM-DD)
        end_date : str
            終了日 (YYYY-MM-DD)
        tickers : list
            銘柄のリスト
            
        Returns:
        --------
        pd.DataFrame
            収集されたデータ
        """
        logger.info("最新データの収集を開始")
        
        # 開始日と終了日の設定
        if end_date is None:
            end_date = datetime.now().strftime('%Y-%m-%d')
            
        if start_date is None:
            start_date = (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d')
        
        # データコレクターの初期化
        if self.data_collector is None:
            from data_collection import DataCollector
            self.data_collector = DataCollector(output_dir=self.data_dir)
        
        # データの収集
        try:
            # 特定の銘柄が指定されている場合
            if tickers:
                collected_data = pd.DataFrame()
                for ticker_batch in [tickers[i:i+5] for i in range(0, len(tickers), 5)]:
                    batch_data = self.data_collector.fetch_data(tickers=ticker_batch)
                    collected_data = pd.concat([collected_data, batch_data])
                    # API制限を回避するための待機
                    time.sleep(2)
            else:
                # 全銘柄のデータを収集
                collected_data = self.data_collector.fetch_data()
            
            # データのクリーニング
            cleaned_data = self.data_collector.clean_data()
            
            logger.info(f"データ収集完了: {len(cleaned_data)}行, {cleaned_data['Ticker'].nunique()}銘柄")
            return cleaned_data
        
        except Exception as e:
            logger.error(f"データ収集エラー: {str(e)}")
            return None
    
    def engineer_features(self, data=None, include_market_features=True):
        """
        特徴量エンジニアリングを実行
        
        Parameters:
        -----------
        data : pd.DataFrame
            処理するデータ（Noneの場合は、キャッシュから読み込み）
        include_market_features : bool
            市場全体の特徴を含めるかどうか
            
        Returns:
        --------
        pd.DataFrame
            特徴量追加後のデータ
        """
        logger.info("特徴量エンジニアリング処理を開始")
        
        # 特徴量エンジニアを初期化
        if self.feature_engineer is None:
            self.feature_engineer = FeatureEngineer(output_dir=self.data_dir)
        
        try:
            if data is not None:
                # 提供されたデータを処理
                temp_file = os.path.join(self.data_dir, "temp_cleaned_data.csv")
                data.to_csv(temp_file, index=False)
                self.feature_engineer.input_file = temp_file
            
            # 特徴量を追加
            processed_data = self.feature_engineer.process_and_save(include_market_features=include_market_features)
            
            logger.info(f"特徴量エンジニアリング完了: {processed_data.shape[1]}特徴量")
            return processed_data
        
        except Exception as e:
            logger.error(f"特徴量エンジニアリングエラー: {str(e)}")
            return None
    
    def evaluate_model(self, model_version=None):
        """
        モデルの評価を実行
        
        Parameters:
        -----------
        model_version : str
            評価するモデルのバージョン（Noneの場合は現行バージョン）
            
        Returns:
        --------
        dict
            評価結果
        """
        logger.info(f"モデル評価を開始: バージョン={model_version or '現行バージョン'}")
        
        # 評価結果を取得
        evaluation_results = self.evaluator.evaluate_performance()
        
        if evaluation_results:
            # パフォーマンスを記録
            version = model_version or self.model_versions["current_version"]
            self.performance_tracker.record_model_performance(version, evaluation_results)
            
            logger.info(f"モデル評価完了: 方向性精度={evaluation_results.get('directional_metrics', {}).get('directional_accuracy', 0):.1f}%")
            
            # 評価レポートを生成
            report_file = os.path.join(
                self.results_dir, 
                f"model_evaluation_{datetime.now().strftime('%Y%m%d_%H%M%S')}.html"
            )
            self.evaluator.generate_report(output_file=report_file)
            
            return evaluation_results
        else:
            logger.warning("評価対象のデータがありません")
            return None
    
    def optimize_model(self, evaluation_results):
        """
        モデルの最適化を実行
        
        Parameters:
        -----------
        evaluation_results : dict
            評価結果
            
        Returns:
        --------
        dict
            最適化されたモデル設定
        """
        logger.info("モデルの最適化を開始")
        
        # ベースモデル設定を評価結果に基づいて最適化
        optimized_config = self.model_optimizer.optimize_based_on_results(evaluation_results)
        
        # 新しいモデルバージョンを生成
        current_version = self.model_versions["current_version"]
        new_version = self._create_new_version(current_version)
        
        # 最適化された設定を保存
        config_file = os.path.join(self.models_dir, f"model_config_{new_version}.json")
        try:
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(optimized_config, f, indent=2, ensure_ascii=False)
            logger.info(f"最適化設定を保存しました: {config_file}")
            
            # バージョン情報を更新
            self.model_versions["versions"].append({
                "version": new_version,
                "created_at": datetime.now().isoformat(),
                "metrics": {},
                "config_file": os.path.basename(config_file),
                "based_on": current_version
            })
            
            return {
                "config": optimized_config,
                "version": new_version,
                "config_file": config_file
            }
        
        except Exception as e:
            logger.error(f"モデル最適化の保存エラー: {str(e)}")
            return None
    
    def retrain_model(self, optimized_model_info, data=None):
        """
        モデルの再学習を実行
        
        Parameters:
        -----------
        optimized_model_info : dict
            最適化されたモデル情報
        data : pd.DataFrame
            学習データ（Noneの場合は自動的に読み込み）
            
        Returns:
        --------
        dict
            再学習結果
        """
        new_version = optimized_model_info["version"]
        optimized_config = optimized_model_info["config"]
        
        logger.info(f"モデルの再学習を開始: 新バージョン={new_version}")
        
        try:
            # データが提供されていない場合は読み込み
            if data is None:
                data_file = os.path.join(self.data_dir, "nikkei225_with_features.csv")
                if not os.path.exists(data_file):
                    logger.error(f"学習データが見つかりません: {data_file}")
                    return None
                
                data = pd.read_csv(data_file, parse_dates=['Datetime'])
            
            # モデルタイプに基づいて再学習
            model_type = optimized_config.get("model_type", "hybrid")
            
            if model_type in ["hybrid", "lstm", "transformer"]:
                # 深層学習モデルの場合
                # データの準備
                prepared_data = self.model.prepare_features(
                    data,
                    sequence_length=optimized_config.get("model_architecture", {}).get("sequence_length", 20),
                    prediction_horizon=optimized_config.get("training_params", {}).get("prediction_horizon", 1)
                )
                
                # モデルの訓練
                training_results = self.model.train_models(
                    prepared_data,
                    epochs=optimized_config.get("training_params", {}).get("epochs", 50),
                    batch_size=optimized_config.get("training_params", {}).get("batch_size", 32)
                )
                
                # モデルのメタデータを保存
                self.model.save_model_metadata(new_version, training_results)
                
                training_result = {
                    "version": new_version,
                    "model_type": model_type,
                    "training_metrics": training_results,
                    "completed": True
                }
            
            elif model_type == "rl":
                # 強化学習モデルの場合
                rl_trader = RLStockTrader(model_dir=self.models_dir)
                
                # 環境の準備
                window_size = optimized_config.get("rl_params", {}).get("window_size", 50)
                env = StockTradingEnv(data, window_size=window_size)
                
                # DQNモデルのトレーニング
                dqn_timesteps = optimized_config.get("rl_params", {}).get("dqn_timesteps", 10000)
                dqn_model = rl_trader.train_dqn(env, total_timesteps=dqn_timesteps)
                
                # PPOモデルのトレーニング
                ppo_timesteps = optimized_config.get("rl_params", {}).get("ppo_timesteps", 20000)
                ppo_model = rl_trader.train_ppo(env, total_timesteps=ppo_timesteps)
                
                # モデル評価
                dqn_mean, dqn_std = rl_trader.evaluate_model(dqn_model, env, n_eval_episodes=5)
                ppo_mean, ppo_std = rl_trader.evaluate_model(ppo_model, env, n_eval_episodes=5)
                
                training_result = {
                    "version": new_version,
                    "model_type": "rl",
                    "training_metrics": {
                        "dqn_mean_reward": dqn_mean,
                        "dqn_std_reward": dqn_std,
                        "ppo_mean_reward": ppo_mean,
                        "ppo_std_reward": ppo_std
                    },
                    "completed": True
                }
            
            else:
                logger.error(f"未対応のモデルタイプ: {model_type}")
                return None
            
            logger.info(f"モデル再学習が完了しました: バージョン={new_version}")
            return training_result
        
        except Exception as e:
            logger.error(f"モデル再学習エラー: {str(e)}")
            return {
                "version": new_version,
                "error": str(e),
                "completed": False
            }
    
    def compare_and_adopt(self, old_version, new_version, results):
        """
        新旧モデルを比較し、採用判断を行う
        
        Parameters:
        -----------
        old_version : str
            旧モデルバージョン
        new_version : str
            新モデルバージョン
        results : dict
            再学習結果
            
        Returns:
        --------
        bool
            採用判断（True=新モデルを採用）
        """
        logger.info(f"モデル比較: {old_version} vs {new_version}")
        
        # パフォーマンス比較
        comparison = self.performance_tracker.compare_models(old_version, new_version)
        
        if "error" in comparison:
            logger.warning(f"モデル比較エラー: {comparison['error']}")
            return False
        
        # 推奨判断
        recommend_adoption = comparison.get("recommend_adoption", False)
        overall_improvement = comparison.get("overall_improvement", 0)
        
        # 詳細な改善点をログに記録
        improvements = comparison.get("improvements", {})
        for metric, data in improvements.items():
            change_direction = "改善" if data.get("improved", False) else "悪化"
            logger.info(f"- {metric}: {data.get('old_value', 0):.2f} → {data.get('new_value', 0):.2f} ({data.get('improvement_pct', 0):.1f}% {change_direction})")
        
        # 採用判断
        if recommend_adoption:
            logger.info(f"新モデル採用を推奨: 全体改善率={overall_improvement:.1f}%")
            
            # バージョン情報を更新
            self.model_versions["previous_version"] = old_version
            self.model_versions["current_version"] = new_version
            self._save_model_versions()
            
            return True
        else:
            logger.info(f"現行モデルの維持を推奨: 全体改善率={overall_improvement:.1f}%（基準値未満）")
            return False
    
    def run_continuous_learning_cycle(self, collect_new_data=True):
        """
        継続学習サイクルを実行
        
        Parameters:
        -----------
        collect_new_data : bool
            新規データを収集するかどうか
            
        Returns:
        --------
        dict
            実行結果
        """
        cycle_start_time = datetime.now()
        logger.info("継続学習サイクルを開始")
        
        results = {
            "start_time": cycle_start_time.isoformat(),
            "steps_completed": [],
            "improved_model": False
        }
        
        try:
            # 1. 最新データの収集
            if collect_new_data:
                data = self.collect_latest_data()
                if data is None:
                    logger.error("データ収集に失敗しました")
                    results["error"] = "データ収集エラー"
                    return results
                
                results["steps_completed"].append("data_collection")
                
                # 2. 特徴量エンジニアリング
                data_with_features = self.engineer_features(data)
                if data_with_features is None:
                    logger.error("特徴量エンジニアリングに失敗しました")
                    results["error"] = "特徴量エンジニアリングエラー"
                    return results
                
                results["steps_completed"].append("feature_engineering")
            
            # 3. モデルの評価
            evaluation_results = self.evaluate_model()
            if evaluation_results is None:
                logger.error("モデル評価に失敗しました")
                results["error"] = "モデル評価エラー"
                return results
            
            results["steps_completed"].append("model_evaluation")
            results["evaluation_results"] = {
                "directional_accuracy": evaluation_results.get("directional_metrics", {}).get("directional_accuracy", 0),
                "win_rate": evaluation_results.get("trading_metrics", {}).get("win_rate", 0)
            }
            
            # 4. パラメータ最適化
            optimized_model = self.optimize_model(evaluation_results)
            if optimized_model is None:
                logger.error("モデル最適化に失敗しました")
                results["error"] = "モデル最適化エラー"
                return results
            
            results["steps_completed"].append("model_optimization")
            results["new_model_version"] = optimized_model["version"]
            
            # 5. モデル再学習
            training_result = self.retrain_model(optimized_model)
            if training_result is None or not training_result.get("completed", False):
                logger.error("モデル再学習に失敗しました")
                results["error"] = "モデル再学習エラー"
                return results
            
            results["steps_completed"].append("model_retraining")
            
            # 6. 新モデルと旧モデルの比較検証
            old_version = self.model_versions["current_version"]
            new_version = optimized_model["version"]
            
            adoption_decision = self.compare_and_adopt(old_version, new_version, training_result)
            results["steps_completed"].append("model_comparison")
            
            # 7. 採用判断と更新
            if adoption_decision:
                logger.info(f"新モデル ({new_version}) を採用しました")
                results["improved_model"] = True
                results["adopted_model_version"] = new_version
            else:
                logger.info(f"現行モデル ({old_version}) を維持します")
            
            results["steps_completed"].append("adoption_decision")
            
            # 8. パフォーマンスグラフの生成
            plot_files = self.performance_tracker.plot_performance_history()
            results["performance_plots"] = plot_files
            
            # 完了時間と所要時間を記録
            cycle_end_time = datetime.now()
            results["end_time"] = cycle_end_time.isoformat()
            results["duration_seconds"] = (cycle_end_time - cycle_start_time).total_seconds()
            
            logger.info(f"継続学習サイクルが完了しました（所要時間: {results['duration_seconds']:.1f}秒）")
            return results
        
        except Exception as e:
            logger.error(f"継続学習サイクル実行エラー: {str(e)}")
            results["error"] = str(e)
            return results

def parse_args():
    """コマンドライン引数を解析"""
    parser = argparse.ArgumentParser(description='日経225 AI取引システム - 継続学習パイプライン')
    
    # モード設定
    parser.add_argument('--mode', type=str, choices=['full', 'evaluate', 'optimize', 'retrain'], 
                      default='full', help='実行モード (default: full)')
    
    # データ収集オプション
    parser.add_argument('--collect-data', action='store_true', help='新規データを収集する')
    parser.add_argument('--start-date', type=str, help='データ収集開始日 (YYYY-MM-DD)')
    parser.add_argument('--end-date', type=str, help='データ収集終了日 (YYYY-MM-DD)')
    
    # 特徴量オプション
    parser.add_argument('--skip-market-features', action='store_true', help='市場全体の特徴を含めない')
    
    # モデルオプション
    parser.add_argument('--model-version', type=str, help='使用するモデルバージョン')
    
    # 出力オプション
    parser.add_argument('--output-dir', type=str, default='results', help='結果出力ディレクトリ')
    parser.add_argument('--verbose', action='store_true', help='詳細なログを出力')
    
    return parser.parse_args()

def main():
    """メイン関数"""
    # 引数の解析
    args = parse_args()
    
    # ログレベルの設定
    if args.verbose:
        logger.setLevel(logging.DEBUG)
    
    # ディレクトリの作成
    os.makedirs("logs", exist_ok=True)
    os.makedirs(args.output_dir, exist_ok=True)
    
    # パイプラインの初期化
    pipeline = ContinuousLearningPipeline(results_dir=args.output_dir)
    
    # モードに応じた処理
    if args.mode == 'evaluate':
        # モデル評価のみ
        evaluation_results = pipeline.evaluate_model(args.model_version)
        if evaluation_results:
            print(f"モデル評価結果: 方向性精度={evaluation_results.get('directional_metrics', {}).get('directional_accuracy', 0):.1f}%")
    
    elif args.mode == 'optimize':
        # モデル評価と最適化
        evaluation_results = pipeline.evaluate_model(args.model_version)
        if evaluation_results:
            optimized_model = pipeline.optimize_model(evaluation_results)
            if optimized_model:
                print(f"モデル最適化完了: 新バージョン={optimized_model['version']}")
    
    elif args.mode == 'retrain':
        # モデル再学習のみ
        evaluation_results = pipeline.evaluate_model(args.model_version)
        if evaluation_results:
            optimized_model = pipeline.optimize_model(evaluation_results)
            if optimized_model:
                training_result = pipeline.retrain_model(optimized_model)
                if training_result and training_result.get("completed", False):
                    print(f"モデル再学習完了: バージョン={training_result['version']}")
    
    else:  # 'full'
        # 完全な継続学習サイクル
        results = pipeline.run_continuous_learning_cycle(collect_new_data=args.collect_data)
        
        if "error" in results:
            print(f"エラーが発生しました: {results['error']}")
        else:
            if results.get("improved_model", False):
                print(f"モデルが改善されました！新バージョン: {results.get('adopted_model_version')}")
            else:
                print("現行モデルが維持されました（十分な改善が見られなかったため）")
            
            print(f"完了したステップ: {', '.join(results['steps_completed'])}")
            print(f"処理時間: {results.get('duration_seconds', 0):.1f}秒")

if __name__ == "__main__":
    main()
