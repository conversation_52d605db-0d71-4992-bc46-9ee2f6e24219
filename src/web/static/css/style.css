/* 日経225 AI取引システム カスタムスタイル */

/* 全体のスタイル */
body {
    font-family: 'Helvetica Neue', Arial, sans-serif;
    background-color: #f8f9fa;
    color: #333;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

.container-fluid {
    padding: 0 20px;
}

/* ナビゲーションバー */
.navbar {
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.navbar-brand {
    font-weight: bold;
}

/* カード */
.card {
    border-radius: 8px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
    border: none;
}

.card-header {
    border-radius: 8px 8px 0 0 !important;
    font-weight: bold;
}

.card-body {
    padding: 20px;
}

/* テーブル */
.table {
    margin-bottom: 0;
}

.table th {
    font-weight: 600;
    background-color: #f8f9fa;
}

.table-hover tbody tr:hover {
    background-color: rgba(0, 123, 255, 0.05);
}

/* ダッシュボード */
#portfolio-value, #profit-loss {
    font-weight: bold;
    margin-bottom: 5px;
}

#profit-loss.positive {
    color: #28a745;
}

#profit-loss.negative {
    color: #dc3545;
}

#profit-loss-pct {
    font-size: 0.9rem;
    padding: 2px 6px;
    border-radius: 4px;
}

#profit-loss-pct.positive {
    background-color: rgba(40, 167, 69, 0.1);
    color: #28a745;
}

#profit-loss-pct.negative {
    background-color: rgba(220, 53, 69, 0.1);
    color: #dc3545;
}

/* 推奨銘柄 */
.prediction-up {
    color: #28a745;
}

.prediction-down {
    color: #dc3545;
}

.action-btn {
    padding: 2px 8px;
    font-size: 0.8rem;
    border-radius: 4px;
}

/* 取引履歴 */
.trade-buy {
    background-color: rgba(40, 167, 69, 0.1);
}

.trade-sell {
    background-color: rgba(220, 53, 69, 0.1);
}

/* 銘柄分析 */
#stock-title {
    margin-bottom: 20px;
    font-weight: bold;
}

#stock-info dl {
    display: grid;
    grid-template-columns: 40% 60%;
    margin-bottom: 0;
}

#stock-info dt {
    font-weight: 600;
    margin-bottom: 8px;
}

#stock-info dd {
    margin-bottom: 8px;
}

#stock-indicators table {
    font-size: 0.9rem;
}

/* 設定 */
#settings-form label {
    font-weight: 600;
}

/* フッター */
.footer {
    margin-top: auto;
    border-top: 1px solid #e9ecef;
}

/* ユーティリティクラス */
.text-success {
    color: #28a745 !important;
}

.text-danger {
    color: #dc3545 !important;
}

.bg-light-success {
    background-color: rgba(40, 167, 69, 0.1);
}

.bg-light-danger {
    background-color: rgba(220, 53, 69, 0.1);
}

/* レスポンシブ調整 */
@media (max-width: 768px) {
    .container-fluid {
        padding: 0 10px;
    }
    
    .card-body {
        padding: 15px;
    }
    
    #stock-info dl {
        grid-template-columns: 100%;
    }
    
    #stock-info dt {
        margin-bottom: 2px;
    }
    
    #stock-info dd {
        margin-bottom: 10px;
        margin-left: 0;
    }
}

/* アニメーション */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.fade-in {
    animation: fadeIn 0.5s ease-in-out;
}

/* ローディングスピナー */
.spinner-container {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 200px;
}

.spinner-border {
    width: 3rem;
    height: 3rem;
}

/* チャート */
canvas {
    max-width: 100%;
}

/* タブ */
.nav-link {
    font-weight: 500;
}

.nav-link.active {
    font-weight: 700;
}

/* 更新ボタン */
#refresh-btn {
    transition: all 0.3s ease;
}

#refresh-btn:disabled {
    opacity: 0.7;
}

.fa-spinner {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 最終更新時間 */
#last-update {
    font-size: 0.9rem;
    opacity: 0.8;
}

/* タブコンテンツ */
.tab-pane {
    display: none;
}

.tab-pane.active {
    display: block;
}

.tab-pane.show {
    opacity: 1;
}

.tab-pane.fade {
    transition: opacity 0.15s linear;
}

.tab-pane.fade:not(.show) {
    opacity: 0;
}
