/**
 * システム監視機能
 */

// グローバル変数
let systemChart = null;
let modelPerformanceChart = null;
let refreshInterval = null;
let currentLogType = 'all';

// 初期化
$(document).ready(function() {
    // システムステータスを取得
    refreshSystemStatus();
    
    // 自動更新（30秒ごと）
    refreshInterval = setInterval(refreshSystemStatus, 30000);
    
    // リアルタイム更新を停止（別のタブに移動時）
    $('a[data-bs-toggle="tab"]').on('shown.bs.tab', function(e) {
        const targetId = $(e.target).attr("href");
        if (targetId !== '#monitor') {
            clearInterval(refreshInterval);
            refreshInterval = null;
        } else {
            if (refreshInterval === null) {
                refreshSystemStatus();
                refreshInterval = setInterval(refreshSystemStatus, 30000);
            }
        }
    });
    
    // ログタイプ切り替え
    $('.btn[data-log-type]').on('click', function() {
        $('.btn[data-log-type]').removeClass('active');
        $(this).addClass('active');
        currentLogType = $(this).data('log-type');
        refreshSystemLogs();
    });
    
    // グラフの初期化
    initCharts();
});

// システムステータスを更新
function refreshSystemStatus() {
    $.ajax({
        url: '/api/system/status',
        method: 'GET',
        success: function(data) {
            updateSystemResourcesUI(data.system);
            updateProcessesUI(data.processes);
            refreshModelPerformance();
            refreshSystemLogs();
        },
        error: function(xhr, status, error) {
            console.error("システムステータス取得エラー:", error);
            $('#system-last-update').text("更新失敗");
        }
    });
}

// モデル性能を更新
function refreshModelPerformance() {
    $.ajax({
        url: '/api/analysis/model-performance',
        method: 'GET',
        success: function(data) {
            updateModelPerformanceUI(data);
        },
        error: function(xhr, status, error) {
            console.error("モデル性能取得エラー:", error);
        }
    });
}

// システムログを更新
function refreshSystemLogs() {
    $.ajax({
        url: '/api/system/logs',
        method: 'GET',
        data: {
            type: currentLogType,
            limit: 100
        },
        success: function(data) {
            updateSystemLogsUI(data);
        },
        error: function(xhr, status, error) {
            console.error("システムログ取得エラー:", error);
        }
    });
}

// システムリソースUIを更新
function updateSystemResourcesUI(systemData) {
    // CPU使用率
    $('#cpu-progress')
        .css('width', systemData.cpu_percent + '%')
        .attr('aria-valuenow', systemData.cpu_percent)
        .text(systemData.cpu_percent.toFixed(1) + '%');
    
    // メモリ使用率
    $('#memory-progress')
        .css('width', systemData.memory_percent + '%')
        .attr('aria-valuenow', systemData.memory_percent)
        .text(systemData.memory_percent.toFixed(1) + '%');
    
    // ディスク使用率
    $('#disk-progress')
        .css('width', systemData.disk_percent + '%')
        .attr('aria-valuenow', systemData.disk_percent)
        .text(systemData.disk_percent.toFixed(1) + '%');
    
    // システム稼働時間
    const uptimeSeconds = systemData.system_uptime;
    const days = Math.floor(uptimeSeconds / 86400);
    const hours = Math.floor((uptimeSeconds % 86400) / 3600);
    const minutes = Math.floor((uptimeSeconds % 3600) / 60);
    const seconds = uptimeSeconds % 60;
    
    const uptimeText = days + '日 ' + 
        String(hours).padStart(2, '0') + ':' + 
        String(minutes).padStart(2, '0') + ':' + 
        String(seconds).padStart(2, '0');
    
    $('#system-uptime').text(uptimeText);
    
    // プロセス数
    $('#process-count').text(systemData.process_count);
    
    // 最終更新時間
    const lastUpdateDate = new Date(systemData.timestamp);
    $('#system-last-update').text(lastUpdateDate.toLocaleTimeString());
}

// プロセスUIを更新
function updateProcessesUI(processesData) {
    const tableBody = $('#processes-table');
    tableBody.empty();
    
    if (Object.keys(processesData).length === 0) {
        tableBody.append('<tr><td colspan="6" class="text-center">実行中のプロセスはありません</td></tr>');
        return;
    }
    
    // プロセスごとの行を追加
    for (const [name, process] of Object.entries(processesData)) {
        const currentInfo = process.current_info || {};
        
        // 実行時間を計算
        let runningTimeText = '不明';
        if (currentInfo.running_time) {
            const runningTimeSeconds = currentInfo.running_time;
            const runningHours = Math.floor(runningTimeSeconds / 3600);
            const runningMinutes = Math.floor((runningTimeSeconds % 3600) / 60);
            const runningSeconds = runningTimeSeconds % 60;
            
            runningTimeText = String(runningHours).padStart(2, '0') + ':' + 
                String(runningMinutes).padStart(2, '0') + ':' + 
                String(runningSeconds).padStart(2, '0');
        }
        
        // 状態に応じたバッジの色を設定
        let statusBadgeClass = 'bg-secondary';
        if (currentInfo.status === 'running') {
            statusBadgeClass = 'bg-success';
        } else if (currentInfo.status === 'sleeping') {
            statusBadgeClass = 'bg-info';
        } else if (currentInfo.status === 'stopped') {
            statusBadgeClass = 'bg-warning';
        } else if (process.status === 'terminated') {
            statusBadgeClass = 'bg-danger';
        }
        
        const statusText = currentInfo.status || process.status || '不明';
        
        // 行を追加
        const row = `
            <tr>
                <td>${name}</td>
                <td>${currentInfo.pid || process.pid || '-'}</td>
                <td><span class="badge ${statusBadgeClass}">${statusText}</span></td>
                <td>${currentInfo.cpu_percent ? currentInfo.cpu_percent.toFixed(1) + '%' : '-'}</td>
                <td>${currentInfo.memory_percent ? currentInfo.memory_percent.toFixed(1) + '%' : '-'}</td>
                <td>${runningTimeText}</td>
            </tr>
        `;
        tableBody.append(row);
    }
}

// モデル性能UIを更新
function updateModelPerformanceUI(data) {
    // 現在のモデル情報
    if (data.models && data.models.length > 0) {
        $('#current-model').text(data.models[data.models.length - 1]);
    } else {
        $('#current-model').text('モデルファイルが見つかりません');
    }
    
    // モデル学習履歴
    const historyContainer = $('#model-history');
    historyContainer.empty();
    
    if (data.learning_history && data.learning_history.length > 0) {
        data.learning_history.forEach(log => {
            const logHeader = `<div class="mb-2"><strong>${log.date}</strong> (${log.file})</div>`;
            let logEntries = '<ul class="list-unstyled ms-3 mb-3">';
            
            log.entries.forEach(entry => {
                logEntries += `<li>${entry}</li>`;
            });
            
            logEntries += '</ul>';
            historyContainer.append(logHeader + logEntries);
        });
    } else {
        historyContainer.append('<p>学習履歴が見つかりません</p>');
    }
    
    // モデル性能グラフの更新
    updateModelPerformanceChart(data);
}

// システムログUIを更新
function updateSystemLogsUI(data) {
    const logsContainer = $('#system-logs');
    logsContainer.empty();
    
    if (data.logs && data.logs.length > 0) {
        data.logs.forEach(log => {
            // ログレベルで色を変える
            let logClass = '';
            if (log.includes('ERROR') || log.includes('CRITICAL')) {
                logClass = 'text-danger';
            } else if (log.includes('WARNING')) {
                logClass = 'text-warning';
            } else if (log.includes('INFO')) {
                logClass = 'text-info';
            }
            
            logsContainer.append(`<div class="${logClass}">${log}</div>`);
        });
        
        // 自動スクロールを最下部に
        logsContainer.scrollTop(logsContainer[0].scrollHeight);
    } else {
        logsContainer.append('<div>ログがありません</div>');
    }
}

// グラフを初期化
function initCharts() {
    // モデル性能グラフの初期化
    const ctx = document.getElementById('model-performance-chart').getContext('2d');
    modelPerformanceChart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: [],
            datasets: [
                {
                    label: '精度',
                    data: [],
                    borderColor: 'rgba(75, 192, 192, 1)',
                    backgroundColor: 'rgba(75, 192, 192, 0.2)',
                    tension: 0.1
                },
                {
                    label: '損失',
                    data: [],
                    borderColor: 'rgba(255, 99, 132, 1)',
                    backgroundColor: 'rgba(255, 99, 132, 0.2)',
                    tension: 0.1
                }
            ]
        },
        options: {
            responsive: true,
            plugins: {
                legend: {
                    position: 'top',
                },
                title: {
                    display: true,
                    text: 'モデル性能の推移'
                }
            },
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });
}

// モデル性能グラフを更新
function updateModelPerformanceChart(data) {
    // ダミーデータ（本来はモデル性能データから生成）
    const labels = ['学習1', '学習2', '学習3', '学習4', '学習5'];
    const accuracyData = [0.65, 0.70, 0.73, 0.75, 0.78];
    const lossData = [0.45, 0.40, 0.35, 0.30, 0.25];
    
    // グラフを更新
    modelPerformanceChart.data.labels = labels;
    modelPerformanceChart.data.datasets[0].data = accuracyData;
    modelPerformanceChart.data.datasets[1].data = lossData;
    modelPerformanceChart.update();
}
