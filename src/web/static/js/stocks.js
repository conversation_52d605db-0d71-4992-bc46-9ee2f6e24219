/**
 * 日経225 AI取引システム - 銘柄分析JavaScript
 */

// グローバル変数
let stockChart = null;
let currentStockData = null;

// 銘柄検索ボタンのイベント
$('#stock-search-btn').on('click', function() {
    const ticker = $('#stock-search').val().trim();
    if (ticker) {
        fetchStockData(ticker);
    }
});

// 銘柄検索フォームのEnterキーイベント
$('#stock-search').on('keypress', function(e) {
    if (e.which === 13) {  // Enterキー
        e.preventDefault();
        $('#stock-search-btn').click();
    }
});

// 銘柄データの取得
const fetchStockData = (ticker) => {
    // ローディング表示
    $('#stock-title').text(`${ticker} のデータを読み込み中...`);
    $('#stock-chart').html('<div class="spinner-container"><div class="spinner-border text-primary" role="status"><span class="visually-hidden">Loading...</span></div></div>');
    $('#stock-info').html('<div class="spinner-container"><div class="spinner-border text-primary" role="status"><span class="visually-hidden">Loading...</span></div></div>');
    $('#stock-indicators').html('<div class="spinner-container"><div class="spinner-border text-primary" role="status"><span class="visually-hidden">Loading...</span></div></div>');
    
    // APIリクエスト
    fetch(`/api/stock/${ticker}`)
        .then(response => {
            if (!response.ok) {
                throw new Error('銘柄データの取得に失敗しました');
            }
            return response.json();
        })
        .then(data => {
            currentStockData = data;
            updateStockView(ticker, data);
        })
        .catch(error => {
            console.error('銘柄データ取得エラー:', error);
            $('#stock-title').text(`${ticker} のデータ取得に失敗しました`);
            $('#stock-chart').html('<div class="alert alert-danger">データの取得に失敗しました。銘柄コードを確認してください。</div>');
            $('#stock-info').html('<p>データがありません</p>');
            $('#stock-indicators').html('<p>データがありません</p>');
        });
};

// 銘柄ビューの更新
const updateStockView = (ticker, data) => {
    // タイトルの更新
    $('#stock-title').text(`${ticker} の分析`);
    
    // 価格チャートの更新
    updateStockChart(ticker, data.price_history);
    
    // 銘柄情報の更新
    updateStockInfo(ticker, data);
    
    // テクニカル指標の更新
    updateStockIndicators(data.indicators);
};

// 価格チャートの更新
const updateStockChart = (ticker, priceHistory) => {
    if (!priceHistory || priceHistory.length === 0) {
        $('#stock-chart').html('<div class="alert alert-warning">価格データがありません</div>');
        return;
    }
    
    // データの準備
    const dates = priceHistory.map(p => p.datetime);
    const prices = {
        open: priceHistory.map(p => p.open),
        high: priceHistory.map(p => p.high),
        low: priceHistory.map(p => p.low),
        close: priceHistory.map(p => p.close)
    };
    const volumes = priceHistory.map(p => p.volume);
    
    // チャートの初期化または更新
    if (stockChart) {
        stockChart.destroy();
    }
    
    const ctx = document.getElementById('stock-chart').getContext('2d');
    stockChart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: dates,
            datasets: [
                {
                    label: '終値',
                    data: prices.close,
                    borderColor: 'rgba(54, 162, 235, 1)',
                    backgroundColor: 'rgba(54, 162, 235, 0.1)',
                    borderWidth: 2,
                    fill: true,
                    tension: 0.1,
                    yAxisID: 'y'
                },
                {
                    label: '出来高',
                    data: volumes,
                    borderColor: 'rgba(153, 102, 255, 1)',
                    backgroundColor: 'rgba(153, 102, 255, 0.5)',
                    borderWidth: 1,
                    type: 'bar',
                    yAxisID: 'y1'
                }
            ]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            interaction: {
                mode: 'index',
                intersect: false
            },
            scales: {
                x: {
                    title: {
                        display: true,
                        text: '日時'
                    }
                },
                y: {
                    type: 'linear',
                    display: true,
                    position: 'left',
                    title: {
                        display: true,
                        text: '価格 (円)'
                    }
                },
                y1: {
                    type: 'linear',
                    display: true,
                    position: 'right',
                    title: {
                        display: true,
                        text: '出来高'
                    },
                    grid: {
                        drawOnChartArea: false
                    }
                }
            },
            plugins: {
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            const label = context.dataset.label || '';
                            const value = context.raw;
                            if (context.datasetIndex === 0) {
                                return `${label}: ${formatCurrency(value)}`;
                            } else {
                                return `${label}: ${formatNumber(value)}`;
                            }
                        }
                    }
                }
            }
        }
    });
};

// 銘柄情報の更新
const updateStockInfo = (ticker, data) => {
    if (!data.price_history || data.price_history.length === 0) {
        $('#stock-info').html('<p>価格データがありません</p>');
        return;
    }
    
    // 最新の価格データ
    const latestPrice = data.price_history[data.price_history.length - 1];
    
    // 基本情報
    const infoHtml = `
        <dl>
            <dt>銘柄コード</dt>
            <dd>${ticker}</dd>
            
            <dt>最終更新</dt>
            <dd>${formatDateTime(latestPrice.datetime)}</dd>
            
            <dt>始値</dt>
            <dd>${formatCurrency(latestPrice.open)}</dd>
            
            <dt>高値</dt>
            <dd>${formatCurrency(latestPrice.high)}</dd>
            
            <dt>安値</dt>
            <dd>${formatCurrency(latestPrice.low)}</dd>
            
            <dt>終値</dt>
            <dd>${formatCurrency(latestPrice.close)}</dd>
            
            <dt>出来高</dt>
            <dd>${formatNumber(latestPrice.volume)}</dd>
            
            <dt>変化率</dt>
            <dd class="${latestPrice.close > latestPrice.open ? 'text-success' : 'text-danger'}">
                ${latestPrice.close > latestPrice.open ? '↑' : '↓'} 
                ${formatPercent((latestPrice.close - latestPrice.open) / latestPrice.open * 100)}
            </dd>
        </dl>
    `;
    
    $('#stock-info').html(infoHtml);
};

// テクニカル指標の更新
const updateStockIndicators = (indicators) => {
    if (!indicators || indicators.length === 0) {
        $('#stock-indicators').html('<p>指標データがありません</p>');
        return;
    }
    
    // 最新の指標データ
    const latestIndicator = indicators[indicators.length - 1];
    
    // 指標がない場合
    if (!latestIndicator.indicators || Object.keys(latestIndicator.indicators).length === 0) {
        $('#stock-indicators').html('<p>指標データがありません</p>');
        return;
    }
    
    // テーブルの作成
    let tableHtml = '<table class="table table-sm table-striped">';
    tableHtml += '<thead><tr><th>指標</th><th>値</th></tr></thead><tbody>';
    
    // 指標の表示
    for (const [key, value] of Object.entries(latestIndicator.indicators)) {
        // キーを整形（例: rsi_14 → RSI(14)）
        const formattedKey = key
            .split('_')
            .map((part, index) => {
                if (index === 0) {
                    return part.toUpperCase();
                } else {
                    return `(${part})`;
                }
            })
            .join('');
        
        tableHtml += `<tr><td>${formattedKey}</td><td>${formatNumber(value, 2)}</td></tr>`;
    }
    
    tableHtml += '</tbody></table>';
    
    $('#stock-indicators').html(tableHtml);
};

// 銘柄タブが表示されたときの処理
$('a[href="#stocks"]').on('shown.bs.tab', function (e) {
    // チャートのリサイズ
    if (stockChart) {
        stockChart.resize();
    }
});
