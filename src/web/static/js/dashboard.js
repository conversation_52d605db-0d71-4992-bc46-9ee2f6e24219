/**
 * 日経225 AI取引システム - ダッシュボードJavaScript
 */

// チャートオブジェクト
let portfolioChart = null;
let holdingsChart = null;
let performanceChart = null;

// ダッシュボードの更新
const updateDashboard = (data) => {
    if (!data) return;
    
    // ポートフォリオ概要の更新
    updatePortfolioSummary(data.portfolio);
    
    // 推奨銘柄の更新
    updateTopStocks(data.top_stocks);
    
    // 保有銘柄の更新
    updateHoldings(data.portfolio.holdings);
    
    // パフォーマンスチャートの更新
    updatePerformanceChart(data.performance);
};

// ポートフォリオ概要の更新
const updatePortfolioSummary = (portfolio) => {
    // ポートフォリオ価値
    $('#portfolio-value').text(formatCurrency(portfolio.portfolio_value));
    
    // 現金残高
    $('#cash-balance').text(formatCurrency(portfolio.balance));
    
    // 損益
    $('#profit-loss').text(formatCurrency(portfolio.profit_loss));
    $('#profit-loss').removeClass('positive negative');
    $('#profit-loss').addClass(portfolio.profit_loss >= 0 ? 'positive' : 'negative');
    
    // 損益率
    $('#profit-loss-pct').text(formatPercent(portfolio.profit_loss_pct));
    $('#profit-loss-pct').removeClass('positive negative');
    $('#profit-loss-pct').addClass(portfolio.profit_loss_pct >= 0 ? 'positive' : 'negative');
    
    // ポートフォリオチャートの更新
    updatePortfolioChart(portfolio);
};

// ポートフォリオチャートの更新
const updatePortfolioChart = (portfolio) => {
    // チャートデータの準備
    const labels = ['現金', '株式'];
    const data = [
        portfolio.balance,
        portfolio.portfolio_value - portfolio.balance
    ];
    const backgroundColor = [
        'rgba(54, 162, 235, 0.7)',
        'rgba(255, 99, 132, 0.7)'
    ];
    
    // チャートの初期化または更新
    if (portfolioChart) {
        portfolioChart.data.datasets[0].data = data;
        portfolioChart.update();
    } else {
        const ctx = document.getElementById('portfolio-chart').getContext('2d');
        portfolioChart = new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: labels,
                datasets: [{
                    data: data,
                    backgroundColor: backgroundColor,
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom'
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const value = context.raw;
                                const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                const percentage = total > 0 ? (value / total * 100).toFixed(1) + '%' : '0%';
                                return `${context.label}: ${formatCurrency(value)} (${percentage})`;
                            }
                        }
                    }
                }
            }
        });
    }
};

// 推奨銘柄の更新
const updateTopStocks = (stocks) => {
    const tableBody = $('#top-stocks-table');
    tableBody.empty();
    
    if (!stocks || stocks.length === 0) {
        tableBody.append(`
            <tr>
                <td colspan="5" class="text-center">推奨銘柄がありません</td>
            </tr>
        `);
        return;
    }
    
    stocks.forEach(stock => {
        const changeClass = stock.predicted_change_pct >= 0 ? 'prediction-up' : 'prediction-down';
        const changeIcon = stock.predicted_change_pct >= 0 ? '↑' : '↓';
        
        tableBody.append(`
            <tr>
                <td>${stock.ticker}</td>
                <td>${formatCurrency(stock.current_price)}</td>
                <td>${formatCurrency(stock.predicted_price)}</td>
                <td class="${changeClass}">${changeIcon} ${formatPercent(stock.predicted_change_pct)}</td>
                <td>
                    <button class="btn btn-sm btn-outline-primary action-btn" data-ticker="${stock.ticker}">
                        詳細
                    </button>
                </td>
            </tr>
        `);
    });
    
    // 詳細ボタンのイベント
    $('.action-btn').on('click', function() {
        const ticker = $(this).data('ticker');
        // 銘柄タブに切り替え
        $('a[href="#stocks"]').tab('show');
        // 銘柄を検索
        $('#stock-search').val(ticker);
        $('#stock-search-btn').click();
    });
};

// 保有銘柄の更新
const updateHoldings = (holdings) => {
    const tableBody = $('#holdings-table');
    tableBody.empty();
    
    if (!holdings || holdings.length === 0) {
        tableBody.append(`
            <tr>
                <td colspan="4" class="text-center">保有銘柄がありません</td>
            </tr>
        `);
        
        // 保有銘柄チャートをクリア
        if (holdingsChart) {
            holdingsChart.data.labels = [];
            holdingsChart.data.datasets[0].data = [];
            holdingsChart.update();
        }
        
        return;
    }
    
    // テーブルの更新
    holdings.forEach(holding => {
        tableBody.append(`
            <tr>
                <td>${holding.ticker}</td>
                <td>${formatNumber(holding.quantity)}</td>
                <td>${formatCurrency(holding.price)}</td>
                <td>${formatCurrency(holding.value)}</td>
            </tr>
        `);
    });
    
    // チャートの更新
    updateHoldingsChart(holdings);
};

// 保有銘柄チャートの更新
const updateHoldingsChart = (holdings) => {
    // チャートデータの準備
    const labels = holdings.map(h => h.ticker);
    const data = holdings.map(h => h.value);
    
    // カラーパレットの生成
    const colors = generateColors(holdings.length);
    
    // チャートの初期化または更新
    if (holdingsChart) {
        holdingsChart.data.labels = labels;
        holdingsChart.data.datasets[0].data = data;
        holdingsChart.data.datasets[0].backgroundColor = colors;
        holdingsChart.update();
    } else {
        const ctx = document.getElementById('holdings-chart').getContext('2d');
        holdingsChart = new Chart(ctx, {
            type: 'pie',
            data: {
                labels: labels,
                datasets: [{
                    data: data,
                    backgroundColor: colors,
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom'
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const value = context.raw;
                                const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                const percentage = total > 0 ? (value / total * 100).toFixed(1) + '%' : '0%';
                                return `${context.label}: ${formatCurrency(value)} (${percentage})`;
                            }
                        }
                    }
                }
            }
        });
    }
};

// パフォーマンスチャートの更新
const updatePerformanceChart = (performance) => {
    if (!performance || !performance.cumulative_performance || performance.cumulative_performance.length === 0) {
        return;
    }
    
    // 期間の取得
    const period = $('.btn-group button.active').data('period') || '1w';
    
    // データのフィルタリング
    let filteredData = performance.cumulative_performance;
    const now = new Date();
    
    if (period === '1w') {
        const oneWeekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        filteredData = filteredData.filter(d => new Date(d.Timestamp) >= oneWeekAgo);
    } else if (period === '1m') {
        const oneMonthAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
        filteredData = filteredData.filter(d => new Date(d.Timestamp) >= oneMonthAgo);
    } else if (period === '3m') {
        const threeMonthsAgo = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000);
        filteredData = filteredData.filter(d => new Date(d.Timestamp) >= threeMonthsAgo);
    }
    
    // チャートデータの準備
    const labels = filteredData.map(d => d.Date);
    const data = filteredData.map(d => d.Cumulative_Profit_Loss);
    const percentData = filteredData.map(d => d.Cumulative_Profit_Loss_Pct);
    
    // チャートの初期化または更新
    if (performanceChart) {
        performanceChart.data.labels = labels;
        performanceChart.data.datasets[0].data = data;
        performanceChart.data.datasets[1].data = percentData;
        performanceChart.update();
    } else {
        const ctx = document.getElementById('performance-chart').getContext('2d');
        performanceChart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: labels,
                datasets: [
                    {
                        label: '累積損益',
                        data: data,
                        borderColor: 'rgba(54, 162, 235, 1)',
                        backgroundColor: 'rgba(54, 162, 235, 0.1)',
                        borderWidth: 2,
                        fill: true,
                        yAxisID: 'y'
                    },
                    {
                        label: '累積損益率',
                        data: percentData,
                        borderColor: 'rgba(255, 99, 132, 1)',
                        backgroundColor: 'rgba(255, 99, 132, 0.1)',
                        borderWidth: 2,
                        fill: false,
                        yAxisID: 'y1'
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                interaction: {
                    mode: 'index',
                    intersect: false
                },
                scales: {
                    x: {
                        title: {
                            display: true,
                            text: '日付'
                        }
                    },
                    y: {
                        type: 'linear',
                        display: true,
                        position: 'left',
                        title: {
                            display: true,
                            text: '累積損益 (円)'
                        }
                    },
                    y1: {
                        type: 'linear',
                        display: true,
                        position: 'right',
                        title: {
                            display: true,
                            text: '累積損益率 (%)'
                        },
                        grid: {
                            drawOnChartArea: false
                        }
                    }
                },
                plugins: {
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const label = context.dataset.label || '';
                                const value = context.raw;
                                if (context.datasetIndex === 0) {
                                    return `${label}: ${formatCurrency(value)}`;
                                } else {
                                    return `${label}: ${formatPercent(value)}`;
                                }
                            }
                        }
                    }
                }
            }
        });
    }
    
    // 期間ボタンのイベント
    $('.btn-group button').on('click', function() {
        $('.btn-group button').removeClass('active');
        $(this).addClass('active');
        updatePerformanceChart(performance);
    });
};

// カラーパレットの生成
const generateColors = (count) => {
    const baseColors = [
        'rgba(255, 99, 132, 0.7)',
        'rgba(54, 162, 235, 0.7)',
        'rgba(255, 206, 86, 0.7)',
        'rgba(75, 192, 192, 0.7)',
        'rgba(153, 102, 255, 0.7)',
        'rgba(255, 159, 64, 0.7)',
        'rgba(199, 199, 199, 0.7)',
        'rgba(83, 102, 255, 0.7)',
        'rgba(40, 167, 69, 0.7)',
        'rgba(220, 53, 69, 0.7)'
    ];
    
    // 必要な色の数がベースカラーより多い場合は繰り返し使用
    const colors = [];
    for (let i = 0; i < count; i++) {
        colors.push(baseColors[i % baseColors.length]);
    }
    
    return colors;
};
