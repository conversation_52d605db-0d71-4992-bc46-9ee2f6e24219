/**
 * 日経225 AI取引システム - 取引履歴JavaScript
 */

// 取引履歴テーブルの更新
const updateTradesTable = (trades) => {
    const tableBody = $('#trades-table-body');
    tableBody.empty();
    
    if (!trades || trades.length === 0) {
        tableBody.append(`
            <tr>
                <td colspan="8" class="text-center">取引履歴がありません</td>
            </tr>
        `);
        return;
    }
    
    // 日付の降順でソート
    trades.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));
    
    // テーブルの更新
    trades.forEach(trade => {
        const rowClass = trade.action === 'BUY' ? 'trade-buy' : 'trade-sell';
        const profitClass = trade.profit >= 0 ? 'text-success' : 'text-danger';
        
        tableBody.append(`
            <tr class="${rowClass}">
                <td>${formatDateTime(trade.timestamp)}</td>
                <td>${trade.ticker}</td>
                <td>${trade.action === 'BUY' ? '買' : '売'}</td>
                <td>${formatNumber(trade.quantity)}</td>
                <td>${formatCurrency(trade.price)}</td>
                <td>${formatCurrency(trade.amount)}</td>
                <td>${formatCurrency(trade.commission)}</td>
                <td class="${profitClass}">${formatCurrency(trade.profit)}</td>
            </tr>
        `);
    });
    
    // データテーブルの初期化（既に初期化されている場合は破棄して再初期化）
    if ($.fn.DataTable.isDataTable('#trades-table')) {
        $('#trades-table').DataTable().destroy();
    }
    
    $('#trades-table').DataTable({
        order: [[0, 'desc']],  // 日時の降順でソート
        language: {
            url: 'https://cdn.datatables.net/plug-ins/1.10.25/i18n/Japanese.json'
        },
        pageLength: 10,
        lengthMenu: [[10, 25, 50, -1], [10, 25, 50, "全て"]],
        responsive: true
    });
};

// 取引統計の計算と表示
const calculateTradeStats = (trades) => {
    if (!trades || trades.length === 0) {
        return;
    }
    
    // 統計の計算
    const totalTrades = trades.length;
    const buyTrades = trades.filter(t => t.action === 'BUY').length;
    const sellTrades = trades.filter(t => t.action === 'SELL').length;
    
    const profitTrades = trades.filter(t => t.profit > 0).length;
    const lossTrades = trades.filter(t => t.profit < 0).length;
    const breakEvenTrades = trades.filter(t => t.profit === 0).length;
    
    const winRate = totalTrades > 0 ? (profitTrades / totalTrades * 100) : 0;
    
    const totalProfit = trades.reduce((sum, t) => sum + (t.profit || 0), 0);
    const totalCommission = trades.reduce((sum, t) => sum + (t.commission || 0), 0);
    
    const avgProfit = profitTrades > 0 ? 
        trades.filter(t => t.profit > 0).reduce((sum, t) => sum + t.profit, 0) / profitTrades : 0;
    
    const avgLoss = lossTrades > 0 ? 
        trades.filter(t => t.profit < 0).reduce((sum, t) => sum + t.profit, 0) / lossTrades : 0;
    
    // 統計の表示
    const statsHtml = `
        <div class="card mt-4">
            <div class="card-header bg-info text-white">
                <h5 class="card-title mb-0">取引統計</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <table class="table table-sm">
                            <tr>
                                <th>総取引数</th>
                                <td>${totalTrades}</td>
                            </tr>
                            <tr>
                                <th>買い注文</th>
                                <td>${buyTrades}</td>
                            </tr>
                            <tr>
                                <th>売り注文</th>
                                <td>${sellTrades}</td>
                            </tr>
                            <tr>
                                <th>勝率</th>
                                <td>${formatNumber(winRate, 2)}%</td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <table class="table table-sm">
                            <tr>
                                <th>総利益</th>
                                <td class="${totalProfit >= 0 ? 'text-success' : 'text-danger'}">${formatCurrency(totalProfit)}</td>
                            </tr>
                            <tr>
                                <th>総手数料</th>
                                <td>${formatCurrency(totalCommission)}</td>
                            </tr>
                            <tr>
                                <th>平均利益</th>
                                <td class="text-success">${formatCurrency(avgProfit)}</td>
                            </tr>
                            <tr>
                                <th>平均損失</th>
                                <td class="text-danger">${formatCurrency(avgLoss)}</td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    // 統計を表示
    if ($('#trade-stats').length === 0) {
        $('#trades .card').after('<div id="trade-stats"></div>');
    }
    $('#trade-stats').html(statsHtml);
};

// 取引履歴タブが表示されたときの処理
$('a[href="#trades"]').on('shown.bs.tab', function (e) {
    if (dashboardData && dashboardData.trades) {
        updateTradesTable(dashboardData.trades);
        calculateTradeStats(dashboardData.trades);
    }
});
