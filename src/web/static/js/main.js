/**
 * 日経225 AI取引システム - メインJavaScript
 */

// グローバル変数
let socket;
let dashboardData = null;
let currentTab = 'dashboard';

// 数値のフォーマット
const formatCurrency = (value) => {
    return new Intl.NumberFormat('ja-JP', {
        style: 'currency',
        currency: 'JPY',
        minimumFractionDigits: 0,
        maximumFractionDigits: 0
    }).format(value);
};

const formatNumber = (value, decimals = 0) => {
    return new Intl.NumberFormat('ja-JP', {
        minimumFractionDigits: decimals,
        maximumFractionDigits: decimals
    }).format(value);
};

const formatPercent = (value) => {
    return new Intl.NumberFormat('ja-JP', {
        style: 'percent',
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
    }).format(value / 100);
};

const formatDateTime = (dateTimeStr) => {
    const date = new Date(dateTimeStr);
    return date.toLocaleString('ja-JP', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
    });
};

const formatDate = (dateTimeStr) => {
    const date = new Date(dateTimeStr);
    return date.toLocaleDateString('ja-JP', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit'
    });
};

const formatTime = (dateTimeStr) => {
    const date = new Date(dateTimeStr);
    return date.toLocaleTimeString('ja-JP', {
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
    });
};

// データの取得
const fetchDashboardData = () => {
    // 更新ボタンのアニメーション
    const refreshBtn = $('#refresh-btn');
    refreshBtn.prop('disabled', true);
    refreshBtn.html('<i class="fas fa-spinner fa-spin"></i> 更新中...');
    
    fetch('/api/dashboard')
        .then(response => response.json())
        .then(data => {
            dashboardData = data;
            
            // 現在のタブに応じた更新処理
            if (currentTab === 'dashboard') {
                updateDashboard(data);
            } else if (currentTab === 'trades') {
                updateTradesTable(data.trades);
            }
            
            updateLastUpdateTime(data.last_update);
            
            // 更新ボタンを元に戻す
            refreshBtn.prop('disabled', false);
            refreshBtn.html('<i class="fas fa-sync-alt"></i> 更新');
        })
        .catch(error => {
            console.error('データ取得エラー:', error);
            
            // 更新ボタンを元に戻す
            refreshBtn.prop('disabled', false);
            refreshBtn.html('<i class="fas fa-sync-alt"></i> 更新');
            
            alert('データの取得に失敗しました。');
        });
};

// 最終更新時間の更新
const updateLastUpdateTime = (timestamp) => {
    const date = new Date(timestamp);
    $('#last-update').text(`最終更新: ${formatTime(date)}`);
};

// Socket.IOの初期化
const initializeSocket = () => {
    socket = io();
    
    socket.on('connect', () => {
        console.log('Socket.IO接続成功');
    });
    
    socket.on('disconnect', () => {
        console.log('Socket.IO切断');
    });
    
    socket.on('data_update', (data) => {
        dashboardData = data;
        
        // 現在のタブに応じた更新処理
        if (currentTab === 'dashboard') {
            updateDashboard(data);
        } else if (currentTab === 'trades') {
            updateTradesTable(data.trades);
        }
        
        updateLastUpdateTime(data.last_update);
    });
};

// タブ切り替え
const initializeTabs = () => {
    // Bootstrap 5のタブイベント
    const tabEls = document.querySelectorAll('a[data-bs-toggle="tab"]');
    tabEls.forEach(tabEl => {
        tabEl.addEventListener('shown.bs.tab', event => {
            // タブIDを取得（#を除去）
            currentTab = event.target.getAttribute('href').substring(1);
            console.log('タブ切り替え:', currentTab);
            
            // タブに応じた処理
            if (currentTab === 'dashboard' && dashboardData) {
                updateDashboard(dashboardData);
            } else if (currentTab === 'trades' && dashboardData) {
                updateTradesTable(dashboardData.trades);
            } else if (currentTab === 'stocks') {
                // 銘柄タブの初期化
                if (typeof stockChart !== 'undefined' && stockChart) {
                    stockChart.resize();
                }
            } else if (currentTab === 'monitor') {
                // モニターシステムタブの初期化
                if (typeof refreshSystemStatus === 'function') {
                    refreshSystemStatus();
                }
            } else if (currentTab === 'control') {
                // システム制御タブの初期化
                if (typeof updateSystemStatus === 'function') {
                    updateSystemStatus();
                }
                if (typeof getReports === 'function') {
                    getReports();
                }
            } else if (currentTab === 'settings') {
                if (typeof loadSettings === 'function') {
                    loadSettings();
                }
            }
        });
    });
};

// 更新ボタン
const initializeRefreshButton = () => {
    $('#refresh-btn').on('click', function() {
        fetchDashboardData();
    });
};

// 初期化
$(document).ready(function() {
    console.log('アプリケーション初期化');
    
    // タブの初期化
    initializeTabs();
    
    // 更新ボタンの初期化
    initializeRefreshButton();
    
    // Socket.IOの初期化
    initializeSocket();
    
    // 初期データの取得
    fetchDashboardData();
});
