/**
 * 日経225 AI取引システム - 設定JavaScript
 */

// 設定の読み込み
const loadSettings = () => {
    fetch('/api/settings')
        .then(response => response.json())
        .then(settings => {
            // フォームに設定を反映
            $('#initial-balance').val(settings.initial_balance);
            $('#max-stocks').val(settings.max_stocks);
            $('#commission-rate').val(settings.commission_rate);
            $('#trading-mode').prop('checked', settings.trading_mode);
        })
        .catch(error => {
            console.error('設定の読み込みエラー:', error);
            alert('設定の読み込みに失敗しました。');
        });
};

// 設定の保存
const saveSettings = (settings) => {
    fetch('/api/settings', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(settings)
    })
        .then(response => response.json())
        .then(result => {
            if (result.success) {
                alert('設定を保存しました。');
            } else {
                alert('設定の保存に失敗しました。');
            }
        })
        .catch(error => {
            console.error('設定の保存エラー:', error);
            alert('設定の保存に失敗しました。');
        });
};

// 設定フォームの送信
$('#settings-form').on('submit', function(e) {
    e.preventDefault();
    
    // フォームの値を取得
    const settings = {
        initial_balance: parseFloat($('#initial-balance').val()),
        max_stocks: parseInt($('#max-stocks').val()),
        commission_rate: parseFloat($('#commission-rate').val()),
        trading_mode: $('#trading-mode').is(':checked')
    };
    
    // バリデーション
    if (isNaN(settings.initial_balance) || settings.initial_balance <= 0) {
        alert('初期資金は正の数値を入力してください。');
        return;
    }
    
    if (isNaN(settings.max_stocks) || settings.max_stocks <= 0 || settings.max_stocks > 10) {
        alert('最大銘柄数は1から10の間の整数を入力してください。');
        return;
    }
    
    if (isNaN(settings.commission_rate) || settings.commission_rate < 0 || settings.commission_rate > 0.01) {
        alert('取引手数料率は0から0.01の間の数値を入力してください。');
        return;
    }
    
    // 実取引モードの確認
    if (settings.trading_mode) {
        const confirmed = confirm('実取引モードを有効にしますか？\n\nau kabu APIの設定が必要です。');
        if (!confirmed) {
            return;
        }
    }
    
    // 設定を保存
    saveSettings(settings);
});

// リセットボタン
$('#reset-btn').on('click', function() {
    const confirmed = confirm('設定をリセットしますか？');
    if (confirmed) {
        // デフォルト値に戻す
        $('#initial-balance').val(1000000);
        $('#max-stocks').val(5);
        $('#commission-rate').val(0.0005);
        $('#trading-mode').prop('checked', false);
        
        // 設定を保存
        saveSettings({
            initial_balance: 1000000,
            max_stocks: 5,
            commission_rate: 0.0005,
            trading_mode: false
        });
    }
});

// 実取引モードの切り替え
$('#trading-mode').on('change', function() {
    if ($(this).is(':checked')) {
        // 警告表示
        $('#trading-mode').closest('.mb-3').append(
            '<div class="alert alert-warning mt-2" id="trading-mode-warning">' +
            '実取引モードでは、au kabu APIを使用して実際の取引が行われます。' +
            '<br>APIキーとパスワードが正しく設定されていることを確認してください。' +
            '</div>'
        );
    } else {
        // 警告を削除
        $('#trading-mode-warning').remove();
    }
});

// 設定タブが表示されたときの処理
$('a[href="#settings"]').on('shown.bs.tab', function (e) {
    loadSettings();
});
