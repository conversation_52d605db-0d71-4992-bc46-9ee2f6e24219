/**
 * システム制御機能
 */

// グローバル変数
let systemStatusInterval = null;

// 初期化
$(document).ready(function() {
    // システム状態を更新
    updateSystemStatus();
    
    // 自動更新（10秒ごと）
    systemStatusInterval = setInterval(updateSystemStatus, 10000);
    
    // リアルタイム更新を停止（別のタブに移動時）
    $('a[data-bs-toggle="tab"]').on('shown.bs.tab', function(e) {
        const targetId = $(e.target).attr("href");
        if (targetId !== '#control') {
            clearInterval(systemStatusInterval);
            systemStatusInterval = null;
        } else {
            if (systemStatusInterval === null) {
                updateSystemStatus();
                systemStatusInterval = setInterval(updateSystemStatus, 10000);
            }
        }
    });
    
    // 継続学習開始ボタン
    $('#start-learning-btn').on('click', function() {
        const days = $('#learning-days').val();
        const forceTrain = $('#force-train').prop('checked');
        
        startLearningPipeline(days, forceTrain);
    });
    
    // 継続学習停止ボタン
    $('#stop-learning-btn').on('click', function() {
        stopLearningPipeline();
    });
    
    // 仮想取引開始ボタン
    $('#start-trading-btn').on('click', function() {
        const initialBalance = $('#initial-balance-trading').val();
        const maxStocks = $('#max-stocks-trading').val();
        
        startVirtualTrading(initialBalance, maxStocks);
    });
    
    // 仮想取引停止ボタン
    $('#stop-trading-btn').on('click', function() {
        stopVirtualTrading();
    });
    
    // フィードバック学習開始ボタン
    $('#start-feedback-btn').on('click', function() {
        const days = $('#feedback-days').val();
        const forceTrain = $('#feedback-force-train').prop('checked');
        
        startFeedbackLearning(days, forceTrain);
    });
    
    // フィードバック学習停止ボタン
    $('#stop-feedback-btn').on('click', function() {
        stopFeedbackLearning();
    });
    
    // レポート生成ボタン
    $('#generate-report-btn').on('click', function() {
        const reportType = $('#report-type').val();
        generateReport(reportType);
    });
    
    // レポート一覧を取得
    getReports();
});

// システム状態を更新
function updateSystemStatus() {
    $.ajax({
        url: '/api/system/status',
        method: 'GET',
        success: function(data) {
            updateLearningStatus(data.learning);
            updateProcessStatus(data.processes);
        },
        error: function(xhr, status, error) {
            console.error("システム状態取得エラー:", error);
        }
    });
}

// 継続学習状態を更新
function updateLearningStatus(learningData) {
    const status = learningData.status || 'inactive';
    
    // 学習状態のバッジ更新
    let badgeClass = 'bg-secondary';
    let statusText = '非アクティブ';
    
    if (status === 'running') {
        badgeClass = 'bg-success';
        statusText = '実行中';
    } else if (status === 'error') {
        badgeClass = 'bg-danger';
        statusText = 'エラー';
    } else if (status === 'completed') {
        badgeClass = 'bg-info';
        statusText = '完了';
    }
    
    $('#learning-status')
        .removeClass('bg-secondary bg-success bg-danger bg-info')
        .addClass(badgeClass)
        .text(statusText);
}

// プロセス状態を更新
function updateProcessStatus(processesData) {
    // 仮想取引状態
    const tradingProcessName = 'virtual_trader';
    if (processesData[tradingProcessName]) {
        const status = processesData[tradingProcessName].status || 'inactive';
        
        let badgeClass = 'bg-secondary';
        let statusText = '非アクティブ';
        
        if (status === 'running') {
            badgeClass = 'bg-success';
            statusText = '実行中';
        } else if (status === 'terminated') {
            badgeClass = 'bg-danger';
            statusText = '終了';
        }
        
        $('#trading-status')
            .removeClass('bg-secondary bg-success bg-danger')
            .addClass(badgeClass)
            .text(statusText);
    }
    
    // フィードバック学習状態
    const feedbackProcessName = 'feedback_learner';
    if (processesData[feedbackProcessName]) {
        const status = processesData[feedbackProcessName].status || 'inactive';
        
        let badgeClass = 'bg-secondary';
        let statusText = '非アクティブ';
        
        if (status === 'running') {
            badgeClass = 'bg-success';
            statusText = '実行中';
        } else if (status === 'terminated') {
            badgeClass = 'bg-danger';
            statusText = '終了';
        }
        
        $('#feedback-status')
            .removeClass('bg-secondary bg-success bg-danger')
            .addClass(badgeClass)
            .text(statusText);
    }
}

// 継続学習パイプラインを開始
function startLearningPipeline(days, forceTrain) {
    // ボタンを無効化
    $('#start-learning-btn').prop('disabled', true).html('<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> 処理中...');
    
    $.ajax({
        url: '/api/system/control',
        method: 'POST',
        contentType: 'application/json',
        data: JSON.stringify({
            action: 'start',
            target: 'learning_pipeline',
            parameters: {
                days: parseInt(days) || 7,
                force_train: forceTrain
            }
        }),
        success: function(response) {
            if (response.success) {
                showAlert('success', '継続学習パイプラインを開始しました');
                setTimeout(updateSystemStatus, 1000);
            } else {
                showAlert('danger', '継続学習パイプラインの開始に失敗しました: ' + (response.error || '不明なエラー'));
            }
        },
        error: function(xhr, status, error) {
            showAlert('danger', '継続学習パイプラインの開始に失敗しました: ' + error);
        },
        complete: function() {
            // ボタンを有効化
            $('#start-learning-btn').prop('disabled', false).html('<i class="fas fa-play me-1"></i> 継続学習を開始');
        }
    });
}

// 継続学習パイプラインを停止
function stopLearningPipeline() {
    // ボタンを無効化
    $('#stop-learning-btn').prop('disabled', true).html('<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> 処理中...');
    
    $.ajax({
        url: '/api/system/control',
        method: 'POST',
        contentType: 'application/json',
        data: JSON.stringify({
            action: 'stop',
            target: 'learning_pipeline'
        }),
        success: function(response) {
            if (response.success) {
                showAlert('success', '継続学習パイプラインを停止しました');
                setTimeout(updateSystemStatus, 1000);
            } else {
                showAlert('danger', '継続学習パイプラインの停止に失敗しました: ' + (response.error || '不明なエラー'));
            }
        },
        error: function(xhr, status, error) {
            showAlert('danger', '継続学習パイプラインの停止に失敗しました: ' + error);
        },
        complete: function() {
            // ボタンを有効化
            $('#stop-learning-btn').prop('disabled', false).html('<i class="fas fa-stop me-1"></i> 継続学習を停止');
        }
    });
}

// 仮想取引を開始
function startVirtualTrading(initialBalance, maxStocks) {
    // ボタンを無効化
    $('#start-trading-btn').prop('disabled', true).html('<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> 処理中...');
    
    $.ajax({
        url: '/api/system/control',
        method: 'POST',
        contentType: 'application/json',
        data: JSON.stringify({
            action: 'start',
            target: 'virtual_trader',
            parameters: {
                initial_balance: parseInt(initialBalance) || 1000000,
                max_stocks: parseInt(maxStocks) || 5
            }
        }),
        success: function(response) {
            if (response.success) {
                showAlert('success', '仮想取引を開始しました');
                setTimeout(updateSystemStatus, 1000);
            } else {
                showAlert('danger', '仮想取引の開始に失敗しました: ' + (response.error || '不明なエラー'));
            }
        },
        error: function(xhr, status, error) {
            showAlert('danger', '仮想取引の開始に失敗しました: ' + error);
        },
        complete: function() {
            // ボタンを有効化
            $('#start-trading-btn').prop('disabled', false).html('<i class="fas fa-play me-1"></i> 仮想取引を開始');
        }
    });
}

// 仮想取引を停止
function stopVirtualTrading() {
    // ボタンを無効化
    $('#stop-trading-btn').prop('disabled', true).html('<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> 処理中...');
    
    $.ajax({
        url: '/api/system/control',
        method: 'POST',
        contentType: 'application/json',
        data: JSON.stringify({
            action: 'stop',
            target: 'virtual_trader'
        }),
        success: function(response) {
            if (response.success) {
                showAlert('success', '仮想取引を停止しました');
                setTimeout(updateSystemStatus, 1000);
            } else {
                showAlert('danger', '仮想取引の停止に失敗しました: ' + (response.error || '不明なエラー'));
            }
        },
        error: function(xhr, status, error) {
            showAlert('danger', '仮想取引の停止に失敗しました: ' + error);
        },
        complete: function() {
            // ボタンを有効化
            $('#stop-trading-btn').prop('disabled', false).html('<i class="fas fa-stop me-1"></i> 仮想取引を停止');
        }
    });
}

// フィードバック学習を開始
function startFeedbackLearning(days, forceTrain) {
    // ボタンを無効化
    $('#start-feedback-btn').prop('disabled', true).html('<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> 処理中...');
    
    $.ajax({
        url: '/api/system/control',
        method: 'POST',
        contentType: 'application/json',
        data: JSON.stringify({
            action: 'start',
            target: 'feedback_learner',
            parameters: {
                collect_days: parseInt(days) || 3,
                force_train: forceTrain
            }
        }),
        success: function(response) {
            if (response.success) {
                showAlert('success', 'フィードバック学習を開始しました');
                setTimeout(updateSystemStatus, 1000);
            } else {
                showAlert('danger', 'フィードバック学習の開始に失敗しました: ' + (response.error || '不明なエラー'));
            }
        },
        error: function(xhr, status, error) {
            showAlert('danger', 'フィードバック学習の開始に失敗しました: ' + error);
        },
        complete: function() {
            // ボタンを有効化
            $('#start-feedback-btn').prop('disabled', false).html('<i class="fas fa-play me-1"></i> フィードバック学習を開始');
        }
    });
}

// フィードバック学習を停止
function stopFeedbackLearning() {
    // ボタンを無効化
    $('#stop-feedback-btn').prop('disabled', true).html('<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> 処理中...');
    
    $.ajax({
        url: '/api/system/control',
        method: 'POST',
        contentType: 'application/json',
        data: JSON.stringify({
            action: 'stop',
            target: 'feedback_learner'
        }),
        success: function(response) {
            if (response.success) {
                showAlert('success', 'フィードバック学習を停止しました');
                setTimeout(updateSystemStatus, 1000);
            } else {
                showAlert('danger', 'フィードバック学習の停止に失敗しました: ' + (response.error || '不明なエラー'));
            }
        },
        error: function(xhr, status, error) {
            showAlert('danger', 'フィードバック学習の停止に失敗しました: ' + error);
        },
        complete: function() {
            // ボタンを有効化
            $('#stop-feedback-btn').prop('disabled', false).html('<i class="fas fa-stop me-1"></i> フィードバック学習を停止');
        }
    });
}

// レポートを生成
function generateReport(reportType) {
    // ボタンを無効化
    $('#generate-report-btn').prop('disabled', true).html('<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> 生成中...');
    
    // レポート生成をシミュレート（実際にはバックエンドでレポート生成APIを呼び出す）
    setTimeout(function() {
        // ダミーレスポンス
        const dummyResponse = {
            success: true,
            report: {
                type: reportType,
                filename: 'report_' + new Date().toISOString().slice(0, 10) + '.pdf',
                url: '#'
            }
        };
        
        if (dummyResponse.success) {
            showAlert('success', 'レポートを生成しました: ' + dummyResponse.report.filename);
            // レポート一覧を更新
            getReports();
        } else {
            showAlert('danger', 'レポート生成に失敗しました');
        }
        
        // ボタンを有効化
        $('#generate-report-btn').prop('disabled', false).html('<i class="fas fa-file-alt me-1"></i> レポート生成');
    }, 1500);
}

// レポート一覧を取得
function getReports() {
    $.ajax({
        url: '/api/analysis/reports',
        method: 'GET',
        success: function(data) {
            updateReportsList(data);
        },
        error: function(xhr, status, error) {
            console.error("レポート一覧取得エラー:", error);
        }
    });
}

// レポート一覧を更新
function updateReportsList(data) {
    const reportsListContainer = $('#reports-list');
    reportsListContainer.empty();
    
    // レポートがない場合
    if ((!data.daily_reports || data.daily_reports.length === 0) && 
        (!data.performance_reports || data.performance_reports.length === 0)) {
        reportsListContainer.append('<div class="list-group-item">レポートはありません</div>');
        return;
    }
    
    // 日次レポート
    if (data.daily_reports && data.daily_reports.length > 0) {
        data.daily_reports.forEach(report => {
            const date = new Date(report.Timestamp || report.Date || '');
            const formattedDate = date.toLocaleDateString();
            const item = `
                <a href="#" class="list-group-item list-group-item-action">
                    <div class="d-flex w-100 justify-content-between">
                        <h6 class="mb-1">日次レポート: ${formattedDate}</h6>
                        <small>損益: ${report.Profit_Loss ? report.Profit_Loss.toLocaleString() + '円' : '-'}</small>
                    </div>
                    <small class="text-muted">クリックで表示</small>
                </a>
            `;
            reportsListContainer.append(item);
        });
    }
    
    // パフォーマンスレポート
    if (data.performance_reports && data.performance_reports.length > 0) {
        data.performance_reports.forEach(report => {
            const date = new Date(report.timestamp || report.date || '');
            const formattedDate = date.toLocaleDateString();
            const item = `
                <a href="#" class="list-group-item list-group-item-action">
                    <div class="d-flex w-100 justify-content-between">
                        <h6 class="mb-1">パフォーマンスレポート: ${formattedDate}</h6>
                        <small>${report.model_name || 'モデル情報'}</small>
                    </div>
                    <small class="text-muted">クリックで表示</small>
                </a>
            `;
            reportsListContainer.append(item);
        });
    }
}

// アラートを表示
function showAlert(type, message) {
    // 既存のアラートを削除
    $('.alert-container').remove();
    
    // アラートコンテナを作成
    const alertContainer = $('<div class="alert-container position-fixed top-0 end-0 p-3" style="z-index: 5"></div>');
    
    // アラートを作成
    const alert = $(`
        <div class="alert alert-${type} alert-dismissible fade show" role="alert">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="閉じる"></button>
        </div>
    `);
    
    // アラートをコンテナに追加
    alertContainer.append(alert);
    
    // コンテナをボディに追加
    $('body').append(alertContainer);
    
    // 5秒後に自動的に閉じる
    setTimeout(function() {
        alert.alert('close');
    }, 5000);
}
