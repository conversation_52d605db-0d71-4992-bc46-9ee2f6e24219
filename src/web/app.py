#!/usr/bin/env python3
from flask import Flask, jsonify, request, render_template
from flask_socketio import Socket<PERSON>, emit
import json
import os
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import threading
import time
import sys
import logging

# ロギングの設定
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(os.path.join("logs", f"web_app_{datetime.now().strftime('%Y%m%d')}.log")),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# 親ディレクトリをパスに追加
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 必要なモジュールをインポート
from trading.virtual_trader import VirtualTrader
from trading.trading_strategy import TradingStrategy

# Flaskアプリケーションの初期化
app = Flask(__name__, static_folder='static', template_folder='templates')
app.config['SECRET_KEY'] = 'nikkei225-ai-trading-secret-key'
socketio = SocketIO(app, cors_allowed_origins="*")

# APIエクステンションの登録
try:
    from api_extensions import api_extensions
    app.register_blueprint(api_extensions)
    logger.info("APIエクステンションが正常に登録されました")
except ImportError as e:
    logger.warning(f"APIエクステンションの登録に失敗しました: {e}")

# グローバル変数
virtual_trader = None
strategy = None
last_update_time = None
background_thread = None
thread_lock = threading.Lock()

def initialize_components():
    """コンポーネントの初期化"""
    global virtual_trader, strategy
    
    # 仮想トレーダーの初期化
    virtual_trader = VirtualTrader(
        initial_balance=1000000,
        commission_rate=0.0005,
        results_dir="results"
    )
    
    # 取引戦略の初期化
    strategy = TradingStrategy(max_stocks=5)
    
    logger.info("コンポーネントを初期化しました")

def get_latest_portfolio():
    """最新のポートフォリオ情報を取得"""
    global virtual_trader
    
    if virtual_trader is None:
        initialize_components()
    
    # ポートフォリオ概要を取得
    portfolio_summary = virtual_trader.get_portfolio_summary()
    
    # 保有銘柄の詳細を取得
    holdings = []
    for ticker, position in portfolio_summary.get('portfolio', {}).items():
        holdings.append({
            'ticker': ticker,
            'quantity': position.get('qty', 0),
            'price': position.get('price', 0),
            'value': position.get('qty', 0) * position.get('price', 0),
            'timestamp': position.get('timestamp', datetime.now().isoformat())
        })
    
    return {
        'balance': portfolio_summary.get('balance', 0),
        'portfolio_value': portfolio_summary.get('portfolio_value', 0),
        'profit_loss': portfolio_summary.get('profit_loss', 0),
        'profit_loss_pct': portfolio_summary.get('profit_loss_pct', 0),
        'holdings': holdings,
        'timestamp': datetime.now().isoformat()
    }

def get_trade_history():
    """取引履歴を取得"""
    global virtual_trader
    
    if virtual_trader is None:
        initialize_components()
    
    # 取引履歴を取得
    trades = []
    for trade in virtual_trader.trade_history:
        trades.append({
            'ticker': trade.get('Ticker', ''),
            'action': trade.get('Action', ''),
            'quantity': trade.get('Qty', 0),
            'price': trade.get('Price', 0),
            'amount': trade.get('Amount', 0),
            'commission': trade.get('Commission', 0),
            'profit': trade.get('Profit', 0),
            'timestamp': trade.get('Timestamp', datetime.now().isoformat())
        })
    
    return trades

def get_performance_data():
    """パフォーマンスデータを取得"""
    # resultsディレクトリからパフォーマンスデータを読み込む
    performance_data = {
        'daily_performance': [],
        'cumulative_performance': []
    }
    
    # パフォーマンスファイルを検索
    performance_files = []
    results_dir = "results"
    if os.path.exists(results_dir):
        for file in os.listdir(results_dir):
            if file.startswith("performance_") and file.endswith(".json"):
                performance_files.append(os.path.join(results_dir, file))
    
    # パフォーマンスデータを読み込む
    for file in sorted(performance_files):
        try:
            with open(file, 'r') as f:
                data = json.load(f)
                performance_data['daily_performance'].append(data)
        except Exception as e:
            logger.error(f"パフォーマンスデータの読み込みエラー: {str(e)}")
    
    # 累積パフォーマンスを計算
    if performance_data['daily_performance']:
        initial_balance = performance_data['daily_performance'][0].get('Initial_Balance', 1000000)
        cumulative_profit = 0
        
        for i, day in enumerate(performance_data['daily_performance']):
            cumulative_profit += day.get('Profit_Loss', 0)
            cumulative_profit_pct = (cumulative_profit / initial_balance) * 100
            
            performance_data['cumulative_performance'].append({
                'Date': day.get('Date', ''),
                'Cumulative_Profit_Loss': cumulative_profit,
                'Cumulative_Profit_Loss_Pct': cumulative_profit_pct,
                'Timestamp': day.get('Timestamp', '')
            })
    
    return performance_data

def get_top_stocks():
    """最新の推奨銘柄を取得"""
    # resultsディレクトリから最新の推奨銘柄を読み込む
    top_stocks = []
    
    # 推奨銘柄ファイルを検索
    stock_files = []
    results_dir = "results"
    if os.path.exists(results_dir):
        for file in os.listdir(results_dir):
            if file.startswith("top_stocks_") and file.endswith(".csv"):
                stock_files.append(os.path.join(results_dir, file))
    
    # 最新の推奨銘柄を読み込む
    if stock_files:
        latest_file = sorted(stock_files)[-1]
        try:
            df = pd.read_csv(latest_file)
            for _, row in df.iterrows():
                top_stocks.append({
                    'ticker': row.get('Ticker', ''),
                    'current_price': row.get('Current_Price', 0),
                    'predicted_price': row.get('Predicted_Price', 0),
                    'predicted_change_pct': row.get('Predicted_Change_Pct', 0)
                })
        except Exception as e:
            logger.error(f"推奨銘柄の読み込みエラー: {str(e)}")
    
    return top_stocks

def get_stock_data(ticker):
    """銘柄データを取得"""
    # dataディレクトリから銘柄データを読み込む
    stock_data = {
        'price_history': [],
        'indicators': []
    }
    
    # 価格データを読み込む
    data_file = os.path.join("data", "nikkei225_cleaned.csv")
    if os.path.exists(data_file):
        try:
            df = pd.read_csv(data_file)
            ticker_data = df[df['Ticker'] == ticker]
            
            for _, row in ticker_data.iterrows():
                stock_data['price_history'].append({
                    'datetime': row.get('Datetime', ''),
                    'open': row.get('Open', 0),
                    'high': row.get('High', 0),
                    'low': row.get('Low', 0),
                    'close': row.get('Close', 0),
                    'volume': row.get('Volume', 0)
                })
        except Exception as e:
            logger.error(f"銘柄データの読み込みエラー: {str(e)}")
    
    # 特徴量データを読み込む
    feature_file = os.path.join("data", "nikkei225_with_features.csv")
    if os.path.exists(feature_file):
        try:
            df = pd.read_csv(feature_file)
            ticker_data = df[df['Ticker'] == ticker]
            
            # 特徴量カラムを抽出
            feature_columns = [col for col in df.columns if col not in ['Datetime', 'Ticker', 'Open', 'High', 'Low', 'Close', 'Volume']]
            
            for _, row in ticker_data.iterrows():
                indicators = {}
                for col in feature_columns:
                    indicators[col] = row.get(col, 0)
                
                stock_data['indicators'].append({
                    'datetime': row.get('Datetime', ''),
                    'indicators': indicators
                })
        except Exception as e:
            logger.error(f"特徴量データの読み込みエラー: {str(e)}")
    
    return stock_data

def get_current_settings():
    """現在の設定を取得"""
    settings = {
        'initial_balance': 1000000,
        'max_stocks': 5,
        'commission_rate': 0.0005,
        'trading_mode': False
    }
    
    # 設定ファイルがあれば読み込む
    settings_file = os.path.join("src", "web", "settings.json")
    if os.path.exists(settings_file):
        try:
            with open(settings_file, 'r') as f:
                settings.update(json.load(f))
        except Exception as e:
            logger.error(f"設定ファイルの読み込みエラー: {str(e)}")
    
    return settings

def update_settings(settings):
    """設定を更新"""
    # 設定ファイルに保存
    settings_file = os.path.join("src", "web", "settings.json")
    try:
        with open(settings_file, 'w') as f:
            json.dump(settings, f, indent=2)
        logger.info("設定を更新しました")
        return True
    except Exception as e:
        logger.error(f"設定ファイルの保存エラー: {str(e)}")
        return False

def background_task():
    """バックグラウンドタスク"""
    global last_update_time
    
    while True:
        # 最新データを取得
        portfolio = get_latest_portfolio()
        trades = get_trade_history()
        top_stocks = get_top_stocks()
        performance = get_performance_data()
        
        # 更新時間を記録
        last_update_time = datetime.now().isoformat()
        
        # クライアントに送信
        socketio.emit('data_update', {
            'portfolio': portfolio,
            'trades': trades,
            'top_stocks': top_stocks,
            'performance': performance,
            'last_update': last_update_time
        })
        
        # 5秒待機
        socketio.sleep(5)

# ルート
@app.route('/')
def index():
    """メインページ"""
    return render_template('index.html')

# API: ダッシュボードデータ
@app.route('/api/dashboard')
def api_dashboard():
    """ダッシュボードデータAPI"""
    portfolio = get_latest_portfolio()
    trades = get_trade_history()
    top_stocks = get_top_stocks()
    performance = get_performance_data()
    
    return jsonify({
        'portfolio': portfolio,
        'trades': trades,
        'top_stocks': top_stocks,
        'performance': performance,
        'last_update': datetime.now().isoformat()
    })

# API: 取引履歴
@app.route('/api/trades')
def api_trades():
    """取引履歴API"""
    trades = get_trade_history()
    return jsonify(trades)

# API: 銘柄データ
@app.route('/api/stock/<ticker>')
def api_stock(ticker):
    """銘柄データAPI"""
    stock_data = get_stock_data(ticker)
    return jsonify(stock_data)

# API: 設定
@app.route('/api/settings', methods=['GET', 'POST'])
def api_settings():
    """設定API"""
    if request.method == 'GET':
        settings = get_current_settings()
        return jsonify(settings)
    else:
        data = request.json
        success = update_settings(data)
        return jsonify({'success': success})

# WebSocket: 接続
@socketio.on('connect')
def ws_connect():
    """WebSocket接続"""
    global background_thread
    
    logger.info('クライアント接続')
    
    with thread_lock:
        if background_thread is None:
            background_thread = socketio.start_background_task(background_task)

# WebSocket: 切断
@socketio.on('disconnect')
def ws_disconnect():
    """WebSocket切断"""
    logger.info('クライアント切断')

# メイン
if __name__ == '__main__':
    # コンポーネントの初期化
    initialize_components()
    
    # アプリケーションの起動
    socketio.run(app, host='0.0.0.0', port=5000, debug=True)
