#!/usr/bin/env python3
import os
import sys
import argparse
import logging
from app import app, socketio

# ロギングの設定
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(os.path.join("logs", f"web_app_{os.path.basename(__file__).split('.')[0]}.log")),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def main():
    """メイン関数"""
    # コマンドライン引数の解析
    parser = argparse.ArgumentParser(description='日経225 AI取引システム ウェブアプリケーション')
    parser.add_argument('--host', type=str, default='0.0.0.0', help='ホスト（デフォルト: 0.0.0.0）')
    parser.add_argument('--port', type=int, default=5000, help='ポート（デフォルト: 5000）')
    parser.add_argument('--debug', action='store_true', help='デバッグモード')
    args = parser.parse_args()
    
    # 必要なディレクトリの作成
    os.makedirs("logs", exist_ok=True)
    os.makedirs("data", exist_ok=True)
    os.makedirs("data/cache", exist_ok=True)
    os.makedirs("results", exist_ok=True)
    
    # アプリケーションの起動
    logger.info(f"ウェブアプリケーションを起動します（ホスト: {args.host}, ポート: {args.port}）")
    socketio.run(app, host=args.host, port=args.port, debug=args.debug)

if __name__ == "__main__":
    main()
