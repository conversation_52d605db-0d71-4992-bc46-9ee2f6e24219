#!/usr/bin/env python3
"""
AIトレーディングシステム - APIエクステンション
システムモニタリングと継続学習制御のための拡張API
"""

import os
import sys
import json
import subprocess
import psutil
import logging
from datetime import datetime
from flask import jsonify, request, Blueprint

# 親ディレクトリをパスに追加
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 必要なモジュールをインポート
try:
    from continuous_learning_pipeline import ContinuousLearningPipeline
except ImportError:
    ContinuousLearningPipeline = None

try:
    from trading.feedback_learner import FeedbackLearner
except ImportError:
    FeedbackLearner = None

# ロギング設定
logger = logging.getLogger(__name__)

# Blueprint作成
api_extensions = Blueprint('api_extensions', __name__)

# グローバル変数
continuous_learning_pipeline = None
feedback_learner = None
running_processes = {}

# ユーティリティ関数
def get_system_stats():
    """システムリソース統計を取得"""
    stats = {
        "cpu_percent": psutil.cpu_percent(interval=0.1),
        "memory_percent": psutil.virtual_memory().percent,
        "disk_percent": psutil.disk_usage('/').percent,
        "system_uptime": int(datetime.now().timestamp() - psutil.boot_time()),
        "process_count": len(psutil.pids()),
        "timestamp": datetime.now().isoformat()
    }
    return stats

def get_process_info(pid):
    """プロセス情報を取得"""
    try:
        process = psutil.Process(pid)
        return {
            "pid": pid,
            "name": process.name(),
            "status": process.status(),
            "cpu_percent": process.cpu_percent(interval=0.1),
            "memory_percent": process.memory_percent(),
            "create_time": datetime.fromtimestamp(process.create_time()).isoformat(),
            "running_time": int(datetime.now().timestamp() - process.create_time())
        }
    except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
        return None

def run_background_process(command, name=None):
    """バックグラウンドプロセスを実行"""
    try:
        process = subprocess.Popen(
            command,
            shell=True,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        
        process_name = name or f"process_{process.pid}"
        running_processes[process_name] = {
            "pid": process.pid,
            "command": command,
            "start_time": datetime.now().isoformat(),
            "status": "running"
        }
        
        logger.info(f"プロセス開始 [{process_name}]: PID={process.pid}, コマンド: {command}")
        return process_name
    except Exception as e:
        logger.error(f"プロセス起動エラー: {str(e)}")
        return None

# API エンドポイント
@api_extensions.route('/api/system/status')
def api_system_status():
    """システム状態API"""
    try:
        # システム統計を取得
        system_stats = get_system_stats()
        
        # 実行中のプロセス情報を更新
        processes_info = {}
        for name, info in running_processes.items():
            pid = info.get("pid")
            if pid:
                process_info = get_process_info(pid)
                if process_info:
                    processes_info[name] = {**info, "current_info": process_info}
                else:
                    # プロセスが終了している場合
                    info["status"] = "terminated"
                    processes_info[name] = info
        
        # 継続学習パイプラインのステータスを取得
        learning_status = {"status": "inactive"}
        if continuous_learning_pipeline:
            learning_status = continuous_learning_pipeline.get_status()
        
        return jsonify({
            "system": system_stats,
            "processes": processes_info,
            "learning": learning_status
        })
    except Exception as e:
        logger.error(f"システム状態取得エラー: {str(e)}")
        return jsonify({"error": str(e)}), 500

@api_extensions.route('/api/system/control', methods=['POST'])
def api_system_control():
    """システム制御API"""
    data = request.json
    action = data.get('action')
    target = data.get('target')
    parameters = data.get('parameters', {})
    
    if not action or not target:
        return jsonify({"error": "アクションとターゲットが必要です"}), 400
    
    result = {"success": False}
    
    try:
        # 継続学習パイプラインの制御
        if target == 'learning_pipeline':
            if action == 'start':
                # 継続学習パイプラインを開始
                command = f"python -m src.continuous_learning_pipeline"
                if parameters.get('force_train'):
                    command += " --force-train"
                if parameters.get('days'):
                    command += f" --days {parameters.get('days')}"
                
                process_name = run_background_process(command, "learning_pipeline")
                result = {"success": True, "process": process_name}
            
            elif action == 'stop':
                # 継続学習パイプラインを停止
                if "learning_pipeline" in running_processes:
                    pid = running_processes["learning_pipeline"].get("pid")
                    if pid:
                        try:
                            process = psutil.Process(pid)
                            process.terminate()
                            result = {"success": True}
                        except psutil.NoSuchProcess:
                            result = {"success": True, "info": "プロセスは既に終了しています"}
                else:
                    result = {"success": False, "error": "実行中のプロセスが見つかりません"}
        
        # 仮想取引の制御
        elif target == 'virtual_trader':
            if action == 'start':
                # 仮想取引を開始
                command = f"python src/simple_virtual_trader.py"
                if parameters.get('initial_balance'):
                    command += f" --initial-balance {parameters.get('initial_balance')}"
                if parameters.get('max_stocks'):
                    command += f" --max-stocks {parameters.get('max_stocks')}"
                
                process_name = run_background_process(command, "virtual_trader")
                result = {"success": True, "process": process_name}
            
            elif action == 'stop':
                # 仮想取引を停止
                if "virtual_trader" in running_processes:
                    pid = running_processes["virtual_trader"].get("pid")
                    if pid:
                        try:
                            process = psutil.Process(pid)
                            process.terminate()
                            result = {"success": True}
                        except psutil.NoSuchProcess:
                            result = {"success": True, "info": "プロセスは既に終了しています"}
                else:
                    result = {"success": False, "error": "実行中のプロセスが見つかりません"}
        
        # フィードバック学習の制御
        elif target == 'feedback_learner':
            if action == 'start':
                # フィードバック学習を開始
                command = f"python -m src.trading.feedback_learner"
                if parameters.get('force_train'):
                    command += " --force-train"
                if parameters.get('collect_days'):
                    command += f" --collect-days {parameters.get('collect_days')}"
                
                process_name = run_background_process(command, "feedback_learner")
                result = {"success": True, "process": process_name}
            
            elif action == 'stop':
                # フィードバック学習を停止
                if "feedback_learner" in running_processes:
                    pid = running_processes["feedback_learner"].get("pid")
                    if pid:
                        try:
                            process = psutil.Process(pid)
                            process.terminate()
                            result = {"success": True}
                        except psutil.NoSuchProcess:
                            result = {"success": True, "info": "プロセスは既に終了しています"}
                else:
                    result = {"success": False, "error": "実行中のプロセスが見つかりません"}
        
        else:
            result = {"success": False, "error": "無効なターゲット"}
    
    except Exception as e:
        logger.error(f"システム制御エラー: {str(e)}")
        return jsonify({"error": str(e)}), 500
    
    return jsonify(result)

@api_extensions.route('/api/analysis/model-performance')
def api_model_performance():
    """モデル性能分析API"""
    try:
        # モデル性能データを収集
        models_dir = "models"
        performance_data = {
            "models": [],
            "learning_history": [],
            "current_model_stats": {}
        }
        
        # モデルファイル一覧
        if os.path.exists(models_dir):
            model_files = [f for f in os.listdir(models_dir) if f.endswith('.h5')]
            performance_data["models"] = model_files
            
            # フィードバックデータファイル
            feedback_db = os.path.join(models_dir, "feedback_db.pkl")
            if os.path.exists(feedback_db):
                performance_data["feedback_db"] = True
        
        # モデル学習履歴
        learning_logs_dir = "logs"
        if os.path.exists(learning_logs_dir):
            learning_logs = [f for f in os.listdir(learning_logs_dir) if "continuous_learning" in f]
            
            for log_file in sorted(learning_logs)[-5:]:  # 最新の5つのログのみ
                log_path = os.path.join(learning_logs_dir, log_file)
                try:
                    with open(log_path, 'r') as f:
                        lines = f.readlines()
                        log_data = {
                            "file": log_file,
                            "date": log_file.split('_')[2].split('.')[0],
                            "entries": []
                        }
                        
                        for line in lines:
                            if "accuracy" in line.lower() or "loss" in line.lower() or "performance" in line.lower():
                                log_data["entries"].append(line.strip())
                        
                        performance_data["learning_history"].append(log_data)
                except Exception as e:
                    logger.error(f"ログファイル読み込みエラー: {str(e)}")
        
        # 現在のモデル統計
        if FeedbackLearner:
            try:
                learner = FeedbackLearner()
                model_stats = learner.get_model_stats()
                performance_data["current_model_stats"] = model_stats
            except Exception as e:
                logger.error(f"モデル統計取得エラー: {str(e)}")
        
        return jsonify(performance_data)
    except Exception as e:
        logger.error(f"モデル性能データ取得エラー: {str(e)}")
        return jsonify({"error": str(e)}), 500

@api_extensions.route('/api/analysis/reports')
def api_analysis_reports():
    """分析レポートAPI"""
    try:
        reports_dir = "reports"
        reports_data = {
            "daily_reports": [],
            "performance_reports": []
        }
        
        if os.path.exists(reports_dir):
            # 日次レポート
            daily_files = [f for f in os.listdir(reports_dir) if f.startswith("daily_stats_")]
            for file in sorted(daily_files)[-10:]:  # 最新の10件のみ
                file_path = os.path.join(reports_dir, file)
                try:
                    with open(file_path, 'r') as f:
                        reports_data["daily_reports"].append(json.load(f))
                except Exception as e:
                    logger.error(f"日次レポート読み込みエラー: {str(e)}")
            
            # パフォーマンスレポート
            perf_files = [f for f in os.listdir(reports_dir) if f.startswith("learning_pipeline_report_")]
            for file in sorted(perf_files)[-5:]:  # 最新の5件のみ
                file_path = os.path.join(reports_dir, file)
                try:
                    with open(file_path, 'r') as f:
                        reports_data["performance_reports"].append(json.load(f))
                except Exception as e:
                    logger.error(f"パフォーマンスレポート読み込みエラー: {str(e)}")
        
        return jsonify(reports_data)
    except Exception as e:
        logger.error(f"レポートデータ取得エラー: {str(e)}")
        return jsonify({"error": str(e)}), 500

@api_extensions.route('/api/system/logs')
def api_system_logs():
    """システムログAPI"""
    try:
        logs_dir = "logs"
        log_type = request.args.get('type', 'all')
        limit = int(request.args.get('limit', 100))
        
        logs_data = {
            "logs": []
        }
        
        if os.path.exists(logs_dir):
            # ログファイル一覧
            log_files = []
            
            if log_type == 'all':
                log_files = os.listdir(logs_dir)
            elif log_type == 'continuous_learning':
                log_files = [f for f in os.listdir(logs_dir) if "continuous_learning" in f]
            elif log_type == 'web_app':
                log_files = [f for f in os.listdir(logs_dir) if "web_app" in f]
            elif log_type == 'collector':
                log_files = [f for f in os.listdir(logs_dir) if "collector" in f]
            
            # 最新のログファイルを取得
            if log_files:
                latest_log = sorted(log_files)[-1]
                log_path = os.path.join(logs_dir, latest_log)
                
                try:
                    with open(log_path, 'r') as f:
                        lines = f.readlines()
                        logs_data["file"] = latest_log
                        logs_data["logs"] = lines[-limit:]  # 最新のlimit行
                except Exception as e:
                    logger.error(f"ログファイル読み込みエラー: {str(e)}")
        
        return jsonify(logs_data)
    except Exception as e:
        logger.error(f"ログデータ取得エラー: {str(e)}")
        return jsonify({"error": str(e)}), 500
