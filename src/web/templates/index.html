<!DOCTYPE html>
<html lang="ja">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>日経225 AI取引システム</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Chart.js -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/chart.js@3.7.1/dist/chart.min.css">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
</head>
<body>
    <!-- ナビゲーションバー -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="#">
                <i class="fas fa-chart-line me-2"></i>日経225 AI取引システム
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav">
                    <li class="nav-item">
                        <a class="nav-link active" href="#dashboard" data-bs-toggle="tab">ダッシュボード</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#trades" data-bs-toggle="tab">取引履歴</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#stocks" data-bs-toggle="tab">銘柄分析</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#monitor" data-bs-toggle="tab">システム監視</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#control" data-bs-toggle="tab">システム制御</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#settings" data-bs-toggle="tab">設定</a>
                    </li>
                </ul>
                <div class="ms-auto d-flex align-items-center">
                    <span class="text-light me-3" id="last-update">最終更新: --:--:--</span>
                    <button class="btn btn-outline-light btn-sm" id="refresh-btn">
                        <i class="fas fa-sync-alt"></i> 更新
                    </button>
                </div>
            </div>
        </div>
    </nav>

    <!-- メインコンテンツ -->
    <div class="container-fluid mt-3">
        <div class="tab-content">
            <!-- ダッシュボード -->
            <div class="tab-pane fade show active" id="dashboard">
                <div class="row">
                    <!-- ポートフォリオ概要 -->
                    <div class="col-md-6 mb-4">
                        <div class="card">
                            <div class="card-header bg-primary text-white">
                                <h5 class="card-title mb-0">ポートフォリオ概要</h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <h6>ポートフォリオ価値</h6>
                                            <h3 id="portfolio-value">¥0</h3>
                                        </div>
                                        <div class="mb-3">
                                            <h6>現金残高</h6>
                                            <h4 id="cash-balance">¥0</h4>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <h6>損益</h6>
                                            <h3 id="profit-loss">¥0</h3>
                                            <span id="profit-loss-pct">0.00%</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="mt-3">
                                    <canvas id="portfolio-chart" height="200"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 推奨銘柄 -->
                    <div class="col-md-6 mb-4">
                        <div class="card">
                            <div class="card-header bg-success text-white">
                                <h5 class="card-title mb-0">推奨銘柄 (TOP5)</h5>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-striped table-hover">
                                        <thead>
                                            <tr>
                                                <th>銘柄</th>
                                                <th>現在価格</th>
                                                <th>予測価格</th>
                                                <th>予測変化率</th>
                                                <th>アクション</th>
                                            </tr>
                                        </thead>
                                        <tbody id="top-stocks-table">
                                            <!-- JavaScriptで動的に生成 -->
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <!-- パフォーマンスチャート -->
                    <div class="col-md-8 mb-4">
                        <div class="card">
                            <div class="card-header bg-info text-white">
                                <h5 class="card-title mb-0">パフォーマンス推移</h5>
                            </div>
                            <div class="card-body">
                                <div class="btn-group mb-3" role="group">
                                    <button type="button" class="btn btn-outline-primary active" data-period="1w">1週間</button>
                                    <button type="button" class="btn btn-outline-primary" data-period="1m">1ヶ月</button>
                                    <button type="button" class="btn btn-outline-primary" data-period="3m">3ヶ月</button>
                                    <button type="button" class="btn btn-outline-primary" data-period="all">全期間</button>
                                </div>
                                <canvas id="performance-chart" height="300"></canvas>
                            </div>
                        </div>
                    </div>

                    <!-- 保有銘柄 -->
                    <div class="col-md-4 mb-4">
                        <div class="card">
                            <div class="card-header bg-warning text-dark">
                                <h5 class="card-title mb-0">保有銘柄</h5>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-striped table-hover">
                                        <thead>
                                            <tr>
                                                <th>銘柄</th>
                                                <th>数量</th>
                                                <th>価格</th>
                                                <th>価値</th>
                                            </tr>
                                        </thead>
                                        <tbody id="holdings-table">
                                            <!-- JavaScriptで動的に生成 -->
                                        </tbody>
                                    </table>
                                </div>
                                <div class="mt-3">
                                    <canvas id="holdings-chart" height="200"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 取引履歴 -->
            <div class="tab-pane fade" id="trades">
                <div class="card">
                    <div class="card-header bg-secondary text-white">
                        <h5 class="card-title mb-0">取引履歴</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped table-hover" id="trades-table">
                                <thead>
                                    <tr>
                                        <th>日時</th>
                                        <th>銘柄</th>
                                        <th>アクション</th>
                                        <th>数量</th>
                                        <th>価格</th>
                                        <th>金額</th>
                                        <th>手数料</th>
                                        <th>損益</th>
                                    </tr>
                                </thead>
                                <tbody id="trades-table-body">
                                    <!-- JavaScriptで動的に生成 -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 銘柄分析 -->
            <div class="tab-pane fade" id="stocks">
                <div class="card">
                    <div class="card-header bg-dark text-white">
                        <div class="d-flex justify-content-between align-items-center">
                            <h5 class="card-title mb-0">銘柄分析</h5>
                            <div class="input-group" style="width: 300px;">
                                <input type="text" class="form-control" id="stock-search" placeholder="銘柄コードを入力...">
                                <button class="btn btn-outline-light" id="stock-search-btn">検索</button>
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-8">
                                <h4 id="stock-title">銘柄を選択してください</h4>
                                <canvas id="stock-chart" height="300"></canvas>
                            </div>
                            <div class="col-md-4">
                                <div class="card">
                                    <div class="card-header bg-light">
                                        <h6 class="card-title mb-0">銘柄情報</h6>
                                    </div>
                                    <div class="card-body">
                                        <div id="stock-info">
                                            <!-- JavaScriptで動的に生成 -->
                                            <p>銘柄を選択すると情報が表示されます</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="card mt-3">
                                    <div class="card-header bg-light">
                                        <h6 class="card-title mb-0">テクニカル指標</h6>
                                    </div>
                                    <div class="card-body">
                                        <div id="stock-indicators">
                                            <!-- JavaScriptで動的に生成 -->
                                            <p>銘柄を選択すると指標が表示されます</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- システム監視 -->
            <div class="tab-pane fade" id="monitor">
                <div class="row">
                    <!-- システムリソース -->
                    <div class="col-md-6 mb-4">
                        <div class="card">
                            <div class="card-header bg-primary text-white">
                                <h5 class="card-title mb-0">システムリソース</h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <h6>CPU使用率</h6>
                                            <div class="progress">
                                                <div id="cpu-progress" class="progress-bar" role="progressbar" style="width: 0%;" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100">0%</div>
                                            </div>
                                        </div>
                                        <div class="mb-3">
                                            <h6>メモリ使用率</h6>
                                            <div class="progress">
                                                <div id="memory-progress" class="progress-bar bg-success" role="progressbar" style="width: 0%;" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100">0%</div>
                                            </div>
                                        </div>
                                        <div class="mb-3">
                                            <h6>ディスク使用率</h6>
                                            <div class="progress">
                                                <div id="disk-progress" class="progress-bar bg-info" role="progressbar" style="width: 0%;" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100">0%</div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <h6>システム稼働時間</h6>
                                            <h4 id="system-uptime">0日 00:00:00</h4>
                                        </div>
                                        <div class="mb-3">
                                            <h6>プロセス数</h6>
                                            <h4 id="process-count">0</h4>
                                        </div>
                                        <div class="mb-3">
                                            <h6>最終更新時間</h6>
                                            <p id="system-last-update">--:--:--</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 実行中プロセス -->
                    <div class="col-md-6 mb-4">
                        <div class="card">
                            <div class="card-header bg-success text-white">
                                <h5 class="card-title mb-0">実行中プロセス</h5>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-striped table-hover">
                                        <thead>
                                            <tr>
                                                <th>プロセス名</th>
                                                <th>PID</th>
                                                <th>状態</th>
                                                <th>CPU%</th>
                                                <th>メモリ%</th>
                                                <th>実行時間</th>
                                            </tr>
                                        </thead>
                                        <tbody id="processes-table">
                                            <!-- JavaScriptで動的に生成 -->
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <!-- モデル性能 -->
                    <div class="col-md-6 mb-4">
                        <div class="card">
                            <div class="card-header bg-info text-white">
                                <h5 class="card-title mb-0">モデル性能</h5>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <h6>現在のモデル</h6>
                                    <p id="current-model">情報がありません</p>
                                </div>
                                <div class="mb-3">
                                    <h6>モデル学習履歴</h6>
                                    <div id="model-history" class="small" style="max-height: 200px; overflow-y: auto;">
                                        <!-- JavaScriptで動的に生成 -->
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <canvas id="model-performance-chart" height="200"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- システムログ -->
                    <div class="col-md-6 mb-4">
                        <div class="card">
                            <div class="card-header bg-warning text-dark">
                                <div class="d-flex justify-content-between align-items-center">
                                    <h5 class="card-title mb-0">システムログ</h5>
                                    <div class="btn-group" role="group">
                                        <button type="button" class="btn btn-sm btn-outline-dark active" data-log-type="all">全て</button>
                                        <button type="button" class="btn btn-sm btn-outline-dark" data-log-type="continuous_learning">学習</button>
                                        <button type="button" class="btn btn-sm btn-outline-dark" data-log-type="web_app">Webアプリ</button>
                                        <button type="button" class="btn btn-sm btn-outline-dark" data-log-type="collector">データ収集</button>
                                    </div>
                                </div>
                            </div>
                            <div class="card-body">
                                <div id="system-logs" class="small bg-dark text-light p-2" style="height: 300px; overflow-y: auto; font-family: monospace;">
                                    <!-- JavaScriptで動的に生成 -->
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- システム制御 -->
            <div class="tab-pane fade" id="control">
                <div class="row">
                    <!-- 継続学習制御 -->
                    <div class="col-md-6 mb-4">
                        <div class="card">
                            <div class="card-header bg-primary text-white">
                                <h5 class="card-title mb-0">継続学習制御</h5>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <h6>学習パイプライン状態</h6>
                                    <p>現在の状態: <span id="learning-status" class="badge bg-secondary">非アクティブ</span></p>
                                </div>
                                <div class="mb-3">
                                    <label for="learning-days" class="form-label">学習期間（日数）</label>
                                    <input type="number" class="form-control" id="learning-days" min="1" max="30" value="7">
                                </div>
                                <div class="mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="force-train">
                                        <label class="form-check-label" for="force-train">強制再学習</label>
                                    </div>
                                </div>
                                <div class="d-flex justify-content-between">
                                    <button id="start-learning-btn" class="btn btn-success">
                                        <i class="fas fa-play me-1"></i> 継続学習を開始
                                    </button>
                                    <button id="stop-learning-btn" class="btn btn-danger">
                                        <i class="fas fa-stop me-1"></i> 継続学習を停止
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 仮想取引制御 -->
                    <div class="col-md-6 mb-4">
                        <div class="card">
                            <div class="card-header bg-success text-white">
                                <h5 class="card-title mb-0">仮想取引制御</h5>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <h6>取引状態</h6>
                                    <p>現在の状態: <span id="trading-status" class="badge bg-secondary">非アクティブ</span></p>
                                </div>
                                <div class="mb-3">
                                    <label for="initial-balance-trading" class="form-label">初期資金</label>
                                    <div class="input-group">
                                        <span class="input-group-text">¥</span>
                                        <input type="number" class="form-control" id="initial-balance-trading" min="100000" step="10000" value="1000000">
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <label for="max-stocks-trading" class="form-label">最大保有銘柄数</label>
                                    <input type="number" class="form-control" id="max-stocks-trading" min="1" max="10" value="5">
                                </div>
                                <div class="d-flex justify-content-between">
                                    <button id="start-trading-btn" class="btn btn-success">
                                        <i class="fas fa-play me-1"></i> 仮想取引を開始
                                    </button>
                                    <button id="stop-trading-btn" class="btn btn-danger">
                                        <i class="fas fa-stop me-1"></i> 仮想取引を停止
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <!-- フィードバック学習制御 -->
                    <div class="col-md-6 mb-4">
                        <div class="card">
                            <div class="card-header bg-info text-white">
                                <h5 class="card-title mb-0">フィードバック学習制御</h5>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <h6>フィードバック学習状態</h6>
                                    <p>現在の状態: <span id="feedback-status" class="badge bg-secondary">非アクティブ</span></p>
                                </div>
                                <div class="mb-3">
                                    <label for="feedback-days" class="form-label">収集期間（日数）</label>
                                    <input type="number" class="form-control" id="feedback-days" min="1" max="30" value="3">
                                </div>
                                <div class="mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="feedback-force-train">
                                        <label class="form-check-label" for="feedback-force-train">強制再学習</label>
                                    </div>
                                </div>
                                <div class="d-flex justify-content-between">
                                    <button id="start-feedback-btn" class="btn btn-success">
                                        <i class="fas fa-play me-1"></i> フィードバック学習を開始
                                    </button>
                                    <button id="stop-feedback-btn" class="btn btn-danger">
                                        <i class="fas fa-stop me-1"></i> フィードバック学習を停止
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- レポート生成 -->
                    <div class="col-md-6 mb-4">
                        <div class="card">
                            <div class="card-header bg-warning text-dark">
                                <h5 class="card-title mb-0">レポート生成</h5>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <h6>最新のレポート</h6>
                                    <div id="reports-list" class="list-group" style="max-height: 200px; overflow-y: auto;">
                                        <!-- JavaScriptで動的に生成 -->
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <label for="report-type" class="form-label">レポートタイプ</label>
                                    <select class="form-select" id="report-type">
                                        <option value="performance">パフォーマンスレポート</option>
                                        <option value="daily">日次レポート</option>
                                        <option value="model">モデル性能レポート</option>
                                    </select>
                                </div>
                                <div class="d-flex justify-content-end">
                                    <button id="generate-report-btn" class="btn btn-primary">
                                        <i class="fas fa-file-alt me-1"></i> レポート生成
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 設定 -->
            <div class="tab-pane fade" id="settings">
                <div class="card">
                    <div class="card-header bg-dark text-white">
                        <h5 class="card-title mb-0">システム設定</h5>
                    </div>
                    <div class="card-body">
                        <form id="settings-form">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="initial-balance" class="form-label">初期資金</label>
                                        <div class="input-group">
                                            <span class="input-group-text">¥</span>
                                            <input type="number" class="form-control" id="initial-balance" name="initial_balance" min="100000" step="10000">
                                        </div>
                                    </div>
                                    <div class="mb-3">
                                        <label for="max-stocks" class="form-label">最大銘柄数</label>
                                        <input type="number" class="form-control" id="max-stocks" name="max_stocks" min="1" max="10">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="commission-rate" class="form-label">取引手数料率</label>
                                        <div class="input-group">
                                            <input type="number" class="form-control" id="commission-rate" name="commission_rate" min="0" max="0.01" step="0.0001">
                                            <span class="input-group-text">%</span>
                                        </div>
                                    </div>
                                    <div class="mb-3">
                                        <div class="form-check form-switch">
                                            <input class="form-check-input" type="checkbox" id="trading-mode" name="trading_mode">
                                            <label class="form-check-label" for="trading-mode">実取引モード</label>
                                        </div>
                                        <small class="text-muted">※実取引モードはau kabu APIの設定が必要です</small>
                                    </div>
                                </div>
                            </div>
                            <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                                <button type="submit" class="btn btn-primary">設定を保存</button>
                                <button type="button" class="btn btn-danger" id="reset-btn">リセット</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- フッター -->
    <footer class="footer mt-auto py-3 bg-light">
        <div class="container text-center">
            <span class="text-muted">© 2025 日経225 AI取引システム</span>
        </div>
    </footer>

    <!-- JavaScript -->
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.7.1/dist/chart.min.js"></script>
    <!-- Socket.IO -->
    <script src="https://cdn.socket.io/4.4.1/socket.io.min.js"></script>
    <!-- Custom JS -->
    <script src="{{ url_for('static', filename='js/main.js') }}"></script>
    <script src="{{ url_for('static', filename='js/dashboard.js') }}"></script>
    <script src="{{ url_for('static', filename='js/trades.js') }}"></script>
    <script src="{{ url_for('static', filename='js/stocks.js') }}"></script>
    <script src="{{ url_for('static', filename='js/monitor.js') }}"></script>
    <script src="{{ url_for('static', filename='js/control.js') }}"></script>
    <script src="{{ url_for('static', filename='js/settings.js') }}"></script>
</body>
</html>
