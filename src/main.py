import os
import argparse
import pandas as pd
import numpy as np
from datetime import datetime
import logging
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.metrics import mean_squared_error, mean_absolute_percentage_error

from .data_collection import DataCollector
from .feature_engineering import FeatureEngineer
from .model import NikkeiAIModel

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("nikkei_ai.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def setup_directories():
    """Create necessary directories for the project"""
    directories = ['data', 'models', 'results', 'logs']
    for directory in directories:
        os.makedirs(directory, exist_ok=True)
        logger.info(f"Created directory: {directory}")

def collect_data(interval="1h", period="3mo", force_refresh=False):
    """
    Collect stock data using DataCollector
    
    Parameters:
    -----------
    interval : str
        Data interval (e.g., '1h', '1d')
    period : str
        Data period (e.g., '1mo', '3mo', '1y')
    force_refresh : bool
        Whether to force refresh the data even if it exists
        
    Returns:
    --------
    pd.DataFrame
        Collected and cleaned data
    """
    logger.info(f"Collecting {period} of {interval} data...")
    
    # Initialize data collector
    collector = DataCollector(output_dir="data", interval=interval, period=period)
    
    # Check if data already exists and we don't want to refresh
    if os.path.exists(os.path.join("data", "nikkei225_cleaned.csv")) and not force_refresh:
        logger.info("Loading existing data instead of collecting new data")
        return pd.read_csv(os.path.join("data", "nikkei225_cleaned.csv"), parse_dates=['Datetime'])
    
    # Fetch data
    data = collector.fetch_data(batch_size=5)
    
    # Clean data
    cleaned_data = collector.clean_data()
    
    logger.info(f"Collected data for {cleaned_data['Ticker'].nunique()} tickers")
    logger.info(f"Data shape: {cleaned_data.shape}")
    
    return cleaned_data

def engineer_features(data, force_rebuild=False):
    """
    Add technical indicators and other features to the data
    
    Parameters:
    -----------
    data : pd.DataFrame
        Input data
    force_rebuild : bool
        Whether to force rebuild features even if they exist
        
    Returns:
    --------
    pd.DataFrame
        Data with added features
    """
    logger.info("Engineering features...")
    
    features_file = os.path.join("data", "nikkei225_with_features.csv")
    
    # Check if features already exist and we don't want to rebuild
    if os.path.exists(features_file) and not force_rebuild:
        logger.info("Loading existing features instead of rebuilding")
        return pd.read_csv(features_file, parse_dates=['Datetime'])
    
    # Initialize feature engineer
    feature_engineer = FeatureEngineer(
        input_file=os.path.join("data", "nikkei225_cleaned.csv"),
        output_dir="data"
    )
    
    # Add features
    processed_data = feature_engineer.process_and_save(include_market_features=True)
    
    logger.info(f"Added features. New data shape: {processed_data.shape}")
    
    return processed_data

def train_and_evaluate_models(data, target_tickers=None, prediction_horizon=1, sequence_length=24):
    """
    Train and evaluate AI models
    
    Parameters:
    -----------
    data : pd.DataFrame
        Input data with features
    target_tickers : list
        List of ticker symbols to focus on (default: select 5 most volatile)
    prediction_horizon : int
        How many hours ahead to predict
    sequence_length : int
        Length of sequence for LSTM
        
    Returns:
    --------
    NikkeiAIModel
        Trained model
    list
        List of selected tickers
    """
    logger.info("Training and evaluating models...")
    
    # Initialize model
    model = NikkeiAIModel(model_dir="models", data_dir="data")
    
    # If target tickers not provided, select the 5 most volatile stocks
    if target_tickers is None:
        # Calculate volatility
        volatility = data.groupby('Ticker')['Close'].pct_change().groupby(data['Ticker']).std().sort_values(ascending=False)
        # Select top 5
        target_tickers = volatility.head(5).index.tolist()
        
    logger.info(f"Selected target tickers: {target_tickers}")
    
    # Filter data for target tickers
    filtered_data = data[data['Ticker'].isin(target_tickers)]
    
    # Prepare features
    logger.info("Preparing features for model training...")
    prepared_data = model.prepare_features(
        filtered_data,
        ticker=None,  # Train on all selected tickers
        target_col='Close',
        sequence_length=sequence_length,
        prediction_horizon=prediction_horizon,
        train_ratio=0.8
    )
    
    # Train models
    logger.info("Training models...")
    results = model.train_models(
        prepared_data,
        ticker="nikkei5",
        epochs=50,
        batch_size=32
    )
    
    # Save model metadata
    model.save_model_metadata("nikkei5", results)
    
    # Perform walk-forward optimization for individual stocks
    logger.info("Performing walk-forward optimization...")
    for ticker in target_tickers:
        logger.info(f"Walk-forward optimization for {ticker}...")
        wf_results = model.walk_forward_optimization(
            data,
            ticker=ticker,
            window_size=30,  # 30 days
            step_size=7,     # 7 days
            prediction_horizon=prediction_horizon,
            sequence_length=sequence_length
        )
        
        if wf_results is not None:
            # Visualize results
            visualize_predictions(wf_results, ticker)
    
    return model, target_tickers

def select_stocks_for_trading(model, data, top_n=5):
    """
    Select top N stocks for trading
    
    Parameters:
    -----------
    model : NikkeiAIModel
        Trained model
    data : pd.DataFrame
        Input data with features
    top_n : int
        Number of top stocks to select
        
    Returns:
    --------
    pd.DataFrame
        DataFrame with selected stocks
    """
    logger.info(f"Selecting top {top_n} stocks for trading...")
    
    # Evaluate all stocks and select the top N
    top_stocks = model.evaluate_and_select_stocks(
        data,
        prediction_horizon=1,
        top_n=top_n
    )
    
    # Save results
    results_file = os.path.join("results", f"top_stocks_{datetime.now().strftime('%Y%m%d')}.csv")
    top_stocks.to_csv(results_file, index=False)
    logger.info(f"Saved top stocks to {results_file}")
    
    # Visualize results
    visualize_stock_selection(top_stocks)
    
    return top_stocks

def visualize_predictions(results, ticker):
    """
    Visualize prediction results
    
    Parameters:
    -----------
    results : pd.DataFrame
        DataFrame with prediction results
    ticker : str
        Ticker symbol
    """
    plt.figure(figsize=(12, 6))
    
    # Plot actual vs. predicted values
    plt.plot(results['Datetime'], results['Actual'], label='Actual', marker='o', alpha=0.7)
    plt.plot(results['Datetime'], results['Predicted'], label='Predicted', marker='x', alpha=0.7)
    
    # Add labels and title
    plt.title(f'Actual vs. Predicted Prices for {ticker}')
    plt.xlabel('Date')
    plt.ylabel('Price')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    # Rotate x-axis labels for better readability
    plt.xticks(rotation=45)
    plt.tight_layout()
    
    # Save plot
    plt.savefig(os.path.join("results", f"{ticker}_predictions.png"))
    logger.info(f"Saved prediction visualization to results/{ticker}_predictions.png")

def visualize_stock_selection(top_stocks):
    """
    Visualize stock selection results
    
    Parameters:
    -----------
    top_stocks : pd.DataFrame
        DataFrame with selected stocks
    """
    plt.figure(figsize=(10, 6))
    
    # Create a bar chart of predicted changes
    sns.barplot(x='Ticker', y='Predicted_Change_Pct', data=top_stocks)
    
    # Add labels and title
    plt.title('Predicted Price Change % for Selected Stocks')
    plt.xlabel('Ticker')
    plt.ylabel('Predicted Change %')
    plt.grid(True, alpha=0.3)
    
    # Annotate bars with values
    for i, v in enumerate(top_stocks['Predicted_Change_Pct']):
        plt.text(i, v, f"{v:.2f}%", ha='center', va='bottom', fontweight='bold')
    
    plt.tight_layout()
    
    # Save plot
    plt.savefig(os.path.join("results", "top_stocks_prediction.png"))
    logger.info(f"Saved stock selection visualization to results/top_stocks_prediction.png")

def main():
    """Main function to run the entire pipeline"""
    parser = argparse.ArgumentParser(description='Nikkei 225 AI Stock Prediction')
    parser.add_argument('--interval', type=str, default='1h', help='Data interval (e.g., 1h, 1d)')
    parser.add_argument('--period', type=str, default='3mo', help='Data period (e.g., 1mo, 3mo, 1y)')
    parser.add_argument('--refresh', action='store_true', help='Force refresh data and features')
    parser.add_argument('--tickers', type=str, nargs='+', help='Specific tickers to analyze')
    parser.add_argument('--top-n', type=int, default=5, help='Number of top stocks to select')
    
    args = parser.parse_args()
    
    # Create directories
    setup_directories()
    
    # Collect data
    data = collect_data(interval=args.interval, period=args.period, force_refresh=args.refresh)
    
    # Engineer features
    data_with_features = engineer_features(data, force_rebuild=args.refresh)
    
    # Train and evaluate models
    model, target_tickers = train_and_evaluate_models(
        data_with_features,
        target_tickers=args.tickers,
        prediction_horizon=1,
        sequence_length=24
    )
    
    # Select stocks for trading
    top_stocks = select_stocks_for_trading(model, data_with_features, top_n=args.top_n)
    
    logger.info("Pipeline completed successfully!")
    logger.info(f"Top {args.top_n} stocks for trading:")
    logger.info(top_stocks[['Ticker', 'Current_Price', 'Predicted_Price', 'Predicted_Change_Pct']])

if __name__ == "__main__":
    main()
