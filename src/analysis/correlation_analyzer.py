import pandas as pd
import numpy as np
import json
import logging
from typing import Dict, List, Tuple, Optional
from pathlib import Path
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats
from sklearn.preprocessing import StandardScaler
from sklearn.decomposition import PCA
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

class InternationalCorrelationAnalyzer:
    """
    Analyze correlations between international markets and Nikkei 225
    to identify key influencing factors for enhanced prediction accuracy.
    """
    
    def __init__(self, config_path: str = "config/international_markets_config.json"):
        self.config_path = Path(config_path)
        self.config = self._load_config()
        self.logger = self._setup_logging()
        self.data_dir = Path("international_data")
        self.analysis_dir = Path("analysis_results")
        self.analysis_dir.mkdir(exist_ok=True)
        
        # Analysis parameters
        self.lookback_periods = self.config.get('correlation_analysis', {}).get('lookback_periods', [5, 10, 20, 50])
        self.correlation_threshold = self.config.get('correlation_analysis', {}).get('correlation_threshold', 0.3)
        
    def _load_config(self) -> Dict:
        """Load international markets configuration."""
        try:
            with open(self.config_path, 'r') as f:
                return json.load(f)
        except FileNotFoundError:
            self.logger.error(f"Config file not found: {self.config_path}")
            return {}
    
    def _setup_logging(self) -> logging.Logger:
        """Setup logging for correlation analysis."""
        logger = logging.getLogger('CorrelationAnalyzer')
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        
        return logger
    
    def load_nikkei_data(self, file_path: str = None) -> pd.DataFrame:
        """
        Load Nikkei 225 data for correlation analysis.
        
        Args:
            file_path: Path to Nikkei data file. If None, searches for recent data.
            
        Returns:
            Nikkei 225 DataFrame
        """
        if file_path is None:
            # Try to find recent Nikkei data
            possible_paths = [
                "data/nikkei225_with_features.csv",
                "data/nikkei225_cleaned.csv",
                "data/nikkei225_data.csv"
            ]
            
            for path in possible_paths:
                if Path(path).exists():
                    file_path = path
                    break
            
            if file_path is None:
                raise FileNotFoundError("Could not find Nikkei 225 data file")
        
        try:
            nikkei_data = pd.read_csv(file_path, index_col=0, parse_dates=True)
            self.logger.info(f"Loaded Nikkei data: {len(nikkei_data)} records from {file_path}")
            return nikkei_data
        except Exception as e:
            self.logger.error(f"Error loading Nikkei data: {e}")
            raise
    
    def load_international_data(self) -> Dict[str, pd.DataFrame]:
        """
        Load all international market data.
        
        Returns:
            Dictionary of international market DataFrames
        """
        international_data = {}
        
        # Load indices
        indices_dir = self.data_dir / 'indices'
        if indices_dir.exists():
            for file_path in indices_dir.glob('*.csv'):
                try:
                    data = pd.read_csv(file_path, index_col=0, parse_dates=True)
                    key = f"index_{file_path.stem}"
                    international_data[key] = data
                except Exception as e:
                    self.logger.warning(f"Could not load {file_path}: {e}")
        
        # Load forex
        forex_dir = self.data_dir / 'forex'
        if forex_dir.exists():
            for file_path in forex_dir.glob('*.csv'):
                try:
                    data = pd.read_csv(file_path, index_col=0, parse_dates=True)
                    key = f"forex_{file_path.stem}"
                    international_data[key] = data
                except Exception as e:
                    self.logger.warning(f"Could not load {file_path}: {e}")
        
        # Load commodities
        commodities_dir = self.data_dir / 'commodities'
        if commodities_dir.exists():
            for file_path in commodities_dir.glob('*.csv'):
                try:
                    data = pd.read_csv(file_path, index_col=0, parse_dates=True)
                    key = f"commodity_{file_path.stem}"
                    international_data[key] = data
                except Exception as e:
                    self.logger.warning(f"Could not load {file_path}: {e}")
        
        # Load economic indicators
        indicators_dir = self.data_dir / 'indicators'
        if indicators_dir.exists():
            for file_path in indicators_dir.glob('*.csv'):
                try:
                    data = pd.read_csv(file_path, index_col=0, parse_dates=True)
                    key = f"indicator_{file_path.stem}"
                    international_data[key] = data
                except Exception as e:
                    self.logger.warning(f"Could not load {file_path}: {e}")
        
        self.logger.info(f"Loaded {len(international_data)} international datasets")
        return international_data
    
    def calculate_rolling_correlations(self, nikkei_data: pd.DataFrame, 
                                     international_data: Dict[str, pd.DataFrame],
                                     window: int = 50) -> pd.DataFrame:
        """
        Calculate rolling correlations between Nikkei and international markets.
        
        Args:
            nikkei_data: Nikkei 225 data
            international_data: Dictionary of international market data
            window: Rolling window size
            
        Returns:
            DataFrame with rolling correlations
        """
        # Prepare data for correlation analysis
        correlation_data = pd.DataFrame()
        correlation_data['nikkei_close'] = nikkei_data['Close']
        correlation_data['nikkei_returns'] = nikkei_data['Close'].pct_change()
        
        # Add international market data
        for market_name, market_data in international_data.items():
            if 'Close' in market_data.columns:
                correlation_data[f"{market_name}_close"] = market_data['Close']
                correlation_data[f"{market_name}_returns"] = market_data['Close'].pct_change()
        
        # Calculate rolling correlations
        rolling_correlations = pd.DataFrame(index=correlation_data.index)
        
        for col in correlation_data.columns:
            if col not in ['nikkei_close', 'nikkei_returns']:
                rolling_correlations[col] = correlation_data['nikkei_returns'].rolling(window=window).corr(
                    correlation_data[col]
                )
        
        return rolling_correlations
    
    def analyze_lead_lag_relationships(self, nikkei_data: pd.DataFrame,
                                     international_data: Dict[str, pd.DataFrame],
                                     max_lag: int = 10) -> Dict[str, Dict]:
        """
        Analyze lead-lag relationships between international markets and Nikkei.
        
        Args:
            nikkei_data: Nikkei 225 data
            international_data: Dictionary of international market data
            max_lag: Maximum lag periods to analyze
            
        Returns:
            Dictionary containing lead-lag analysis results
        """
        nikkei_returns = nikkei_data['Close'].pct_change().dropna()
        lead_lag_results = {}
        
        for market_name, market_data in international_data.items():
            if 'Close' not in market_data.columns:
                continue
                
            market_returns = market_data['Close'].pct_change().dropna()
            
            # Align data
            aligned_data = pd.concat([nikkei_returns, market_returns], axis=1, join='inner')
            aligned_data.columns = ['nikkei', 'market']
            aligned_data = aligned_data.dropna()
            
            if len(aligned_data) < max_lag * 2:
                continue
            
            correlations = {}
            
            # Test different lags
            for lag in range(-max_lag, max_lag + 1):
                if lag == 0:
                    corr = aligned_data['nikkei'].corr(aligned_data['market'])
                elif lag > 0:
                    # Market leads Nikkei
                    corr = aligned_data['nikkei'].corr(aligned_data['market'].shift(lag))
                else:
                    # Nikkei leads Market
                    corr = aligned_data['nikkei'].shift(-lag).corr(aligned_data['market'])
                
                correlations[lag] = corr
            
            # Find best correlation
            best_lag = max(correlations.keys(), key=lambda k: abs(correlations[k]))
            best_correlation = correlations[best_lag]
            
            lead_lag_results[market_name] = {
                'best_lag': best_lag,
                'best_correlation': best_correlation,
                'all_correlations': correlations,
                'interpretation': self._interpret_lead_lag(best_lag, best_correlation)
            }
        
        return lead_lag_results
    
    def _interpret_lead_lag(self, lag: int, correlation: float) -> str:
        """Interpret lead-lag relationship."""
        strength = "strong" if abs(correlation) > 0.5 else "moderate" if abs(correlation) > 0.3 else "weak"
        direction = "positive" if correlation > 0 else "negative"
        
        if lag == 0:
            return f"{strength} {direction} contemporaneous correlation"
        elif lag > 0:
            return f"{strength} {direction} correlation with market leading Nikkei by {lag} periods"
        else:
            return f"{strength} {direction} correlation with Nikkei leading market by {-lag} periods"
    
    def identify_regime_changes(self, nikkei_data: pd.DataFrame,
                              international_data: Dict[str, pd.DataFrame],
                              window: int = 50) -> Dict[str, List]:
        """
        Identify regime changes in correlations.
        
        Args:
            nikkei_data: Nikkei 225 data
            international_data: Dictionary of international market data
            window: Rolling window for correlation calculation
            
        Returns:
            Dictionary containing regime change points
        """
        rolling_corr = self.calculate_rolling_correlations(nikkei_data, international_data, window)
        regime_changes = {}
        
        for column in rolling_corr.columns:
            if rolling_corr[column].isnull().all():
                continue
                
            corr_series = rolling_corr[column].dropna()
            
            # Detect significant changes using rolling standard deviation
            rolling_std = corr_series.rolling(window=20).std()
            threshold = rolling_std.mean() + 2 * rolling_std.std()
            
            # Find regime changes
            changes = []
            for i in range(len(corr_series) - 1):
                if abs(corr_series.iloc[i+1] - corr_series.iloc[i]) > threshold:
                    changes.append({
                        'date': corr_series.index[i+1],
                        'old_corr': corr_series.iloc[i],
                        'new_corr': corr_series.iloc[i+1],
                        'change_magnitude': abs(corr_series.iloc[i+1] - corr_series.iloc[i])
                    })
            
            regime_changes[column] = changes
        
        return regime_changes
    
    def calculate_factor_loadings(self, nikkei_data: pd.DataFrame,
                                international_data: Dict[str, pd.DataFrame]) -> Dict[str, float]:
        """
        Calculate factor loadings using PCA to identify key international factors.
        
        Args:
            nikkei_data: Nikkei 225 data
            international_data: Dictionary of international market data
            
        Returns:
            Dictionary containing factor loadings
        """
        # Prepare returns data
        returns_data = pd.DataFrame()
        returns_data['nikkei'] = nikkei_data['Close'].pct_change()
        
        for market_name, market_data in international_data.items():
            if 'Close' in market_data.columns:
                returns_data[market_name] = market_data['Close'].pct_change()
        
        # Remove NaN values
        returns_data = returns_data.dropna()
        
        if len(returns_data) == 0:
            return {}
        
        # Standardize the data
        scaler = StandardScaler()
        scaled_data = scaler.fit_transform(returns_data)
        
        # Perform PCA
        pca = PCA()
        pca.fit(scaled_data)
        
        # Get factor loadings
        loadings = pd.DataFrame(
            pca.components_.T,
            columns=[f'PC{i+1}' for i in range(len(pca.components_))],
            index=returns_data.columns
        )
        
        # Focus on first few principal components
        factor_loadings = {}
        for i in range(min(3, len(pca.components_))):
            pc_name = f'PC{i+1}'
            explained_variance = pca.explained_variance_ratio_[i]
            
            factor_loadings[pc_name] = {
                'explained_variance': explained_variance,
                'loadings': loadings[pc_name].to_dict(),
                'top_contributors': loadings[pc_name].abs().sort_values(ascending=False).head(5).to_dict()
            }
        
        return factor_loadings
    
    def generate_comprehensive_report(self, nikkei_data: pd.DataFrame = None) -> Dict:
        """
        Generate comprehensive correlation analysis report.
        
        Args:
            nikkei_data: Nikkei 225 data. If None, loads automatically.
            
        Returns:
            Complete analysis report
        """
        if nikkei_data is None:
            nikkei_data = self.load_nikkei_data()
        
        international_data = self.load_international_data()
        
        if not international_data:
            self.logger.warning("No international data available for analysis")
            return {}
        
        self.logger.info("Generating comprehensive correlation analysis report")
        
        # Basic correlations
        basic_correlations = self._calculate_basic_correlations(nikkei_data, international_data)
        
        # Rolling correlations
        rolling_correlations = self.calculate_rolling_correlations(nikkei_data, international_data)
        
        # Lead-lag analysis
        lead_lag_analysis = self.analyze_lead_lag_relationships(nikkei_data, international_data)
        
        # Regime changes
        regime_changes = self.identify_regime_changes(nikkei_data, international_data)
        
        # Factor analysis
        factor_loadings = self.calculate_factor_loadings(nikkei_data, international_data)
        
        # Compile report
        report = {
            'analysis_date': datetime.now().isoformat(),
            'data_period': {
                'nikkei_start': nikkei_data.index.min().isoformat(),
                'nikkei_end': nikkei_data.index.max().isoformat(),
                'nikkei_records': len(nikkei_data)
            },
            'international_datasets': len(international_data),
            'basic_correlations': basic_correlations,
            'rolling_correlations_summary': self._summarize_rolling_correlations(rolling_correlations),
            'lead_lag_analysis': lead_lag_analysis,
            'regime_changes_summary': self._summarize_regime_changes(regime_changes),
            'factor_analysis': factor_loadings,
            'key_findings': self._extract_key_findings(basic_correlations, lead_lag_analysis, factor_loadings),
            'recommendations': self._generate_recommendations(basic_correlations, lead_lag_analysis, factor_loadings)
        }
        
        # Save report
        report_path = self.analysis_dir / f"correlation_analysis_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_path, 'w') as f:
            json.dump(report, f, indent=2, default=str)
        
        self.logger.info(f"Analysis report saved to {report_path}")
        
        return report
    
    def _calculate_basic_correlations(self, nikkei_data: pd.DataFrame,
                                    international_data: Dict[str, pd.DataFrame]) -> Dict[str, float]:
        """Calculate basic correlations between Nikkei and international markets."""
        correlations = {}
        nikkei_returns = nikkei_data['Close'].pct_change().dropna()
        
        for market_name, market_data in international_data.items():
            if 'Close' in market_data.columns:
                market_returns = market_data['Close'].pct_change().dropna()
                
                # Align data and calculate correlation
                aligned_data = pd.concat([nikkei_returns, market_returns], axis=1, join='inner')
                if len(aligned_data) > 0:
                    correlation = aligned_data.iloc[:, 0].corr(aligned_data.iloc[:, 1])
                    if not np.isnan(correlation):
                        correlations[market_name] = correlation
        
        return correlations
    
    def _summarize_rolling_correlations(self, rolling_correlations: pd.DataFrame) -> Dict:
        """Summarize rolling correlations analysis."""
        summary = {}
        
        for column in rolling_correlations.columns:
            if not rolling_correlations[column].isnull().all():
                corr_series = rolling_correlations[column].dropna()
                summary[column] = {
                    'mean_correlation': corr_series.mean(),
                    'std_correlation': corr_series.std(),
                    'max_correlation': corr_series.max(),
                    'min_correlation': corr_series.min(),
                    'stability': 1 - (corr_series.std() / (abs(corr_series.mean()) + 1e-8))
                }
        
        return summary
    
    def _summarize_regime_changes(self, regime_changes: Dict[str, List]) -> Dict:
        """Summarize regime changes analysis."""
        summary = {}
        
        for market, changes in regime_changes.items():
            if changes:
                summary[market] = {
                    'num_regime_changes': len(changes),
                    'avg_change_magnitude': np.mean([c['change_magnitude'] for c in changes]),
                    'most_recent_change': changes[-1] if changes else None
                }
        
        return summary
    
    def _extract_key_findings(self, basic_correlations: Dict, lead_lag_analysis: Dict,
                            factor_loadings: Dict) -> List[str]:
        """Extract key findings from the analysis."""
        findings = []
        
        # High correlation findings
        high_corr_markets = {k: v for k, v in basic_correlations.items() if abs(v) > 0.5}
        if high_corr_markets:
            findings.append(f"Strong correlations found with: {', '.join(high_corr_markets.keys())}")
        
        # Leading indicators
        leading_markets = [
            market for market, analysis in lead_lag_analysis.items()
            if analysis['best_lag'] > 0 and abs(analysis['best_correlation']) > 0.3
        ]
        if leading_markets:
            findings.append(f"Leading indicators identified: {', '.join(leading_markets)}")
        
        # Factor analysis insights
        if factor_loadings and 'PC1' in factor_loadings:
            pc1_variance = factor_loadings['PC1']['explained_variance']
            findings.append(f"First principal component explains {pc1_variance:.1%} of variance")
        
        return findings
    
    def _generate_recommendations(self, basic_correlations: Dict, lead_lag_analysis: Dict,
                                factor_loadings: Dict) -> List[str]:
        """Generate recommendations based on analysis."""
        recommendations = []
        
        # High-priority features
        critical_features = [
            market for market, corr in basic_correlations.items()
            if abs(corr) > self.correlation_threshold
        ]
        
        if critical_features:
            recommendations.append(
                f"Include these high-correlation features in ML model: {', '.join(critical_features[:10])}"
            )
        
        # Leading indicators
        leading_features = [
            market for market, analysis in lead_lag_analysis.items()
            if analysis['best_lag'] > 0 and abs(analysis['best_correlation']) > 0.3
        ]
        
        if leading_features:
            recommendations.append(
                f"Use these leading indicators for prediction: {', '.join(leading_features[:5])}"
            )
        
        # Factor-based features
        if factor_loadings:
            recommendations.append("Consider using principal components as composite features")
        
        recommendations.append("Monitor correlation stability for model retraining triggers")
        
        return recommendations