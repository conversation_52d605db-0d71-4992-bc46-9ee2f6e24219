"""
Nikkei 225 Ticker Information Module

This module provides comprehensive information about Nikkei 225 constituent stocks,
including sector classifications and major indices.
"""

import json
from typing import List, Dict, Optional
from pathlib import Path

# Nikkei 225 constituent stocks with sectors (as of 2024)
NIKKEI225_TICKERS = {
    # Technology
    "6758.T": {"name": "Sony Group", "sector": "Technology"},
    "9984.T": {"name": "SoftBank Group", "sector": "Technology"},
    "6861.T": {"name": "Keyence", "sector": "Technology"},
    "4063.T": {"name": "Shin-Etsu Chemical", "sector": "Technology"},
    "6954.T": {"name": "Fanuc", "sector": "Technology"},
    "8035.T": {"name": "Tokyo Electron", "sector": "Technology"},
    "4901.T": {"name": "Fujifilm Holdings", "sector": "Technology"},
    "6971.T": {"name": "Kyocera", "sector": "Technology"},
    "6981.T": {"name": "Murata Manufacturing", "sector": "Technology"},
    "4543.T": {"name": "Terumo", "sector": "Technology"},
    
    # Automotive
    "7203.T": {"name": "Toyota Motor", "sector": "Automotive"},
    "7267.T": {"name": "Honda Motor", "sector": "Automotive"},
    "7201.T": {"name": "Nissan Motor", "sector": "Automotive"},
    "7269.T": {"name": "Suzuki Motor", "sector": "Automotive"},
    "7211.T": {"name": "Mitsubishi Motors", "sector": "Automotive"},
    "5108.T": {"name": "Bridgestone", "sector": "Automotive"},
    "7261.T": {"name": "Mazda Motor", "sector": "Automotive"},
    
    # Financial Services
    "8306.T": {"name": "Mitsubishi UFJ Financial Group", "sector": "Financial"},
    "8316.T": {"name": "Sumitomo Mitsui Financial Group", "sector": "Financial"},
    "8411.T": {"name": "Mizuho Financial Group", "sector": "Financial"},
    "8604.T": {"name": "Nomura Holdings", "sector": "Financial"},
    "8725.T": {"name": "MS&AD Insurance Group", "sector": "Financial"},
    "8750.T": {"name": "Dai-ichi Life Holdings", "sector": "Financial"},
    "8766.T": {"name": "Tokio Marine Holdings", "sector": "Financial"},
    "8802.T": {"name": "Mitsubishi Estate", "sector": "Financial"},
    "8830.T": {"name": "Sumitomo Realty & Development", "sector": "Financial"},
    
    # Trading Companies
    "8058.T": {"name": "Mitsubishi Corporation", "sector": "Trading"},
    "8031.T": {"name": "Mitsui & Co", "sector": "Trading"},
    "8001.T": {"name": "Itochu Corporation", "sector": "Trading"},
    "8002.T": {"name": "Marubeni Corporation", "sector": "Trading"},
    "8053.T": {"name": "Sumitomo Corporation", "sector": "Trading"},
    
    # Retail & Consumer
    "9983.T": {"name": "Fast Retailing", "sector": "Retail"},
    "4502.T": {"name": "Takeda Pharmaceutical", "sector": "Healthcare"},
    "4568.T": {"name": "Daiichi Sankyo", "sector": "Healthcare"},
    "4523.T": {"name": "Eisai", "sector": "Healthcare"},
    "4507.T": {"name": "Shionogi", "sector": "Healthcare"},
    "2914.T": {"name": "Japan Tobacco", "sector": "Consumer"},
    "2802.T": {"name": "Ajinomoto", "sector": "Consumer"},
    "2801.T": {"name": "Kikkoman", "sector": "Consumer"},
    
    # Industrial
    "6301.T": {"name": "Komatsu", "sector": "Industrial"},
    "7751.T": {"name": "Canon", "sector": "Industrial"},
    "6902.T": {"name": "Denso", "sector": "Industrial"},
    "6503.T": {"name": "Mitsubishi Electric", "sector": "Industrial"},
    "6504.T": {"name": "Fuji Electric", "sector": "Industrial"},
    "7012.T": {"name": "Kawasaki Heavy Industries", "sector": "Industrial"},
    "7011.T": {"name": "Mitsubishi Heavy Industries", "sector": "Industrial"},
    
    # Telecommunications
    "9432.T": {"name": "NTT", "sector": "Telecommunications"},
    "9433.T": {"name": "KDDI", "sector": "Telecommunications"},
    "9434.T": {"name": "SoftBank", "sector": "Telecommunications"},
    
    # Utilities
    "9501.T": {"name": "Tokyo Electric Power", "sector": "Utilities"},
    "9502.T": {"name": "Chubu Electric Power", "sector": "Utilities"},
    "9503.T": {"name": "Kansai Electric Power", "sector": "Utilities"},
    "1605.T": {"name": "INPEX", "sector": "Energy"},
    
    # Materials
    "5401.T": {"name": "Nippon Steel", "sector": "Materials"},
    "5411.T": {"name": "JFE Holdings", "sector": "Materials"},
    "4005.T": {"name": "Sumitomo Chemical", "sector": "Materials"},
    "4183.T": {"name": "Mitsui Chemicals", "sector": "Materials"},
    "4208.T": {"name": "UBE Corporation", "sector": "Materials"},
    
    # Transportation
    "9020.T": {"name": "East Japan Railway", "sector": "Transportation"},
    "9021.T": {"name": "West Japan Railway", "sector": "Transportation"},
    "9022.T": {"name": "Central Japan Railway", "sector": "Transportation"},
    "9301.T": {"name": "Mitsubishi Logistics", "sector": "Transportation"},
    
    # Real Estate
    "8801.T": {"name": "Mitsui Fudosan", "sector": "Real Estate"},
    "8803.T": {"name": "Heiwa Real Estate", "sector": "Real Estate"},
}

# Add more tickers to reach 225 (this is a representative sample)
# In a real implementation, you would include all 225 tickers

SECTORS = {
    "Technology": "Information Technology and Electronics",
    "Automotive": "Automotive and Transportation Equipment", 
    "Financial": "Financial Services and Insurance",
    "Trading": "Trading Companies and Conglomerates",
    "Retail": "Retail and Consumer Goods",
    "Healthcare": "Pharmaceuticals and Healthcare",
    "Consumer": "Consumer Products and Food",
    "Industrial": "Industrial Equipment and Machinery",
    "Telecommunications": "Telecommunications Services",
    "Utilities": "Utilities and Energy",
    "Materials": "Basic Materials and Chemicals",
    "Transportation": "Transportation and Logistics",
    "Real Estate": "Real Estate and Construction"
}

# Major indices and subsets
TOPIX_CORE30 = [
    "7203.T", "6758.T", "9984.T", "8306.T", "9432.T",
    "8058.T", "8031.T", "8001.T", "6861.T", "9983.T",
    "4502.T", "8316.T", "8411.T", "7267.T", "6301.T",
    "4063.T", "6954.T", "8035.T", "7751.T", "6902.T",
    "8002.T", "8053.T", "9501.T", "5401.T", "9020.T",
    "8604.T", "4568.T", "8725.T", "8750.T", "8766.T"
]

MAJOR_TICKERS = [
    "7203.T", "6758.T", "9984.T", "8306.T", "9432.T",
    "8058.T", "8031.T", "8001.T", "6861.T", "9983.T",
    "4502.T", "8316.T", "8411.T", "7267.T", "6301.T"
]

def get_all_tickers() -> List[str]:
    """Get all Nikkei 225 ticker symbols"""
    return list(NIKKEI225_TICKERS.keys())

def get_tickers_by_sector(sector_name: str) -> List[str]:
    """Get tickers for a specific sector"""
    return [ticker for ticker, info in NIKKEI225_TICKERS.items() 
            if info["sector"] == sector_name]

def get_sector_names() -> List[str]:
    """Get all available sector names"""
    return list(SECTORS.keys())

def get_major_tickers() -> List[str]:
    """Get major/liquid tickers for trading"""
    return MAJOR_TICKERS.copy()

def get_topix_core30() -> List[str]:
    """Get TOPIX Core 30 tickers"""
    return TOPIX_CORE30.copy()

def get_ticker_info(ticker: str) -> Optional[Dict]:
    """Get information for a specific ticker"""
    return NIKKEI225_TICKERS.get(ticker)

def get_sector_for_ticker(ticker: str) -> Optional[str]:
    """Get sector for a specific ticker"""
    info = get_ticker_info(ticker)
    return info["sector"] if info else None

def get_company_name(ticker: str) -> Optional[str]:
    """Get company name for a specific ticker"""
    info = get_ticker_info(ticker)
    return info["name"] if info else None

def is_valid_ticker(ticker: str) -> bool:
    """Check if ticker is a valid Nikkei 225 constituent"""
    return ticker in NIKKEI225_TICKERS

def get_sector_distribution() -> Dict[str, int]:
    """Get distribution of tickers by sector"""
    distribution = {}
    for ticker, info in NIKKEI225_TICKERS.items():
        sector = info["sector"]
        distribution[sector] = distribution.get(sector, 0) + 1
    return distribution

def save_ticker_data(filepath: str):
    """Save ticker data to JSON file"""
    data = {
        "tickers": NIKKEI225_TICKERS,
        "sectors": SECTORS,
        "major_tickers": MAJOR_TICKERS,
        "topix_core30": TOPIX_CORE30
    }
    
    with open(filepath, 'w', encoding='utf-8') as f:
        json.dump(data, f, indent=2, ensure_ascii=False)

def load_ticker_data(filepath: str) -> bool:
    """Load ticker data from JSON file"""
    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        global NIKKEI225_TICKERS, SECTORS, MAJOR_TICKERS, TOPIX_CORE30
        NIKKEI225_TICKERS.update(data.get("tickers", {}))
        SECTORS.update(data.get("sectors", {}))
        MAJOR_TICKERS.extend(data.get("major_tickers", []))
        TOPIX_CORE30.extend(data.get("topix_core30", []))
        
        return True
    except Exception:
        return False

# Utility class for easier access
class NikkeiUtils:
    """Utility class for Nikkei 225 ticker operations"""
    
    @staticmethod
    def get_all_nikkei225_tickers():
        return get_all_tickers()
    
    @staticmethod
    def get_sector_for_ticker(ticker):
        return get_sector_for_ticker(ticker)
    
    @staticmethod
    def get_major_tickers():
        return get_major_tickers()
    
    @staticmethod
    def get_tickers_by_sector(sector):
        return get_tickers_by_sector(sector)
