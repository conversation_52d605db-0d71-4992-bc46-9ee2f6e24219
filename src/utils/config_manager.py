#!/usr/bin/env python3
"""
Configuration Manager for Nikkei225 AI Trading System

This module handles all configuration management including environment variables,
config files, and system settings.
"""

import os
import json
import logging
from pathlib import Path
from typing import Dict, Any, Optional
from dotenv import load_dotenv

logger = logging.getLogger(__name__)

class ConfigManager:
    """Centralized configuration management"""

    def __init__(self, config_dir: str = "config"):
        self.config_dir = Path(config_dir)
        self.config_dir.mkdir(exist_ok=True)

        # Load environment variables
        load_dotenv()

        # Default configuration
        self.config = self._load_default_config()

        # Load configuration files
        self._load_config_files()

        # Override with environment variables
        self._load_env_variables()
    
    def _load_default_config(self) -> Dict[str, Any]:
        """Load default configuration"""
        return {
            "trading": {
                "mode": "paper",
                "initial_balance": 1000000,
                "max_stocks": 5,
                "commission_rate": 0.0001,
                "trading_hours": {
                    "start": "09:00",
                    "end": "15:00",
                    "timezone": "Asia/Tokyo"
                }
            },
            "data": {
                "update_interval": 3600,
                "cache_enabled": True,
                "cache_expiry_hours": 24,
                "sources": ["yfinance", "pandas_datareader"],
                "days": 30,
                "interval": "1d"
            },
            "model": {
                "ensemble_enabled": True,
                "models": ["lstm", "xgboost", "lightgbm"],
                "retrain_interval_days": 7,
                "prediction_horizon": 1
            },
            "api": {
                "kabu_url": "http://localhost:18080/kabusapi",
                "rate_limit": True,
                "timeout": 30
            },
            "web": {
                "host": "localhost",
                "port": 5000,
                "debug": False
            },
            "logging": {
                "level": "INFO",
                "file_enabled": True,
                "console_enabled": True
            },
            "paths": {
                "results_dir": "results",
                "test_results_dir": "results/test",
                "production_results_dir": "results/production",
                "hybrid_results_dir": "results/hybrid"
            }
        }

    def _load_config_files(self):
        """Load configuration from JSON files"""
        config_files = [
            "simple_trading_config.json",
            "production_trading_config.json",
            "international_markets_config.json",
            "trading_config.json"
        ]

        for config_file in config_files:
            file_path = self.config_dir / config_file
            if file_path.exists():
                try:
                    with open(file_path, 'r') as f:
                        file_config = json.load(f)
                    self._merge_config(self.config, file_config)
                    logger.info(f"Loaded config from {config_file}")
                except Exception as e:
                    logger.warning(f"Failed to load {config_file}: {e}")

    def _load_env_variables(self):
        """Load configuration from environment variables"""
        env_mappings = {
            "KABU_API_PWD": ("api", "password"),
            "KABU_API_URL": ("api", "kabu_url"),
            "TRADING_MODE": ("trading", "mode"),
            "INITIAL_BALANCE": ("trading", "initial_balance"),
            "MAX_STOCKS": ("trading", "max_stocks"),
            "GPU_ENABLED": ("system", "gpu_enabled"),
            "LOG_LEVEL": ("logging", "level"),
            "WEB_HOST": ("web", "host"),
            "WEB_PORT": ("web", "port"),
            "WEB_DEBUG": ("web", "debug")
        }

        for env_var, (section, key) in env_mappings.items():
            value = os.getenv(env_var)
            if value is not None:
                # Convert types
                if key in ["initial_balance", "max_stocks", "port"]:
                    value = int(value)
                elif key in ["gpu_enabled", "debug"]:
                    value = value.lower() in ["true", "1", "yes"]

                if section not in self.config:
                    self.config[section] = {}
                self.config[section][key] = value

    def _merge_config(self, base: Dict, override: Dict):
        """Recursively merge configuration dictionaries"""
        for key, value in override.items():
            if key in base and isinstance(base[key], dict) and isinstance(value, dict):
                self._merge_config(base[key], value)
            else:
                base[key] = value

    def get(self, section: str, key: str = None, default: Any = None) -> Any:
        """Get configuration value"""
        if key is None:
            return self.config.get(section, default)
        return self.config.get(section, {}).get(key, default)

    def set(self, section: str, key: str, value: Any):
        """Set configuration value"""
        if section not in self.config:
            self.config[section] = {}
        self.config[section][key] = value

    def get_trading_mode(self):
        """Get trading mode"""
        return self.get("trading", "mode", "paper")

    def save_config(self, filename: str = "current_config.json"):
        """Save current configuration to file"""
        file_path = self.config_dir / filename
        with open(file_path, 'w') as f:
            json.dump(self.config, f, indent=2)
        logger.info(f"Configuration saved to {file_path}")

    def validate_config(self) -> bool:
        """Validate configuration"""
        required_sections = ["trading", "data", "api"]

        for section in required_sections:
            if section not in self.config:
                logger.error(f"Missing required configuration section: {section}")
                return False

        # Validate trading mode
        valid_modes = ["nocash", "paper", "dryrun", "live"]
        trading_mode = self.get("trading", "mode")
        if trading_mode not in valid_modes:
            logger.error(f"Invalid trading mode: {trading_mode}")
            return False

        return True

    def get_results_dir(self, mode=None):
        """Get results directory path"""
        if mode is None:
            mode = self.get_trading_mode()

        if mode == "test":
            return self.config["paths"]["test_results_dir"]
        elif mode == "production":
            return self.config["paths"]["production_results_dir"]
        elif mode == "hybrid":
            return self.config["paths"]["hybrid_results_dir"]
        else:
            return self.config["paths"]["results_dir"]

    def get_mode_specific_config(self, mode=None):
        """Get mode-specific configuration"""
        if mode is None:
            mode = self.get_trading_mode()

        base_config = self.config.copy()

        # Override with mode-specific settings
        mode_key = f"{mode}_specific"
        if mode_key in self.config:
            for section, values in self.config[mode_key].items():
                if section in base_config:
                    base_config[section].update(values)

        return base_config

    def create_mode_specific_config(self):
        """Create mode-specific configuration templates"""
        if "paper_specific" not in self.config:
            self.config["paper_specific"] = {
                "trading": {
                    "initial_balance": 1000000,
                    "commission_rate": 0.0,  # No commission in paper mode
                    "max_stocks": 10  # Allow more positions for testing
                },
                "data": {
                    "days": 14  # Shorter data period for testing
                }
            }

        if "live_specific" not in self.config:
            self.config["live_specific"] = {
                "trading": {
                    "initial_balance": 1000000,
                    "commission_rate": 0.0001,  # Real commission rates
                    "max_stocks": 5  # Conservative position limit
                },
                "data": {
                    "days": 30  # Longer data period for production
                }
            }

        if "dryrun_specific" not in self.config:
            self.config["dryrun_specific"] = {
                "trading": {
                    "initial_balance": 1000000,
                    "commission_rate": 0.0001,
                    "max_stocks": 7  # Between paper and live
                },
                "data": {
                    "days": 21  # Between paper and live
                }
            }

        self.save_config()
        return True


# Global configuration instance
config = ConfigManager()
