#!/usr/bin/env python3
"""
中央集権的ティッカー管理システム

全てのティッカー関連操作を統一的に管理し、
データの一貫性と品質を保証します。
"""

import os
import sys
import json
import logging
from datetime import datetime
from pathlib import Path
from typing import List, Dict, Set, Optional, Union

# プロジェクトルートを追加
PROJECT_ROOT = Path(__file__).parent.parent.parent
sys.path.insert(0, str(PROJECT_ROOT))

from scripts.setup.nikkei225_tickers import (
    NIKKEI225_TICKERS,
    SECTOR_MAP,
    TOPIX_CORE30,
    MAJOR_TICKERS,
    get_tickers_by_sector,
    get_all_tickers,
    get_topix_core30,
    get_major_tickers,
    get_sector_names
)

logger = logging.getLogger(__name__)

class TickerRegistry:
    """
    中央集権的ティッカー管理クラス
    
    機能：
    - 全銘柄データの統一管理
    - ティッカーリストの検証
    - 自動重複チェック
    - 業種別分類管理
    - データ整合性監視
    """
    
    def __init__(self):
        self._official_tickers: List[str] = NIKKEI225_TICKERS.copy()
        self._sector_map: Dict[str, List[str]] = SECTOR_MAP.copy()
        self._topix_core30: List[str] = TOPIX_CORE30.copy()
        self._major_tickers: List[str] = MAJOR_TICKERS.copy()
        self._metadata: Dict[str, any] = {}
        self._last_validation: Optional[datetime] = None
        
        # 初期化時に検証実行
        self._validate_consistency()
    
    def get_official_nikkei225_tickers(self) -> List[str]:
        """日経225公式銘柄リストを取得"""
        return self._official_tickers.copy()
    
    def get_sector_tickers(self, sector: str) -> List[str]:
        """指定業種の銘柄リストを取得"""
        return self._sector_map.get(sector, []).copy()
    
    def get_all_sectors(self) -> List[str]:
        """全業種名リストを取得"""
        return list(self._sector_map.keys())
    
    def get_topix_core30_tickers(self) -> List[str]:
        """TOPIX Core30銘柄リストを取得"""
        return self._topix_core30.copy()
    
    def get_major_tickers(self) -> List[str]:
        """主要銘柄リストを取得"""
        return self._major_tickers.copy()
    
    def is_valid_ticker(self, ticker: str) -> bool:
        """ティッカーが有効な日経225銘柄か確認"""
        return ticker in self._official_tickers
    
    def get_ticker_sector(self, ticker: str) -> Optional[str]:
        """ティッカーの業種を取得"""
        for sector, tickers in self._sector_map.items():
            if ticker in tickers:
                return sector
        return None
    
    def get_ticker_info(self, ticker: str) -> Dict[str, any]:
        """ティッカーの詳細情報を取得"""
        if not self.is_valid_ticker(ticker):
            return {"error": f"無効なティッカー: {ticker}"}
        
        return {
            "ticker": ticker,
            "sector": self.get_ticker_sector(ticker),
            "is_topix_core30": ticker in self._topix_core30,
            "is_major_ticker": ticker in self._major_tickers,
            "valid": True
        }
    
    def validate_ticker_list(self, tickers: List[str]) -> Dict[str, any]:
        """ティッカーリストの検証"""
        invalid_tickers = [t for t in tickers if not self.is_valid_ticker(t)]
        duplicates = self._find_duplicates(tickers)
        
        return {
            "total_count": len(tickers),
            "unique_count": len(set(tickers)),
            "valid_count": len(tickers) - len(invalid_tickers),
            "invalid_tickers": invalid_tickers,
            "duplicates": duplicates,
            "is_valid": len(invalid_tickers) == 0 and len(duplicates) == 0
        }
    
    def get_registry_stats(self) -> Dict[str, any]:
        """レジストリ統計情報を取得"""
        sector_stats = {
            sector: len(tickers) 
            for sector, tickers in self._sector_map.items()
        }
        
        return {
            "total_nikkei225_tickers": len(self._official_tickers),
            "total_sectors": len(self._sector_map),
            "topix_core30_count": len(self._topix_core30),
            "major_tickers_count": len(self._major_tickers),
            "sector_distribution": sector_stats,
            "last_validation": self._last_validation.isoformat() if self._last_validation else None,
            "registry_health": self._get_health_status()
        }
    
    def export_to_json(self, filepath: str) -> bool:
        """レジストリデータをJSONで出力"""
        try:
            export_data = {
                "metadata": {
                    "export_time": datetime.now().isoformat(),
                    "version": "1.0",
                    "source": "TickerRegistry"
                },
                "nikkei225_tickers": self._official_tickers,
                "sector_map": self._sector_map,
                "topix_core30": self._topix_core30,
                "major_tickers": self._major_tickers,
                "statistics": self.get_registry_stats()
            }
            
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(export_data, f, ensure_ascii=False, indent=2)
            
            logger.info(f"ティッカーレジストリをエクスポート: {filepath}")
            return True
            
        except Exception as e:
            logger.error(f"エクスポートエラー: {e}")
            return False
    
    def _validate_consistency(self) -> None:
        """データ整合性検証"""
        issues = []
        
        # 重複チェック
        duplicates = self._find_duplicates(self._official_tickers)
        if duplicates:
            issues.append(f"重複ティッカー: {duplicates}")
        
        # 銘柄数チェック
        if len(self._official_tickers) != 225:
            issues.append(f"銘柄数異常: {len(self._official_tickers)} (期待値: 225)")
        
        # セクターマップ整合性チェック
        sector_tickers = set()
        for tickers in self._sector_map.values():
            sector_tickers.update(tickers)
        
        official_set = set(self._official_tickers)
        sector_only = sector_tickers - official_set
        official_only = official_set - sector_tickers
        
        if sector_only:
            issues.append(f"セクターマップのみ存在: {list(sector_only)}")
        if official_only:
            issues.append(f"公式リストのみ存在: {list(official_only)}")
        
        self._last_validation = datetime.now()
        
        if issues:
            logger.warning(f"データ整合性の問題: {issues}")
        else:
            logger.info("データ整合性検証完了 - 問題なし")
    
    def _find_duplicates(self, items: List[str]) -> List[str]:
        """重複要素を検出"""
        seen = set()
        duplicates = set()
        
        for item in items:
            if item in seen:
                duplicates.add(item)
            else:
                seen.add(item)
        
        return list(duplicates)
    
    def _get_health_status(self) -> str:
        """レジストリの健全性ステータス"""
        if not self._last_validation:
            return "未検証"
        
        # 基本チェック
        if len(self._official_tickers) != 225:
            return "異常"
        
        if self._find_duplicates(self._official_tickers):
            return "異常"
        
        return "正常"

# グローバルインスタンス
_ticker_registry = None

def get_ticker_registry() -> TickerRegistry:
    """ティッカーレジストリのシングルトンインスタンスを取得"""
    global _ticker_registry
    if _ticker_registry is None:
        _ticker_registry = TickerRegistry()
    return _ticker_registry

# 便利関数
def get_validated_nikkei225_tickers() -> List[str]:
    """検証済みの日経225銘柄リストを取得"""
    return get_ticker_registry().get_official_nikkei225_tickers()

def validate_tickers(tickers: List[str]) -> Dict[str, any]:
    """ティッカーリストを検証"""
    return get_ticker_registry().validate_ticker_list(tickers)

def is_nikkei225_ticker(ticker: str) -> bool:
    """日経225銘柄かどうか確認"""
    return get_ticker_registry().is_valid_ticker(ticker)

if __name__ == "__main__":
    # デモ実行
    registry = get_ticker_registry()
    
    print("=== ティッカーレジストリ統計 ===")
    stats = registry.get_registry_stats()
    print(f"日経225銘柄数: {stats['total_nikkei225_tickers']}")
    print(f"業種数: {stats['total_sectors']}")
    print(f"レジストリ状態: {stats['registry_health']}")
    
    print("\n=== 業種別銘柄数 ===")
    for sector, count in stats['sector_distribution'].items():
        print(f"  {sector}: {count}銘柄")
    
    # サンプル検証
    sample_tickers = ["7203.T", "9984.T", "INVALID.T"]
    validation = registry.validate_ticker_list(sample_tickers)
    print(f"\n=== サンプル検証結果 ===")
    print(f"検証対象: {sample_tickers}")
    print(f"検証結果: {validation}")