#!/usr/bin/env python3
# Force CPU only mode first, before any TensorFlow imports
import os
# Force TensorFlow to use CPU only
os.environ["CUDA_VISIBLE_DEVICES"] = "-1"

import sys
import logging
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import time
import threading
import queue
import pytz
import schedule
import yfinance as yf
from collections import defaultdict

from data_collection import DataCollector
from feature_engineering import FeatureEngineer
from model import NikkeiAIModel

# Create necessary directories
os.makedirs("logs", exist_ok=True)
os.makedirs("results", exist_ok=True)
os.makedirs("data", exist_ok=True)

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(os.path.join("logs", f"batch_processor_{datetime.now().strftime('%Y%m%d')}.log")),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class StockMonitor:
    """
    Monitor stocks at a high frequency and execute actions based on prediction
    """
    def __init__(self, tickers=None, interval="5m", monitoring_period=60, model=None):
        """
        Initialize stock monitor
        
        Parameters:
        -----------
        tickers : list
            List of ticker symbols to monitor
        interval : str
            Monitoring interval (default: 5m)
        monitoring_period : int
            How long to monitor in minutes before checking for profitability
        model : NikkeiAIModel
            Model for predictions
        """
        self.tickers = tickers if tickers else []
        self.interval = interval
        self.monitoring_period = monitoring_period
        self.model = model
        
        # Store monitoring data
        self.monitoring_data = defaultdict(list)
        self.profits = defaultdict(float)
        self.start_prices = {}
        
        # Threads
        self.monitoring_threads = {}
        self.thread_stop_events = {}
        self.data_queue = queue.Queue()
        self.processing_thread = None
        self.stop_processing = threading.Event()
        
    def start_monitoring(self):
        """Start the monitoring process"""
        logger.info(f"Starting monitoring for {len(self.tickers)} tickers")
        
        # Start data processing thread
        self.processing_thread = threading.Thread(target=self._process_data)
        self.processing_thread.daemon = True
        self.processing_thread.start()
        
        # Start monitoring threads for each ticker
        for ticker in self.tickers:
            self.add_ticker_to_monitor(ticker)
    
    def add_ticker_to_monitor(self, ticker):
        """Add a new ticker to monitor"""
        if ticker in self.monitoring_threads:
            logger.warning(f"Ticker {ticker} is already being monitored")
            return
        
        logger.info(f"Adding ticker {ticker} to monitoring list")
        
        # Get current price
        try:
            ticker_data = yf.Ticker(ticker)
            current_price = ticker_data.history(period="1d", interval="1m").iloc[-1]['Close']
            self.start_prices[ticker] = current_price
        except Exception as e:
            logger.error(f"Error getting current price for {ticker}: {str(e)}")
            current_price = 0
            
        # Create stop event for this thread
        stop_event = threading.Event()
        self.thread_stop_events[ticker] = stop_event
        
        # Create and start monitoring thread
        thread = threading.Thread(
            target=self._monitor_ticker,
            args=(ticker, stop_event)
        )
        thread.daemon = True
        thread.start()
        
        self.monitoring_threads[ticker] = thread
        
    def remove_ticker_from_monitor(self, ticker):
        """Remove a ticker from monitoring"""
        if ticker not in self.monitoring_threads:
            logger.warning(f"Ticker {ticker} is not being monitored")
            return
        
        logger.info(f"Removing ticker {ticker} from monitoring list")
        
        # Signal thread to stop
        self.thread_stop_events[ticker].set()
        
        # Wait for thread to finish
        self.monitoring_threads[ticker].join(timeout=5)
        
        # Clean up
        del self.monitoring_threads[ticker]
        del self.thread_stop_events[ticker]
        
        # Calculate final profit
        if ticker in self.start_prices:
            try:
                ticker_data = yf.Ticker(ticker)
                current_price = ticker_data.history(period="1d", interval="1m").iloc[-1]['Close']
                profit_pct = (current_price - self.start_prices[ticker]) / self.start_prices[ticker] * 100
                self.profits[ticker] = profit_pct
                logger.info(f"Final profit for {ticker}: {profit_pct:.2f}%")
            except Exception as e:
                logger.error(f"Error calculating profit for {ticker}: {str(e)}")
    
    def _monitor_ticker(self, ticker, stop_event):
        """
        Monitor a single ticker
        
        Parameters:
        -----------
        ticker : str
            Ticker symbol
        stop_event : threading.Event
            Event to signal thread to stop
        """
        logger.info(f"Started monitoring thread for {ticker}")
        
        while not stop_event.is_set():
            try:
                # Get latest data
                ticker_data = yf.Ticker(ticker)
                history = ticker_data.history(period="1d", interval=self.interval)
                
                if not history.empty:
                    latest_data = history.iloc[-1]
                    
                    # Put data in queue for processing
                    self.data_queue.put({
                        'ticker': ticker,
                        'timestamp': datetime.now(),
                        'price': latest_data['Close'],
                        'volume': latest_data['Volume']
                    })
                    
                    # Check if profitable
                    if ticker in self.start_prices:
                        profit_pct = (latest_data['Close'] - self.start_prices[ticker]) / self.start_prices[ticker] * 100
                        self.profits[ticker] = profit_pct
                        
                        # If profitable, signal to stop monitoring
                        if profit_pct > 0:
                            logger.info(f"Ticker {ticker} is now profitable ({profit_pct:.2f}%). Recommending to sell.")
                            stop_event.set()
                
            except Exception as e:
                logger.error(f"Error monitoring {ticker}: {str(e)}")
            
            # Sleep for a while to avoid API rate limits
            # yfinance free tier allows ~2000 requests per hour, so we need to be careful
            time.sleep(60)  # Sleep for 1 minute between checks
    
    def _process_data(self):
        """Process data from the queue"""
        while not self.stop_processing.is_set():
            try:
                # Get data from queue with timeout
                data = self.data_queue.get(timeout=1)
                
                # Store data
                self.monitoring_data[data['ticker']].append(data)
                
                # Log data
                logger.debug(f"Processed data for {data['ticker']}: {data['price']}")
                
                # Mark task as done
                self.data_queue.task_done()
                
            except queue.Empty:
                # Queue is empty, just continue
                pass
            except Exception as e:
                logger.error(f"Error processing data: {str(e)}")
    
    def stop_all_monitoring(self):
        """Stop all monitoring threads"""
        logger.info("Stopping all monitoring threads")
        
        # Signal all threads to stop
        for ticker, stop_event in self.thread_stop_events.items():
            stop_event.set()
        
        # Wait for all threads to finish
        for ticker, thread in self.monitoring_threads.items():
            thread.join(timeout=5)
        
        # Signal processing thread to stop
        self.stop_processing.set()
        
        if self.processing_thread:
            self.processing_thread.join(timeout=5)
        
        # Clear data
        self.monitoring_threads.clear()
        self.thread_stop_events.clear()
        
        logger.info("All monitoring threads stopped")
    
    def get_profits(self):
        """Get profit data for all monitored tickers"""
        return self.profits

class BatchProcessor:
    """
    Process Nikkei 225 stocks in batch
    """
    def __init__(self):
        """Initialize batch processor"""
        self.model = None
        self.monitor = None
        self.jst_timezone = pytz.timezone('Asia/Tokyo')
        self.market_open_time = "09:00"  # JST
        self.market_close_time = "15:00"  # JST
        self.last_predictions = None
        self.performance_tracker = defaultdict(list)
        
        # Initialize directories
        os.makedirs("data", exist_ok=True)
        os.makedirs("models", exist_ok=True)
        os.makedirs("results", exist_ok=True)
        os.makedirs("logs", exist_ok=True)
        
    def is_market_open(self):
        """Check if the market is currently open"""
        now = datetime.now(self.jst_timezone)
        
        # Market is closed on weekends
        if now.weekday() >= 5:  # 5=Saturday, 6=Sunday
            return False
        
        # Check if current time is within market hours
        market_open = datetime.strptime(self.market_open_time, "%H:%M").time()
        market_close = datetime.strptime(self.market_close_time, "%H:%M").time()
        
        return market_open <= now.time() <= market_close
    
    def setup_model(self):
        """Setup and train the model if needed"""
        logger.info("Setting up model")
        
        # Check if we have a trained model
        if os.path.exists(os.path.join("models", "nikkei_all_lstm.h5")):
            logger.info("Loading existing model")
            self.model = NikkeiAIModel(model_dir="models", data_dir="data")
            return True
        
        # Train a new model
        logger.info("Training new model")
        
        # Skip data collection - using synthetic test data
        logger.info("Using synthetic test data")
        
        # Engineer features
        feature_engineer = FeatureEngineer(
            input_file=os.path.join("data", "nikkei225_cleaned.csv"),
            output_dir="data"
        )
        processed_data = feature_engineer.process_and_save(include_market_features=False)  # Disable market features
        
        # 3. Initialize model
        self.model = NikkeiAIModel(model_dir="models", data_dir="data")
        
        # 4. Prepare features
        prepared_data = self.model.prepare_features(
            processed_data,
            ticker=None,  # Train on all tickers
            target_col='Close',
            sequence_length=24,
            prediction_horizon=1,  # 1 hour ahead
            train_ratio=0.8
        )
        
        # 5. Train models
        results = self.model.train_models(
            prepared_data,
            ticker="nikkei_all",
            epochs=50,
            batch_size=32
        )
        
        # 6. Save model metadata
        self.model.save_model_metadata("nikkei_all", results)
        
        return True
    
    def run_hourly_batch(self):
        """Run the hourly batch job"""
        current_time = datetime.now(self.jst_timezone)
        logger.info(f"Running hourly batch at {current_time}")
        
        # Always run in test mode, even if market is closed
        # if not self.is_market_open():
        #     logger.info("Market is closed. Skipping batch.")
        #     return
        
        logger.info("Using synthetic test data")
        
        # Engineer features
        logger.info("Engineering features")
        feature_engineer = FeatureEngineer(
            input_file=os.path.join("data", "nikkei225_cleaned.csv"),
            output_dir="data"
        )
        processed_data = feature_engineer.process_and_save(include_market_features=False)
        
        # 3. Make predictions for all tickers
        logger.info("Making predictions")
        top_stocks = self.model.evaluate_and_select_stocks(
            processed_data,
            prediction_horizon=1,  # 1 hour ahead
            top_n=5  # Select top 5 stocks
        )
        
        # 4. Save predictions
        logger.info("Saving predictions")
        results_file = os.path.join("results", f"top_stocks_{current_time.strftime('%Y%m%d_%H%M')}.csv")
        top_stocks.to_csv(results_file, index=False)
        
        # 5. Update monitor
        if self.monitor:
            # Stop monitoring previous tickers
            self.monitor.stop_all_monitoring()
            
            # Save performance data from previous hour
            if self.last_predictions is not None:
                profits = self.monitor.get_profits()
                for ticker in self.last_predictions['Ticker']:
                    if ticker in profits:
                        self.performance_tracker[ticker].append({
                            'timestamp': current_time,
                            'profit': profits[ticker]
                        })
        
        # 6. Create new monitor for this hour's tickers
        logger.info("Setting up monitoring for new tickers")
        if not top_stocks.empty and 'Ticker' in top_stocks.columns:
            tickers_to_monitor = top_stocks['Ticker'].tolist()
            self.monitor = StockMonitor(
                tickers=tickers_to_monitor,
                interval="5m",
                monitoring_period=60,
                model=self.model
            )
            
            # Start monitoring
            self.monitor.start_monitoring()
            
            # Store this hour's predictions
            self.last_predictions = top_stocks
        else:
            logger.warning("No stocks to monitor - using empty list")
            self.monitor = StockMonitor(
                tickers=[],
                interval="5m",
                monitoring_period=60,
                model=self.model
            )
            self.last_predictions = None
        
        
        # 7. Log performance metrics
        self._log_performance()
        
        if not top_stocks.empty and 'Ticker' in top_stocks.columns:
            logger.info(f"Batch completed. Top 5 stocks: {', '.join(top_stocks['Ticker'].tolist())}")
        else:
            logger.info("Batch completed. No valid stocks selected.")
        return top_stocks
    
    def _log_performance(self):
        """Log performance metrics"""
        if not self.performance_tracker:
            return
        
        # Calculate daily performance
        today = datetime.now(self.jst_timezone).date()
        daily_profits = {}
        
        for ticker, data in self.performance_tracker.items():
            daily_data = [entry for entry in data if entry['timestamp'].date() == today]
            if daily_data:
                daily_profits[ticker] = sum(entry['profit'] for entry in daily_data) / len(daily_data)
        
        # Save to CSV
        if daily_profits:
            df = pd.DataFrame([
                {'Date': today, 'Ticker': ticker, 'Profit': profit}
                for ticker, profit in daily_profits.items()
            ])
            
            performance_file = os.path.join("results", f"performance_{today.strftime('%Y%m%d')}.csv")
            
            # Append to existing file if it exists
            if os.path.exists(performance_file):
                existing_df = pd.read_csv(performance_file)
                df = pd.concat([existing_df, df])
            
            df.to_csv(performance_file, index=False)
            
            logger.info(f"Performance logged to {performance_file}")
            logger.info(f"Daily average profits: {daily_profits}")
    
    def schedule_jobs(self):
        """Schedule the batch jobs"""
        logger.info("Scheduling batch jobs")
        
        # Setup model
        self.setup_model()
        
        # Schedule hourly job during market hours
        schedule.every().hour.at(":00").do(self.run_hourly_batch)
        
        # Also run once at startup
        self.run_hourly_batch()
        
        logger.info("Jobs scheduled")
        
        # Run the scheduler
        while True:
            schedule.run_pending()
            time.sleep(1)
    
    def shutdown(self):
        """Clean shutdown"""
        logger.info("Shutting down")
        
        if self.monitor:
            self.monitor.stop_all_monitoring()
        
        logger.info("Shutdown complete")

def main():
    """Main function"""
    logger.info("Starting batch processor")
    
    processor = BatchProcessor()
    
    try:
        processor.schedule_jobs()
    except KeyboardInterrupt:
        logger.info("Keyboard interrupt received. Shutting down.")
        processor.shutdown()
    except Exception as e:
        logger.error(f"Error in batch processor: {str(e)}")
        processor.shutdown()
        raise

if __name__ == "__main__":
    main()
