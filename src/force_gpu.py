#!/usr/bin/env python3
"""
GPU強制実行モード（非推奨 - gpu_config.pyを使用することを推奨）
"""

import os
import sys
import warnings
from pathlib import Path

# プロジェクトルートに移動
ROOT_DIR = Path(__file__).parent
sys.path.insert(0, str(ROOT_DIR))

# 新しいGPU設定を使用
try:
    from gpu_config import initialize_gpu, force_cpu_mode
    warnings.warn(
        "force_gpu.py は非推奨です。gpu_config.initialize_gpu() を使用してください。",
        DeprecationWarning,
        stacklevel=2
    )
    
    def configure_gpu():
        """
        GPU環境の最適設定 (新しいAPIを使用)
        """
        return initialize_gpu()
    
    print("新しいGPU設定システムを使用しています...")
    
except ImportError:
    # フォールバック: 元の実装
    import tensorflow as tf
    import torch
    
    def configure_gpu():
        """
        GPU環境の最適設定 (フォールバック実装)
        """
        print("⚠️ フォールバック実装を使用しています")
        
        # TensorFlow GPU設定
        try:
            gpus = tf.config.experimental.list_physical_devices('GPU')
            if gpus:
                # GPU Memory Growth有効化（必要に応じてメモリ拡張）
                for gpu in gpus:
                    try:
                        tf.config.experimental.set_memory_growth(gpu, True)
                    except RuntimeError as e:
                        if "cannot be modified" not in str(e):
                            raise
                        
                # Mixed Precision（FP16）有効化で高速化
                try:
                    tf.keras.mixed_precision.set_global_policy('mixed_float16')
                except Exception as e:
                    print(f"Mixed precision設定失敗: {e}")
                    
                print(f"✅ TensorFlow GPU設定完了: {len(gpus)}基のGPU検出")
            else:
                print("⚠️ TensorFlow: GPU未検出、CPU使用")
        except Exception as e:
            print(f"⚠️ TensorFlow GPU設定エラー: {e}")
        
        # PyTorch GPU確認
        try:
            if torch.cuda.is_available():
                device = torch.device('cuda')
                print(f"✅ PyTorch GPU使用可能: {torch.cuda.get_device_name()}")
                print(f"✅ VRAM: {torch.cuda.get_device_properties(0).total_memory // 1024**3}GB")
                
                # CUDA最適化設定
                torch.backends.cudnn.benchmark = True  # 高速化
                torch.backends.cudnn.deterministic = False  # 非決定論的だが高速
                
                return {'pytorch_device': device, 'tensorflow_gpu': True}
            else:
                print("⚠️ PyTorch: CUDA未使用、CPU使用")
                return {'pytorch_device': torch.device('cpu'), 'tensorflow_gpu': False}
        except Exception as e:
            print(f"⚠️ PyTorch設定エラー: {e}")
            return {'pytorch_device': torch.device('cpu'), 'tensorflow_gpu': False}

if __name__ == "__main__":
    configure_gpu()