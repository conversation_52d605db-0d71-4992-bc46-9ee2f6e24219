#!/usr/bin/env python3
import numpy as np
import pandas as pd

def SMA(close, timeperiod=20):
    """
    Simple Moving Average
    
    Parameters:
    -----------
    close : np.array
        Close price array
    timeperiod : int
        Period for MA calculation
        
    Returns:
    --------
    np.array
        SMA values
    """
    return pd.Series(close).rolling(window=timeperiod).mean().values

def EMA(close, timeperiod=20):
    """
    Exponential Moving Average
    
    Parameters:
    -----------
    close : np.array
        Close price array
    timeperiod : int
        Period for EMA calculation
        
    Returns:
    --------
    np.array
        EMA values
    """
    return pd.Series(close).ewm(span=timeperiod, adjust=False).mean().values

def WMA(close, timeperiod=20):
    """
    Weighted Moving Average
    
    Parameters:
    -----------
    close : np.array
        Close price array
    timeperiod : int
        Period for WMA calculation
        
    Returns:
    --------
    np.array
        WMA values
    """
    weights = np.arange(1, timeperiod + 1)
    return pd.Series(close).rolling(timeperiod).apply(
        lambda x: np.sum(weights * x) / weights.sum(), raw=True
    ).values

def BBANDS(close, timeperiod=20, nbdevup=2, nbdevdn=2):
    """
    Bollinger Bands
    
    Parameters:
    -----------
    close : np.array
        Close price array
    timeperiod : int
        Period for SMA calculation
    nbdevup : float
        Standard deviation multiplier for upper band
    nbdevdn : float
        Standard deviation multiplier for lower band
        
    Returns:
    --------
    tuple
        (upperband, middleband, lowerband)
    """
    series = pd.Series(close)
    middleband = series.rolling(window=timeperiod).mean()
    stdev = series.rolling(window=timeperiod).std()
    upperband = middleband + nbdevup * stdev
    lowerband = middleband - nbdevdn * stdev
    return upperband.values, middleband.values, lowerband.values

def RSI(close, timeperiod=14):
    """
    Relative Strength Index
    
    Parameters:
    -----------
    close : np.array
        Close price array
    timeperiod : int
        Period for RSI calculation
        
    Returns:
    --------
    np.array
        RSI values
    """
    series = pd.Series(close)
    delta = series.diff()
    up, down = delta.copy(), delta.copy()
    up[up < 0] = 0
    down[down > 0] = 0
    down = down.abs()
    
    avg_gain = up.rolling(window=timeperiod).mean()
    avg_loss = down.rolling(window=timeperiod).mean()
    
    rs = avg_gain / avg_loss
    rsi = 100 - (100 / (1 + rs))
    return rsi.values

def MACD(close, fastperiod=12, slowperiod=26, signalperiod=9):
    """
    Moving Average Convergence/Divergence
    
    Parameters:
    -----------
    close : np.array
        Close price array
    fastperiod : int
        Fast period
    slowperiod : int
        Slow period
    signalperiod : int
        Signal period
        
    Returns:
    --------
    tuple
        (macd, signal, histogram)
    """
    fast_ema = pd.Series(close).ewm(span=fastperiod, adjust=False).mean()
    slow_ema = pd.Series(close).ewm(span=slowperiod, adjust=False).mean()
    macd = fast_ema - slow_ema
    signal = macd.ewm(span=signalperiod, adjust=False).mean()
    histogram = macd - signal
    return macd.values, signal.values, histogram.values

def ATR(high, low, close, timeperiod=14):
    """
    Average True Range
    
    Parameters:
    -----------
    high : np.array
        High price array
    low : np.array
        Low price array
    close : np.array
        Close price array
    timeperiod : int
        Period for ATR calculation
        
    Returns:
    --------
    np.array
        ATR values
    """
    high_s = pd.Series(high)
    low_s = pd.Series(low)
    close_s = pd.Series(close)
    
    prev_close = close_s.shift(1)
    tr1 = high_s - low_s
    tr2 = (high_s - prev_close).abs()
    tr3 = (low_s - prev_close).abs()
    
    tr = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)
    atr = tr.rolling(window=timeperiod).mean()
    return atr.values

def OBV(close, volume):
    """
    On-Balance Volume
    
    Parameters:
    -----------
    close : np.array
        Close price array
    volume : np.array
        Volume array
        
    Returns:
    --------
    np.array
        OBV values
    """
    close_s = pd.Series(close)
    volume_s = pd.Series(volume)
    
    direction = np.sign(close_s.diff())
    direction[0] = 0  # First element has no direction
    
    obv = (direction * volume_s).cumsum()
    return obv.values

def AD(high, low, close, volume):
    """
    Chaikin A/D Line
    
    Parameters:
    -----------
    high : np.array
        High price array
    low : np.array
        Low price array
    close : np.array
        Close price array
    volume : np.array
        Volume array
        
    Returns:
    --------
    np.array
        A/D Line values
    """
    high_s = pd.Series(high)
    low_s = pd.Series(low)
    close_s = pd.Series(close)
    volume_s = pd.Series(volume)
    
    mfm = ((close_s - low_s) - (high_s - close_s)) / (high_s - low_s)
    mfm = mfm.fillna(0)  # Handle division by zero
    
    mfv = mfm * volume_s
    ad = mfv.cumsum()
    return ad.values

def MFI(high, low, close, volume, timeperiod=14):
    """
    Money Flow Index
    
    Parameters:
    -----------
    high : np.array
        High price array
    low : np.array
        Low price array
    close : np.array
        Close price array
    volume : np.array
        Volume array
    timeperiod : int
        Period for MFI calculation
        
    Returns:
    --------
    np.array
        MFI values
    """
    high_s = pd.Series(high)
    low_s = pd.Series(low)
    close_s = pd.Series(close)
    volume_s = pd.Series(volume)
    
    # Calculate typical price
    tp = (high_s + low_s + close_s) / 3
    
    # Calculate money flow
    mf = tp * volume_s
    
    # Get the direction
    direction = np.sign(tp.diff())
    
    # Create positive and negative money flow series
    pos_mf = pd.Series(np.where(direction > 0, mf, 0))
    neg_mf = pd.Series(np.where(direction < 0, mf, 0))
    
    # Calculate the money flow ratio
    pos_mf_sum = pos_mf.rolling(window=timeperiod).sum()
    neg_mf_sum = neg_mf.rolling(window=timeperiod).sum()
    
    # Calculate MFI
    mfi = 100 - (100 / (1 + (pos_mf_sum / neg_mf_sum)))
    
    return mfi.values

def ROC(close, timeperiod=10):
    """
    Rate of Change
    
    Parameters:
    -----------
    close : np.array
        Close price array
    timeperiod : int
        Period for ROC calculation
        
    Returns:
    --------
    np.array
        ROC values
    """
    close_s = pd.Series(close)
    roc = (close_s / close_s.shift(timeperiod) - 1) * 100
    return roc.values

def CCI(high, low, close, timeperiod=14):
    """
    Commodity Channel Index
    
    Parameters:
    -----------
    high : np.array
        High price array
    low : np.array
        Low price array
    close : np.array
        Close price array
    timeperiod : int
        Period for CCI calculation
        
    Returns:
    --------
    np.array
        CCI values
    """
    high_s = pd.Series(high)
    low_s = pd.Series(low)
    close_s = pd.Series(close)
    
    tp = (high_s + low_s + close_s) / 3
    tp_sma = tp.rolling(window=timeperiod).mean()
    tp_dev = tp - tp_sma
    tp_mad = tp.rolling(window=timeperiod).apply(lambda x: np.abs(x - x.mean()).mean())
    
    cci = tp_dev / (0.015 * tp_mad)
    return cci.values

def ADX(high, low, close, timeperiod=14):
    """
    Average Directional Index
    
    Parameters:
    -----------
    high : np.array
        High price array
    low : np.array
        Low price array
    close : np.array
        Close price array
    timeperiod : int
        Period for ADX calculation
        
    Returns:
    --------
    np.array
        ADX values
    """
    high_s = pd.Series(high)
    low_s = pd.Series(low)
    close_s = pd.Series(close)
    
    # Calculate True Range
    prev_close = close_s.shift(1)
    tr1 = high_s - low_s
    tr2 = (high_s - prev_close).abs()
    tr3 = (low_s - prev_close).abs()
    tr = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)
    
    # Calculate directional movement
    up_move = high_s - high_s.shift(1)
    down_move = low_s.shift(1) - low_s
    
    # Calculate Directional Indicators
    pos_dm = np.where((up_move > down_move) & (up_move > 0), up_move, 0)
    neg_dm = np.where((down_move > up_move) & (down_move > 0), down_move, 0)
    
    # Calculate smoothed values
    smoothed_tr = tr.rolling(window=timeperiod).sum()
    smoothed_pos_dm = pd.Series(pos_dm).rolling(window=timeperiod).sum()
    smoothed_neg_dm = pd.Series(neg_dm).rolling(window=timeperiod).sum()
    
    # Calculate directional indices
    pos_di = 100 * (smoothed_pos_dm / smoothed_tr)
    neg_di = 100 * (smoothed_neg_dm / smoothed_tr)
    
    # Calculate directional index difference and sum
    di_diff = (pos_di - neg_di).abs()
    di_sum = pos_di + neg_di
    
    # Calculate ADX
    dx = 100 * (di_diff / di_sum)
    adx = dx.rolling(window=timeperiod).mean()
    
    return adx.values

def SAR(high, low, acceleration=0.02, maximum=0.2):
    """
    Parabolic SAR
    
    Parameters:
    -----------
    high : np.array
        High price array
    low : np.array
        Low price array
    acceleration : float
        Acceleration factor
    maximum : float
        Maximum acceleration factor
        
    Returns:
    --------
    np.array
        SAR values
    """
    high_s = pd.Series(high)
    low_s = pd.Series(low)
    
    # Initialize values
    sar = np.zeros_like(high)
    trend = np.zeros_like(high)
    ep = np.zeros_like(high)
    af = np.zeros_like(high)
    
    # Set initial values
    sar[0] = low_s.iloc[0]
    trend[0] = 1  # Start with uptrend
    ep[0] = high_s.iloc[0]
    af[0] = acceleration
    
    # Calculate SAR
    for i in range(1, len(high)):
        if trend[i-1] == 1:  # Uptrend
            sar[i] = sar[i-1] + af[i-1] * (ep[i-1] - sar[i-1])
            
            # Ensure SAR is not above the prior two lows
            if i >= 2:
                sar[i] = min(sar[i], low_s.iloc[i-1], low_s.iloc[i-2])
            
            # Check for trend change
            if low_s.iloc[i] < sar[i]:
                trend[i] = -1  # Change to downtrend
                sar[i] = ep[i-1]
                ep[i] = low_s.iloc[i]
                af[i] = acceleration
            else:
                trend[i] = 1  # Continue uptrend
                if high_s.iloc[i] > ep[i-1]:
                    ep[i] = high_s.iloc[i]
                    af[i] = min(af[i-1] + acceleration, maximum)
                else:
                    ep[i] = ep[i-1]
                    af[i] = af[i-1]
        else:  # Downtrend
            sar[i] = sar[i-1] - af[i-1] * (sar[i-1] - ep[i-1])
            
            # Ensure SAR is not below the prior two highs
            if i >= 2:
                sar[i] = max(sar[i], high_s.iloc[i-1], high_s.iloc[i-2])
            
            # Check for trend change
            if high_s.iloc[i] > sar[i]:
                trend[i] = 1  # Change to uptrend
                sar[i] = ep[i-1]
                ep[i] = high_s.iloc[i]
                af[i] = acceleration
            else:
                trend[i] = -1  # Continue downtrend
                if low_s.iloc[i] < ep[i-1]:
                    ep[i] = low_s.iloc[i]
                    af[i] = min(af[i-1] + acceleration, maximum)
                else:
                    ep[i] = ep[i-1]
                    af[i] = af[i-1]
    
    return sar

def STDDEV(close, timeperiod=5, nbdev=1):
    """
    Standard Deviation
    
    Parameters:
    -----------
    close : np.array
        Close price array
    timeperiod : int
        Period for calculation
    nbdev : float
        Multiplier for standard deviation
        
    Returns:
    --------
    np.array
        Standard deviation values
    """
    return pd.Series(close).rolling(window=timeperiod).std().values * nbdev

# Candlestick pattern recognition functions
def CDLDOJI(open_prices, high, low, close):
    """
    Doji candlestick pattern
    
    Parameters:
    -----------
    open_prices : np.array
        Open price array
    high : np.array
        High price array
    low : np.array
        Low price array
    close : np.array
        Close price array
        
    Returns:
    --------
    np.array
        Pattern recognition (0 for no pattern, 100 for pattern)
    """
    body_size = np.abs(close - open_prices)
    avg_body_size = pd.Series(body_size).rolling(window=10).mean().values
    total_range = high - low
    
    # Doji has very small body compared to total range
    doji = np.where((body_size <= 0.1 * total_range) & 
                    (body_size <= 0.1 * avg_body_size) &
                    (total_range > 0),
                    100, 0)
    
    return doji

def CDLHAMMER(open_prices, high, low, close):
    """
    Hammer candlestick pattern
    
    Parameters:
    -----------
    open_prices : np.array
        Open price array
    high : np.array
        High price array
    low : np.array
        Low price array
    close : np.array
        Close price array
        
    Returns:
    --------
    np.array
        Pattern recognition (0 for no pattern, 100 for pattern)
    """
    body_size = np.abs(close - open_prices)
    total_range = high - low
    
    # For a hammer:
    # 1. Lower shadow should be at least twice the body
    # 2. Upper shadow should be small
    # 3. Body should be in the upper half of the candle
    lower_shadow = np.minimum(open_prices, close) - low
    upper_shadow = high - np.maximum(open_prices, close)
    
    hammer = np.where((lower_shadow >= 2 * body_size) & 
                      (upper_shadow <= 0.1 * total_range) &
                      (np.minimum(open_prices, close) >= (low + total_range * 0.5)) &
                      (total_range > 0),
                      100, 0)
    
    return hammer

def CDLENGULFING(open_prices, high, low, close):
    """
    Engulfing candlestick pattern
    
    Parameters:
    -----------
    open_prices : np.array
        Open price array
    high : np.array
        High price array
    low : np.array
        Low price array
    close : np.array
        Close price array
        
    Returns:
    --------
    np.array
        Pattern recognition (100 for bullish engulfing, -100 for bearish engulfing, 0 for no pattern)
    """
    prev_open = pd.Series(open_prices).shift(1).values
    prev_close = pd.Series(close).shift(1).values
    
    # Bullish engulfing: current candle opens below previous close and closes above previous open
    # Current candle is green (close > open) and previous is red (close < open)
    bullish = np.where((open_prices < prev_close) & 
                       (close > prev_open) &
                       (close > open_prices) &
                       (prev_close < prev_open),
                       100, 0)
    
    # Bearish engulfing: current candle opens above previous close and closes below previous open
    # Current candle is red (close < open) and previous is green (close > open)
    bearish = np.where((open_prices > prev_close) & 
                       (close < prev_open) &
                       (close < open_prices) &
                       (prev_close > prev_open),
                       -100, 0)
    
    # Combine patterns
    engulfing = bullish + bearish
    
    return engulfing
