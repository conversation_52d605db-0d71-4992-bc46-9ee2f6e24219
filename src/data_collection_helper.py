"""
日経225 AI取引システム - データ収集ヘルパー

このモジュールは、データ収集に関連する共通機能を提供します。
Yahoo Financeの制限を考慮し、安定したデータ取得を行います。
"""

import yfinance as yf
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import os
import time
import logging
import random
import requests
import json
from urllib.parse import urlencode
import threading

# Import ticker utilities
try:
    from .utils.nikkei225_tickers import get_all_tickers, get_major_tickers
except ImportError:
    # Fallback if module not available
    def get_all_tickers():
        return ["7203.T", "6758.T", "9984.T", "8306.T", "9432.T"]

    def get_major_tickers():
        return get_all_tickers()

# グローバルロック（スレッド間でのAPI呼び出しを制御）
API_LOCK = threading.RLock()
# 前回APIリクエスト時刻記録用
LAST_REQUEST_TIME = {}
# リクエスト間隔（分単位でのリクエスト数）
REQUEST_LIMIT = 250  # 300未満の値に設定
REQUEST_WINDOW = 60  # 60秒（1分）

# ユーザーエージェントのリスト
USER_AGENTS = [
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Safari/605.1.15",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:89.0) Gecko/20100101 Firefox/89.0",
    "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/90.0.4430.212 Safari/537.36",
    "Mozilla/5.0 (iPhone; CPU iPhone OS 14_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1",
    "Mozilla/5.0 (iPad; CPU OS 14_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1",
    "Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:88.0) Gecko/20100101 Firefox/88.0",
    "Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.114 Safari/537.36"
]

def get_random_user_agent():
    """
    ランダムなユーザーエージェントを返す
    """
    return random.choice(USER_AGENTS)

def wait_for_rate_limit():
    """
    レート制限回避のための待機
    1分間の最大リクエスト数を超えないようにする
    """
    with API_LOCK:
        now = time.time()
        thread_id = threading.get_ident()
        last_requests = LAST_REQUEST_TIME.get(thread_id, [])
        
        # 直近1分間のリクエスト数を確認
        cutoff = now - REQUEST_WINDOW
        recent_requests = [t for t in last_requests if t > cutoff]
        
        # 制限に達していたら待機
        if len(recent_requests) >= REQUEST_LIMIT:
            wait_time = recent_requests[0] + REQUEST_WINDOW - now
            if wait_time > 0:
                logging.info(f"API制限に達しました。{wait_time:.2f}秒待機します")
                time.sleep(wait_time + 0.1)  # 少し余裕を持たせる
        
        # 最新リクエスト時刻を記録
        LAST_REQUEST_TIME[thread_id] = recent_requests + [now]
        
        # ランダムな短い待機時間を追加（連続リクエストを避ける）
        jitter = random.uniform(0.1, 0.5)
        time.sleep(jitter)

def fetch_stock_data(tickers, start_date, end_date, interval="1d", use_cache=True, force_cache=False, use_random_fallback=False, max_retries=3):
    """
    指定した銘柄の株価データを取得する

    Parameters:
    ----------
    tickers : list or str
        取得する銘柄のリスト（例：['7203.T', '9984.T']）または単一銘柄
    start_date : str
        取得開始日（例: '2024-01-01'）
    end_date : str
        取得終了日（例: '2024-01-31'）
    interval : str, default '1d'
        データ間隔（例: '1h', '1d'）
    use_cache : bool, default True
        キャッシュを使用するかどうか
    force_cache : bool, default False
        APIを使わず強制的にキャッシュを使用するかどうか
    use_random_fallback : bool, default False
        実データ取得に失敗した場合にランダムデータを生成するかどうか
    max_retries : int, default 3
        データ取得の最大リトライ回数

    Returns:
    -------
    pandas.DataFrame
        取得した株価データ
    """
    logger = logging.getLogger(__name__)
    
    # キャッシュディレクトリの確認と生成
    cache_dir = os.path.join('data', 'cache')
    os.makedirs(cache_dir, exist_ok=True)

    # キャッシュファイル名の生成
    if isinstance(tickers, list):
        if len(tickers) <= 3:
            tickers_str = "_".join(tickers)
        else:
            tickers_str = f"{len(tickers)}_tickers"
    else:
        tickers_str = tickers
    
    cache_file = os.path.join(cache_dir, f"{tickers_str}_{start_date}_{end_date}_{interval}.csv")
    
    # キャッシュファイルが存在し、キャッシュを使用する設定なら読み込む
    if (use_cache or force_cache) and os.path.exists(cache_file):
        try:
            logger.info(f"キャッシュからデータを読み込み: {cache_file}")
            df_processed = pd.read_csv(cache_file, parse_dates=['Timestamp'])
            # キャッシュデータが空でなければ、そのまま返す
            if not df_processed.empty:
                return df_processed
            else:
                logger.warning("キャッシュファイルが空です")
        except Exception as e:
            logger.warning(f"キャッシュ読み込みエラー: {str(e)}")
    
    # force_cacheがTrueで、キャッシュがない場合はエラー
    if force_cache:
        logger.error("強制キャッシュモードだがキャッシュファイルが存在しないか読み込めません")
        # ランダムデータフォールバックが有効ならランダムデータを返す
        if use_random_fallback:
            logger.info("ランダムデータにフォールバックします")
            return generate_random_stock_data(tickers, start_date, end_date, interval)
        else:
            return pd.DataFrame()  # 空のDataFrameを返す

    # リトライループ
    for retry in range(max_retries):
        try:
            # API制限回避のための待機
            wait_for_rate_limit()
            
            # ユーザーエージェントの設定
            yf.set_tz_session_preload(None)  # セッションリセット
            user_agent = get_random_user_agent()
            session = requests.Session()
            session.headers.update({'User-Agent': user_agent})
            
            # バッチに分割してデータ取得（ティッカーが多い場合）
            df_all = None
            if isinstance(tickers, list) and len(tickers) > 5:
                logger.info(f"{len(tickers)}ティッカーのデータ取得を分割して実行")
                batch_size = 5
                ticker_batches = [tickers[i:i+batch_size] for i in range(0, len(tickers), batch_size)]
                
                for i, batch in enumerate(ticker_batches):
                    logger.info(f"バッチ {i+1}/{len(ticker_batches)} の取得開始")
                    # API制限回避のための待機（バッチごと）
                    if i > 0:
                        time.sleep(random.uniform(1.0, 2.0))
                    
                    # 日本株式のティッカーシンボル修正
                    fixed_batch = _fix_japan_tickers(batch)
                    
                    # yfinanceでデータ取得
                    df_batch = yf.download(
                        tickers=fixed_batch,
                        start=start_date,
                        end=end_date,
                        interval=interval,
                        group_by='ticker',
                        auto_adjust=True,
                        prepost=True,
                        threads=True,
                        session=session
                    )
                    
                    if not df_batch.empty:
                        # データの整形
                        df_processed_batch = _process_yfinance_data(df_batch, batch)
                        
                        if df_all is None:
                            df_all = df_processed_batch
                        else:
                            df_all = pd.concat([df_all, df_processed_batch])
            else:
                # 少数のティッカーの場合は一度に取得
                # 日本株式のティッカーシンボル修正
                fixed_tickers = _fix_japan_tickers(tickers)
                
                # yfinanceでデータ取得
                logger.info(f"YFinance APIを使用してデータを取得: {fixed_tickers}")
                df = yf.download(
                    tickers=fixed_tickers,
                    start=start_date,
                    end=end_date,
                    interval=interval,
                    group_by='ticker',
                    auto_adjust=True,
                    prepost=True,
                    threads=True,
                    session=session
                )
                
                if not df.empty:
                    df_all = _process_yfinance_data(df, tickers)
            
            # 取得失敗または空の場合
            if df_all is None or df_all.empty:
                if retry < max_retries - 1:
                    wait_time = (retry + 1) * 2  # 指数バックオフ
                    logger.warning(f"データ取得失敗または空のデータ。{wait_time}秒後にリトライします (リトライ {retry + 1}/{max_retries})")
                    time.sleep(wait_time)
                    continue
                else:
                    logger.error(f"データ取得失敗: 最大リトライ回数 ({max_retries}) に達しました")
                    if use_random_fallback:
                        logger.info("ランダムデータにフォールバックします")
                        return generate_random_stock_data(tickers, start_date, end_date, interval)
                    else:
                        return pd.DataFrame()
            
            # キャッシュに保存
            if use_cache and not df_all.empty:
                try:
                    df_all.to_csv(cache_file, index=False)
                    logger.info(f"データをキャッシュに保存: {cache_file}")
                except Exception as e:
                    logger.warning(f"キャッシュ保存エラー: {str(e)}")
            
            return df_all

        except Exception as e:
            logger.error(f"データ取得エラー: {str(e)}")
            if retry < max_retries - 1:
                wait_time = (retry + 1) * 2  # 指数バックオフ
                logger.warning(f"{wait_time}秒後にリトライします (リトライ {retry + 1}/{max_retries})")
                time.sleep(wait_time)
            else:
                logger.error(f"最大リトライ回数 ({max_retries}) に達しました")
                # ランダムデータフォールバックが有効ならランダムデータを返す
                if use_random_fallback:
                    logger.info("データ取得エラーのためランダムデータにフォールバックします")
                    return generate_random_stock_data(tickers, start_date, end_date, interval)
                else:
                    # エラー時は発生した例外をそのまま上に投げる
                    raise

def _process_yfinance_data(df, original_tickers):
    """
    YFinanceから取得したデータを処理する内部関数
    
    Parameters:
    ----------
    df : pd.DataFrame
        YFinanceから取得した生データ
    original_tickers : list or str
        元のティッカーリストまたは単一ティッカー
    
    Returns:
    -------
    pd.DataFrame
        処理済みデータフレーム
    """
    logger = logging.getLogger(__name__)
    
    if df.empty:
        return pd.DataFrame()
    
    # データの整形
    # 複数銘柄の場合とシングル銘柄の場合でフォーマットが異なる
    if isinstance(original_tickers, list) and len(original_tickers) > 1:
        # 複数銘柄の場合はマルチインデックスになるので、それを平坦化
        df_processed = pd.DataFrame()
        
        for i, ticker in enumerate(df.columns.levels[0] if hasattr(df.columns, 'levels') else []):
            try:
                ticker_df = df[ticker].copy() if hasattr(df.columns, 'levels') else df.copy()
                
                # 元のティッカーに戻す（日本株の場合）
                original_ticker = original_tickers[i] if i < len(original_tickers) else ticker
                ticker_df['Ticker'] = original_ticker
                
                ticker_df.reset_index(inplace=True)
                ticker_df.rename(columns={'index': 'Timestamp', 'Date': 'Timestamp'}, inplace=True)
                
                if df_processed.empty:
                    df_processed = ticker_df
                else:
                    df_processed = pd.concat([df_processed, ticker_df])
            except Exception as e:
                logger.warning(f"ティッカー {ticker} の処理中にエラー: {str(e)}")
                continue
    else:
        # 単一銘柄の場合
        df_processed = df.copy()
        df_processed.reset_index(inplace=True)
        df_processed.rename(columns={'index': 'Timestamp', 'Date': 'Timestamp'}, inplace=True)
        
        if isinstance(original_tickers, list) and len(original_tickers) > 0:
            df_processed['Ticker'] = original_tickers[0]
        else:
            df_processed['Ticker'] = original_tickers

    # カラム名の標準化
    standard_columns = ['Timestamp', 'Ticker', 'Open', 'High', 'Low', 'Close', 'Volume']
    for col in standard_columns:
        if col not in df_processed.columns:
            if col == 'Volume':
                df_processed[col] = 0
            elif col not in ['Timestamp', 'Ticker']:
                df_processed[col] = np.nan

    # 必要なカラムのみ選択（存在するカラムだけ）
    available_columns = [col for col in standard_columns if col in df_processed.columns]
    df_processed = df_processed[available_columns]
    
    return df_processed

def _fix_japan_tickers(tickers):
    """
    日本株のティッカーシンボルをYFinance形式に修正

    Parameters:
    ----------
    tickers : list or str
        修正するティッカーのリスト（例：['7203.T', '9984.T']）または単一銘柄

    Returns:
    -------
    list or str
        修正されたティッカーのリスト
    """
    # 複数のティッカーの場合
    if isinstance(tickers, list):
        return [ticker.replace('.T', '.JP') if '.T' in ticker else ticker for ticker in tickers]
    # 単一ティッカーの場合
    else:
        return tickers.replace('.T', '.JP') if '.T' in tickers else tickers

def generate_random_stock_data(tickers, start_date, end_date, interval="1d", volatility=0.02):
    """
    ランダムな株価データを生成する

    Parameters:
    ----------
    tickers : list or str
        生成するティッカーのリスト（例：['7203.T', '9984.T']）または単一銘柄
    start_date : str
        開始日（例: '2024-01-01'）
    end_date : str
        終了日（例: '2024-01-31'）
    interval : str, default '1d'
        データ間隔（例: '1h', '1d'）
    volatility : float, default 0.02
        価格のボラティリティ

    Returns:
    -------
    pd.DataFrame
        生成されたランダム株価データ
    """
    logger = logging.getLogger(__name__)
    logger.info(f"ランダム株価データを生成します: {tickers}")

    # 日付範囲作成
    start = pd.to_datetime(start_date)
    end = pd.to_datetime(end_date)
    
    # 間隔に応じたfrequencyを設定
    if interval == '1d':
        freq = 'D'
    elif interval == '1h':
        freq = 'H'
    elif interval == '1m':
        freq = 'min'
    else:
        freq = 'D'  # デフォルトは日次

    date_range = pd.date_range(start=start, end=end, freq=freq)
    
    # ティッカーをリスト形式に統一
    ticker_list = tickers if isinstance(tickers, list) else [tickers]
    
    all_data = []
    
    for ticker in ticker_list:
        # 基本価格の設定
        base_price = random.uniform(1000, 10000)  # 1000円〜10000円の範囲でランダム

        # ランダムウォークで株価生成
        np.random.seed(hash(ticker) % 2**32)  # ティッカーごとに異なるシードを設定

        # 日次リターンを生成（ボラティリティと弱いトレンドを加える）
        trend = random.uniform(-0.001, 0.002)  # わずかなトレンド
        daily_returns = np.random.normal(trend, volatility, len(date_range))

        # 各日の価格を計算
        prices = [base_price]
        for ret in daily_returns:
            prices.append(prices[-1] * (1 + ret))
        prices = prices[1:]  # 初期値を除外

        # 株価データの生成
        for i, date in enumerate(date_range):
            # 乱数成分
            random_factor = 0.005  # 0.5%の乱数成分

            # 終値
            close = prices[i]

            # 始値、高値、安値を生成
            open_price = close * (1 + np.random.uniform(-random_factor, random_factor))
            high = max(open_price, close) * (1 + abs(np.random.uniform(0, random_factor * 2)))
            low = min(open_price, close) * (1 - abs(np.random.uniform(0, random_factor * 2)))

            # 出来高
            volume = int(np.random.uniform(50000, 1000000))

            # データに追加
            all_data.append({
                'Timestamp': date,
                'Open': open_price,
                'High': high,
                'Low': low,
                'Close': close,
                'Volume': volume,
                'Ticker': ticker
            })
    
    # DataFrameに変換
    df = pd.DataFrame(all_data)
    logger.info(f"ランダム株価データを生成しました: {len(df)}行")
    
    return df
