"""
Custom Technical Indicators - TA-Lib Fallback

This module provides basic technical indicators when TA-Lib is not available.
"""

import pandas as pd
import numpy as np

def sma(data, window):
    """Simple Moving Average"""
    return data.rolling(window=window).mean()

def ema(data, window):
    """Exponential Moving Average"""
    return data.ewm(span=window).mean()

def rsi(data, window=14):
    """Relative Strength Index"""
    delta = data.diff()
    gain = (delta.where(delta > 0, 0)).rolling(window=window).mean()
    loss = (-delta.where(delta < 0, 0)).rolling(window=window).mean()
    rs = gain / loss
    return 100 - (100 / (1 + rs))

def bollinger_bands(data, window=20, num_std=2):
    """Bollinger Bands"""
    sma_val = sma(data, window)
    std_val = data.rolling(window=window).std()
    upper = sma_val + (std_val * num_std)
    lower = sma_val - (std_val * num_std)
    return upper, sma_val, lower

def macd(data, fast=12, slow=26, signal=9):
    """MACD Indicator"""
    ema_fast = ema(data, fast)
    ema_slow = ema(data, slow)
    macd_line = ema_fast - ema_slow
    signal_line = ema(macd_line, signal)
    histogram = macd_line - signal_line
    return macd_line, signal_line, histogram

# Add more indicators as needed
