#!/usr/bin/env python3
"""
パフォーマンス分析モジュール

取引結果のパフォーマンスを分析し、レポートを生成するツール
"""

import os
import json
import logging
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import matplotlib.pyplot as plt
from collections import defaultdict

logger = logging.getLogger(__name__)

class PerformanceAnalyzer:
    """パフォーマンス分析クラス"""

    def __init__(self, results_dir="results"):
        """
        初期化

        Parameters:
        -----------
        results_dir : str
            結果ディレクトリのパス
        """
        self.results_dir = results_dir
        os.makedirs(results_dir, exist_ok=True)

    def analyze_daily_performance(self, days=7):
        """
        日次パフォーマンスを分析

        Parameters:
        -----------
        days : int
            分析する日数

        Returns:
        --------
        pd.DataFrame
            日次パフォーマンスのデータフレーム
        """
        # 現在日時
        now = datetime.now()

        # 日次パフォーマンスファイルを取得
        performance_files = []
        for root, _, files in os.walk(self.results_dir):
            for file in files:
                if file.startswith("daily_performance_") and file.endswith(".json"):
                    file_path = os.path.join(root, file)

                    # ファイル日時を取得
                    try:
                        date_str = file.replace("daily_performance_", "").replace(".json", "")
                        file_date = datetime.strptime(date_str, '%Y%m%d')

                        # 指定日数以内のファイルのみ処理
                        if (now - file_date).days <= days:
                            performance_files.append((file_date, file_path))
                    except:
                        # 日付パースエラー - ファイル名が想定と異なる場合
                        continue

        # 日付でソート
        performance_files.sort()

        # データ収集
        performance_data = []
        for _, file_path in performance_files:
            try:
                with open(file_path, 'r') as f:
                    data = json.load(f)
                    # データを正規化
                    normalized_data = self._normalize_performance_data(data)
                    performance_data.append(normalized_data)
            except Exception as e:
                logger.error(f"パフォーマンスデータ読み込みエラー: {e}")

        # データフレームに変換
        if performance_data:
            df = pd.DataFrame(performance_data)
            return df
        else:
            logger.warning("パフォーマンスデータが見つかりませんでした")
            return pd.DataFrame()

    def _normalize_performance_data(self, data):
        """
        パフォーマンスデータを正規化

        Parameters:
        -----------
        data : dict
            パフォーマンスデータ

        Returns:
        --------
        dict
            正規化されたデータ
        """
        normalized = {}

        # 基本フィールド
        normalized['date'] = data.get('date', '')
        normalized['timestamp'] = data.get('timestamp', '')
        normalized['mode'] = data.get('mode', 'unknown')
        normalized['is_learning_enabled'] = data.get('is_learning_enabled', False)

        # ポートフォリオデータの抽出
        if 'portfolio' in data:
            portfolio = data['portfolio']
            normalized['balance'] = portfolio.get('balance', 0)
            normalized['portfolio_value'] = portfolio.get('portfolio_value', 0)
            normalized['initial_balance'] = portfolio.get('initial_balance', 0)
            normalized['profit_loss'] = portfolio.get('profit_loss', 0)
            normalized['profit_loss_pct'] = portfolio.get('profit_loss_pct', 0)
            normalized['holdings_count'] = portfolio.get('holdings_count', 0)
        else:
            # 古い形式のデータの場合
            normalized['balance'] = data.get('balance', 0)
            normalized['portfolio_value'] = data.get('portfolio_value', 0)
            normalized['initial_balance'] = data.get('initial_balance', 0)
            normalized['profit_loss'] = data.get('profit_loss', 0)
            normalized['profit_loss_pct'] = data.get('profit_loss_pct', 0)
            normalized['holdings_count'] = len(data.get('portfolio', {}))

        # 予測分析の抽出
        if 'prediction_analysis' in data:
            analysis = data['prediction_analysis']
            normalized['prediction_accuracy'] = analysis.get('accuracy', 0)
            normalized['prediction_count'] = analysis.get('count', 0)
            normalized['prediction_error_rate'] = analysis.get('error_rate', 0)

        # 取引カウント
        normalized['trade_count_today'] = data.get('trade_count_today', 0)

        return normalized

    def analyze_trade_history(self, days=30):
        """
        取引履歴を分析

        Parameters:
        -----------
        days : int
            分析する日数

        Returns:
        --------
        pd.DataFrame
            取引履歴の分析結果
        """
        # 現在日時
        now = datetime.now()

        # 取引履歴ファイルを取得
        trade_files = []
        for root, _, files in os.walk(self.results_dir):
            for file in files:
                if file.startswith("trades_") and file.endswith(".csv"):
                    file_path = os.path.join(root, file)

                    # ファイルの更新日時を取得
                    try:
                        file_mtime = datetime.fromtimestamp(os.path.getmtime(file_path))

                        # 指定日数以内のファイルのみ処理
                        if (now - file_mtime).days <= days:
                            trade_files.append(file_path)
                    except:
                        continue

        # 取引データの読み込み
        all_trades = []
        for file_path in trade_files:
            try:
                df = pd.read_csv(file_path)
                # 列名の正規化
                df.columns = [col.lower() for col in df.columns]

                # timestampカラムがあるか確認
                if 'timestamp' in df.columns:
                    # 文字列からdatetime型に変換
                    df['timestamp'] = pd.to_datetime(df['timestamp'])

                all_trades.append(df)
            except Exception as e:
                logger.error(f"取引データ読み込みエラー: {e} - {file_path}")

        # 全データを結合
        if all_trades:
            combined_df = pd.concat(all_trades, ignore_index=True)
            return combined_df
        else:
            logger.warning("取引データが見つかりませんでした")
            return pd.DataFrame()

    def calculate_daily_returns(self, trade_df=None):
        """
        日次リターンを計算

        Parameters:
        -----------
        trade_df : pd.DataFrame
            取引データフレーム

        Returns:
        --------
        pd.DataFrame
            日次リターンのデータフレーム
        """
        if trade_df is None:
            trade_df = self.analyze_trade_history()

        if trade_df.empty:
            return pd.DataFrame()

        # 日付カラムの確認と変換
        date_col = None
        if 'timestamp' in trade_df.columns:
            trade_df['date'] = pd.to_datetime(trade_df['timestamp']).dt.date
            date_col = 'date'
        elif 'date' in trade_df.columns:
            trade_df['date'] = pd.to_datetime(trade_df['date']).dt.date
            date_col = 'date'

        if date_col is None:
            logger.error("日付カラムが見つかりません")
            return pd.DataFrame()

        # 日次のPL集計
        if 'profit' in trade_df.columns:
            daily_returns = trade_df.groupby(date_col)['profit'].sum().reset_index()
            daily_returns.columns = ['date', 'daily_profit']

            # 取引回数の追加
            trade_counts = trade_df.groupby(date_col).size().reset_index()
            trade_counts.columns = ['date', 'trade_count']

            # 買い/売りの回数も追加
            if 'action' in trade_df.columns:
                buys = trade_df[trade_df['action'].str.upper() == 'BUY'].groupby(date_col).size().reset_index()
                buys.columns = ['date', 'buy_count']

                sells = trade_df[trade_df['action'].str.upper() == 'SELL'].groupby(date_col).size().reset_index()
                sells.columns = ['date', 'sell_count']

                daily_returns = pd.merge(daily_returns, buys, on='date', how='left')
                daily_returns = pd.merge(daily_returns, sells, on='date', how='left')

            daily_returns = pd.merge(daily_returns, trade_counts, on='date', how='left')

            # NaNをゼロに変換
            daily_returns.fillna(0, inplace=True)

            return daily_returns
        else:
            logger.error("利益カラムが見つかりません")
            return pd.DataFrame()

    def generate_daily_report(self, date=None, output_file=None):
        """
        指定日の日次レポートを生成

        Parameters:
        -----------
        date : str or datetime
            レポート対象日（YYYY-MM-DD形式）。Noneの場合は今日
        output_file : str
            出力ファイルパス。Noneの場合はデフォルトパス

        Returns:
        --------
        dict
            レポート内容
        """
        # 日付の設定
        if date is None:
            target_date = datetime.now().date()
        elif isinstance(date, str):
            target_date = datetime.strptime(date, '%Y-%m-%d').date()
        else:
            target_date = date.date() if hasattr(date, 'date') else date

        date_str = target_date.strftime('%Y%m%d')

        # デフォルト出力ファイル
        if output_file is None:
            output_file = os.path.join(self.results_dir, f"daily_report_{date_str}.json")

        # 取引履歴の取得
        trades_df = self.analyze_trade_history(days=30)

        # 該当日の取引をフィルタリング
        if not trades_df.empty and 'timestamp' in trades_df.columns:
            trades_df['date'] = pd.to_datetime(trades_df['timestamp']).dt.date
            day_trades = trades_df[trades_df['date'] == target_date]
        else:
            day_trades = pd.DataFrame()

        # パフォーマンスデータの取得
        performance_df = self.analyze_daily_performance(days=30)

        # 該当日のパフォーマンスをフィルタリング
        day_performance = None
        if not performance_df.empty and 'date' in performance_df.columns:
            date_matches = performance_df[performance_df['date'].str.startswith(date_str[:8])]
            if not date_matches.empty:
                day_performance = date_matches.iloc[-1].to_dict()

        # レポートの作成
        report = {
            "date": target_date.strftime('%Y-%m-%d'),
            "generated_at": datetime.now().isoformat(),
            "performance": day_performance,
            "trades": {
                "count": len(day_trades),
                "buy_count": len(day_trades[day_trades['action'].str.upper() == 'BUY']) if not day_trades.empty else 0,
                "sell_count": len(day_trades[day_trades['action'].str.upper() == 'SELL']) if not day_trades.empty else 0,
                "total_profit": day_trades['profit'].sum() if not day_trades.empty and 'profit' in day_trades.columns else 0,
                "average_profit": day_trades['profit'].mean() if not day_trades.empty and 'profit' in day_trades.columns else 0,
                "max_profit": day_trades['profit'].max() if not day_trades.empty and 'profit' in day_trades.columns else 0,
                "min_profit": day_trades['profit'].min() if not day_trades.empty and 'profit' in day_trades.columns else 0
            }
        }

        # 銘柄別パフォーマンス
        if not day_trades.empty and 'ticker' in day_trades.columns and 'profit' in day_trades.columns:
            ticker_performance = day_trades.groupby('ticker')['profit'].agg(['sum', 'mean', 'count']).reset_index()
            ticker_performance.columns = ['ticker', 'total_profit', 'average_profit', 'trade_count']
            report['ticker_performance'] = ticker_performance.to_dict(orient='records')

        # ファイル出力
        with open(output_file, 'w') as f:
            json.dump(report, f, indent=2, default=str)

        logger.info(f"日次レポートを生成しました: {output_file}")
        return report

    def generate_summary_report(self, days=30, output_file=None):
        """
        期間サマリーレポートを生成

        Parameters:
        -----------
        days : int
            レポート対象期間（日）
        output_file : str
            出力ファイルパス。Noneの場合はデフォルトパス

        Returns:
        --------
        dict
            レポート内容
        """
        # 現在日時
        now = datetime.now()
        date_str = now.strftime('%Y%m%d')

        # デフォルト出力ファイル
        if output_file is None:
            output_file = os.path.join(self.results_dir, f"summary_report_{date_str}.json")

        # 取引履歴の取得と分析
        trades_df = self.analyze_trade_history(days=days)

        # 日次パフォーマンスの取得
        performance_df = self.analyze_daily_performance(days=days)

        # 日次リターンの計算
        daily_returns = self.calculate_daily_returns(trades_df)

        # レポートの作成
        report = {
            "period": f"Past {days} days",
            "start_date": (now - timedelta(days=days)).strftime('%Y-%m-%d'),
            "end_date": now.strftime('%Y-%m-%d'),
            "generated_at": now.isoformat(),
            "trades": {
                "count": len(trades_df),
                "buy_count": len(trades_df[trades_df['action'].str.upper() == 'BUY']) if not trades_df.empty else 0,
                "sell_count": len(trades_df[trades_df['action'].str.upper() == 'SELL']) if not trades_df.empty else 0,
                "total_profit": trades_df['profit'].sum() if not trades_df.empty and 'profit' in trades_df.columns else 0,
                "average_profit": trades_df['profit'].mean() if not trades_df.empty and 'profit' in trades_df.columns else 0
            },
            "daily_stats": {
                "trading_days": len(daily_returns),
                "average_daily_profit": daily_returns['daily_profit'].mean() if not daily_returns.empty else 0,
                "profit_days": len(daily_returns[daily_returns['daily_profit'] > 0]) if not daily_returns.empty else 0,
                "loss_days": len(daily_returns[daily_returns['daily_profit'] < 0]) if not daily_returns.empty else 0
            }
        }

        # 銘柄別パフォーマンス
        if not trades_df.empty and 'ticker' in trades_df.columns and 'profit' in trades_df.columns:
            ticker_performance = trades_df.groupby('ticker')['profit'].agg(['sum', 'mean', 'count']).reset_index()
            ticker_performance.columns = ['ticker', 'total_profit', 'average_profit', 'trade_count']
            # 総利益で降順ソート
            ticker_performance = ticker_performance.sort_values('total_profit', ascending=False)
            report['ticker_performance'] = ticker_performance.to_dict(orient='records')

        # モード別パフォーマンス
        if not trades_df.empty and 'mode' in trades_df.columns and 'profit' in trades_df.columns:
            mode_performance = trades_df.groupby('mode')['profit'].agg(['sum', 'mean', 'count']).reset_index()
            mode_performance.columns = ['mode', 'total_profit', 'average_profit', 'trade_count']
            report['mode_performance'] = mode_performance.to_dict(orient='records')

        # 最終ポートフォリオ状態
        if not performance_df.empty:
            latest_performance = performance_df.iloc[-1].to_dict()
            report['latest_portfolio'] = {
                'balance': latest_performance.get('balance', 0),
                'portfolio_value': latest_performance.get('portfolio_value', 0),
                'profit_loss': latest_performance.get('profit_loss', 0),
                'profit_loss_pct': latest_performance.get('profit_loss_pct', 0),
                'holdings_count': latest_performance.get('holdings_count', 0),
                'date': latest_performance.get('date', '')
            }

        # ファイル出力
        with open(output_file, 'w') as f:
            json.dump(report, f, indent=2, default=str)

        logger.info(f"サマリーレポートを生成しました: {output_file}")
        return report

    def save_performance_metrics(self, metrics_data, tag=""):
        """
        パフォーマンス指標を保存

        Parameters:
        -----------
        metrics_data : dict
            パフォーマンス指標データ
        tag : str
            ファイル名に付けるタグ

        Returns:
        --------
        str
            保存したファイルパス
        """
        # 現在の日時
        now = datetime.now()
        date_str = now.strftime('%Y%m%d')
        time_str = now.strftime('%H%M%S')

        # タグの処理
        if tag:
            if not tag.startswith('_'):
                tag = f"_{tag}"

        # ファイル名
        file_name = f"performance_metrics{tag}_{date_str}_{time_str}.json"
        file_path = os.path.join(self.results_dir, file_name)

        # 保存するデータに時刻を追加
        save_data = {
            "timestamp": now.isoformat(),
            "data": metrics_data
        }

        # ファイル保存
        with open(file_path, 'w') as f:
            json.dump(save_data, f, indent=2, default=str)

        logger.info(f"パフォーマンス指標を保存しました: {file_path}")
        return file_path
