#!/usr/bin/env python3
"""
GPU設定の統一管理
セグメンテーションフォルト対策とエラーハンドリング強化
"""

import os
import sys
import warnings
import logging
from typing import Dict, Any, Optional

# TensorFlow警告抑制とセグメンテーションフォルト対策
os.environ['TF_CPP_MIN_LOG_LEVEL'] = '2'
os.environ['TF_ENABLE_ONEDNN_OPTS'] = '0'  # OneDNN最適化無効化
os.environ['TF_FORCE_GPU_ALLOW_GROWTH'] = 'true'  # GPU メモリ成長を強制

# セグメンテーションフォルト対策用環境変数
os.environ['CUDA_LAUNCH_BLOCKING'] = '1'
os.environ['PYTORCH_CUDA_ALLOC_CONF'] = 'max_split_size_mb:512'

warnings.filterwarnings('ignore')

logger = logging.getLogger(__name__)

def configure_tensorflow_gpu() -> bool:
    """
    TensorFlowのGPU設定（セグメンテーションフォルト対策付き）
    
    Returns:
    --------
    bool
        GPU設定成功フラグ
    """
    try:
        # TensorFlowのインポートを安全に実行
        try:
            import tensorflow as tf
        except ImportError:
            logger.warning("TensorFlow がインストールされていません")
            return False
        except Exception as e:
            logger.error(f"TensorFlow インポートエラー: {e}")
            return False
        
        # GPU検出と設定（安全に実行）
        try:
            gpus = tf.config.experimental.list_physical_devices('GPU')
            
            if gpus:
                # Memory Growth有効化（セグメンテーションフォルト対策）
                for gpu in gpus:
                    try:
                        tf.config.experimental.set_memory_growth(gpu, True)
                        # 仮想GPU設定でメモリ制限を設定
                        tf.config.experimental.set_virtual_device_configuration(
                            gpu,
                            [tf.config.experimental.VirtualDeviceConfiguration(memory_limit=2048)]
                        )
                    except RuntimeError as e:
                        # 既に初期化済みの場合はスキップ
                        if "cannot be modified" not in str(e):
                            raise
                
                # Mixed Precision有効化（安全に実行）
                try:
                    tf.keras.mixed_precision.set_global_policy('mixed_float16')
                    logger.info("Mixed precision有効化")
                except Exception as e:
                    logger.warning(f"Mixed precision設定失敗: {e}")
                
                logger.info(f"✅ TensorFlow GPU有効: {len(gpus)}基のGPU")
                return True
            else:
                logger.info("⚠️ TensorFlow: GPU未検出")
                return False
                
        except Exception as gpu_error:
            logger.error(f"GPU設定エラー: {gpu_error}")
            # GPU設定に失敗した場合はCPUモードにフォールバック
            try:
                tf.config.experimental.set_visible_devices([], 'GPU')
                logger.warning("CPUモードにフォールバック")
            except Exception:
                pass
            return False
            
    except Exception as e:
        logger.error(f"TensorFlow GPU設定エラー: {e}")
        return False

def configure_pytorch_gpu() -> 'torch.device':
    """
    PyTorchのGPU設定（エラーハンドリング強化）
    
    Returns:
    --------
    torch.device
        使用デバイス（GPU or CPU）
    """
    try:
        # PyTorchのインポートを安全に実行
        try:
            import torch
        except ImportError:
            logger.warning("PyTorch がインストールされていません")
            return torch.device('cpu')
        except Exception as e:
            logger.error(f"PyTorch インポートエラー: {e}")
            return torch.device('cpu')
        
        # CUDA利用可能性チェック
        try:
            if torch.cuda.is_available():
                # CUDA最適化（安全に実行）
                try:
                    torch.backends.cudnn.benchmark = True
                    torch.backends.cudnn.deterministic = False
                    
                    # メモリ使用量制限設定
                    torch.cuda.empty_cache()  # メモリクリア
                    
                    device = torch.device('cuda')
                    gpu_name = torch.cuda.get_device_name()
                    vram_gb = torch.cuda.get_device_properties(0).total_memory // 1024**3
                    
                    logger.info(f"✅ PyTorch GPU有効: {gpu_name} ({vram_gb}GB)")
                    return device
                except Exception as cuda_error:
                    logger.warning(f"CUDA設定エラー、CPUにフォールバック: {cuda_error}")
                    return torch.device('cpu')
            else:
                logger.info("⚠️ PyTorch: CUDA未使用")
                return torch.device('cpu')
        except Exception as e:
            logger.error(f"PyTorch CUDA チェックエラー: {e}")
            return torch.device('cpu')
            
    except Exception as e:
        logger.error(f"PyTorch GPU設定エラー: {e}")
        return torch.device('cpu')

def initialize_gpu() -> Dict[str, Any]:
    """
    GPU環境の初期化（安全なエラーハンドリング付き）
    
    Returns:
    --------
    Dict[str, Any]
        GPU設定結果
    """
    logger.info("=== GPU環境初期化 ===")
    
    # セグメンテーションフォルト対策の追加設定
    try:
        import signal
        
        def segfault_handler(signum, frame):
            logger.error("セグメンテーションフォルトが検出されました。CPUモードで継続します。")
            os.environ["CUDA_VISIBLE_DEVICES"] = "-1"
            
        signal.signal(signal.SIGSEGV, segfault_handler)
    except Exception:
        pass  # シグナルハンドラ設定失敗は無視
    
    # 段階的にGPU設定を試行
    tf_gpu = False
    pytorch_device = None
    
    try:
        tf_gpu = configure_tensorflow_gpu()
    except Exception as e:
        logger.error(f"TensorFlow GPU初期化失敗: {e}")
        tf_gpu = False
    
    try:
        import torch
        pytorch_device = configure_pytorch_gpu()
    except Exception as e:
        logger.error(f"PyTorch GPU初期化失敗: {e}")
        pytorch_device = torch.device('cpu') if 'torch' in locals() else None
    
    # 結果レポート
    if tf_gpu or (pytorch_device and pytorch_device.type == 'cuda'):
        logger.info("🚀 GPU加速準備完了")
    else:
        logger.info("💻 CPU実行モード")
    
    return {
        'tensorflow_gpu': tf_gpu,
        'pytorch_device': pytorch_device,
        'safe_mode': not (tf_gpu or (pytorch_device and pytorch_device.type == 'cuda'))
    }

def force_cpu_mode():
    """
    CPUモードを強制する（セグメンテーションフォルト対策）
    """
    os.environ["CUDA_VISIBLE_DEVICES"] = "-1"
    os.environ["CUDA_DEVICE_ORDER"] = "PCI_BUS_ID"
    logger.warning("CPUモードを強制しました")

def get_safe_device():
    """
    安全なデバイスを取得（フォールバック機能付き）
    
    Returns:
    --------
    str
        デバイス名（'cpu' or 'cuda'）
    """
    try:
        config = initialize_gpu()
        if config.get('safe_mode', True):
            return 'cpu'
        else:
            return 'cuda' if config.get('tensorflow_gpu') or (
                config.get('pytorch_device') and config['pytorch_device'].type == 'cuda'
            ) else 'cpu'
    except Exception:
        return 'cpu'

if __name__ == "__main__":
    initialize_gpu()