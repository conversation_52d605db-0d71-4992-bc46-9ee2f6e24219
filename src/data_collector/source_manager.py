#!/usr/bin/env python3
"""
日経225データ収集 - データソース管理モジュール

利用可能なデータソースの管理を行います。
"""

class DataSourceManager:
    """利用可能なデータソースを管理するクラス"""
    
    def __init__(self):
        """初期化"""
        self.sources = {}
        self.available_sources = []
        
        # Pandas DataReader
        try:
            import pandas_datareader as pdr
            self.sources["pandas_datareader"] = pdr
            self.available_sources.append("pandas_datareader")
        except ImportError:
            pass
        
        # yfinance
        try:
            import yfinance as yf
            self.sources["yfinance"] = yf
            self.available_sources.append("yfinance")
        except ImportError:
            pass
        
        # Stooq Web Scraper
        try:
            import stooq_web_scraper_fixed
            self.sources["stooq_scraper"] = stooq_web_scraper_fixed
            self.available_sources.append("stooq_scraper")
        except ImportError:
            try:
                import stooq_web_scraper
                self.sources["stooq_scraper"] = stooq_web_scraper
                self.available_sources.append("stooq_scraper")
            except ImportError:
                pass
        
        # ティッカー情報モジュール
        try:
            from nikkei225_tickers import (
                get_all_tickers,
                get_tickers_by_sector,
                get_sector_names,
                get_major_tickers,
                get_topix_core30
            )
            self.get_all_tickers = get_all_tickers
            self.get_tickers_by_sector = get_tickers_by_sector
            self.get_sector_names = get_sector_names
            self.get_major_tickers = get_major_tickers
            self.get_topix_core30 = get_topix_core30
        except ImportError:
            # デフォルトの簡易関数
            self.get_all_tickers = lambda: ["8306.T", "9432.T", "9984.T", "6758.T", "7203.T"]
            self.get_tickers_by_sector = lambda sector: self.get_all_tickers()
            self.get_sector_names = lambda: ["全銘柄"]
            self.get_major_tickers = lambda: self.get_all_tickers()
            self.get_topix_core30 = lambda: self.get_all_tickers()[:10]
    
    def get_source(self, source_name):
        """指定ソースのモジュールを取得"""
        return self.sources.get(source_name)
    
    def is_available(self, source_name):
        """ソースが利用可能かチェック"""
        return source_name in self.available_sources
    
    def get_available_sources(self):
        """利用可能なソース一覧を取得"""
        return self.available_sources
