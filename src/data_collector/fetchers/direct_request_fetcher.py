#!/usr/bin/env python3
"""
日経225データ収集 - 直接リクエストフェッチャー

Yahoo FinanceへのHTTP直接リクエストを使用してデータを取得するクラスを提供します。
"""

import time
import traceback
import json
import pandas as pd
import requests
from datetime import datetime
from .base_fetcher import BaseFetcher
from ..utils import WebUtils

class DirectRequestFetcher(BaseFetcher):
    """HTTPリクエストを直接送信して株価データを取得するクラス"""
    
    def __init__(self, logger=None):
        """
        初期化
        
        Args:
            logger: ロガーオブジェクト
        """
        super().__init__(logger)
        self.name = "DirectRequestFetcher"
    
    def fetch(self, ticker, start_date, end_date):
        """
        直接HTTPリクエストを使用してデータを取得
        
        Args:
            ticker (str): 銘柄ティッカー
            start_date (datetime): 取得開始日
            end_date (datetime): 取得終了日
            
        Returns:
            tuple: (DataFrame または None, エラーメッセージ または None)
        """
        try:
            start_time = time.time()
            self._log_debug(f"直接リクエストでの取得を試行: {ticker}, {start_date} - {end_date}")
            
            # まずquery1エンドポイント（CSVダウンロード）を試行
            result, error_msg = self._try_query1_endpoint(ticker, start_date, end_date)
            
            if result is not None:
                return result, None
            
            # query1が失敗した場合はquery2エンドポイント（JSON API）を試行
            self._log_debug(f"query1エンドポイントが失敗: {error_msg}")
            self._log_debug(f"query2エンドポイントでの取得を試行")
            
            return self._try_query2_endpoint(ticker, start_date, end_date)
            
        except Exception as e:
            elapsed_time = self._record_execution_time(start_time)
            
            error_msg = str(e)
            self._log_error(f"データ取得エラー: {error_msg}")
            self._log_debug(traceback.format_exc())
            
            return None, error_msg
    
    def _try_query1_endpoint(self, ticker, start_date, end_date):
        """
        query1エンドポイント（CSVダウンロード）からデータを取得
        
        Returns:
            tuple: (DataFrame または None, エラーメッセージ)
        """
        start_time = time.time()
        
        try:
            # Yahoo Finance APIパラメータを計算
            period1 = int(start_date.timestamp())
            period2 = int(end_date.timestamp())
            interval = '1d'
            
            # Yahoo Finance専用ヘッダー設定
            headers = self._get_yahoo_headers(ticker)
            
            # query1エンドポイント（CSVダウンロード）
            url = f"https://query1.finance.yahoo.com/v7/finance/download/{ticker}?period1={period1}&period2={period2}&interval={interval}&events=history"
            
            # リクエスト送信
            response = requests.get(url, headers=headers, timeout=20)
            
            if response.status_code != 200:
                return None, f"HTTP error: {response.status_code}"
            
            # CSVとしてパース
            try:
                df = pd.read_csv(pd.StringIO(response.text))
            except Exception as e:
                return None, f"CSVパースエラー: {str(e)}"
            
            if df.empty:
                return None, "空のデータフレームが返されました"
            
            # 日付を変換
            df['Date'] = pd.to_datetime(df['Date'])
            
            # データフレームを標準化
            df = self._standardize_dataframe(df)
            
            if not self._validate_dataframe(df):
                return None, "Invalid dataframe structure"
            
            elapsed_time = self._record_execution_time(start_time)
            self._log_info(f"query1エンドポイントからデータ取得成功: {ticker}, 所要時間: {elapsed_time:.2f}秒")
            
            return df, None
            
        except Exception as e:
            return None, f"query1エンドポイントエラー: {str(e)}"
    
    def _try_query2_endpoint(self, ticker, start_date, end_date):
        """
        query2エンドポイント（JSON API）からデータを取得
        
        Returns:
            tuple: (DataFrame または None, エラーメッセージ または None)
        """
        start_time = time.time()
        
        try:
            # Yahoo Finance APIパラメータを計算
            period1 = int(start_date.timestamp())
            period2 = int(end_date.timestamp())
            interval = '1d'
            
            # Yahoo Finance専用ヘッダー設定
            headers = self._get_yahoo_headers(ticker)
            
            # query2エンドポイント（JSONデータ）
            url = f"https://query2.finance.yahoo.com/v8/finance/chart/{ticker}?period1={period1}&period2={period2}&interval={interval}"
            
            # リクエスト送信
            response = requests.get(url, headers=headers, timeout=20)
            
            if response.status_code != 200:
                elapsed_time = self._record_execution_time(start_time)
                return None, f"HTTP error: {response.status_code}"
            
            # JSONデータをパース
            data = response.json()
            
            # JSONからデータフレームを作成
            try:
                chart_data = data.get('chart', {}).get('result', [{}])[0]
                
                # タイムスタンプ
                timestamps = chart_data.get('timestamp', [])
                
                # 価格データ
                quote = chart_data.get('indicators', {}).get('quote', [{}])[0]
                opens = quote.get('open', [])
                highs = quote.get('high', [])
                lows = quote.get('low', [])
                closes = quote.get('close', [])
                volumes = quote.get('volume', [])
                
                # 調整後終値
                adjclose_data = chart_data.get('indicators', {}).get('adjclose', [{}])
                adjcloses = adjclose_data[0].get('adjclose', []) if adjclose_data else []
                
                # データフレーム作成
                data_dict = {
                    'Date': [datetime.fromtimestamp(ts) for ts in timestamps if ts],
                    'Open': opens,
                    'High': highs,
                    'Low': lows,
                    'Close': closes,
                    'Volume': volumes
                }
                
                # Adj Closeが利用可能な場合は追加
                if adjcloses:
                    data_dict['Adj Close'] = adjcloses
                
                df = pd.DataFrame(data_dict)
                
                # NaNがない行だけを保持
                df = df.dropna(how='any', subset=['Open', 'High', 'Low', 'Close'])
                
                if df.empty:
                    return None, "No valid data after NaN removal"
                
                # データフレームを標準化
                df = self._standardize_dataframe(df)
                
                if not self._validate_dataframe(df):
                    return None, "Invalid dataframe structure"
                
                elapsed_time = self._record_execution_time(start_time)
                self._log_info(f"query2エンドポイントからデータ取得成功: {ticker}, 所要時間: {elapsed_time:.2f}秒")
                
                return df, None
                
            except Exception as e:
                return None, f"JSONパースエラー: {str(e)}"
            
        except Exception as e:
            elapsed_time = self._record_execution_time(start_time)
            return None, f"query2エンドポイントエラー: {str(e)}"
