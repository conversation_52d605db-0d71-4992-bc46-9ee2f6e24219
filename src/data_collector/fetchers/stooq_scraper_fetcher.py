#!/usr/bin/env python3
"""
日経225データ収集 - Stooqスクレイパーフェッチャー

Stooq.comからデータをスクレイピングするクラスを提供します。
"""

import time
import traceback
import pandas as pd
import requests
from .base_fetcher import BaseFetcher
from ..utils import WebUtils

class StooqScraperFetcher(BaseFetcher):
    """Stooqをスクレイピングして株価データを取得するクラス"""
    
    def __init__(self, source_manager, logger=None):
        """
        初期化
        
        Args:
            source_manager: データソース管理オブジェクト
            logger: ロガーオブジェクト
        """
        super().__init__(logger)
        self.source_manager = source_manager
        self.name = "StooqScraperFetcher"
    
    def is_available(self):
        """stooq_scraperが利用可能かをチェック"""
        return self.source_manager.is_available("stooq_scraper")
    
    def fetch(self, ticker, start_date, end_date):
        """
        Stooqのスクレイピングでデータを取得
        
        Args:
            ticker (str): 銘柄ティッカー
            start_date (datetime): 取得開始日
            end_date (datetime): 取得終了日
            
        Returns:
            tuple: (DataFrame または None, エラーメッセージ または None)
        """
        if not self.is_available():
            return None, "stooq_scraper not available"
        
        scraper = self.source_manager.get_source("stooq_scraper")
        
        try:
            start_time = time.time()
            self._log_debug(f"stooq_scraperでの取得を試行: {ticker}, {start_date} - {end_date}")
            
            # Stooq用にティッカーを変換
            stooq_ticker = self._convert_ticker_for_source(ticker, "stooq")
            
            # 日付形式の変換
            start_date_str = start_date.strftime('%Y%m%d')
            end_date_str = end_date.strftime('%Y%m%d')
            
            # StooqScraper クラスのインスタンスを作成
            if hasattr(scraper, "StooqScraper"):
                try:
                    scraper_instance = scraper.StooqScraper()
                    
                    # データ取得 (get_stock_data メソッドを使用)
                    df = scraper_instance.get_stock_data(
                        stooq_ticker, 
                        start_date, 
                        end_date
                    )
                except Exception as e:
                    self._log_error(f"StooqScraperクラスの使用中にエラー: {str(e)}")
                    df = None
            # または直接関数を呼び出し
            elif hasattr(scraper, "get_stock_data"):
                try:
                    df = scraper.get_stock_data(
                        stooq_ticker, 
                        start_date, 
                        end_date
                    )
                except Exception as e:
                    self._log_error(f"get_stock_data関数の使用中にエラー: {str(e)}")
                    df = None
            else:
                # 直接Stooqからデータを取得する最後の手段
                df = self._fetch_stooq_data_directly(stooq_ticker, start_date_str, end_date_str)
            
            if df is None or df.empty:
                elapsed_time = self._record_execution_time(start_time)
                self._log_warning("空のデータフレームが返されました")
                return None, "Empty dataframe returned"
            
            # データフレームを標準化
            df = self._standardize_dataframe(df)
            
            if not self._validate_dataframe(df):
                return None, "Invalid dataframe structure"
            
            elapsed_time = self._record_execution_time(start_time)
            self._log_info(f"データ取得成功: {ticker}, 所要時間: {elapsed_time:.2f}秒")
            
            return df, None
            
        except Exception as e:
            elapsed_time = self._record_execution_time(start_time)
            
            error_msg = str(e)
            self._log_error(f"データ取得エラー: {error_msg}")
            self._log_debug(traceback.format_exc())
            
            return None, error_msg
    
    def _fetch_stooq_data_directly(self, ticker, start_date_str, end_date_str):
        """
        直接Stooqからデータを取得
        
        Returns:
            DataFrame: スクレイピングで取得したデータ
        """
        url = f"https://stooq.com/q/d/l/?s={ticker}&d1={start_date_str}&d2={end_date_str}&i=d"
        
        headers = self._get_stooq_headers()
        
        # セッションを使用
        with requests.Session() as session:
            response = session.get(url, headers=headers, timeout=30)
            
            if response.status_code != 200:
                raise Exception(f"HTTP error {response.status_code}")
            
            # CSVデータを解析
            try:
                df = pd.read_csv(pd.StringIO(response.text))
                
                # 日付形式の確認と変換
                if 'Date' in df.columns:
                    df['Date'] = pd.to_datetime(df['Date'])
                
                return df
            except Exception as e:
                self._log_error(f"Stooqデータ解析エラー: {str(e)}")
                
                # HTMLからテーブルをパース（CSVダウンロードが失敗した場合）
                try:
                    tables = pd.read_html(response.text)
                    if tables and len(tables) > 0:
                        df = tables[0]
                        
                        # カラム名のマッピング
                        mapping = {
                            'Date': 'Date',
                            'Open': 'Open',
                            'High': 'High',
                            'Low': 'Low',
                            'Close': 'Close',
                            'Volume': 'Volume'
                        }
                        
                        # カラム名を標準化
                        df = df.rename(columns={k: v for k, v in mapping.items() if k in df.columns})
                        
                        # 日付を変換
                        if 'Date' in df.columns:
                            df['Date'] = pd.to_datetime(df['Date'])
                        
                        return df
                except Exception as html_error:
                    self._log_error(f"Stooq HTMLテーブル解析エラー: {str(html_error)}")
                    raise
