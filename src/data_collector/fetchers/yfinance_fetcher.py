#!/usr/bin/env python3
"""
日経225データ収集 - yfinanceフェッチャー

yfinanceライブラリを使用してデータを取得するクラスを提供します。
"""

import time
import traceback
from .base_fetcher import BaseFetcher
from ..utils import WebUtils

class YFinanceFetcher(BaseFetcher):
    """yfinanceを使用して株価データを取得するクラス"""
    
    def __init__(self, source_manager, logger=None):
        """
        初期化
        
        Args:
            source_manager: データソース管理オブジェクト
            logger: ロガーオブジェクト
        """
        super().__init__(logger)
        self.source_manager = source_manager
        self.name = "YFinanceFetcher"
    
    def is_available(self):
        """yfinanceが利用可能かをチェック"""
        return self.source_manager.is_available("yfinance")
    
    def fetch(self, ticker, start_date, end_date):
        """
        yfinanceを使用してデータを取得
        
        Args:
            ticker (str): 銘柄ティッカー
            start_date (datetime): 取得開始日
            end_date (datetime): 取得終了日
            
        Returns:
            tuple: (DataFrame または None, エラーメッセージ または None)
        """
        if not self.is_available():
            return None, "yfinance not available"
        
        yf = self.source_manager.get_source("yfinance")
        
        try:
            start_time = time.time()
            self._log_debug(f"yfinanceでの取得を試行: {ticker}, {start_date} - {end_date}")
            
            # プロキシやタイムアウトの設定
            proxies = None
            timeout = 30  # タイムアウト値を増加
            
            # レート制限回避のための設定
            headers = self._get_yahoo_headers(ticker)
            
            # Tickerオブジェクト取得
            stock = yf.Ticker(ticker)
            
            # データ取得（レート制限に対応したエラーハンドリング付き）
            max_retries = 3
            retry_delay = 2
            
            for attempt in range(max_retries):
                try:
                    # ユーザーエージェントの動的設定
                    session = self.source_manager.get_source("requests").Session()
                    session.headers.update(headers)
                    
                    # yfinance設定
                    yf.set_tz_session_object(session)
                    
                    # データ取得
                    df = stock.history(
                        start=start_date.strftime('%Y-%m-%d'),
                        end=end_date.strftime('%Y-%m-%d'),
                        interval="1d",
                        proxy=proxies,
                        timeout=timeout
                    )
                    
                    # 成功した場合はループを抜ける
                    break
                    
                except Exception as retry_error:
                    # レート制限エラーやネットワークエラーの場合、リトライ
                    if "429" in str(retry_error) or "Connection" in str(retry_error):
                        wait_time = retry_delay * (attempt + 1)
                        self._log_warning(f"レート制限またはネットワークエラー、{wait_time}秒後にリトライ ({attempt+1}/{max_retries})")
                        time.sleep(wait_time)
                        
                        # 次のリトライで別のユーザーエージェントを使用
                        headers = self._get_yahoo_headers(ticker)
                    else:
                        # その他のエラーは即座に再送出
                        raise
            else:
                # すべてのリトライが失敗した場合
                raise Exception(f"最大リトライ回数({max_retries})に到達しました")
            
            if df.empty:
                elapsed_time = self._record_execution_time(start_time)
                self._log_warning("空のデータフレームが返されました")
                return None, "Empty dataframe returned"
            
            # インデックスリセットとカラム名標準化
            df = df.reset_index()
            df = self._standardize_dataframe(df)
            
            if not self._validate_dataframe(df):
                return None, "Invalid dataframe structure"
            
            elapsed_time = self._record_execution_time(start_time)
            self._log_info(f"データ取得成功: {ticker}, 所要時間: {elapsed_time:.2f}秒")
            
            return df, None
            
        except Exception as e:
            elapsed_time = self._record_execution_time(start_time)
            
            error_msg = str(e)
            self._log_error(f"データ取得エラー: {error_msg}")
            self._log_debug(traceback.format_exc())
            
            return None, error_msg
