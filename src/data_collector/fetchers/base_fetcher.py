#!/usr/bin/env python3
"""
日経225データ収集 - フェッチャー基底クラス

データソースからデータを取得するための基底クラスを提供します。
"""

import time
from abc import ABC, abstractmethod
import pandas as pd
import logging
import traceback
from datetime import datetime

from ..utils import DataUtils, WebUtils, NikkeiUtils

class BaseFetcher(ABC):
    """データ取得の基底クラス"""
    
    def __init__(self, logger=None):
        """初期化"""
        self.logger = logger
        self.name = self.__class__.__name__
    
    @abstractmethod
    def fetch(self, ticker, start_date, end_date):
        """
        データを取得する抽象メソッド
        
        Args:
            ticker (str): 銘柄ティッカー
            start_date (datetime): 取得開始日
            end_date (datetime): 取得終了日
            
        Returns:
            tuple: (DataFrame または None, エラーメッセージ または None)
        """
        pass
    
    def _log_debug(self, message):
        """デバッグログを出力"""
        if self.logger:
            self.logger.debug(f"{self.name}: {message}")
    
    def _log_info(self, message):
        """情報ログを出力"""
        if self.logger:
            self.logger.info(f"{self.name}: {message}")
    
    def _log_warning(self, message):
        """警告ログを出力"""
        if self.logger:
            self.logger.warning(f"{self.name}: {message}")
    
    def _log_error(self, message, exc_info=None):
        """エラーログを出力"""
        if self.logger:
            if exc_info:
                self.logger.error(f"{self.name}: {message}", exc_info=True)
            else:
                self.logger.error(f"{self.name}: {message}")
    
    def _record_execution_time(self, start_time):
        """実行時間を計算"""
        return time.time() - start_time
    
    def _validate_dataframe(self, df):
        """
        データフレームの有効性を検証
        
        Args:
            df (DataFrame): 検証するデータフレーム
            
        Returns:
            bool: 有効な場合はTrue、無効な場合はFalse
        """
        # DataUtilsクラスを利用してデータフレームを検証
        result = DataUtils.validate_dataframe(df, self.logger)
        if not result and self.logger:
            self._log_warning("データフレーム検証に失敗しました")
        return result
    
    def _standardize_dataframe(self, df):
        """
        データフレームを標準形式に変換
        
        Args:
            df (DataFrame): 標準化するデータフレーム
            
        Returns:
            DataFrame: 標準化されたデータフレーム
        """
        # DataUtilsクラスを利用してデータフレームを標準化
        result = DataUtils.standardize_dataframe(df, self.logger)
        if result is None and self.logger:
            self._log_warning("データフレーム標準化に失敗しました")
        return result
    
    def _get_yahoo_headers(self, ticker):
        """
        Yahoo Finance用のヘッダーを取得
        
        Args:
            ticker (str): 銘柄ティッカー
            
        Returns:
            dict: ヘッダー情報
        """
        return WebUtils.get_yahoo_finance_headers(ticker)
    
    def _get_stooq_headers(self):
        """
        Stooq用のヘッダーを取得
        
        Returns:
            dict: ヘッダー情報
        """
        return WebUtils.get_stooq_headers()
    
    def _get_base_headers(self):
        """
        基本的なヘッダーを取得
        
        Returns:
            dict: ヘッダー情報
        """
        return WebUtils.get_base_headers()
    
    def _convert_ticker_for_source(self, ticker, source):
        """
        データソース用にティッカーを変換
        
        Args:
            ticker (str): 元のティッカー
            source (str): データソース名
            
        Returns:
            str: 変換されたティッカー
        """
        return NikkeiUtils.convert_ticker_for_source(ticker, source)
