#!/usr/bin/env python3
"""
日経225データ収集 - pandas-datareaderフェッチャー

pandas-datareaderライブラリを使用してデータを取得するクラスを提供します。
"""

import time
import traceback
from .base_fetcher import BaseFetcher

class PandasDataReaderFetcher(BaseFetcher):
    """pandas-datareaderを使用して株価データを取得するクラス"""
    
    def __init__(self, source_manager, direct_request_fetcher=None, logger=None):
        """
        初期化
        
        Args:
            source_manager: データソース管理オブジェクト
            direct_request_fetcher: 直接リクエスト用のフェッチャー（フォールバック用）
            logger: ロガーオブジェクト
        """
        super().__init__(logger)
        self.source_manager = source_manager
        self.direct_request_fetcher = direct_request_fetcher
        self.name = "PandasDataReaderFetcher"
    
    def is_available(self):
        """pandas-datareaderが利用可能かをチェック"""
        return self.source_manager.is_available("pandas_datareader")
    
    def fetch(self, ticker, start_date, end_date):
        """
        pandas-datareaderを使用してデータを取得
        
        Args:
            ticker (str): 銘柄ティッカー
            start_date (datetime): 取得開始日
            end_date (datetime): 取得終了日
            
        Returns:
            tuple: (DataFrame または None, エラーメッセージ または None)
        """
        if not self.is_available():
            return None, "pandas_datareader not available"
        
        pdr = self.source_manager.get_source("pandas_datareader")
        
        try:
            start_time = time.time()
            self._log_debug(f"pandas-datareaderでの取得を試行: {ticker}, {start_date} - {end_date}")
            
            # セッションとヘッダーの設定
            session = pdr.data._get_default_session()
            headers = self._get_yahoo_headers(ticker)
            session.headers.update(headers)
            
            # 直接パラメータを構築してYahoo Financeからデータを取得（エラー処理強化）
            try:
                # 最初の方法 - 標準的な方法
                df = pdr.data.get_data_yahoo(
                    ticker,
                    start=start_date,
                    end=end_date,
                    session=session
                )
            except AttributeError as e:
                # 'NoneType' object has no attribute 'group' エラー対策
                if "'NoneType' object has no attribute 'group'" in str(e):
                    self._log_warning("pandas-datareaderで正規表現エラーが発生しました。内部メソッドでの取得を試行します。")
                    
                    # pandas-datareaderの内部関数を直接使用して取得を試行
                    try:
                        from pandas_datareader.yahoo.daily import YahooDailyReader
                        reader = YahooDailyReader(
                            symbols=ticker,
                            start=start_date,
                            end=end_date,
                            session=session
                        )
                        df = reader.read()
                    except Exception as inner_e:
                        self._log_warning(f"内部メソッドでの取得に失敗: {str(inner_e)}、直接リクエストにフォールバック")
                        # それでも失敗した場合はDirectRequestFetcherにフォールバック
                        if self.direct_request_fetcher:
                            return self.direct_request_fetcher.fetch(ticker, start_date, end_date)
                        else:
                            return None, "Internal method failed and no fallback fetcher available"
                else:
                    # その他の属性エラーは再送出
                    raise
            
            if df.empty:
                elapsed_time = self._record_execution_time(start_time)
                self._log_warning("空のデータフレームが返されました")
                return None, "Empty dataframe returned"
            
            # インデックスリセットとカラム名標準化
            df = df.reset_index()
            df = self._standardize_dataframe(df)
            
            if not self._validate_dataframe(df):
                return None, "Invalid dataframe structure"
            
            elapsed_time = self._record_execution_time(start_time)
            self._log_info(f"データ取得成功: {ticker}, 所要時間: {elapsed_time:.2f}秒")
            
            return df, None
            
        except Exception as e:
            elapsed_time = self._record_execution_time(start_time)
            
            error_msg = str(e)
            self._log_error(f"データ取得エラー: {error_msg}")
            self._log_debug(traceback.format_exc())
            
            return None, error_msg
