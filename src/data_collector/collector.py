#!/usr/bin/env python3
"""
日経225データ収集 - メインコレクタークラス

データ収集全体を管理するクラスを提供します。
"""

import os
import time
from datetime import datetime, timedelta
import pandas as pd
import json
import traceback

from .logger import setup_logging
from .source_manager import DataSourceManager
from .utils import FileUtils
from .fetchers import (
    BaseFetcher,
    YFinanceFetcher, 
    PandasDataReaderFetcher, 
    DirectRequestFetcher,
    StooqScraperFetcher
)

class StockDataCollector:
    """株価データ収集の統合管理クラス"""
    
    def __init__(self, base_dir="nikkei225_data", cache_dir="data/cache", debug=False):
        """
        初期化
        
        Args:
            base_dir (str): 出力ディレクトリのベースパス
            cache_dir (str): キャッシュディレクトリのパス
            debug (bool): デバッグモードの有効・無効
        """
        self.base_dir = base_dir
        self.cache_dir = cache_dir
        self.debug = debug
        
        # ロガー設定
        self.logger = setup_logging(debug=debug)
        
        # ディレクトリ構造
        self.daily_dir = os.path.join(base_dir, "daily")
        self.metadata_dir = os.path.join(base_dir, "metadata")
        self.consolidated_dir = os.path.join(base_dir, "consolidated")
        
        # ディレクトリ作成
        for dir_path in [self.daily_dir, self.metadata_dir, self.consolidated_dir, self.cache_dir]:
            os.makedirs(dir_path, exist_ok=True)
        
        # ステータスとソース統計ファイル
        self.status_file = os.path.join(self.metadata_dir, "collection_status.json")
        self.source_stats_file = os.path.join(self.metadata_dir, "source_stats.json")
        
        # ステータスとソース統計の読み込み
        self.status = FileUtils.load_json_file(
            self.status_file, 
            self._get_default_status(),
            self.logger
        )
        
        self.source_stats = FileUtils.load_json_file(
            self.source_stats_file, 
            self._get_default_source_stats(),
            self.logger
        )
        
        # データソース管理
        self.source_manager = DataSourceManager()
        self.logger.info(f"利用可能なデータソース: {', '.join(self.source_manager.get_available_sources())}")
        
        # フェッチャーの初期化
        self._init_fetchers()
        
        # ソース優先順位の初期化（低いほど優先）
        self.source_weights = {
            "yfinance": 1.0,
            "pandas_datareader": 2.0,
            "stooq_scraper": 3.0,
            "direct_request": 4.0
        }
        
        # 動的な優先順位の調整
        self._adjust_source_weights()
    
    def _get_default_status(self):
        """デフォルトステータスの取得"""
        return {
            "sectors": {},
            "years": {},
            "tickers": {},
            "last_update": None,
            "total_records": 0,
            "total_symbols": 0,
            "completed_years": []
        }
    
    def _get_default_source_stats(self):
        """デフォルトのデータソース統計情報の取得"""
        return {
            "success_rate": {
                "yfinance": {"success": 0, "failure": 0},
                "pandas_datareader": {"success": 0, "failure": 0},
                "stooq_scraper": {"success": 0, "failure": 0},
                "direct_request": {"success": 0, "failure": 0}
            },
            "average_time": {
                "yfinance": 0,
                "pandas_datareader": 0,
                "stooq_scraper": 0,
                "direct_request": 0
            },
            "tickers_by_source": {},
            "last_update": None
        }
    
    def _init_fetchers(self):
        """フェッチャーの初期化"""
        # 直接リクエストフェッチャーを先に作成（他のフェッチャーで参照するため）
        self.direct_request_fetcher = DirectRequestFetcher(logger=self.logger)
        
        # その他のフェッチャーを作成
        self.fetchers = {
            "yfinance": YFinanceFetcher(
                source_manager=self.source_manager,
                logger=self.logger
            ),
            "pandas_datareader": PandasDataReaderFetcher(
                source_manager=self.source_manager,
                direct_request_fetcher=self.direct_request_fetcher,
                logger=self.logger
            ),
            "stooq_scraper": StooqScraperFetcher(
                source_manager=self.source_manager,
                logger=self.logger
            ),
            "direct_request": self.direct_request_fetcher
        }
    
    def _update_status(self):
        """ステータスの更新"""
        self.status["last_update"] = datetime.now().isoformat()
        return FileUtils.save_json_file(self.status_file, self.status, self.logger)
    
    def _update_source_stats(self, ticker, source, success, elapsed_time=0):
        """データソース統計の更新"""
        # 成功/失敗カウントを更新
        if success:
            self.source_stats["success_rate"][source]["success"] += 1
        else:
            self.source_stats["success_rate"][source]["failure"] += 1
        
        # 平均時間の更新
        total_attempts = (self.source_stats["success_rate"][source]["success"] + 
                           self.source_stats["success_rate"][source]["failure"])
        
        current_avg = self.source_stats["average_time"][source]
        if total_attempts > 1:
            new_avg = current_avg + (elapsed_time - current_avg) / total_attempts
        else:
            new_avg = elapsed_time
        
        self.source_stats["average_time"][source] = new_avg
        
        # ティッカーごとの最適ソースを記録
        if success:
            self.source_stats["tickers_by_source"][ticker] = source
        
        # 統計情報を保存
        self.source_stats["last_update"] = datetime.now().isoformat()
        FileUtils.save_json_file(self.source_stats_file, self.source_stats, self.logger)
        
        # ソースウェイトの調整
        self._adjust_source_weights()
    
    def _adjust_source_weights(self):
        """データソースの優先度を成功率に応じて動的に調整"""
        for source in self.source_weights.keys():
            success = self.source_stats["success_rate"][source]["success"]
            failure = self.source_stats["success_rate"][source]["failure"]
            
            if success + failure > 10:  # 十分なデータがある場合のみ調整
                success_rate = success / (success + failure)
                # 成功率が高いほど優先度を高く（ウェイト値を低く）
                adjusted_weight = 5.0 - 4.0 * success_rate
                self.source_weights[source] = max(0.5, min(5.0, adjusted_weight))
    
    def _get_prioritized_sources(self, ticker):
        """ティッカーに最適なデータソースの優先順位を取得"""
        # この銘柄で以前成功したソース
        previous_source = self.source_stats["tickers_by_source"].get(ticker)
        
        # 利用可能なソースをウェイトでソート
        sources = [(s, w) for s, w in self.source_weights.items() 
                  if s in self.fetchers]
        sources.sort(key=lambda x: x[1])  # ウェイトの昇順（優先度の降順）
        
        prioritized = [s[0] for s in sources]
        
        # 以前成功したソースを最優先
        if previous_source and previous_source in prioritized:
            prioritized.remove(previous_source)
            prioritized.insert(0, previous_source)
        
        return prioritized
    
    def _get_cache_filepath(self, ticker, start_date, end_date):
        """キャッシュファイルパスを取得"""
        start_str = start_date.strftime('%Y%m%d')
        end_str = end_date.strftime('%Y%m%d')
        return os.path.join(self.cache_dir, f"{ticker}_{start_str}_{end_str}.csv")
    
    def _check_cache(self, ticker, start_date, end_date):
        """キャッシュを確認"""
        cache_file = self._get_cache_filepath(ticker, start_date, end_date)
        
        if os.path.exists(cache_file):
            try:
                df = pd.read_csv(cache_file)
                if not df.empty and 'Date' in df.columns:
                    df['Date'] = pd.to_datetime(df['Date'])
                    self.logger.info(f"キャッシュから読み込み: {ticker}, {start_date} - {end_date}")
                    return df
            except Exception as e:
                self.logger.warning(f"キャッシュファイル読み込みエラー: {str(e)}")
        
        return None
    
    def _save_to_cache(self, df, ticker, start_date, end_date):
        """データをキャッシュに保存"""
        if df is None or df.empty:
            return False
        
        cache_file = self._get_cache_filepath(ticker, start_date, end_date)
        
        try:
            df.to_csv(cache_file, index=False)
            self.logger.debug(f"キャッシュに保存: {ticker}, {cache_file}")
            return True
        except Exception as e:
            self.logger.warning(f"キャッシュ保存エラー: {str(e)}")
            return False
    
    def get_stock_data(self, ticker, start_date, end_date, use_cache=True, retry_count=3):
        """
        指定された銘柄の株価データを取得
        
        Args:
            ticker (str): 銘柄ティッカー
            start_date (datetime): 取得開始日
            end_date (datetime): 取得終了日
            use_cache (bool): キャッシュを使用するかどうか
            retry_count (int): リトライ回数
            
        Returns:
            DataFrame: 取得したデータ
        """
        self.logger.info(f"データ取得開始: {ticker}, {start_date} - {end_date}")
        
        # キャッシュをチェック
        if use_cache:
            cached_data = self._check_cache(ticker, start_date, end_date)
            if cached_data is not None:
                return cached_data
        
        # 複数のデータソースを試行
        prioritized_sources = self._get_prioritized_sources(ticker)
        self.logger.debug(f"データソース優先順位: {prioritized_sources}")
        
        last_error = None
        
        for source_name in prioritized_sources:
            fetcher = self.fetchers.get(source_name)
            if not fetcher:
                continue
            
            # 特定のフェッチャーが利用可能かチェック（yfinance, pandas-datareader, stooq_scraperのみ）
            if hasattr(fetcher, 'is_available') and not fetcher.is_available():
                self.logger.debug(f"{source_name}は利用できません")
                continue
            
            for attempt in range(retry_count):
                try:
                    self.logger.info(f"{source_name}でデータ取得を試行中... (試行 {attempt+1}/{retry_count})")
                    start_time = time.time()
                    
                    df, error = fetcher.fetch(ticker, start_date, end_date)
                    
                    elapsed_time = time.time() - start_time
                    
                    if df is not None and not df.empty:
                        # 成功
                        self._update_source_stats(ticker, source_name, True, elapsed_time)
                        self.logger.info(f"{source_name}でデータ取得成功: {ticker}, 行数: {len(df)}")
                        
                        # キャッシュに保存
                        if use_cache:
                            self._save_to_cache(df, ticker, start_date, end_date)
                        
                        return df
                    else:
                        # 失敗
                        self._update_source_stats(ticker, source_name, False, elapsed_time)
                        last_error = error
                        self.logger.warning(f"{source_name}でのデータ取得失敗: {error}")
                        
                        # リトライの前に少し待機
                        if attempt < retry_count - 1:
                            wait_time = (attempt + 1) * 2  # 指数バックオフ
                            self.logger.debug(f"{wait_time}秒待機してリトライ...")
                            time.sleep(wait_time)
                
                except Exception as e:
                    self._update_source_stats(ticker, source_name, False)
                    self.logger.error(f"{source_name}でのデータ取得中に例外発生: {str(e)}")
                    if self.debug:
                        self.logger.debug(traceback.format_exc())
                    last_error = str(e)
        
        # すべてのソースが失敗
        self.logger.error(f"すべてのデータソースが失敗しました: {ticker}, 最後のエラー: {last_error}")
        return None
    
    def collect_nikkei225_data(self, year=None, sector=None, tickers=None, 
                             start_date=None, end_date=None, use_cache=True):
        """
        日経225のデータを収集
        
        Args:
            year (int): 特定の年のデータを収集
            sector (str): 特定のセクターのデータを収集
            tickers (list): 特定の銘柄リストのデータを収集
            start_date (datetime): 開始日
            end_date (datetime): 終了日
            use_cache (bool): キャッシュを使用するかどうか
            
        Returns:
            dict: 収集結果の統計情報
        """
        # パラメータの処理
        today = datetime.now()
        
        if not start_date and not end_date and year:
            start_date = datetime(year, 1, 1)
            end_date = datetime(year, 12, 31)
            if end_date > today:
                end_date = today
        elif not start_date:
            start_date = datetime(today.year, 1, 1)
        
        if not end_date:
            end_date = today
        
        # ティッカーリストの取得
        if tickers:
            ticker_list = tickers
        elif sector:
            ticker_list = self.source_manager.get_tickers_by_sector(sector)
        else:
            ticker_list = self.source_manager.get_all_tickers()
        
        self.logger.info(f"データ収集開始: {len(ticker_list)}銘柄, {start_date} - {end_date}")
        
        # 収集結果の統計
        stats = {
            "total": len(ticker_list),
            "success": 0,
            "failure": 0,
            "skipped": 0,
            "tickers": {}
        }
        
        # 各銘柄のデータを取得
        for ticker in ticker_list:
            try:
                df = self.get_stock_data(ticker, start_date, end_date, use_cache=use_cache)
                
                if df is not None and not df.empty:
                    # 保存先ディレクトリ
                    year_str = str(start_date.year)
                    save_dir = os.path.join(self.daily_dir, year_str)
                    os.makedirs(save_dir, exist_ok=True)
                    
                    # ファイル名
                    filename = f"{ticker}_{start_date.strftime('%Y%m%d')}_{end_date.strftime('%Y%m%d')}.csv"
                    filepath = os.path.join(save_dir, filename)
                    
                    # 保存
                    df.to_csv(filepath, index=False)
                    
                    stats["success"] += 1
                    stats["tickers"][ticker] = {
                        "status": "success",
                        "rows": len(df),
                        "file": filepath
                    }
                    
                    self.logger.info(f"データ保存完了: {ticker}, 行数: {len(df)}, ファイル: {filepath}")
                else:
                    stats["failure"] += 1
                    stats["tickers"][ticker] = {
                        "status": "failure",
                        "reason": "No data returned"
                    }
            
            except Exception as e:
                stats["failure"] += 1
                stats["tickers"][ticker] = {
                    "status": "error",
                    "reason": str(e)
                }
                self.logger.error(f"データ収集エラー: {ticker}, エラー: {str(e)}")
                if self.debug:
                    self.logger.debug(traceback.format_exc())
        
        # ステータス更新
        year_key = str(start_date.year)
        if year_key not in self.status["years"]:
            self.status["years"][year_key] = {"total": 0, "success": 0, "updated": None}
        
        self.status["years"][year_key]["total"] = stats["total"]
        self.status["years"][year_key]["success"] = stats["success"]
        self.status["years"][year_key]["updated"] = datetime.now().isoformat()
        
        if sector:
            if sector not in self.status["sectors"]:
                self.status["sectors"][sector] = {"total": 0, "success": 0, "updated": None}
            
            self.status["sectors"][sector]["total"] = stats["total"]
            self.status["sectors"][sector]["success"] = stats["success"]
            self.status["sectors"][sector]["updated"] = datetime.now().isoformat()
        
        self._update_status()
        
        # 結果表示
        self.logger.info(f"データ収集完了: 全体 {stats['total']}銘柄, 成功 {stats['success']}, 失敗 {stats['failure']}")
        
        return stats
    
    def consolidate_data(self, year=None, output_file=None):
        """
        収集したデータを1つのファイルに統合
        
        Args:
            year (int): 特定の年のデータを統合
            output_file (str): 出力ファイル名
            
        Returns:
            str: 統合ファイルのパス
        """
        today = datetime.now()
        year = year or today.year
        year_str = str(year)
        
        # データディレクトリ
        data_dir = os.path.join(self.daily_dir, year_str)
        
        if not os.path.exists(data_dir):
            self.logger.warning(f"データディレクトリが存在しません: {data_dir}")
            return None
        
        # 出力ファイル名
        if not output_file:
            output_file = os.path.join(self.consolidated_dir, f"nikkei225_data_{year_str}.csv")
        
        # CSVファイルのリストを取得
        csv_files = [f for f in os.listdir(data_dir) if f.endswith('.csv')]
        
        if not csv_files:
            self.logger.warning(f"統合対象のCSVファイルがありません: {data_dir}")
            return None
        
        self.logger.info(f"データ統合開始: {len(csv_files)}ファイル, 年: {year_str}")
        
        # 結合用のデータフレームリスト
        all_data = []
        
        # 各ファイルを読み込んで結合
        for csv_file in csv_files:
            try:
                file_path = os.path.join(data_dir, csv_file)
                
                # ティッカーを抽出
                ticker = csv_file.split('_')[0]
                
                # データ読み込み
                df = pd.read_csv(file_path)
                
                if 'Date' in df.columns:
                    df['Date'] = pd.to_datetime(df['Date'])
                
                # ティッカー列を追加
                df['Ticker'] = ticker
                
                all_data.append(df)
                
            except Exception as e:
                self.logger.error(f"ファイル読み込みエラー: {csv_file}, エラー: {str(e)}")
        
        if not all_data:
            self.logger.warning("統合するデータがありません")
            return None
        
        # データを結合
        combined_df = pd.concat(all_data, ignore_index=True)
        
        # 日付でソート
        combined_df = combined_df.sort_values(by=['Ticker', 'Date'])
        
        # 保存
        combined_df.to_csv(output_file, index=False)
        
        self.logger.info(f"データ統合完了: {len(combined_df)}行, ファイル: {output_file}")
        
        return output_file
