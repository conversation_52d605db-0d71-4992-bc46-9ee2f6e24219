#!/usr/bin/env python3
"""
日経225データ収集 - コマンドラインインターフェース

データ収集機能をコマンドラインから利用するためのインターフェースを提供します。
"""

import argparse
import sys
import os
from datetime import datetime
import textwrap

from .collector import StockDataCollector

def parse_date(date_str):
    """日付文字列をdatetimeオブジェクトに変換"""
    if not date_str:
        return None
    
    formats = ['%Y-%m-%d', '%Y/%m/%d', '%Y%m%d']
    
    for fmt in formats:
        try:
            return datetime.strptime(date_str, fmt)
        except ValueError:
            continue
    
    raise ValueError(f"サポートされていない日付形式です: {date_str}. 使用例: YYYY-MM-DD, YYYY/MM/DD, YYYYMMDD")

def main():
    """メイン関数"""
    parser = argparse.ArgumentParser(
        description='日経225のデータを収集するコマンドラインツール',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog=textwrap.dedent('''
            使用例:
              # デフォルト設定で実行（今年のデータを全銘柄収集）
              python -m src.data_collector.cli
              
              # 特定の年のデータを収集
              python -m src.data_collector.cli --year 2023
              
              # 特定の期間のデータを収集
              python -m src.data_collector.cli --start 2023-01-01 --end 2023-12-31
              
              # 特定のセクターのデータを収集
              python -m src.data_collector.cli --sector 電気機器
              
              # 特定の銘柄のデータを収集
              python -m src.data_collector.cli --tickers 7203.T,9984.T
              
              # データを統合
              python -m src.data_collector.cli --consolidate --year 2023
        ''')
    )
    
    parser.add_argument('--year', type=int, help='収集する年（例: 2023）')
    parser.add_argument('--start', type=str, help='開始日（例: 2023-01-01）')
    parser.add_argument('--end', type=str, help='終了日（例: 2023-12-31）')
    parser.add_argument('--sector', type=str, help='収集するセクター')
    parser.add_argument('--tickers', type=str, help='収集する銘柄（カンマ区切り）')
    parser.add_argument('--base-dir', type=str, default='nikkei225_data', help='出力ディレクトリのベースパス')
    parser.add_argument('--cache-dir', type=str, default='data/cache', help='キャッシュディレクトリのパス')
    parser.add_argument('--no-cache', action='store_true', help='キャッシュを使用しない')
    parser.add_argument('--debug', action='store_true', help='デバッグモードを有効にする')
    parser.add_argument('--consolidate', action='store_true', help='収集データを統合する')
    parser.add_argument('--output', type=str, help='統合データの出力ファイル名')
    
    args = parser.parse_args()
    
    # 日付の処理
    start_date = None
    end_date = None
    
    if args.start:
        try:
            start_date = parse_date(args.start)
        except ValueError as e:
            print(f"エラー: {e}", file=sys.stderr)
            return 1
    
    if args.end:
        try:
            end_date = parse_date(args.end)
        except ValueError as e:
            print(f"エラー: {e}", file=sys.stderr)
            return 1
    
    # ティッカーの処理
    tickers = None
    if args.tickers:
        tickers = [t.strip() for t in args.tickers.split(',')]
    
    # コレクターの初期化
    collector = StockDataCollector(
        base_dir=args.base_dir,
        cache_dir=args.cache_dir,
        debug=args.debug
    )
    
    # 動作の分岐
    if args.consolidate:
        # データの統合
        output_file = args.output
        year = args.year or datetime.now().year
        
        print(f"{year}年のデータを統合中...")
        result = collector.consolidate_data(year=year, output_file=output_file)
        
        if result:
            print(f"データ統合完了: {result}")
        else:
            print("データ統合に失敗しました", file=sys.stderr)
            return 1
    else:
        # データの収集
        use_cache = not args.no_cache
        
        print("データ収集開始...")
        stats = collector.collect_nikkei225_data(
            year=args.year,
            sector=args.sector,
            tickers=tickers,
            start_date=start_date,
            end_date=end_date,
            use_cache=use_cache
        )
        
        print(f"データ収集完了:")
        print(f"  全体: {stats['total']}銘柄")
        print(f"  成功: {stats['success']}銘柄")
        print(f"  失敗: {stats['failure']}銘柄")
    
    return 0

if __name__ == '__main__':
    sys.exit(main())
