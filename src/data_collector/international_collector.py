import json
import logging
import pandas as pd
import yfinance as yf
from typing import Dict, List, Optional, Tuple
from datetime import datetime, timedelta
import pytz
from pathlib import Path
import time
from concurrent.futures import ThreadPoolExecutor, as_completed
import numpy as np

class InternationalDataCollector:
    """
    Enhanced data collector for international markets, forex, and commodities
    that influence Nikkei 225 performance.
    """
    
    def __init__(self, config_path: str = "config/international_markets_config.json"):
        self.config_path = Path(config_path)
        self.config = self._load_config()
        self.logger = self._setup_logging()
        self.data_dir = Path("international_data")
        self.data_dir.mkdir(exist_ok=True)
        
        # Create subdirectories for different data types
        for data_type in ['indices', 'forex', 'commodities', 'indicators']:
            (self.data_dir / data_type).mkdir(exist_ok=True)
        
        self.rate_limiter = {}
        self.last_request_time = {}
        
    def _load_config(self) -> Dict:
        """Load international markets configuration."""
        try:
            with open(self.config_path, 'r') as f:
                return json.load(f)
        except FileNotFoundError:
            self.logger.error(f"Config file not found: {self.config_path}")
            return {}
    
    def _setup_logging(self) -> logging.Logger:
        """Setup logging for international data collection."""
        logger = logging.getLogger('InternationalDataCollector')
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        
        return logger
    
    def _respect_rate_limits(self, source: str = "yahoo_finance"):
        """Implement rate limiting for API calls."""
        current_time = time.time()
        
        if source not in self.last_request_time:
            self.last_request_time[source] = 0
        
        time_since_last = current_time - self.last_request_time[source]
        min_interval = 60 / self.config.get('collection_settings', {}).get('rate_limits', {}).get(source, 100)
        
        if time_since_last < min_interval:
            sleep_time = min_interval - time_since_last
            time.sleep(sleep_time)
        
        self.last_request_time[source] = time.time()
    
    def collect_market_indices(self, period: str = "1y", interval: str = "1h") -> Dict[str, pd.DataFrame]:
        """
        Collect data from major international market indices.
        
        Args:
            period: Data period (1d, 5d, 1mo, 3mo, 6mo, 1y, 2y, 5y, 10y, ytd, max)
            interval: Data interval (1m, 2m, 5m, 15m, 30m, 60m, 90m, 1h, 1d, 5d, 1wk, 1mo, 3mo)
        
        Returns:
            Dictionary of DataFrames containing market data
        """
        all_indices = {}
        
        # Collect all indices from different markets
        for market_name, market_config in self.config.get('international_markets', {}).items():
            if not market_config.get('enabled', False):
                continue
                
            indices = market_config.get('indices', {})
            self.logger.info(f"Collecting {len(indices)} indices from {market_name}")
            
            for index_name, index_config in indices.items():
                try:
                    self._respect_rate_limits()
                    
                    symbol = index_config['symbol']
                    ticker = yf.Ticker(symbol)
                    
                    # Get historical data
                    data = ticker.history(period=period, interval=interval)
                    
                    if not data.empty:
                        # Add metadata
                        data['market'] = market_name
                        data['index_name'] = index_name
                        data['priority'] = index_config.get('priority', 'medium')
                        data['symbol'] = symbol
                        
                        # Calculate additional features
                        data = self._add_technical_indicators(data)
                        
                        all_indices[f"{market_name}_{index_name}"] = data
                        
                        # Save to file
                        filename = f"{symbol.replace('^', '').replace('=', '_')}_{'_'.join(index_name.split())}.csv"
                        filepath = self.data_dir / 'indices' / filename
                        data.to_csv(filepath)
                        
                        self.logger.info(f"Collected {len(data)} records for {index_name} ({symbol})")
                    else:
                        self.logger.warning(f"No data available for {index_name} ({symbol})")
                        
                except Exception as e:
                    self.logger.error(f"Error collecting {index_name}: {str(e)}")
        
        return all_indices
    
    def collect_forex_data(self, period: str = "1y", interval: str = "1h") -> Dict[str, pd.DataFrame]:
        """
        Collect forex data for currency pairs that influence Japanese markets.
        
        Args:
            period: Data period
            interval: Data interval
            
        Returns:
            Dictionary of DataFrames containing forex data
        """
        all_forex = {}
        
        # Collect major pairs
        for category, pairs in self.config.get('forex_pairs', {}).items():
            self.logger.info(f"Collecting {len(pairs)} pairs from {category}")
            
            for pair_name, pair_config in pairs.items():
                try:
                    self._respect_rate_limits()
                    
                    symbol = pair_config['symbol']
                    ticker = yf.Ticker(symbol)
                    
                    # Get historical data
                    data = ticker.history(period=period, interval=interval)
                    
                    if not data.empty:
                        # Add metadata
                        data['pair_name'] = pair_name
                        data['category'] = category
                        data['priority'] = pair_config.get('priority', 'medium')
                        data['symbol'] = symbol
                        
                        # Calculate forex-specific indicators
                        data = self._add_forex_indicators(data)
                        
                        all_forex[f"{category}_{pair_name}"] = data
                        
                        # Save to file
                        filename = f"{symbol.replace('/', '_').replace('=', '_')}.csv"
                        filepath = self.data_dir / 'forex' / filename
                        data.to_csv(filepath)
                        
                        self.logger.info(f"Collected {len(data)} records for {pair_name} ({symbol})")
                    else:
                        self.logger.warning(f"No data available for {pair_name} ({symbol})")
                        
                except Exception as e:
                    self.logger.error(f"Error collecting {pair_name}: {str(e)}")
        
        return all_forex
    
    def collect_commodities_data(self, period: str = "1y", interval: str = "1h") -> Dict[str, pd.DataFrame]:
        """
        Collect commodities data that affects Japanese economy.
        
        Args:
            period: Data period
            interval: Data interval
            
        Returns:
            Dictionary of DataFrames containing commodities data
        """
        all_commodities = {}
        
        for category, commodities in self.config.get('commodities', {}).items():
            self.logger.info(f"Collecting {len(commodities)} commodities from {category}")
            
            for commodity_name, commodity_config in commodities.items():
                try:
                    self._respect_rate_limits()
                    
                    symbol = commodity_config['symbol']
                    ticker = yf.Ticker(symbol)
                    
                    # Get historical data
                    data = ticker.history(period=period, interval=interval)
                    
                    if not data.empty:
                        # Add metadata
                        data['commodity_name'] = commodity_name
                        data['category'] = category
                        data['priority'] = commodity_config.get('priority', 'medium')
                        data['symbol'] = symbol
                        
                        # Calculate commodity-specific indicators
                        data = self._add_commodity_indicators(data)
                        
                        all_commodities[f"{category}_{commodity_name}"] = data
                        
                        # Save to file
                        filename = f"{symbol.replace('=', '_')}_{'_'.join(commodity_name.split())}.csv"
                        filepath = self.data_dir / 'commodities' / filename
                        data.to_csv(filepath)
                        
                        self.logger.info(f"Collected {len(data)} records for {commodity_name} ({symbol})")
                    else:
                        self.logger.warning(f"No data available for {commodity_name} ({symbol})")
                        
                except Exception as e:
                    self.logger.error(f"Error collecting {commodity_name}: {str(e)}")
        
        return all_commodities
    
    def collect_economic_indicators(self, period: str = "1y", interval: str = "1d") -> Dict[str, pd.DataFrame]:
        """
        Collect economic indicators that influence global markets.
        
        Args:
            period: Data period
            interval: Data interval
            
        Returns:
            Dictionary of DataFrames containing economic indicators
        """
        all_indicators = {}
        
        for category, indicators in self.config.get('economic_indicators', {}).items():
            self.logger.info(f"Collecting {len(indicators)} indicators from {category}")
            
            for indicator_name, indicator_config in indicators.items():
                try:
                    self._respect_rate_limits()
                    
                    symbol = indicator_config['symbol']
                    ticker = yf.Ticker(symbol)
                    
                    # Get historical data
                    data = ticker.history(period=period, interval=interval)
                    
                    if not data.empty:
                        # Add metadata
                        data['indicator_name'] = indicator_name
                        data['category'] = category
                        data['priority'] = indicator_config.get('priority', 'medium')
                        data['symbol'] = symbol
                        
                        # Calculate indicator-specific features
                        data = self._add_indicator_features(data)
                        
                        all_indicators[f"{category}_{indicator_name}"] = data
                        
                        # Save to file
                        filename = f"{symbol.replace('^', '').replace('=', '_')}_{'_'.join(indicator_name.split())}.csv"
                        filepath = self.data_dir / 'indicators' / filename
                        data.to_csv(filepath)
                        
                        self.logger.info(f"Collected {len(data)} records for {indicator_name} ({symbol})")
                    else:
                        self.logger.warning(f"No data available for {indicator_name} ({symbol})")
                        
                except Exception as e:
                    self.logger.error(f"Error collecting {indicator_name}: {str(e)}")
        
        return all_indicators
    
    def collect_all_data(self, period: str = "1y", interval: str = "1h") -> Dict[str, Dict[str, pd.DataFrame]]:
        """
        Collect all international data types.
        
        Args:
            period: Data period
            interval: Data interval
            
        Returns:
            Dictionary containing all collected data organized by type
        """
        self.logger.info("Starting comprehensive international data collection")
        
        all_data = {
            'indices': self.collect_market_indices(period, interval),
            'forex': self.collect_forex_data(period, interval),
            'commodities': self.collect_commodities_data(period, interval),
            'indicators': self.collect_economic_indicators(period, '1d')  # Daily for indicators
        }
        
        # Generate collection summary
        total_datasets = sum(len(datasets) for datasets in all_data.values())
        self.logger.info(f"International data collection completed: {total_datasets} datasets collected")
        
        # Save collection metadata
        metadata = {
            'collection_time': datetime.now().isoformat(),
            'period': period,
            'interval': interval,
            'datasets_collected': {
                data_type: list(datasets.keys()) 
                for data_type, datasets in all_data.items()
            },
            'total_datasets': total_datasets
        }
        
        with open(self.data_dir / 'collection_metadata.json', 'w') as f:
            json.dump(metadata, f, indent=2)
        
        return all_data
    
    def _add_technical_indicators(self, data: pd.DataFrame) -> pd.DataFrame:
        """Add technical indicators to market data."""
        df = data.copy()
        
        # Price-based indicators
        df['price_change'] = df['Close'].pct_change()
        df['price_change_5d'] = df['Close'].pct_change(periods=5)
        df['price_change_20d'] = df['Close'].pct_change(periods=20)
        
        # Moving averages
        df['sma_5'] = df['Close'].rolling(window=5).mean()
        df['sma_20'] = df['Close'].rolling(window=20).mean()
        df['sma_50'] = df['Close'].rolling(window=50).mean()
        
        # Volatility
        df['volatility_20d'] = df['price_change'].rolling(window=20).std()
        
        # Volume indicators
        if 'Volume' in df.columns:
            df['volume_change'] = df['Volume'].pct_change()
            df['volume_sma_20'] = df['Volume'].rolling(window=20).mean()
        
        return df
    
    def _add_forex_indicators(self, data: pd.DataFrame) -> pd.DataFrame:
        """Add forex-specific indicators."""
        df = data.copy()
        
        # Currency-specific indicators
        df['fx_change'] = df['Close'].pct_change()
        df['fx_volatility'] = df['fx_change'].rolling(window=20).std()
        df['fx_momentum'] = df['Close'] / df['Close'].shift(20) - 1
        
        # Support/Resistance levels
        df['high_20d'] = df['High'].rolling(window=20).max()
        df['low_20d'] = df['Low'].rolling(window=20).min()
        df['position_in_range'] = (df['Close'] - df['low_20d']) / (df['high_20d'] - df['low_20d'])
        
        return df
    
    def _add_commodity_indicators(self, data: pd.DataFrame) -> pd.DataFrame:
        """Add commodity-specific indicators."""
        df = data.copy()
        
        # Commodity-specific features
        df['commodity_change'] = df['Close'].pct_change()
        df['commodity_volatility'] = df['commodity_change'].rolling(window=20).std()
        df['commodity_trend'] = df['Close'].rolling(window=50).mean()
        
        return df
    
    def _add_indicator_features(self, data: pd.DataFrame) -> pd.DataFrame:
        """Add features for economic indicators."""
        df = data.copy()
        
        # Rate of change for indicators
        df['indicator_change'] = df['Close'].pct_change()
        df['indicator_change_5d'] = df['Close'].pct_change(periods=5)
        df['indicator_trend'] = df['Close'].rolling(window=20).mean()
        
        return df
    
    def get_correlation_matrix(self, nikkei_data: pd.DataFrame) -> pd.DataFrame:
        """
        Calculate correlation matrix between international data and Nikkei 225.
        
        Args:
            nikkei_data: Nikkei 225 historical data
            
        Returns:
            Correlation matrix DataFrame
        """
        # Load all international data
        all_international_data = {}
        
        # Load indices data
        indices_dir = self.data_dir / 'indices'
        for file_path in indices_dir.glob('*.csv'):
            try:
                data = pd.read_csv(file_path, index_col=0, parse_dates=True)
                key = file_path.stem
                all_international_data[f"index_{key}"] = data['Close']
            except Exception as e:
                self.logger.warning(f"Could not load {file_path}: {e}")
        
        # Load forex data
        forex_dir = self.data_dir / 'forex'
        for file_path in forex_dir.glob('*.csv'):
            try:
                data = pd.read_csv(file_path, index_col=0, parse_dates=True)
                key = file_path.stem
                all_international_data[f"forex_{key}"] = data['Close']
            except Exception as e:
                self.logger.warning(f"Could not load {file_path}: {e}")
        
        # Load commodities data
        commodities_dir = self.data_dir / 'commodities'
        for file_path in commodities_dir.glob('*.csv'):
            try:
                data = pd.read_csv(file_path, index_col=0, parse_dates=True)
                key = file_path.stem
                all_international_data[f"commodity_{key}"] = data['Close']
            except Exception as e:
                self.logger.warning(f"Could not load {file_path}: {e}")
        
        # Combine all data
        combined_data = pd.DataFrame(all_international_data)
        combined_data['nikkei_225'] = nikkei_data['Close']
        
        # Calculate correlations
        correlation_matrix = combined_data.corr()
        
        # Save correlation matrix
        correlation_matrix.to_csv(self.data_dir / 'correlation_matrix.csv')
        
        return correlation_matrix
    
    def get_high_correlation_features(self, nikkei_data: pd.DataFrame, threshold: float = 0.3) -> List[str]:
        """
        Get international features with high correlation to Nikkei 225.
        
        Args:
            nikkei_data: Nikkei 225 historical data
            threshold: Minimum correlation threshold
            
        Returns:
            List of highly correlated features
        """
        correlation_matrix = self.get_correlation_matrix(nikkei_data)
        nikkei_correlations = correlation_matrix['nikkei_225'].abs()
        
        high_correlation_features = nikkei_correlations[
            (nikkei_correlations >= threshold) & 
            (nikkei_correlations.index != 'nikkei_225')
        ].sort_values(ascending=False)
        
        self.logger.info(f"Found {len(high_correlation_features)} features with correlation >= {threshold}")
        
        return high_correlation_features.index.tolist()