#!/usr/bin/env python3
"""
Optimal Data Collector for AI Learning

This module implements the optimal data collection strategy:
- Japanese stocks (Nikkei 225): 1-hour intervals via Kabu Station API
- International markets: Daily intervals via Yahoo Finance API
- Designed for AI learning optimization
"""

import json
import logging
import pandas as pd
import numpy as np
import time
import threading
import schedule
from datetime import datetime, timed<PERSON>ta
from typing import Dict, List, Optional, Tuple
from pathlib import Path
import sys

# Add project root to path
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

# Import existing modules
from src.trading.kabu_api import KabuSession
from src.trading.kabu_collector import KabuDataCollector
import os

class OptimalDataCollector:
    """
    Optimal data collector implementing the specified strategy:
    - Japanese stocks: 1-hour intervals from Kabu Station API
    - All market data through Kabu Station API only
    - No Yahoo Finance API dependency
    - Optimized for AI learning
    """
    
    def __init__(self, config_path: str = "config/international_markets_config.json"):
        self.config_path = Path(config_path)
        self.config = self._load_config()
        self.logger = self._setup_logging()
        
        # Initialize collectors
        self.kabu_session = None
        self.kabu_collector = None
        
        # Data storage
        self.hourly_data_dir = Path("data/hourly_nikkei225")
        self.kabu_data_dir = Path("data/kabu_all_data")
        self.ai_learning_data_dir = Path("data/ai_learning")
        
        # Create directories
        for directory in [self.hourly_data_dir, self.kabu_data_dir, self.ai_learning_data_dir]:
            directory.mkdir(parents=True, exist_ok=True)
        
        # Collection state
        self.is_running = False
        self.collection_thread = None
        
        # Nikkei 225 symbols
        self.nikkei_225_symbols = self._get_nikkei_225_symbols()
        
    def _load_config(self) -> Dict:
        """Load configuration."""
        try:
            with open(self.config_path, 'r') as f:
                return json.load(f)
        except FileNotFoundError:
            self.logger.error(f"Config file not found: {self.config_path}")
            return {}
    
    def _setup_logging(self) -> logging.Logger:
        """Setup logging."""
        logger = logging.getLogger('OptimalDataCollector')
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        
        return logger
    
    def _get_nikkei_225_symbols(self) -> List[str]:
        """Get Nikkei 225 symbols list."""
        # Try to load from existing meta data
        meta_file = Path("results/kabu/data/meta/symbol_meta_20250702.json")
        
        if meta_file.exists():
            try:
                with open(meta_file, 'r') as f:
                    data = json.load(f)
                symbols = list(data.keys())
                self.logger.info(f"Loaded {len(symbols)} Nikkei 225 symbols from meta file")
                return symbols
            except Exception as e:
                self.logger.warning(f"Error loading symbols from meta file: {e}")
        
        # Fallback to major Nikkei 225 symbols
        major_symbols = [
            "1332.T", "1605.T", "1801.T", "1802.T", "1803.T", "1812.T", "1925.T", "1928.T", "1963.T",
            "2002.T", "2269.T", "2282.T", "2413.T", "2432.T", "2433.T", "2501.T", "2502.T", "2503.T",
            "2531.T", "2730.T", "2768.T", "2801.T", "2802.T", "2871.T", "2914.T", "3086.T", "3099.T",
            "3101.T", "3103.T", "3289.T", "3382.T", "3401.T", "3402.T", "3405.T", "3407.T", "3659.T",
            "3769.T", "3861.T", "3863.T", "4004.T", "4005.T", "4021.T", "4042.T", "4043.T", "4061.T",
            "4063.T", "4151.T", "4183.T", "4188.T", "4208.T", "4324.T", "4452.T", "4502.T", "4503.T",
            "4506.T", "4507.T", "4519.T", "4523.T", "4543.T", "4568.T", "4661.T", "4689.T", "4704.T",
            "4751.T", "4755.T", "4901.T", "4902.T", "4911.T", "5020.T", "5101.T", "5108.T", "5110.T",
            "5201.T", "5202.T", "5214.T", "5232.T", "5233.T", "5301.T", "5332.T", "5333.T", "5401.T",
            "5406.T", "5411.T", "5541.T", "5631.T", "5703.T", "5706.T", "5707.T", "5711.T", "5713.T",
            "5714.T", "5801.T", "5802.T", "5803.T", "5901.T", "6098.T", "6103.T", "6113.T", "6301.T",
            "6302.T", "6305.T", "6326.T", "6361.T", "6366.T", "6367.T", "6471.T", "6472.T", "6473.T",
            "6474.T", "6479.T", "6501.T", "6503.T", "6504.T", "6506.T", "6586.T", "6645.T", "6674.T",
            "6752.T", "6758.T", "6762.T", "6770.T", "6841.T", "6857.T", "6861.T", "6902.T", "6952.T",
            "6954.T", "6971.T", "6976.T", "7004.T", "7011.T", "7013.T", "7201.T", "7202.T", "7203.T",
            "7205.T", "7211.T", "7261.T", "7267.T", "7269.T", "7270.T", "7272.T", "7731.T", "7733.T",
            "7735.T", "7741.T", "7751.T", "7752.T", "7762.T", "7911.T", "7912.T", "7951.T", "7974.T",
            "8001.T", "8002.T", "8015.T", "8031.T", "8035.T", "8053.T", "8058.T", "8233.T", "8252.T",
            "8267.T", "8304.T", "8306.T", "8308.T", "8309.T", "8316.T", "8331.T", "8354.T", "8411.T",
            "8570.T", "8593.T", "8601.T", "8604.T", "8628.T", "8630.T", "8697.T", "8725.T", "8750.T",
            "8766.T", "8795.T", "8801.T", "8802.T", "8804.T", "8830.T", "9001.T", "9005.T", "9007.T",
            "9008.T", "9009.T", "9020.T", "9021.T", "9022.T", "9064.T", "9101.T", "9104.T", "9107.T",
            "9202.T", "9301.T", "9412.T", "9432.T", "9433.T", "9434.T", "9501.T", "9502.T", "9503.T",
            "9531.T", "9532.T", "9613.T", "9684.T", "9697.T", "9719.T", "9735.T", "9740.T", "9742.T",
            "9766.T", "9983.T", "9984.T"
        ]
        
        self.logger.info(f"Using fallback list of {len(major_symbols)} major Nikkei 225 symbols")
        return major_symbols
    
    def initialize_kabu_connection(self) -> bool:
        """Initialize Kabu Station API connection."""
        try:
            # Get API password from environment
            api_password = os.environ.get('KABU_API_PWD', 'marumori02')
            
            # Initialize Kabu session
            self.kabu_session = KabuSession(api_password)
            
            # Test connection by ensuring token is valid
            self.kabu_session.ensure_token()
            if not self.kabu_session.token:
                self.logger.error("Failed to get Kabu Station API token")
                return False
            
            # Initialize collector
            self.kabu_collector = KabuDataCollector(
                session=self.kabu_session,
                output_dir=str(self.hourly_data_dir)
            )
            
            self.logger.info("Kabu Station API connection initialized successfully")
            return True
            
        except Exception as e:
            self.logger.error(f"Error initializing Kabu connection: {e}")
            return False
    
    def collect_hourly_nikkei_data(self) -> bool:
        """
        Collect 1-hour interval data for Nikkei 225 stocks from Kabu Station API.
        
        Returns:
            bool: Success status
        """
        if not self.kabu_collector:
            self.logger.error("Kabu collector not initialized")
            return False
        
        try:
            self.logger.info("Starting hourly Nikkei 225 data collection")
            
            # Collect meta information first
            meta_data = self.kabu_collector.collect_all_symbol_meta()
            if meta_data:
                self.logger.info(f"Collected meta data for {len(meta_data)} symbols")
                
                # Save meta data for AI learning
                meta_file = self.ai_learning_data_dir / f"nikkei_meta_{datetime.now().strftime('%Y%m%d_%H')}.json"
                with open(meta_file, 'w') as f:
                    json.dump(meta_data, f, indent=2, default=str)
            
            # Collect board data in batches (due to 50-symbol API limit)
            batch_size = 50
            all_board_data = {}
            
            for i in range(0, len(self.nikkei_225_symbols), batch_size):
                batch_symbols = self.nikkei_225_symbols[i:i + batch_size]
                self.logger.info(f"Collecting board data for batch {i//batch_size + 1}: {len(batch_symbols)} symbols")
                
                # Rotate symbols if necessary
                self.kabu_collector.rotate_symbols(batch_symbols)
                time.sleep(2)  # Wait for registration
                
                # Collect board data
                batch_data = self.kabu_collector.collect_board_data(batch_symbols)
                all_board_data.update(batch_data)
                
                self.logger.info(f"Collected board data for {len(batch_data)} symbols in batch")
                
                # Small delay between batches
                time.sleep(1)
            
            # Save hourly data for AI learning
            timestamp = datetime.now().strftime('%Y%m%d_%H')
            hourly_file = self.ai_learning_data_dir / f"nikkei_hourly_{timestamp}.json"
            
            # Convert to AI-friendly format
            ai_data = self._format_data_for_ai(all_board_data, 'hourly_nikkei')
            
            with open(hourly_file, 'w') as f:
                json.dump(ai_data, f, indent=2, default=str)
            
            self.logger.info(f"Hourly Nikkei data saved: {len(all_board_data)} symbols -> {hourly_file}")
            return True
            
        except Exception as e:
            self.logger.error(f"Error collecting hourly Nikkei data: {e}")
            return False
    
    def collect_additional_kabu_data(self) -> bool:
        """
        Collect additional market data available through Kabu Station API.
        This includes market indices, board snapshots, and extended analysis data.
        
        Returns:
            bool: Success status
        """
        try:
            self.logger.info("Starting additional Kabu Station API data collection")
            
            if not self.kabu_collector:
                self.logger.error("Kabu collector not initialized")
                return False
            
            # Collect comprehensive market snapshot data
            additional_data = {
                'timestamp': datetime.now().isoformat(),
                'market_snapshot': {},
                'board_summary': {},
                'volume_analysis': {},
                'price_movements': {}
            }
            
            # Get current board data for all symbols
            all_board_data = {}
            batch_size = 50
            
            for i in range(0, len(self.nikkei_225_symbols), batch_size):
                batch_symbols = self.nikkei_225_symbols[i:i + batch_size]
                self.logger.info(f"Collecting additional data for batch {i//batch_size + 1}: {len(batch_symbols)} symbols")
                
                # Rotate symbols if necessary
                self.kabu_collector.rotate_symbols(batch_symbols)
                time.sleep(2)  # Wait for registration
                
                # Collect board data
                batch_data = self.kabu_collector.collect_board_data(batch_symbols)
                all_board_data.update(batch_data)
                
                time.sleep(1)  # Small delay between batches
            
            # Analyze market conditions
            additional_data['market_snapshot'] = self._analyze_market_conditions(all_board_data)
            additional_data['board_summary'] = self._create_board_summary(all_board_data)
            additional_data['volume_analysis'] = self._analyze_volume_patterns(all_board_data)
            additional_data['price_movements'] = self._analyze_price_movements(all_board_data)
            
            # Save additional data for AI learning
            timestamp = datetime.now().strftime('%Y%m%d')
            kabu_file = self.ai_learning_data_dir / f"kabu_additional_{timestamp}.json"
            
            # Convert to AI-friendly format
            ai_data = self._format_data_for_ai(additional_data, 'kabu_additional')
            
            with open(kabu_file, 'w') as f:
                json.dump(ai_data, f, indent=2, default=str)
            
            self.logger.info(f"Additional Kabu data saved: {len(all_board_data)} symbols -> {kabu_file}")
            return True
            
        except Exception as e:
            self.logger.error(f"Error collecting additional Kabu data: {e}")
            return False
    
    def _format_data_for_ai(self, raw_data: Dict, data_type: str) -> Dict:
        """
        Format collected data for AI learning optimization.
        
        Args:
            raw_data: Raw collected data
            data_type: Type of data ('hourly_nikkei' or 'daily_international')
            
        Returns:
            Formatted data optimized for AI learning
        """
        ai_data = {
            'metadata': {
                'collection_time': datetime.now().isoformat(),
                'data_type': data_type,
                'collection_source': 'kabu_station_api' if 'nikkei' in data_type else 'yahoo_finance',
                'record_count': len(raw_data) if isinstance(raw_data, dict) else 0
            },
            'features': {},
            'targets': {},
            'market_conditions': {}
        }
        
        if data_type == 'hourly_nikkei':
            # Format Nikkei hourly data for AI learning
            for symbol, data in raw_data.items():
                if isinstance(data, dict) and 'CurrentPrice' in data:
                    ai_data['features'][symbol] = {
                        'price': data.get('CurrentPrice', 0),
                        'volume': data.get('TradingVolume', 0),
                        'bid_price': data.get('BidPrice', 0),
                        'ask_price': data.get('AskPrice', 0),
                        'high': data.get('HighPrice', 0),
                        'low': data.get('LowPrice', 0),
                        'change_from_previous': data.get('ChangeFromPreviousClose', 0),
                        'change_percent': data.get('ChangeFromPreviousClosePercent', 0)
                    }
                    
                    # Calculate target for next hour prediction
                    ai_data['targets'][symbol] = {
                        'next_hour_change_target': data.get('ChangeFromPreviousClosePercent', 0)
                    }
        
        elif data_type == 'kabu_additional':
            # Format additional Kabu data for AI learning
            ai_data['features']['market_conditions'] = raw_data.get('market_snapshot', {})
            ai_data['features']['board_summary'] = raw_data.get('board_summary', {})
            ai_data['features']['volume_analysis'] = raw_data.get('volume_analysis', {})
            ai_data['features']['price_movements'] = raw_data.get('price_movements', {})
        
        return ai_data
    
    def _analyze_market_conditions(self, board_data: Dict) -> Dict:
        """Analyze overall market conditions from board data."""
        if not board_data:
            return {}
        
        total_symbols = len(board_data)
        positive_moves = 0
        negative_moves = 0
        total_volume = 0
        price_changes = []
        
        for symbol, data in board_data.items():
            if isinstance(data, dict):
                change = data.get('ChangeFromPreviousClosePercent', 0)
                volume = data.get('TradingVolume', 0)
                
                if change > 0:
                    positive_moves += 1
                elif change < 0:
                    negative_moves += 1
                
                total_volume += volume
                price_changes.append(change)
        
        avg_change = np.mean(price_changes) if price_changes else 0
        volatility = np.std(price_changes) if price_changes else 0
        
        return {
            'total_symbols': total_symbols,
            'positive_moves': positive_moves,
            'negative_moves': negative_moves,
            'market_sentiment': positive_moves / total_symbols if total_symbols > 0 else 0,
            'total_volume': total_volume,
            'average_change': avg_change,
            'volatility': volatility
        }
    
    def _create_board_summary(self, board_data: Dict) -> Dict:
        """Create board data summary for AI learning."""
        summary = {
            'high_volume_symbols': [],
            'high_volatility_symbols': [],
            'top_gainers': [],
            'top_losers': []
        }
        
        if not board_data:
            return summary
        
        # Extract data for analysis
        symbols_data = []
        for symbol, data in board_data.items():
            if isinstance(data, dict):
                symbols_data.append({
                    'symbol': symbol,
                    'volume': data.get('TradingVolume', 0),
                    'change': data.get('ChangeFromPreviousClosePercent', 0),
                    'price': data.get('CurrentPrice', 0),
                    'high': data.get('HighPrice', 0),
                    'low': data.get('LowPrice', 0)
                })
        
        if not symbols_data:
            return summary
        
        # Sort by volume
        by_volume = sorted(symbols_data, key=lambda x: x['volume'], reverse=True)
        summary['high_volume_symbols'] = [s['symbol'] for s in by_volume[:10]]
        
        # Sort by change
        by_gain = sorted(symbols_data, key=lambda x: x['change'], reverse=True)
        summary['top_gainers'] = [s['symbol'] for s in by_gain[:10]]
        summary['top_losers'] = [s['symbol'] for s in by_gain[-10:]]
        
        # Calculate volatility (high-low range)
        for s in symbols_data:
            if s['low'] > 0:
                s['volatility'] = (s['high'] - s['low']) / s['low']
            else:
                s['volatility'] = 0
        
        by_volatility = sorted(symbols_data, key=lambda x: x['volatility'], reverse=True)
        summary['high_volatility_symbols'] = [s['symbol'] for s in by_volatility[:10]]
        
        return summary
    
    def _analyze_volume_patterns(self, board_data: Dict) -> Dict:
        """Analyze volume patterns across the market."""
        if not board_data:
            return {}
        
        volumes = []
        for symbol, data in board_data.items():
            if isinstance(data, dict):
                volume = data.get('TradingVolume', 0)
                if volume > 0:
                    volumes.append(volume)
        
        if not volumes:
            return {}
        
        return {
            'total_volume': sum(volumes),
            'average_volume': np.mean(volumes),
            'median_volume': np.median(volumes),
            'volume_std': np.std(volumes),
            'high_volume_threshold': np.percentile(volumes, 90),
            'low_volume_threshold': np.percentile(volumes, 10)
        }
    
    def _analyze_price_movements(self, board_data: Dict) -> Dict:
        """Analyze price movement patterns."""
        if not board_data:
            return {}
        
        price_changes = []
        price_ranges = []
        
        for symbol, data in board_data.items():
            if isinstance(data, dict):
                change = data.get('ChangeFromPreviousClosePercent', 0)
                high = data.get('HighPrice', 0)
                low = data.get('LowPrice', 0)
                current = data.get('CurrentPrice', 0)
                
                price_changes.append(change)
                
                if low > 0:
                    price_range = (high - low) / low * 100
                    price_ranges.append(price_range)
        
        if not price_changes:
            return {}
        
        return {
            'average_change': np.mean(price_changes),
            'change_std': np.std(price_changes),
            'positive_changes': len([c for c in price_changes if c > 0]),
            'negative_changes': len([c for c in price_changes if c < 0]),
            'average_range': np.mean(price_ranges) if price_ranges else 0,
            'max_gain': max(price_changes),
            'max_loss': min(price_changes)
        }
    
    def setup_collection_schedule(self):
        """Setup automated collection schedule."""
        # Schedule hourly Nikkei data collection (market hours: 9:00-15:00)
        for hour in range(9, 16):  # 9:00 to 15:00
            schedule.every().day.at(f"{hour:02d}:00").do(self.collect_hourly_nikkei_data)
            schedule.every().day.at(f"{hour:02d}:30").do(self.collect_hourly_nikkei_data)  # Half-hourly for more data
        
        # Schedule additional Kabu data collection (after market close)
        schedule.every().day.at("16:30").do(self.collect_additional_kabu_data)
        
        self.logger.info("Collection schedule configured:")
        self.logger.info("  - Nikkei 225: Every 30 minutes during market hours (9:00-15:00)")
        self.logger.info("  - Additional Kabu data: Daily at 16:30")
    
    def start_automated_collection(self):
        """Start automated data collection."""
        if self.is_running:
            self.logger.warning("Collection already running")
            return
        
        # Initialize Kabu connection
        if not self.initialize_kabu_connection():
            self.logger.error("Failed to initialize Kabu connection")
            return
        
        # Setup schedule
        self.setup_collection_schedule()
        
        # Start collection thread
        self.is_running = True
        self.collection_thread = threading.Thread(target=self._collection_worker, daemon=True)
        self.collection_thread.start()
        
        self.logger.info("Automated data collection started")
    
    def _collection_worker(self):
        """Worker thread for automated collection."""
        self.logger.info("Collection worker thread started")
        
        while self.is_running:
            try:
                schedule.run_pending()
                time.sleep(60)  # Check every minute
            except Exception as e:
                self.logger.error(f"Error in collection worker: {e}")
                time.sleep(300)  # Wait 5 minutes before retry
    
    def stop_automated_collection(self):
        """Stop automated data collection."""
        self.is_running = False
        if self.collection_thread and self.collection_thread.is_alive():
            self.collection_thread.join(timeout=10)
        
        self.logger.info("Automated data collection stopped")
    
    def run_manual_collection(self):
        """Run manual data collection once."""
        self.logger.info("Starting manual data collection")
        
        # Initialize Kabu connection
        if not self.initialize_kabu_connection():
            self.logger.error("Failed to initialize Kabu connection - using international data only")
            nikkei_success = False
        else:
            # Collect Nikkei hourly data
            nikkei_success = self.collect_hourly_nikkei_data()
        
        # Collect additional Kabu data
        additional_success = self.collect_additional_kabu_data()
        
        # Generate collection summary
        summary = {
            'collection_time': datetime.now().isoformat(),
            'nikkei_success': nikkei_success,
            'additional_kabu_success': additional_success,
            'collection_type': 'manual'
        }
        
        summary_file = self.ai_learning_data_dir / f"collection_summary_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(summary_file, 'w') as f:
            json.dump(summary, f, indent=2)
        
        if nikkei_success and additional_success:
            self.logger.info("✅ Manual collection completed successfully")
        elif nikkei_success or additional_success:
            self.logger.warning("⚠️  Partial collection completed")
        else:
            self.logger.error("❌ Collection failed")
        
        return nikkei_success, additional_success
    
    def get_ai_learning_datasets(self, days_back: int = 7) -> Dict:
        """
        Get formatted datasets for AI learning.
        
        Args:
            days_back: Number of days to look back for data
            
        Returns:
            Combined datasets optimized for AI learning
        """
        cutoff_date = datetime.now() - timedelta(days=days_back)
        
        # Load recent data files
        nikkei_files = []
        kabu_additional_files = []
        
        for file_path in self.ai_learning_data_dir.glob("*.json"):
            if file_path.stat().st_mtime > cutoff_date.timestamp():
                if "nikkei_hourly" in file_path.name:
                    nikkei_files.append(file_path)
                elif "kabu_additional" in file_path.name:
                    kabu_additional_files.append(file_path)
        
        # Combine datasets
        combined_data = {
            'metadata': {
                'generation_time': datetime.now().isoformat(),
                'days_back': days_back,
                'nikkei_files': len(nikkei_files),
                'kabu_additional_files': len(kabu_additional_files)
            },
            'nikkei_hourly_data': [],
            'kabu_additional_data': [],
            'feature_summary': {}
        }
        
        # Load and combine Nikkei hourly data
        for file_path in sorted(nikkei_files):
            try:
                with open(file_path, 'r') as f:
                    data = json.load(f)
                combined_data['nikkei_hourly_data'].append(data)
            except Exception as e:
                self.logger.warning(f"Error loading {file_path}: {e}")
        
        # Load and combine Kabu additional data
        for file_path in sorted(kabu_additional_files):
            try:
                with open(file_path, 'r') as f:
                    data = json.load(f)
                combined_data['kabu_additional_data'].append(data)
            except Exception as e:
                self.logger.warning(f"Error loading {file_path}: {e}")
        
        # Generate feature summary
        if combined_data['nikkei_hourly_data']:
            sample_nikkei = combined_data['nikkei_hourly_data'][-1]
            combined_data['feature_summary']['nikkei_symbols'] = len(sample_nikkei.get('features', {}))
            combined_data['feature_summary']['nikkei_features_per_symbol'] = len(
                list(sample_nikkei.get('features', {}).values())[0] if sample_nikkei.get('features') else {}
            )
        
        if combined_data['kabu_additional_data']:
            sample_additional = combined_data['kabu_additional_data'][-1]
            combined_data['feature_summary']['kabu_additional_categories'] = len(sample_additional.get('features', {}))
        
        self.logger.info(f"Generated AI learning dataset: {len(nikkei_files)} hourly + {len(kabu_additional_files)} additional files")
        
        return combined_data


def main():
    """Main function for testing the optimal data collector."""
    logging.basicConfig(level=logging.INFO)
    
    collector = OptimalDataCollector()
    
    print("=== Optimal Data Collector Test ===")
    print("Strategy:")
    print("  • Japanese stocks (Nikkei 225): 1-hour intervals via Kabu Station API")
    print("  • Additional market analysis: Via Kabu Station API only")
    print("  • No Yahoo Finance API dependency")
    print("  • Optimized for AI learning")
    print()
    
    # Test manual collection
    print("Running manual collection test...")
    nikkei_success, additional_success = collector.run_manual_collection()
    
    print(f"Results:")
    print(f"  • Nikkei 225 hourly collection: {'✅ Success' if nikkei_success else '❌ Failed'}")
    print(f"  • Additional Kabu data collection: {'✅ Success' if additional_success else '❌ Failed'}")
    
    # Test AI dataset generation
    print("\nGenerating AI learning datasets...")
    ai_datasets = collector.get_ai_learning_datasets(days_back=1)
    
    print(f"AI Dataset Summary:")
    print(f"  • Nikkei hourly files: {ai_datasets['metadata']['nikkei_files']}")
    print(f"  • Kabu additional files: {ai_datasets['metadata']['kabu_additional_files']}")
    
    if ai_datasets['feature_summary']:
        summary = ai_datasets['feature_summary']
        print(f"  • Nikkei symbols: {summary.get('nikkei_symbols', 0)}")
        print(f"  • Features per symbol: {summary.get('nikkei_features_per_symbol', 0)}")
        print(f"  • Kabu additional categories: {summary.get('kabu_additional_categories', 0)}")
    
    print("\n=== Optimal Data Collection System Ready ===")


if __name__ == "__main__":
    main()