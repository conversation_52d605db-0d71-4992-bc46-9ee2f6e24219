#!/usr/bin/env python3
"""
日経225データ収集 - ユーティリティ

データ収集に関する共通ユーティリティ機能を提供します。
"""

import os
import json
import random
import logging
import pandas as pd
from datetime import datetime

class FileUtils:
    """ファイル操作ユーティリティ"""
    
    @staticmethod
    def load_json_file(filepath, default_value=None, logger=None):
        """
        JSONファイルを読み込む
        
        Args:
            filepath (str): ファイルパス
            default_value: デフォルト値（ファイルが存在しない場合）
            logger: ロガーオブジェクト
            
        Returns:
            dict: 読み込んだJSONデータ
        """
        if not os.path.exists(filepath):
            if logger:
                logger.debug(f"ファイルが存在しません: {filepath}, デフォルト値を使用します")
            return default_value
        
        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            if logger:
                logger.debug(f"JSONファイルを読み込みました: {filepath}")
            
            return data
        except Exception as e:
            if logger:
                logger.error(f"JSONファイル読み込みエラー: {filepath}, {str(e)}")
            return default_value
    
    @staticmethod
    def save_json_file(filepath, data, logger=None):
        """
        JSONファイルを保存する
        
        Args:
            filepath (str): ファイルパス
            data: 保存するデータ
            logger: ロガーオブジェクト
            
        Returns:
            bool: 保存成功かどうか
        """
        try:
            # ディレクトリを作成
            os.makedirs(os.path.dirname(filepath), exist_ok=True)
            
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            
            if logger:
                logger.debug(f"JSONファイルを保存しました: {filepath}")
            
            return True
        except Exception as e:
            if logger:
                logger.error(f"JSONファイル保存エラー: {filepath}, {str(e)}")
            return False
    
    @staticmethod
    def ensure_dir(directory):
        """
        ディレクトリの存在を確認し、なければ作成する
        
        Args:
            directory (str): ディレクトリパス
            
        Returns:
            bool: 作成成功かどうか
        """
        try:
            os.makedirs(directory, exist_ok=True)
            return True
        except Exception:
            return False

class WebUtils:
    """ウェブ関連ユーティリティ"""
    
    # 一般的なブラウザのユーザーエージェントリスト
    USER_AGENTS = [
        # Chrome
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/102.0.5005.63 Safari/537.36",
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/102.0.5005.61 Safari/537.36",
        "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/102.0.5005.61 Safari/537.36",
        # Firefox
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:100.0) Gecko/20100101 Firefox/100.0",
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:100.0) Gecko/20100101 Firefox/100.0",
        "Mozilla/5.0 (X11; Linux i686; rv:100.0) Gecko/20100101 Firefox/100.0",
        # Safari
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.5 Safari/605.1.15",
        # Edge
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/102.0.5005.63 Safari/537.36 Edg/102.0.1245.33",
        # Opera
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/102.0.5005.63 Safari/537.36 OPR/88.0.4412.40",
    ]
    
    @staticmethod
    def get_random_user_agent():
        """
        ランダムなユーザーエージェント文字列を取得
        
        Returns:
            str: ユーザーエージェント文字列
        """
        return random.choice(WebUtils.USER_AGENTS)
    
    @staticmethod
    def get_base_headers():
        """
        基本的なHTTPヘッダーを取得
        
        Returns:
            dict: HTTPヘッダー
        """
        return {
            "User-Agent": WebUtils.get_random_user_agent(),
            "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,*/*;q=0.8",
            "Accept-Language": "en-US,en;q=0.5",
            "Accept-Encoding": "gzip, deflate, br",
            "Connection": "keep-alive",
            "Upgrade-Insecure-Requests": "1",
            "Sec-Fetch-Dest": "document",
            "Sec-Fetch-Mode": "navigate",
            "Sec-Fetch-Site": "none",
            "Sec-Fetch-User": "?1",
            "Cache-Control": "max-age=0"
        }
    
    @staticmethod
    def get_yahoo_finance_headers(ticker):
        """
        Yahoo Finance用のHTTPヘッダーを取得
        
        Args:
            ticker (str): 銘柄ティッカー
            
        Returns:
            dict: HTTPヘッダー
        """
        headers = WebUtils.get_base_headers()
        headers.update({
            "Origin": "https://finance.yahoo.com",
            "Referer": f"https://finance.yahoo.com/quote/{ticker}/history"
        })
        return headers
    
    @staticmethod
    def get_stooq_headers():
        """
        Stooq用のHTTPヘッダーを取得
        
        Returns:
            dict: HTTPヘッダー
        """
        headers = WebUtils.get_base_headers()
        headers.update({
            "Referer": "https://stooq.com/",
            "Origin": "https://stooq.com"
        })
        return headers

class DataUtils:
    """データ処理ユーティリティ"""
    
    @staticmethod
    def standardize_dataframe(df, logger=None):
        """
        データフレームを標準形式に変換する
        
        Args:
            df (pd.DataFrame): 入力データフレーム
            logger: ロガーオブジェクト
            
        Returns:
            pd.DataFrame: 標準化されたデータフレーム
        """
        if df is None or df.empty:
            return df
        
        try:
            # 列名のマッピング
            column_mapping = {
                'Date': 'Date',
                'Datetime': 'Date',
                'TIME': 'Date',
                'DATETIME': 'Date',
                'timestamp': 'Date',
                'time': 'Date',
                'datetime': 'Date',
                
                'Open': 'Open',
                'OPEN': 'Open',
                'open': 'Open',
                'o': 'Open',
                'O': 'Open',
                
                'High': 'High',
                'HIGH': 'High',
                'high': 'High',
                'h': 'High',
                'H': 'High',
                
                'Low': 'Low',
                'LOW': 'Low',
                'low': 'Low',
                'l': 'Low',
                'L': 'Low',
                
                'Close': 'Close',
                'CLOSE': 'Close',
                'close': 'Close',
                'c': 'Close',
                'C': 'Close',
                
                'Volume': 'Volume',
                'VOLUME': 'Volume',
                'volume': 'Volume',
                'vol': 'Volume',
                'v': 'Volume',
                'V': 'Volume',
                
                'Adj Close': 'Adj Close',
                'Adj_Close': 'Adj Close',
                'adj_close': 'Adj Close',
                'Adjusted Close': 'Adj Close',
                'adjusted_close': 'Adj Close'
            }
            
            # 列名を標準化
            for old_col, new_col in column_mapping.items():
                if old_col in df.columns and old_col != new_col:
                    df.rename(columns={old_col: new_col}, inplace=True)
            
            # 必須列を確認（Date, Open, High, Low, Close は必要）
            required_columns = ['Date', 'Open', 'High', 'Low', 'Close']
            
            for col in required_columns:
                if col not in df.columns:
                    if logger:
                        logger.warning(f"必須列 '{col}' がデータフレームにありません")
                    return None
            
            # 日付の型を確認し、datetime型に変換
            if not pd.api.types.is_datetime64_any_dtype(df['Date']):
                df['Date'] = pd.to_datetime(df['Date'], errors='coerce')
            
            # 欠損値の行を削除
            df = df.dropna(subset=required_columns)
            
            # 不要な列を削除
            keep_columns = ['Date', 'Open', 'High', 'Low', 'Close', 'Volume', 'Adj Close']
            actual_columns = [col for col in keep_columns if col in df.columns]
            df = df[actual_columns]
            
            # 日付でソート
            df = df.sort_values('Date')
            
            return df
            
        except Exception as e:
            if logger:
                logger.error(f"データフレーム標準化エラー: {str(e)}")
            return None
    
    @staticmethod
    def validate_dataframe(df, logger=None):
        """
        データフレームを検証する
        
        Args:
            df (pd.DataFrame): 検証するデータフレーム
            logger: ロガーオブジェクト
            
        Returns:
            bool: 検証結果
        """
        if df is None or df.empty:
            if logger:
                logger.warning("空のデータフレーム")
            return False
        
        # 必須列の確認
        required_columns = ['Date', 'Open', 'High', 'Low', 'Close']
        for col in required_columns:
            if col not in df.columns:
                if logger:
                    logger.warning(f"必須列 '{col}' がありません")
                return False
        
        # データ型の確認
        if not pd.api.types.is_datetime64_any_dtype(df['Date']):
            if logger:
                logger.warning("'Date'列がdatetime型ではありません")
            return False
        
        for col in ['Open', 'High', 'Low', 'Close']:
            if not pd.api.types.is_numeric_dtype(df[col]):
                if logger:
                    logger.warning(f"'{col}'列が数値型ではありません")
                return False
        
        if 'Volume' in df.columns and not pd.api.types.is_numeric_dtype(df['Volume']):
            if logger:
                logger.warning("'Volume'列が数値型ではありません")
            return False
        
        # データの整合性確認
        if df['High'].min() < df['Low'].min():
            if logger:
                logger.warning("データ整合性エラー: 最小High値が最小Low値より小さい")
            return False
        
        # 日付の重複確認
        if df['Date'].duplicated().any():
            if logger:
                logger.warning("日付に重複があります")
            # 警告のみとし、検証は通す
        
        return True

class NikkeiUtils:
    """日経225関連ユーティリティ"""
    
    # 日経225のセクター情報
    NIKKEI225_SECTORS = {
        "水産・農林業": ["1332.T"],
        "鉱業": ["1605.T"],
        "建設業": ["1801.T", "1802.T", "1803.T", "1812.T", "1925.T", "1928.T", "1963.T"],
        "食料品": ["2002.T", "2269.T", "2282.T", "2501.T", "2502.T", "2503.T", "2531.T", "2801.T", "2802.T", "2871.T", "2897.T"],
        "繊維製品": ["3101.T", "3402.T", "3861.T"],
        "パルプ・紙": ["3863.T", "3865.T"],
        "化学": ["3405.T", "3407.T", "4004.T", "4005.T", "4021.T", "4042.T", "4043.T", "4061.T", "4063.T", "4183.T", "4188.T", "4208.T", "4272.T", "4452.T", "4901.T", "4911.T"],
        "医薬品": ["4151.T", "4502.T", "4503.T", "4506.T", "4507.T", "4519.T", "4523.T", "4568.T"],
        "石油・石炭製品": ["5020.T"],
        "ゴム製品": ["5101.T", "5108.T"],
        "ガラス・土石製品": ["5201.T", "5202.T", "5214.T", "5232.T", "5233.T", "5301.T", "5332.T", "5333.T"],
        "鉄鋼": ["5401.T", "5406.T", "5411.T", "5541.T"],
        "非鉄金属": ["5703.T", "5706.T", "5711.T", "5713.T", "5714.T", "5741.T", "5801.T", "5802.T"],
        "金属製品": ["5631.T", "5901.T", "5943.T"],
        "機械": ["6113.T", "6134.T", "6301.T", "6302.T", "6305.T", "6326.T", "6361.T", "6366.T", "6367.T", "6383.T", "6473.T", "6474.T", "6501.T", "6504.T", "6506.T", "6586.T", "6645.T", "6674.T"],
        "電気機器": ["4902.T", "6479.T", "6503.T", "6508.T", "6701.T", "6702.T", "6703.T", "6724.T", "6752.T", "6753.T", "6754.T", "6758.T", "6762.T", "6770.T", "6841.T", "6857.T", "6902.T", "6952.T", "6954.T", "6971.T", "6976.T", "6981.T", "7735.T", "7751.T", "7752.T", "8035.T"],
        "輸送用機器": ["7003.T", "7011.T", "7012.T", "7013.T", "7201.T", "7202.T", "7203.T", "7205.T", "7211.T", "7261.T", "7267.T", "7269.T", "7270.T", "7272.T"],
        "精密機器": ["4543.T", "7731.T", "7732.T", "7733.T"],
        "その他製品": ["7911.T", "7912.T", "7951.T", "7974.T"],
        "電気・ガス業": ["9501.T", "9502.T", "9503.T", "9531.T"],
        "陸運業": ["9001.T", "9005.T", "9006.T", "9007.T", "9008.T", "9009.T", "9020.T", "9021.T", "9022.T", "9064.T", "9065.T"],
        "海運業": ["9101.T", "9104.T", "9107.T"],
        "空運業": ["9201.T", "9202.T"],
        "倉庫・運輸関連業": ["9301.T", "9302.T"],
        "情報・通信業": ["3659.T", "3769.T", "4324.T", "4689.T", "4704.T", "4751.T", "4755.T", "6758.T", "9432.T", "9433.T", "9613.T", "9766.T", "9984.T"],
        "卸売業": ["2768.T", "3086.T", "8001.T", "8015.T", "8031.T", "8053.T", "8058.T", "9861.T"],
        "小売業": ["2730.T", "3086.T", "3099.T", "3382.T", "3383.T", "8028.T", "8233.T", "8252.T", "8267.T", "9983.T"],
        "銀行業": ["8303.T", "8304.T", "8306.T", "8308.T", "8309.T", "8316.T", "8331.T", "8354.T", "8355.T", "8411.T"],
        "証券・商品先物取引業": ["8601.T", "8604.T", "8628.T"],
        "保険業": ["8630.T", "8725.T", "8729.T", "8750.T", "8766.T", "8795.T"],
        "その他金融業": ["8253.T", "8697.T", "8801.T", "8802.T", "8804.T"],
        "不動産業": ["3289.T", "8801.T", "8802.T", "8804.T", "8830.T"],
        "サービス業": ["2327.T", "2432.T", "4324.T", "4689.T", "4704.T", "4751.T", "4755.T", "6098.T", "9602.T", "9613.T", "9681.T", "9735.T", "9766.T"]
    }
    
    @staticmethod
    def get_all_nikkei225_tickers():
        """
        日経225全銘柄のティッカーリストを取得
        
        Returns:
            list: ティッカーのリスト
        """
        all_tickers = []
        for sector_tickers in NikkeiUtils.NIKKEI225_SECTORS.values():
            for ticker in sector_tickers:
                if ticker not in all_tickers:
                    all_tickers.append(ticker)
        
        return sorted(all_tickers)
    
    @staticmethod
    def get_sector_for_ticker(ticker):
        """
        指定されたティッカーのセクターを取得
        
        Args:
            ticker (str): ティッカー
            
        Returns:
            str: セクター名
        """
        for sector, tickers in NikkeiUtils.NIKKEI225_SECTORS.items():
            if ticker in tickers:
                return sector
        
        return None
    
    @staticmethod
    def get_tickers_for_sector(sector):
        """
        指定されたセクターの全ティッカーを取得
        
        Args:
            sector (str): セクター名
            
        Returns:
            list: ティッカーのリスト
        """
        return NikkeiUtils.NIKKEI225_SECTORS.get(sector, [])
    
    @staticmethod
    def convert_ticker_for_source(ticker, source):
        """
        データソース用にティッカーを変換
        
        Args:
            ticker (str): 元のティッカー
            source (str): データソース ('yahoo', 'stooq', 'pandas_datareader')
            
        Returns:
            str: 変換されたティッカー
        """
        if source.lower() == 'stooq':
            # .T -> .JP変換
            return ticker.replace('.T', '.JP')
        
        # その他のソースはそのまま使用
        return ticker
