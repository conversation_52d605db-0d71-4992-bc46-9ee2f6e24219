#!/usr/bin/env python3
"""
日経225データ収集 - ロギングモジュール

ロギングの設定と機能を提供します。
"""

import os
import logging
from datetime import datetime

def setup_logging(debug=False):
    """
    ロギングの設定を行い、ロガーインスタンスを返します。
    
    Args:
        debug (bool): デバッグモードの有効・無効
    
    Returns:
        logging.Logger: 設定されたロガーインスタンス
    """
    log_level = logging.DEBUG if debug else logging.INFO
    log_dir = "logs"
    os.makedirs(log_dir, exist_ok=True)
    
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    log_file = f"{log_dir}/nikkei_data_collector_{timestamp}.log"
    
    logging.basicConfig(
        level=log_level,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file),
            logging.StreamHandler()
        ]
    )
    
    logger = logging.getLogger(__name__)
    return logger
