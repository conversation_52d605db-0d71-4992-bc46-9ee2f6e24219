#!/usr/bin/env python3
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import os
import random
import logging
import sys
import re
import json
import uuid

# ログ設定
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class SimpleVirtualTrader:
    """
    シンプルな仮想取引クラス 
    テスト用にシンプルなインターフェースを提供します
    """
    def __init__(self, initial_balance=1000000, commission_rate=0.0, results_dir="results"):
        """
        初期化

        Parameters:
        -----------
        initial_balance : float
            初期資金
        commission_rate : float
            取引手数料率（例: 0.0005 = 0.05%）
        results_dir : str
            結果保存ディレクトリ
        """
        self.initial_balance = initial_balance
        self.balance = initial_balance
        self.portfolio = {}  # {ticker: {"qty": qty, "price": price}}
        self.trades = []  # 取引履歴
        self.commission_rate = commission_rate
        self.results_dir = results_dir
        
        # 結果保存ディレクトリを作成
        os.makedirs(self.results_dir, exist_ok=True)
    
    def buy_stock(self, ticker, price, qty, timestamp):
        """
        株式購入

        Parameters:
        -----------
        ticker : str
            銘柄コード
        price : float
            購入価格
        qty : int
            購入数量
        timestamp : datetime
            取引時刻

        Returns:
        --------
        str
            取引ID
        """
        # 購入金額計算
        amount = price * qty
        commission = amount * self.commission_rate
        total_cost = amount + commission
        
        # 残高チェック
        if total_cost > self.balance:
            raise ValueError(f"残高不足: 必要金額 {total_cost:.2f}, 残高 {self.balance:.2f}")
        
        # ポートフォリオに追加（既存の銘柄なら平均取得単価を計算）
        if ticker in self.portfolio:
            current_qty = self.portfolio[ticker]["qty"]
            current_price = self.portfolio[ticker]["price"]
            total_qty = current_qty + qty
            total_amount = (current_qty * current_price) + amount
            avg_price = total_amount / total_qty
            
            self.portfolio[ticker] = {
                "qty": total_qty,
                "price": avg_price
            }
        else:
            self.portfolio[ticker] = {
                "qty": qty,
                "price": price
            }
        
        # 残高更新
        self.balance -= total_cost
        
        # 取引履歴に追加
        trade_id = str(uuid.uuid4())
        trade = {
            "Timestamp": timestamp.strftime("%Y-%m-%d %H:%M:%S") if isinstance(timestamp, datetime) else timestamp,
            "TradeID": trade_id,
            "Ticker": ticker,
            "Action": "BUY",
            "Qty": qty,
            "Price": price,
            "Amount": amount,
            "Commission": commission,
            "Profit": 0  # 購入時は利益なし
        }
        self.trades.append(trade)
        
        return trade_id
    
    def sell_stock(self, ticker, price, qty, timestamp):
        """
        株式売却

        Parameters:
        -----------
        ticker : str
            銘柄コード
        price : float
            売却価格
        qty : int
            売却数量
        timestamp : datetime
            取引時刻

        Returns:
        --------
        str
            取引ID
        """
        # ポートフォリオに銘柄があるか確認
        if ticker not in self.portfolio:
            raise ValueError(f"ポートフォリオに銘柄 {ticker} が存在しません")
        
        # 数量が十分にあるか確認
        if self.portfolio[ticker]["qty"] < qty:
            raise ValueError(f"売却数量不足: 必要数量 {qty}, 保有数量 {self.portfolio[ticker]['qty']}")
        
        # 取得単価
        avg_price = self.portfolio[ticker]["price"]
        
        # 売却金額計算
        amount = price * qty
        commission = amount * self.commission_rate
        
        # 利益計算
        profit = (price - avg_price) * qty - commission
        
        # ポートフォリオ更新
        self.portfolio[ticker]["qty"] -= qty
        
        # 数量が0になったら削除
        if self.portfolio[ticker]["qty"] == 0:
            del self.portfolio[ticker]
        
        # 残高更新
        self.balance += amount - commission
        
        # 取引履歴に追加
        trade_id = str(uuid.uuid4())
        trade = {
            "Timestamp": timestamp.strftime("%Y-%m-%d %H:%M:%S") if isinstance(timestamp, datetime) else timestamp,
            "TradeID": trade_id,
            "Ticker": ticker,
            "Action": "SELL",
            "Qty": qty,
            "Price": price,
            "Amount": amount,
            "Commission": commission,
            "Profit": profit
        }
        self.trades.append(trade)
        
        return trade_id
    
    def calculate_portfolio_value(self, current_prices):
        """
        ポートフォリオ価値の計算

        Parameters:
        -----------
        current_prices : dict
            現在価格の辞書 {ticker: price}

        Returns:
        --------
        float
            ポートフォリオ価値
        """
        value = 0
        
        for ticker, info in self.portfolio.items():
            if ticker in current_prices:
                value += info["qty"] * current_prices[ticker]
            else:
                value += info["qty"] * info["price"]  # 現在価格がなければ取得価格で計算
        
        return value
    
    def get_profit_loss(self, portfolio_value):
        """
        損益計算

        Parameters:
        -----------
        portfolio_value : float
            ポートフォリオ価値

        Returns:
        --------
        tuple
            (損益, 損益率)
        """
        total_value = portfolio_value + self.balance
        profit_loss = total_value - self.initial_balance
        profit_loss_pct = (profit_loss / self.initial_balance) * 100
        
        return profit_loss, profit_loss_pct
    
    def save_portfolio(self, timestamp, current_prices):
        """
        ポートフォリオ状態を保存

        Parameters:
        -----------
        timestamp : datetime
            保存時刻
        current_prices : dict
            現在価格の辞書 {ticker: price}

        Returns:
        --------
        str
            保存ファイルパス
        """
        # ポートフォリオ価値の計算
        portfolio_value = self.calculate_portfolio_value(current_prices)
        
        # 損益計算
        profit_loss, profit_loss_pct = self.get_profit_loss(portfolio_value)
        
        # 保存用のデータ
        timestamp_str = timestamp.strftime("%Y%m%d_%H%M") if isinstance(timestamp, datetime) else "snapshot"
        data = {
            "balance": self.balance,
            "portfolio": self.portfolio,
            "portfolio_value": portfolio_value,
            "initial_balance": self.initial_balance,
            "profit_loss": profit_loss,
            "profit_loss_pct": profit_loss_pct,
            "last_update": datetime.now().isoformat()
        }
        
        # ファイル名
        filename = f"portfolio_{timestamp_str}.json"
        filepath = os.path.join(self.results_dir, filename)
        
        # 保存
        with open(filepath, "w") as f:
            json.dump(data, f, indent=2, default=str)
        
        return filepath
    
    def save_trades(self, timestamp):
        """
        取引履歴を保存

        Parameters:
        -----------
        timestamp : datetime
            保存時刻

        Returns:
        --------
        str
            保存ファイルパス
        """
        # 保存用のデータフレーム
        df = pd.DataFrame(self.trades)
        
        # ファイル名
        timestamp_str = timestamp.strftime("%Y%m%d_%H%M") if isinstance(timestamp, datetime) else "snapshot"
        filename = f"trades_{timestamp_str}.csv"
        filepath = os.path.join(self.results_dir, filename)
        
        # 保存
        df.to_csv(filepath, index=False)
        
        return filepath
    
    def reset(self):
        """
        状態をリセット
        """
        self.balance = self.initial_balance
        self.portfolio = {}
        self.trades = []
