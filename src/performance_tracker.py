#!/usr/bin/env python3
import os
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime, timedelta
import glob
import json

class PerformanceTracker:
    """
    Track and visualize performance of the trading system
    """
    def __init__(self, results_dir="results"):
        """
        Initialize performance tracker
        
        Parameters:
        -----------
        results_dir : str
            Directory containing results files
        """
        self.results_dir = results_dir
        
    def load_predictions(self, days_back=7):
        """
        Load prediction data from the last N days
        
        Parameters:
        -----------
        days_back : int
            Number of days to look back
            
        Returns:
        --------
        pd.DataFrame
            DataFrame with all predictions
        """
        # Calculate start date
        start_date = datetime.now() - timedelta(days=days_back)
        start_date_str = start_date.strftime('%Y%m%d')
        
        # Find all prediction files
        prediction_files = []
        for file in glob.glob(os.path.join(self.results_dir, "top_stocks_*.csv")):
            # Extract date from filename
            try:
                file_date = os.path.basename(file).split('_')[2].split('.')[0]
                # If the date is in YYYYMMDD_HHMM format, take just the date part
                if '_' in file_date:
                    file_date = file_date.split('_')[0]
                
                if file_date >= start_date_str:
                    prediction_files.append(file)
            except:
                # If we can't parse the date, include the file anyway
                prediction_files.append(file)
        
        if not prediction_files:
            print("No prediction files found")
            return pd.DataFrame()
        
        # Load and combine all files
        all_predictions = []
        for file in prediction_files:
            try:
                predictions = pd.read_csv(file)
                
                # Add timestamp from filename
                filename = os.path.basename(file)
                if '_' in filename:
                    date_str = filename.split('_')[2]
                    time_str = filename.split('_')[3].split('.')[0]
                    if len(date_str) == 8 and len(time_str) == 4:  # YYYYMMDD_HHMM
                        timestamp = datetime.strptime(f"{date_str}_{time_str}", "%Y%m%d_%H%M")
                        predictions['Timestamp'] = timestamp
                
                # Append to list
                all_predictions.append(predictions)
            except Exception as e:
                print(f"Error loading {file}: {str(e)}")
                continue
        
        if not all_predictions:
            print("No valid prediction files could be loaded")
            return pd.DataFrame()
        
        # Combine all predictions
        combined = pd.concat(all_predictions, ignore_index=True)
        
        return combined
    
    def load_performance(self, days_back=7):
        """
        Load performance data from the last N days
        
        Parameters:
        -----------
        days_back : int
            Number of days to look back
            
        Returns:
        --------
        pd.DataFrame
            DataFrame with performance data
        """
        # Calculate start date
        start_date = datetime.now() - timedelta(days=days_back)
        start_date_str = start_date.strftime('%Y%m%d')
        
        # Find all performance files
        performance_files = []
        for file in glob.glob(os.path.join(self.results_dir, "performance_*.csv")):
            # Extract date from filename
            try:
                file_date = os.path.basename(file).split('_')[1].split('.')[0]
                if file_date >= start_date_str:
                    performance_files.append(file)
            except:
                # If we can't parse the date, include the file anyway
                performance_files.append(file)
        
        if not performance_files:
            print("No performance files found")
            return pd.DataFrame()
        
        # Load and combine all files
        all_performance = []
        for file in performance_files:
            try:
                performance = pd.read_csv(file)
                all_performance.append(performance)
            except Exception as e:
                print(f"Error loading {file}: {str(e)}")
                continue
        
        if not all_performance:
            print("No valid performance files could be loaded")
            return pd.DataFrame()
        
        # Combine all performance data
        combined = pd.concat(all_performance, ignore_index=True)
        
        # Convert Date to datetime
        combined['Date'] = pd.to_datetime(combined['Date'])
        
        return combined
    
    def generate_performance_report(self, days_back=7, output_file=None):
        """
        Generate a comprehensive performance report
        
        Parameters:
        -----------
        days_back : int
            Number of days to look back
        output_file : str
            File to save the report to, if None report will be displayed
        """
        # Load data
        predictions = self.load_predictions(days_back)
        performance = self.load_performance(days_back)
        
        if predictions.empty and performance.empty:
            print("No data available for report")
            return
        
        # Create figure
        plt.figure(figsize=(15, 12))
        
        # 1. Top prediction accuracy
        plt.subplot(2, 2, 1)
        if not predictions.empty and 'Predicted_Change_Pct' in predictions.columns:
            # Count positive vs. negative predictions
            positive_count = (predictions['Predicted_Change_Pct'] > 0).sum()
            negative_count = (predictions['Predicted_Change_Pct'] <= 0).sum()
            
            plt.bar(['Positive', 'Negative'], [positive_count, negative_count])
            plt.title('Prediction Counts')
            plt.ylabel('Count')
            plt.grid(alpha=0.3)
        else:
            plt.text(0.5, 0.5, 'No prediction data available', ha='center', va='center')
            plt.title('Prediction Counts')
        
        # 2. Performance over time
        plt.subplot(2, 2, 2)
        if not performance.empty and 'Profit' in performance.columns:
            # Group by date
            daily_performance = performance.groupby('Date')['Profit'].mean().reset_index()
            
            plt.plot(daily_performance['Date'], daily_performance['Profit'], marker='o')
            plt.title('Average Daily Profit (%)')
            plt.ylabel('Profit (%)')
            plt.grid(alpha=0.3)
            plt.xticks(rotation=45)
        else:
            plt.text(0.5, 0.5, 'No performance data available', ha='center', va='center')
            plt.title('Average Daily Profit (%)')
        
        # 3. Top performing tickers
        plt.subplot(2, 2, 3)
        if not performance.empty and 'Ticker' in performance.columns and 'Profit' in performance.columns:
            # Group by ticker
            ticker_performance = performance.groupby('Ticker')['Profit'].mean().sort_values(ascending=False).head(10)
            
            plt.bar(ticker_performance.index, ticker_performance.values)
            plt.title('Top 10 Performing Tickers')
            plt.ylabel('Average Profit (%)')
            plt.xticks(rotation=45)
            plt.grid(alpha=0.3)
        else:
            plt.text(0.5, 0.5, 'No ticker performance data available', ha='center', va='center')
            plt.title('Top 10 Performing Tickers')
            
        # 4. Most frequently selected tickers
        plt.subplot(2, 2, 4)
        if not predictions.empty and 'Ticker' in predictions.columns:
            # Count occurrences of each ticker
            ticker_counts = predictions['Ticker'].value_counts().head(10)
            
            plt.bar(ticker_counts.index, ticker_counts.values)
            plt.title('Top 10 Most Frequently Selected Tickers')
            plt.ylabel('Count')
            plt.xticks(rotation=45)
            plt.grid(alpha=0.3)
        else:
            plt.text(0.5, 0.5, 'No ticker selection data available', ha='center', va='center')
            plt.title('Top 10 Most Frequently Selected Tickers')
        
        plt.tight_layout()
        
        # Save or display the report
        if output_file:
            plt.savefig(output_file)
            print(f"Report saved to {output_file}")
        else:
            plt.show()
    
    def generate_ticker_report(self, ticker, days_back=30, output_file=None):
        """
        Generate a report for a specific ticker
        
        Parameters:
        -----------
        ticker : str
            Ticker symbol
        days_back : int
            Number of days to look back
        output_file : str
            File to save the report to, if None report will be displayed
        """
        # Load data
        predictions = self.load_predictions(days_back)
        performance = self.load_performance(days_back)
        
        # Filter for the specific ticker
        ticker_predictions = predictions[predictions['Ticker'] == ticker] if not predictions.empty else pd.DataFrame()
        ticker_performance = performance[performance['Ticker'] == ticker] if not performance.empty else pd.DataFrame()
        
        if ticker_predictions.empty and ticker_performance.empty:
            print(f"No data available for ticker {ticker}")
            return
        
        # Create figure
        plt.figure(figsize=(15, 8))
        
        # 1. Predicted vs. Actual Change
        plt.subplot(1, 2, 1)
        if not ticker_predictions.empty and 'Timestamp' in ticker_predictions.columns and 'Predicted_Change_Pct' in ticker_predictions.columns:
            plt.plot(ticker_predictions['Timestamp'], ticker_predictions['Predicted_Change_Pct'], marker='o', label='Predicted')
            
            if not ticker_performance.empty and 'Date' in ticker_performance.columns and 'Profit' in ticker_performance.columns:
                plt.plot(ticker_performance['Date'], ticker_performance['Profit'], marker='x', label='Actual')
            
            plt.title(f'Predicted vs. Actual Change for {ticker}')
            plt.ylabel('Change (%)')
            plt.legend()
            plt.grid(alpha=0.3)
            plt.xticks(rotation=45)
        else:
            plt.text(0.5, 0.5, f'No prediction data available for {ticker}', ha='center', va='center')
            plt.title(f'Predicted vs. Actual Change for {ticker}')
        
        # 2. Cumulative profit
        plt.subplot(1, 2, 2)
        if not ticker_performance.empty and 'Date' in ticker_performance.columns and 'Profit' in ticker_performance.columns:
            # Sort by date
            ticker_performance = ticker_performance.sort_values('Date')
            
            # Calculate cumulative profit
            ticker_performance['Cumulative_Profit'] = ticker_performance['Profit'].cumsum()
            
            plt.plot(ticker_performance['Date'], ticker_performance['Cumulative_Profit'], marker='o')
            plt.title(f'Cumulative Profit for {ticker}')
            plt.ylabel('Cumulative Profit (%)')
            plt.grid(alpha=0.3)
            plt.xticks(rotation=45)
        else:
            plt.text(0.5, 0.5, f'No performance data available for {ticker}', ha='center', va='center')
            plt.title(f'Cumulative Profit for {ticker}')
        
        plt.tight_layout()
        
        # Save or display the report
        if output_file:
            plt.savefig(output_file)
            print(f"Report saved to {output_file}")
        else:
            plt.show()
    
    def export_summary(self, days_back=30, output_file=None):
        """
        Export a summary of performance data
        
        Parameters:
        -----------
        days_back : int
            Number of days to look back
        output_file : str
            File to save the summary to, if None summary will be printed
        """
        # Load data
        predictions = self.load_predictions(days_back)
        performance = self.load_performance(days_back)
        
        summary = {
            "period": {
                "start_date": (datetime.now() - timedelta(days=days_back)).strftime('%Y-%m-%d'),
                "end_date": datetime.now().strftime('%Y-%m-%d'),
                "days": days_back
            },
            "predictions": {},
            "performance": {}
        }
        
        # Predictions summary
        if not predictions.empty:
            summary["predictions"] = {
                "total_count": len(predictions),
                "unique_tickers": predictions['Ticker'].nunique(),
                "positive_predictions": (predictions['Predicted_Change_Pct'] > 0).sum(),
                "negative_predictions": (predictions['Predicted_Change_Pct'] <= 0).sum(),
                "avg_predicted_change": predictions['Predicted_Change_Pct'].mean(),
                "max_predicted_change": predictions['Predicted_Change_Pct'].max(),
                "min_predicted_change": predictions['Predicted_Change_Pct'].min(),
                "top_predicted_tickers": predictions['Ticker'].value_counts().head(10).to_dict()
            }
        
        # Performance summary
        if not performance.empty:
            summary["performance"] = {
                "total_records": len(performance),
                "unique_tickers": performance['Ticker'].nunique(),
                "avg_profit": performance['Profit'].mean(),
                "total_profit": performance['Profit'].sum(),
                "profitable_trades": (performance['Profit'] > 0).sum(),
                "unprofitable_trades": (performance['Profit'] <= 0).sum(),
                "top_performing_tickers": performance.groupby('Ticker')['Profit'].mean().sort_values(ascending=False).head(10).to_dict(),
                "daily_performance": performance.groupby('Date')['Profit'].mean().to_dict()
            }
        
        # Export or print the summary
        if output_file:
            with open(output_file, 'w') as f:
                json.dump(summary, f, indent=2)
            print(f"Summary exported to {output_file}")
        else:
            print(json.dumps(summary, indent=2))
        
        return summary

if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description='Generate performance reports for the trading system')
    parser.add_argument('--days', type=int, default=7, help='Number of days to look back')
    parser.add_argument('--ticker', type=str, help='Generate report for a specific ticker')
    parser.add_argument('--summary', action='store_true', help='Generate a summary report')
    parser.add_argument('--output', type=str, help='Output file for the report')
    
    args = parser.parse_args()
    
    tracker = PerformanceTracker()
    
    if args.ticker:
        tracker.generate_ticker_report(args.ticker, days_back=args.days, output_file=args.output)
    elif args.summary:
        tracker.export_summary(days_back=args.days, output_file=args.output)
    else:
        tracker.generate_performance_report(days_back=args.days, output_file=args.output)
