"""
Fallback Data Collection Module

This module provides basic data collection functionality when yfinance is not available.
It generates synthetic data for testing and development purposes.
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging
import os
from typing import List, Optional

logger = logging.getLogger(__name__)

class FallbackDataCollector:
    """Fallback data collector that generates synthetic data"""
    
    def __init__(self, output_dir: str = "data", interval: str = "1h", period: str = "1mo"):
        self.output_dir = output_dir
        self.interval = interval
        self.period = period
        
        # Create output directory
        os.makedirs(output_dir, exist_ok=True)
        
        logger.info("Using fallback data collector (synthetic data)")
    
    def generate_synthetic_data(self, ticker: str, days: int = 30) -> pd.DataFrame:
        """Generate synthetic stock data for testing"""
        
        # Create date range
        end_date = datetime.now()
        start_date = end_date - timedelta(days=days)
        
        if self.interval == "1h":
            freq = "H"
            periods = days * 24
        elif self.interval == "1d":
            freq = "D"
            periods = days
        else:
            freq = "H"
            periods = days * 24
        
        dates = pd.date_range(start=start_date, end=end_date, freq=freq)[:periods]
        
        # Generate realistic stock price data
        np.random.seed(hash(ticker) % 2**32)  # Consistent data for same ticker
        
        # Starting price based on ticker
        if ticker.startswith("7203"):  # Toyota
            base_price = 2500
        elif ticker.startswith("6758"):  # Sony
            base_price = 12000
        elif ticker.startswith("9984"):  # SoftBank
            base_price = 5000
        else:
            base_price = np.random.randint(1000, 10000)
        
        # Generate price movements
        returns = np.random.normal(0.0001, 0.02, len(dates))  # Small positive drift with volatility
        prices = [base_price]
        
        for i in range(1, len(dates)):
            new_price = prices[-1] * (1 + returns[i])
            prices.append(max(new_price, base_price * 0.5))  # Prevent prices from going too low
        
        # Create OHLCV data
        data = []
        for i, (date, price) in enumerate(zip(dates, prices)):
            # Generate realistic OHLC from close price
            volatility = price * 0.01  # 1% intraday volatility
            
            high = price + np.random.uniform(0, volatility)
            low = price - np.random.uniform(0, volatility)
            open_price = low + np.random.uniform(0, high - low)
            close = price
            
            # Ensure OHLC relationships are correct
            high = max(high, open_price, close)
            low = min(low, open_price, close)
            
            # Generate volume
            base_volume = 1000000
            volume = int(base_volume * np.random.uniform(0.5, 2.0))
            
            data.append({
                'Date': date,
                'Open': round(open_price, 2),
                'High': round(high, 2),
                'Low': round(low, 2),
                'Close': round(close, 2),
                'Volume': volume
            })
        
        df = pd.DataFrame(data)
        df.set_index('Date', inplace=True)
        
        return df
    
    def collect_data(self, tickers: Optional[List[str]] = None):
        """Collect synthetic data for tickers"""
        if tickers is None:
            # Use some default tickers
            tickers = ["7203.T", "6758.T", "9984.T", "8306.T", "9432.T"]
        
        logger.info(f"Generating synthetic data for {len(tickers)} tickers...")
        
        for ticker in tickers:
            try:
                data = self.generate_synthetic_data(ticker)
                filename = os.path.join(self.output_dir, f"{ticker}_data.csv")
                data.to_csv(filename)
                logger.info(f"Generated synthetic data for {ticker}: {filename}")
            except Exception as e:
                logger.error(f"Failed to generate data for {ticker}: {e}")
    
    def collect_batch(self, output_file: str = "nikkei225_data.csv", tickers: Optional[List[str]] = None):
        """Collect batch data and save to single file"""
        if tickers is None:
            tickers = ["7203.T", "6758.T", "9984.T", "8306.T", "9432.T"]
        
        logger.info(f"Generating batch synthetic data for {len(tickers)} tickers...")
        
        all_data = {}
        for ticker in tickers:
            try:
                data = self.generate_synthetic_data(ticker)
                all_data[ticker] = data
            except Exception as e:
                logger.error(f"Failed to generate data for {ticker}: {e}")
        
        if all_data:
            # Combine all data
            combined_data = pd.concat(all_data, axis=1)
            filename = os.path.join(self.output_dir, output_file)
            combined_data.to_csv(filename)
            logger.info(f"Generated batch synthetic data: {filename}")
            return combined_data
        
        return None

# Create a fallback function that mimics yfinance download
def download_fallback(tickers, period="1mo", interval="1d"):
    """Fallback function that mimics yfinance.download()"""
    logger.warning("Using synthetic data fallback (yfinance not available)")
    
    if isinstance(tickers, str):
        tickers = [tickers]
    
    collector = FallbackDataCollector(interval=interval, period=period)
    
    # Convert period to days
    if period == "1mo":
        days = 30
    elif period == "3mo":
        days = 90
    elif period == "6mo":
        days = 180
    elif period == "1y":
        days = 365
    else:
        days = 30
    
    if len(tickers) == 1:
        return collector.generate_synthetic_data(tickers[0], days)
    else:
        # Multiple tickers
        all_data = {}
        for ticker in tickers:
            data = collector.generate_synthetic_data(ticker, days)
            all_data[ticker] = data
        
        return pd.concat(all_data, axis=1, keys=tickers)

# Monkey patch yfinance if it's not available
try:
    import yfinance as yf
    logger.info("yfinance is available")
except ImportError:
    logger.warning("yfinance not available, using fallback")
    
    # Create a mock yfinance module
    class MockYFinance:
        @staticmethod
        def download(*args, **kwargs):
            return download_fallback(*args, **kwargs)
    
    # Make it available as yf
    yf = MockYFinance()
    
    # Also make it available for import
    import sys
    sys.modules['yfinance'] = MockYFinance()
