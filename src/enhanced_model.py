import pandas as pd
import numpy as np
import json
import logging
from typing import Dict, List, Tuple, Optional, Any
from pathlib import Path
import pickle
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

# ML imports
from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor
from sklearn.linear_model import Ridge, Lasso
from sklearn.preprocessing import StandardScaler, RobustScaler
from sklearn.model_selection import TimeSeriesSplit, GridSearchCV
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
import xgboost as xgb
import lightgbm as lgb

# Deep learning imports
try:
    import torch
    import torch.nn as nn
    from torch.utils.data import DataLoader, TensorDataset
    HAS_TORCH = True
except ImportError:
    HAS_TORCH = False

try:
    from tensorflow.keras.models import Sequential
    from tensorflow.keras.layers import LSTM, Dense, Dropout, BatchNormalization
    from tensorflow.keras.optimizers import Adam
    from tensorflow.keras.callbacks import EarlyStopping, ReduceLROnPlateau
    HAS_TENSORFLOW = True
except ImportError:
    HAS_TENSORFLOW = False

class EnhancedNikkeiModel:
    """
    Enhanced Nikkei 225 prediction model incorporating international market features
    for improved prediction accuracy through multi-market analysis.
    """
    
    def __init__(self, config_path: str = "config/international_markets_config.json"):
        self.config_path = Path(config_path)
        self.config = self._load_config()
        self.logger = self._setup_logging()
        
        # Model storage
        self.models = {}
        self.scalers = {}
        self.feature_importance = {}
        self.performance_metrics = {}
        
        # Data storage
        self.international_data = {}
        self.nikkei_data = None
        self.combined_features = None
        
        # Model parameters
        self.model_types = [
            'random_forest',
            'xgboost',
            'lightgbm',
            'gradient_boosting',
            'ridge',
            'ensemble'
        ]
        
        if HAS_TENSORFLOW:
            self.model_types.append('lstm')
        
        # Create directories
        self.models_dir = Path("models/enhanced")
        self.models_dir.mkdir(parents=True, exist_ok=True)
        
        self.results_dir = Path("results/enhanced")
        self.results_dir.mkdir(parents=True, exist_ok=True)
    
    def _load_config(self) -> Dict:
        """Load configuration."""
        try:
            with open(self.config_path, 'r') as f:
                return json.load(f)
        except FileNotFoundError:
            self.logger.error(f"Config file not found: {self.config_path}")
            return {}
    
    def _setup_logging(self) -> logging.Logger:
        """Setup logging."""
        logger = logging.getLogger('EnhancedNikkeiModel')
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        
        return logger
    
    def load_data(self, nikkei_path: str = None, international_data_dir: str = "international_data"):
        """
        Load Nikkei and international market data.
        
        Args:
            nikkei_path: Path to Nikkei data file
            international_data_dir: Directory containing international data
        """
        self.logger.info("Loading data for enhanced modeling")
        
        # Load Nikkei data
        if nikkei_path is None:
            # Try to find recent Nikkei data
            possible_paths = [
                "data/nikkei225_with_features.csv",
                "data/nikkei225_cleaned.csv",
                "data/nikkei225_data.csv"
            ]
            
            for path in possible_paths:
                if Path(path).exists():
                    nikkei_path = path
                    break
        
        if nikkei_path and Path(nikkei_path).exists():
            self.nikkei_data = pd.read_csv(nikkei_path, index_col=0, parse_dates=True)
            self.logger.info(f"Loaded Nikkei data: {len(self.nikkei_data)} records")
        else:
            raise FileNotFoundError("Could not find Nikkei 225 data file")
        
        # Load international data
        international_dir = Path(international_data_dir)
        if not international_dir.exists():
            self.logger.warning(f"International data directory not found: {international_dir}")
            return
        
        self.international_data = {}
        
        # Load all types of international data
        for data_type in ['indices', 'forex', 'commodities', 'indicators']:
            type_dir = international_dir / data_type
            if type_dir.exists():
                for file_path in type_dir.glob('*.csv'):
                    try:
                        data = pd.read_csv(file_path, index_col=0, parse_dates=True)
                        key = f"{data_type}_{file_path.stem}"
                        self.international_data[key] = data
                    except Exception as e:
                        self.logger.warning(f"Could not load {file_path}: {e}")
        
        self.logger.info(f"Loaded {len(self.international_data)} international datasets")
    
    def create_enhanced_features(self, lookback_periods: List[int] = [1, 5, 10, 20]) -> pd.DataFrame:
        """
        Create enhanced feature set combining Nikkei and international market features.
        
        Args:
            lookback_periods: Periods for creating lagged features
            
        Returns:
            Enhanced feature DataFrame
        """
        self.logger.info("Creating enhanced feature set")
        
        if self.nikkei_data is None:
            raise ValueError("Nikkei data not loaded")
        
        # Start with Nikkei features
        features = pd.DataFrame(index=self.nikkei_data.index)
        
        # Basic Nikkei features
        features['nikkei_close'] = self.nikkei_data['Close']
        features['nikkei_volume'] = self.nikkei_data.get('Volume', 0)
        features['nikkei_returns'] = self.nikkei_data['Close'].pct_change()
        
        # Nikkei technical indicators
        if 'sma_5' in self.nikkei_data.columns:
            for col in self.nikkei_data.columns:
                if any(indicator in col.lower() for indicator in ['sma', 'ema', 'rsi', 'macd', 'bb', 'atr']):
                    features[f"nikkei_{col}"] = self.nikkei_data[col]
        
        # Add lagged Nikkei features
        for period in lookback_periods:
            features[f'nikkei_returns_lag_{period}'] = features['nikkei_returns'].shift(period)
            features[f'nikkei_close_lag_{period}'] = features['nikkei_close'].shift(period)
        
        # Add international market features
        priority_weights = self.config.get('collection_settings', {}).get('priority_weighting', {})
        
        for market_name, market_data in self.international_data.items():
            if 'Close' not in market_data.columns:
                continue
            
            # Determine priority weight
            priority = 'medium'  # default
            if 'critical' in str(market_data.get('priority', '')):
                priority = 'critical'
            elif 'high' in str(market_data.get('priority', '')):
                priority = 'high'
            elif 'low' in str(market_data.get('priority', '')):
                priority = 'low'
            
            weight = priority_weights.get(priority, 0.6)
            
            # Basic international features
            market_returns = market_data['Close'].pct_change()
            features[f'{market_name}_returns'] = market_returns * weight
            features[f'{market_name}_close'] = market_data['Close']
            
            # Add lagged international features for high priority markets
            if weight >= 0.6:  # High and critical priority
                for period in [1, 5, 10]:
                    features[f'{market_name}_returns_lag_{period}'] = market_returns.shift(period) * weight
            
            # Add volatility features
            features[f'{market_name}_volatility'] = market_returns.rolling(window=20).std() * weight
            
            # Add momentum features
            features[f'{market_name}_momentum'] = (market_data['Close'] / market_data['Close'].shift(20) - 1) * weight
        
        # Add cross-market features
        features = self._add_cross_market_features(features)
        
        # Add time-based features
        features = self._add_time_features(features)
        
        # Handle missing values
        features = features.fillna(method='ffill').fillna(method='bfill')
        
        # Remove any remaining NaN rows
        features = features.dropna()
        
        self.combined_features = features
        self.logger.info(f"Created enhanced feature set: {features.shape[1]} features, {len(features)} samples")
        
        return features
    
    def _add_cross_market_features(self, features: pd.DataFrame) -> pd.DataFrame:
        """Add cross-market interaction features."""
        # USD/JPY interaction with US markets
        if 'forex_USDJPY_X_returns' in features.columns:
            usdjpy_returns = features['forex_USDJPY_X_returns']
            
            # Interaction with US indices
            for col in features.columns:
                if 'index' in col and any(us_market in col for us_market in ['SP500', 'NASDAQ', 'DOW']):
                    features[f'{col}_usdjpy_interaction'] = features[col] * usdjpy_returns
        
        # VIX interaction features
        if 'index_VIX_returns' in features.columns:
            vix_returns = features['index_VIX_returns']
            
            # VIX interaction with major indices
            for col in features.columns:
                if 'returns' in col and any(market in col for market in ['SP500', 'NASDAQ', 'DAX', 'FTSE']):
                    features[f'{col}_vix_interaction'] = features[col] * vix_returns
        
        # Commodity-currency interactions
        if 'commodity_CL_F_Crude_Oil_WTI_returns' in features.columns and 'forex_USDJPY_X_returns' in features.columns:
            oil_returns = features['commodity_CL_F_Crude_Oil_WTI_returns']
            usdjpy_returns = features['forex_USDJPY_X_returns']
            features['oil_usdjpy_interaction'] = oil_returns * usdjpy_returns
        
        return features
    
    def _add_time_features(self, features: pd.DataFrame) -> pd.DataFrame:
        """Add time-based features."""
        # Day of week
        features['day_of_week'] = features.index.dayofweek
        
        # Month
        features['month'] = features.index.month
        
        # Quarter
        features['quarter'] = features.index.quarter
        
        # Year
        features['year'] = features.index.year
        
        # Hour (if available)
        if hasattr(features.index, 'hour'):
            features['hour'] = features.index.hour
        
        return features
    
    def prepare_training_data(self, target_column: str = 'nikkei_returns', 
                            sequence_length: int = 20) -> Tuple[np.ndarray, np.ndarray]:
        """
        Prepare training data for ML models.
        
        Args:
            target_column: Column to predict
            sequence_length: Length of sequences for LSTM
            
        Returns:
            Tuple of (X, y) arrays
        """
        if self.combined_features is None:
            raise ValueError("Features not created. Call create_enhanced_features() first.")
        
        # Prepare target variable
        if target_column not in self.combined_features.columns:
            raise ValueError(f"Target column {target_column} not found in features")
        
        # Shift target for next-period prediction
        y = self.combined_features[target_column].shift(-1).dropna()
        
        # Align features with target
        X = self.combined_features.loc[y.index]
        
        # Remove target from features
        feature_columns = [col for col in X.columns if col != target_column]
        X = X[feature_columns]
        
        self.logger.info(f"Prepared training data: {X.shape[1]} features, {len(X)} samples")
        
        return X.values, y.values
    
    def train_models(self, X: np.ndarray, y: np.ndarray, 
                    test_size: float = 0.2, cv_folds: int = 5):
        """
        Train multiple models with the enhanced feature set.
        
        Args:
            X: Feature matrix
            y: Target vector
            test_size: Proportion of data for testing
            cv_folds: Number of cross-validation folds
        """
        self.logger.info("Training enhanced models")
        
        # Time series split
        split_idx = int(len(X) * (1 - test_size))
        X_train, X_test = X[:split_idx], X[split_idx:]
        y_train, y_test = y[:split_idx], y[split_idx:]
        
        # Scale features
        scaler = RobustScaler()
        X_train_scaled = scaler.fit_transform(X_train)
        X_test_scaled = scaler.transform(X_test)
        self.scalers['main'] = scaler
        
        # Time series cross-validation
        tscv = TimeSeriesSplit(n_splits=cv_folds)
        
        # Train each model type
        for model_type in self.model_types:
            try:
                self.logger.info(f"Training {model_type} model")
                
                if model_type == 'random_forest':
                    self._train_random_forest(X_train_scaled, y_train, X_test_scaled, y_test, tscv)
                elif model_type == 'xgboost':
                    self._train_xgboost(X_train_scaled, y_train, X_test_scaled, y_test, tscv)
                elif model_type == 'lightgbm':
                    self._train_lightgbm(X_train_scaled, y_train, X_test_scaled, y_test, tscv)
                elif model_type == 'gradient_boosting':
                    self._train_gradient_boosting(X_train_scaled, y_train, X_test_scaled, y_test, tscv)
                elif model_type == 'ridge':
                    self._train_ridge(X_train_scaled, y_train, X_test_scaled, y_test, tscv)
                elif model_type == 'lstm' and HAS_TENSORFLOW:
                    self._train_lstm(X_train_scaled, y_train, X_test_scaled, y_test)
                elif model_type == 'ensemble':
                    self._train_ensemble(X_train_scaled, y_train, X_test_scaled, y_test)
                
            except Exception as e:
                self.logger.error(f"Error training {model_type}: {e}")
        
        # Save models and results
        self._save_models()
        self._save_results()
    
    def _train_random_forest(self, X_train, y_train, X_test, y_test, tscv):
        """Train Random Forest model."""
        param_grid = {
            'n_estimators': [100, 200, 300],
            'max_depth': [10, 20, None],
            'min_samples_split': [2, 5, 10],
            'min_samples_leaf': [1, 2, 4]
        }
        
        rf = RandomForestRegressor(random_state=42, n_jobs=-1)
        grid_search = GridSearchCV(rf, param_grid, cv=tscv, scoring='neg_mean_squared_error', n_jobs=-1)
        grid_search.fit(X_train, y_train)
        
        best_model = grid_search.best_estimator_
        y_pred = best_model.predict(X_test)
        
        self.models['random_forest'] = best_model
        self.performance_metrics['random_forest'] = self._calculate_metrics(y_test, y_pred)
        self.feature_importance['random_forest'] = best_model.feature_importances_
    
    def _train_xgboost(self, X_train, y_train, X_test, y_test, tscv):
        """Train XGBoost model."""
        param_grid = {
            'n_estimators': [100, 200, 300],
            'max_depth': [3, 6, 9],
            'learning_rate': [0.01, 0.1, 0.2],
            'subsample': [0.8, 0.9, 1.0]
        }
        
        xgb_model = xgb.XGBRegressor(random_state=42, n_jobs=-1)
        grid_search = GridSearchCV(xgb_model, param_grid, cv=tscv, scoring='neg_mean_squared_error', n_jobs=-1)
        grid_search.fit(X_train, y_train)
        
        best_model = grid_search.best_estimator_
        y_pred = best_model.predict(X_test)
        
        self.models['xgboost'] = best_model
        self.performance_metrics['xgboost'] = self._calculate_metrics(y_test, y_pred)
        self.feature_importance['xgboost'] = best_model.feature_importances_
    
    def _train_lightgbm(self, X_train, y_train, X_test, y_test, tscv):
        """Train LightGBM model."""
        param_grid = {
            'n_estimators': [100, 200, 300],
            'max_depth': [3, 6, 9],
            'learning_rate': [0.01, 0.1, 0.2],
            'num_leaves': [31, 50, 100]
        }
        
        lgb_model = lgb.LGBMRegressor(random_state=42, n_jobs=-1, verbose=-1)
        grid_search = GridSearchCV(lgb_model, param_grid, cv=tscv, scoring='neg_mean_squared_error', n_jobs=-1)
        grid_search.fit(X_train, y_train)
        
        best_model = grid_search.best_estimator_
        y_pred = best_model.predict(X_test)
        
        self.models['lightgbm'] = best_model
        self.performance_metrics['lightgbm'] = self._calculate_metrics(y_test, y_pred)
        self.feature_importance['lightgbm'] = best_model.feature_importances_
    
    def _train_gradient_boosting(self, X_train, y_train, X_test, y_test, tscv):
        """Train Gradient Boosting model."""
        param_grid = {
            'n_estimators': [100, 200],
            'max_depth': [3, 5, 7],
            'learning_rate': [0.01, 0.1, 0.2]
        }
        
        gb = GradientBoostingRegressor(random_state=42)
        grid_search = GridSearchCV(gb, param_grid, cv=tscv, scoring='neg_mean_squared_error', n_jobs=-1)
        grid_search.fit(X_train, y_train)
        
        best_model = grid_search.best_estimator_
        y_pred = best_model.predict(X_test)
        
        self.models['gradient_boosting'] = best_model
        self.performance_metrics['gradient_boosting'] = self._calculate_metrics(y_test, y_pred)
        self.feature_importance['gradient_boosting'] = best_model.feature_importances_
    
    def _train_ridge(self, X_train, y_train, X_test, y_test, tscv):
        """Train Ridge regression model."""
        param_grid = {
            'alpha': [0.1, 1.0, 10.0, 100.0, 1000.0]
        }
        
        ridge = Ridge()
        grid_search = GridSearchCV(ridge, param_grid, cv=tscv, scoring='neg_mean_squared_error', n_jobs=-1)
        grid_search.fit(X_train, y_train)
        
        best_model = grid_search.best_estimator_
        y_pred = best_model.predict(X_test)
        
        self.models['ridge'] = best_model
        self.performance_metrics['ridge'] = self._calculate_metrics(y_test, y_pred)
        self.feature_importance['ridge'] = abs(best_model.coef_)
    
    def _train_lstm(self, X_train, y_train, X_test, y_test):
        """Train LSTM model."""
        if not HAS_TENSORFLOW:
            self.logger.warning("TensorFlow not available, skipping LSTM training")
            return
        
        # Reshape data for LSTM
        sequence_length = 20
        X_train_lstm = self._create_sequences(X_train, sequence_length)
        X_test_lstm = self._create_sequences(X_test, sequence_length)
        y_train_lstm = y_train[sequence_length:]
        y_test_lstm = y_test[sequence_length:]
        
        # Build LSTM model
        model = Sequential([
            LSTM(50, return_sequences=True, input_shape=(sequence_length, X_train.shape[1])),
            Dropout(0.2),
            LSTM(50, return_sequences=False),
            Dropout(0.2),
            Dense(25),
            Dense(1)
        ])
        
        model.compile(optimizer=Adam(learning_rate=0.001), loss='mse')
        
        # Train model
        early_stopping = EarlyStopping(patience=10, restore_best_weights=True)
        reduce_lr = ReduceLROnPlateau(patience=5, factor=0.5)
        
        model.fit(
            X_train_lstm, y_train_lstm,
            epochs=100,
            batch_size=32,
            validation_data=(X_test_lstm, y_test_lstm),
            callbacks=[early_stopping, reduce_lr],
            verbose=0
        )
        
        y_pred = model.predict(X_test_lstm).flatten()
        
        self.models['lstm'] = model
        self.performance_metrics['lstm'] = self._calculate_metrics(y_test_lstm, y_pred)
    
    def _train_ensemble(self, X_train, y_train, X_test, y_test):
        """Train ensemble model."""
        if len(self.models) < 2:
            self.logger.warning("Not enough models for ensemble, skipping")
            return
        
        # Get predictions from all models
        predictions = []
        weights = []
        
        for model_name, model in self.models.items():
            if model_name == 'lstm':
                continue  # Skip LSTM for simplicity in ensemble
            
            try:
                y_pred = model.predict(X_test)
                predictions.append(y_pred)
                
                # Weight by inverse of MSE
                mse = self.performance_metrics[model_name]['mse']
                weights.append(1 / (mse + 1e-8))
            except Exception as e:
                self.logger.warning(f"Could not get predictions from {model_name}: {e}")
        
        if not predictions:
            return
        
        # Normalize weights
        weights = np.array(weights)
        weights = weights / weights.sum()
        
        # Create weighted ensemble prediction
        ensemble_pred = np.average(predictions, axis=0, weights=weights)
        
        self.models['ensemble'] = {
            'models': list(self.models.keys()),
            'weights': weights.tolist()
        }
        self.performance_metrics['ensemble'] = self._calculate_metrics(y_test, ensemble_pred)
    
    def _create_sequences(self, data, sequence_length):
        """Create sequences for LSTM."""
        sequences = []
        for i in range(len(data) - sequence_length):
            sequences.append(data[i:i + sequence_length])
        return np.array(sequences)
    
    def _calculate_metrics(self, y_true, y_pred):
        """Calculate performance metrics."""
        return {
            'mse': mean_squared_error(y_true, y_pred),
            'mae': mean_absolute_error(y_true, y_pred),
            'rmse': np.sqrt(mean_squared_error(y_true, y_pred)),
            'r2': r2_score(y_true, y_pred)
        }
    
    def _save_models(self):
        """Save trained models."""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        
        for model_name, model in self.models.items():
            try:
                if model_name == 'lstm':
                    model.save(self.models_dir / f"{model_name}_{timestamp}.h5")
                else:
                    with open(self.models_dir / f"{model_name}_{timestamp}.pkl", 'wb') as f:
                        pickle.dump(model, f)
            except Exception as e:
                self.logger.error(f"Error saving {model_name}: {e}")
        
        # Save scalers
        with open(self.models_dir / f"scalers_{timestamp}.pkl", 'wb') as f:
            pickle.dump(self.scalers, f)
    
    def _save_results(self):
        """Save training results."""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        
        results = {
            'timestamp': timestamp,
            'performance_metrics': self.performance_metrics,
            'feature_importance': {
                model_name: importance.tolist() if hasattr(importance, 'tolist') else importance
                for model_name, importance in self.feature_importance.items()
            },
            'model_config': {
                'models_trained': list(self.models.keys()),
                'feature_count': self.combined_features.shape[1] if self.combined_features is not None else 0
            }
        }
        
        with open(self.results_dir / f"training_results_{timestamp}.json", 'w') as f:
            json.dump(results, f, indent=2, default=str)
        
        self.logger.info(f"Results saved to {self.results_dir}")
    
    def get_best_model(self) -> Tuple[str, Any]:
        """Get the best performing model."""
        if not self.performance_metrics:
            return None, None
        
        best_model_name = min(self.performance_metrics.keys(), 
                            key=lambda x: self.performance_metrics[x]['mse'])
        
        return best_model_name, self.models[best_model_name]
    
    def predict(self, X: np.ndarray, model_name: str = None) -> np.ndarray:
        """
        Make predictions using specified model or best model.
        
        Args:
            X: Feature matrix
            model_name: Name of model to use. If None, uses best model.
            
        Returns:
            Predictions array
        """
        if model_name is None:
            model_name, _ = self.get_best_model()
        
        if model_name not in self.models:
            raise ValueError(f"Model {model_name} not found")
        
        model = self.models[model_name]
        
        # Scale features if needed
        if 'main' in self.scalers:
            X_scaled = self.scalers['main'].transform(X)
        else:
            X_scaled = X
        
        if model_name == 'ensemble':
            # Handle ensemble predictions
            predictions = []
            weights = model['weights']
            
            for i, sub_model_name in enumerate(model['models']):
                if sub_model_name in self.models and sub_model_name != 'ensemble':
                    sub_model = self.models[sub_model_name]
                    pred = sub_model.predict(X_scaled)
                    predictions.append(pred)
            
            if predictions:
                return np.average(predictions, axis=0, weights=weights[:len(predictions)])
            else:
                raise ValueError("No valid models in ensemble")
        else:
            return model.predict(X_scaled)
    
    def generate_model_report(self) -> Dict:
        """Generate comprehensive model performance report."""
        report = {
            'generation_time': datetime.now().isoformat(),
            'models_trained': list(self.models.keys()),
            'performance_comparison': self.performance_metrics,
            'best_model': self.get_best_model()[0] if self.performance_metrics else None,
            'feature_importance_summary': self._summarize_feature_importance(),
            'international_data_impact': self._analyze_international_impact(),
            'recommendations': self._generate_model_recommendations()
        }
        
        # Save report
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        report_path = self.results_dir / f"model_report_{timestamp}.json"
        with open(report_path, 'w') as f:
            json.dump(report, f, indent=2, default=str)
        
        return report
    
    def _summarize_feature_importance(self) -> Dict:
        """Summarize feature importance across models."""
        if not self.feature_importance or self.combined_features is None:
            return {}
        
        feature_names = [col for col in self.combined_features.columns if col != 'nikkei_returns']
        importance_summary = {}
        
        for model_name, importance in self.feature_importance.items():
            if len(importance) == len(feature_names):
                # Get top 10 features
                top_indices = np.argsort(importance)[-10:][::-1]
                importance_summary[model_name] = {
                    'top_features': [feature_names[i] for i in top_indices],
                    'top_importance_scores': [float(importance[i]) for i in top_indices]
                }
        
        return importance_summary
    
    def _analyze_international_impact(self) -> Dict:
        """Analyze the impact of international features."""
        if not self.feature_importance or self.combined_features is None:
            return {}
        
        feature_names = [col for col in self.combined_features.columns if col != 'nikkei_returns']
        international_impact = {}
        
        for model_name, importance in self.feature_importance.items():
            if len(importance) != len(feature_names):
                continue
            
            # Categorize features
            nikkei_importance = sum(importance[i] for i, name in enumerate(feature_names) if 'nikkei' in name)
            forex_importance = sum(importance[i] for i, name in enumerate(feature_names) if 'forex' in name)
            index_importance = sum(importance[i] for i, name in enumerate(feature_names) if 'index' in name)
            commodity_importance = sum(importance[i] for i, name in enumerate(feature_names) if 'commodity' in name)
            
            total_importance = nikkei_importance + forex_importance + index_importance + commodity_importance
            
            if total_importance > 0:
                international_impact[model_name] = {
                    'nikkei_features': nikkei_importance / total_importance,
                    'forex_features': forex_importance / total_importance,
                    'index_features': index_importance / total_importance,
                    'commodity_features': commodity_importance / total_importance
                }
        
        return international_impact
    
    def _generate_model_recommendations(self) -> List[str]:
        """Generate recommendations based on model performance."""
        recommendations = []
        
        if self.performance_metrics:
            best_model = self.get_best_model()[0]
            recommendations.append(f"Use {best_model} as primary model for predictions")
            
            # Check if ensemble is significantly better
            if 'ensemble' in self.performance_metrics and best_model != 'ensemble':
                ensemble_mse = self.performance_metrics['ensemble']['mse']
                best_mse = self.performance_metrics[best_model]['mse']
                
                if ensemble_mse < best_mse * 0.95:
                    recommendations.append("Consider using ensemble model for improved robustness")
        
        # Feature recommendations
        if self.feature_importance:
            recommendations.append("Monitor top performing international features for stability")
            recommendations.append("Consider feature selection to reduce overfitting")
        
        recommendations.append("Implement rolling window retraining for model adaptation")
        recommendations.append("Monitor correlation changes to trigger model updates")
        
        return recommendations