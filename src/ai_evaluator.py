"""
AI Model Evaluator for Nikkei225 Trading System

This module provides comprehensive evaluation metrics and analysis
for AI trading models including performance, risk, and reliability metrics.
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from typing import Dict, List, Tuple, Optional, Any
from datetime import datetime, timedelta
import logging
from pathlib import Path
import json

# Setup logging
logger = logging.getLogger(__name__)

class AIEvaluator:
    """
    Comprehensive AI model evaluator for trading systems
    """
    
    def __init__(self, output_dir: str = "results/evaluation"):
        """
        Initialize the AI evaluator
        
        Args:
            output_dir: Directory to save evaluation results
        """
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # Evaluation metrics storage
        self.metrics = {}
        self.predictions_history = []
        self.performance_history = []
        
    def evaluate_predictions(self, 
                           y_true: np.ndarray, 
                           y_pred: np.ndarray,
                           model_name: str = "model") -> Dict[str, float]:
        """
        Evaluate prediction accuracy
        
        Args:
            y_true: Actual values
            y_pred: Predicted values
            model_name: Name of the model being evaluated
            
        Returns:
            Dictionary of evaluation metrics
        """
        from sklearn.metrics import (
            mean_squared_error, mean_absolute_error, 
            mean_absolute_percentage_error, r2_score
        )
        
        # Basic regression metrics
        mse = mean_squared_error(y_true, y_pred)
        rmse = np.sqrt(mse)
        mae = mean_absolute_error(y_true, y_pred)
        mape = mean_absolute_percentage_error(y_true, y_pred)
        r2 = r2_score(y_true, y_pred)
        
        # Trading-specific metrics
        direction_accuracy = self._calculate_direction_accuracy(y_true, y_pred)
        profit_correlation = self._calculate_profit_correlation(y_true, y_pred)
        
        metrics = {
            "mse": mse,
            "rmse": rmse,
            "mae": mae,
            "mape": mape,
            "r2_score": r2,
            "direction_accuracy": direction_accuracy,
            "profit_correlation": profit_correlation,
            "model_name": model_name,
            "evaluation_date": datetime.now().isoformat()
        }
        
        # Store metrics
        self.metrics[model_name] = metrics
        
        logger.info(f"Evaluation completed for {model_name}")
        logger.info(f"RMSE: {rmse:.4f}, Direction Accuracy: {direction_accuracy:.2%}")
        
        return metrics
    
    def evaluate_trading_performance(self, 
                                   trades: List[Dict],
                                   initial_balance: float = 1000000) -> Dict[str, float]:
        """
        Evaluate trading performance from trade history
        
        Args:
            trades: List of trade dictionaries
            initial_balance: Starting balance
            
        Returns:
            Trading performance metrics
        """
        if not trades:
            return {"error": "No trades to evaluate"}
        
        # Convert to DataFrame for easier analysis
        df = pd.DataFrame(trades)
        
        # Calculate returns
        df['timestamp'] = pd.to_datetime(df['timestamp'])
        df = df.sort_values('timestamp')
        
        # Calculate cumulative P&L
        df['pnl'] = 0.0
        current_positions = {}
        
        for idx, trade in df.iterrows():
            symbol = trade['symbol']
            side = trade['side']
            qty = trade['qty']
            price = trade['price']
            
            if side == 'buy':
                current_positions[symbol] = current_positions.get(symbol, 0) + qty
                df.loc[idx, 'pnl'] = -qty * price  # Cash outflow
            else:  # sell
                current_positions[symbol] = current_positions.get(symbol, 0) - qty
                df.loc[idx, 'pnl'] = qty * price   # Cash inflow
        
        df['cumulative_pnl'] = df['pnl'].cumsum()
        df['portfolio_value'] = initial_balance + df['cumulative_pnl']
        
        # Calculate performance metrics
        total_return = (df['portfolio_value'].iloc[-1] - initial_balance) / initial_balance
        
        # Daily returns for risk metrics
        daily_returns = df.groupby(df['timestamp'].dt.date)['pnl'].sum()
        daily_returns_pct = daily_returns / initial_balance
        
        # Risk metrics
        volatility = daily_returns_pct.std() * np.sqrt(252)  # Annualized
        sharpe_ratio = (daily_returns_pct.mean() * 252) / (volatility + 1e-8)
        
        # Drawdown analysis
        cumulative_returns = (1 + daily_returns_pct).cumprod()
        running_max = cumulative_returns.expanding().max()
        drawdown = (cumulative_returns - running_max) / running_max
        max_drawdown = drawdown.min()
        
        # Win rate
        profitable_trades = df[df['pnl'] > 0]
        win_rate = len(profitable_trades) / len(df) if len(df) > 0 else 0
        
        # Average trade metrics
        avg_profit = profitable_trades['pnl'].mean() if len(profitable_trades) > 0 else 0
        losing_trades = df[df['pnl'] < 0]
        avg_loss = losing_trades['pnl'].mean() if len(losing_trades) > 0 else 0
        profit_factor = abs(avg_profit / avg_loss) if avg_loss != 0 else float('inf')
        
        performance_metrics = {
            "total_return": total_return,
            "annualized_return": total_return * (252 / len(daily_returns)) if len(daily_returns) > 0 else 0,
            "volatility": volatility,
            "sharpe_ratio": sharpe_ratio,
            "max_drawdown": max_drawdown,
            "win_rate": win_rate,
            "total_trades": len(df),
            "profitable_trades": len(profitable_trades),
            "avg_profit": avg_profit,
            "avg_loss": avg_loss,
            "profit_factor": profit_factor,
            "final_portfolio_value": df['portfolio_value'].iloc[-1],
            "evaluation_date": datetime.now().isoformat()
        }
        
        # Store performance history
        self.performance_history.append(performance_metrics)
        
        logger.info(f"Trading Performance Evaluation:")
        logger.info(f"Total Return: {total_return:.2%}")
        logger.info(f"Sharpe Ratio: {sharpe_ratio:.2f}")
        logger.info(f"Win Rate: {win_rate:.2%}")
        
        return performance_metrics
    
    def _calculate_direction_accuracy(self, y_true: np.ndarray, y_pred: np.ndarray) -> float:
        """Calculate directional prediction accuracy"""
        if len(y_true) < 2:
            return 0.0
        
        true_direction = np.diff(y_true) > 0
        pred_direction = np.diff(y_pred) > 0
        
        return np.mean(true_direction == pred_direction)
    
    def _calculate_profit_correlation(self, y_true: np.ndarray, y_pred: np.ndarray) -> float:
        """Calculate correlation between predictions and actual profits"""
        if len(y_true) < 2:
            return 0.0
        
        true_returns = np.diff(y_true) / y_true[:-1]
        pred_returns = np.diff(y_pred) / y_pred[:-1]
        
        correlation = np.corrcoef(true_returns, pred_returns)[0, 1]
        return correlation if not np.isnan(correlation) else 0.0
    
    def compare_models(self, models_metrics: Dict[str, Dict]) -> pd.DataFrame:
        """
        Compare multiple models
        
        Args:
            models_metrics: Dictionary of model metrics
            
        Returns:
            Comparison DataFrame
        """
        comparison_df = pd.DataFrame(models_metrics).T
        
        # Rank models by key metrics
        comparison_df['rank_rmse'] = comparison_df['rmse'].rank()
        comparison_df['rank_direction'] = comparison_df['direction_accuracy'].rank(ascending=False)
        comparison_df['rank_r2'] = comparison_df['r2_score'].rank(ascending=False)
        
        # Overall score (lower is better)
        comparison_df['overall_score'] = (
            comparison_df['rank_rmse'] + 
            comparison_df['rank_direction'] + 
            comparison_df['rank_r2']
        ) / 3
        
        comparison_df = comparison_df.sort_values('overall_score')
        
        return comparison_df
    
    def generate_evaluation_report(self, 
                                 model_metrics: Dict = None,
                                 trading_metrics: Dict = None,
                                 save_plots: bool = True) -> str:
        """
        Generate comprehensive evaluation report
        
        Args:
            model_metrics: Model prediction metrics
            trading_metrics: Trading performance metrics
            save_plots: Whether to save visualization plots
            
        Returns:
            Report text
        """
        report_lines = []
        report_lines.append("=" * 60)
        report_lines.append("AI TRADING SYSTEM EVALUATION REPORT")
        report_lines.append("=" * 60)
        report_lines.append(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report_lines.append("")
        
        # Model Performance Section
        if model_metrics:
            report_lines.append("MODEL PREDICTION PERFORMANCE")
            report_lines.append("-" * 30)
            report_lines.append(f"Model: {model_metrics.get('model_name', 'Unknown')}")
            report_lines.append(f"RMSE: {model_metrics.get('rmse', 0):.4f}")
            report_lines.append(f"MAE: {model_metrics.get('mae', 0):.4f}")
            report_lines.append(f"MAPE: {model_metrics.get('mape', 0):.2%}")
            report_lines.append(f"R² Score: {model_metrics.get('r2_score', 0):.4f}")
            report_lines.append(f"Direction Accuracy: {model_metrics.get('direction_accuracy', 0):.2%}")
            report_lines.append("")
        
        # Trading Performance Section
        if trading_metrics:
            report_lines.append("TRADING PERFORMANCE")
            report_lines.append("-" * 20)
            report_lines.append(f"Total Return: {trading_metrics.get('total_return', 0):.2%}")
            report_lines.append(f"Annualized Return: {trading_metrics.get('annualized_return', 0):.2%}")
            report_lines.append(f"Volatility: {trading_metrics.get('volatility', 0):.2%}")
            report_lines.append(f"Sharpe Ratio: {trading_metrics.get('sharpe_ratio', 0):.2f}")
            report_lines.append(f"Max Drawdown: {trading_metrics.get('max_drawdown', 0):.2%}")
            report_lines.append(f"Win Rate: {trading_metrics.get('win_rate', 0):.2%}")
            report_lines.append(f"Total Trades: {trading_metrics.get('total_trades', 0)}")
            report_lines.append(f"Profit Factor: {trading_metrics.get('profit_factor', 0):.2f}")
            report_lines.append("")
        
        # Recommendations
        report_lines.append("RECOMMENDATIONS")
        report_lines.append("-" * 15)
        
        if model_metrics:
            if model_metrics.get('direction_accuracy', 0) < 0.55:
                report_lines.append("⚠ Low directional accuracy - consider model retraining")
            if model_metrics.get('r2_score', 0) < 0.3:
                report_lines.append("⚠ Low R² score - model may need more features")
        
        if trading_metrics:
            if trading_metrics.get('sharpe_ratio', 0) < 1.0:
                report_lines.append("⚠ Low Sharpe ratio - consider risk management improvements")
            if trading_metrics.get('max_drawdown', 0) < -0.15:
                report_lines.append("⚠ High drawdown - implement better stop-loss strategies")
            if trading_metrics.get('win_rate', 0) < 0.5:
                report_lines.append("⚠ Low win rate - review entry/exit criteria")
        
        report_text = "\n".join(report_lines)
        
        # Save report
        report_file = self.output_dir / f"evaluation_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
        with open(report_file, 'w') as f:
            f.write(report_text)
        
        logger.info(f"Evaluation report saved to {report_file}")
        
        return report_text
    
    def save_metrics(self, filename: str = None):
        """Save all metrics to JSON file"""
        if filename is None:
            filename = f"evaluation_metrics_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        filepath = self.output_dir / filename
        
        data = {
            "model_metrics": self.metrics,
            "performance_history": self.performance_history,
            "evaluation_timestamp": datetime.now().isoformat()
        }
        
        with open(filepath, 'w') as f:
            json.dump(data, f, indent=2, default=str)
        
        logger.info(f"Metrics saved to {filepath}")
        
        return str(filepath)
