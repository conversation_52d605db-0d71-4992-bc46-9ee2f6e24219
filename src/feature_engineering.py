import pandas as pd
import numpy as np
import os
from datetime import timedelta
# import talib  # Commented out due to installation issues with Python 3.12

# Custom implementations of technical indicators to replace TA-Lib functionality
def calculate_sma(data, period):
    """Calculate Simple Moving Average"""
    return data.rolling(window=period).mean()

def calculate_ema(data, period):
    """Calculate Exponential Moving Average"""
    return data.ewm(span=period, adjust=False).mean()

def calculate_wma(data, period):
    """Calculate Weighted Moving Average"""
    weights = np.arange(1, period + 1)
    return data.rolling(window=period).apply(lambda x: np.sum(weights * x) / weights.sum(), raw=True)

def calculate_bbands(data, period=20, stddev=2):
    """Calculate Bollinger Bands"""
    middle = calculate_sma(data, period)
    std = data.rolling(window=period).std()
    upper = middle + (std * stddev)
    lower = middle - (std * stddev)
    return upper, middle, lower

def calculate_rsi(data, period=14):
    """Calculate Relative Strength Index"""
    delta = data.diff()
    gain = (delta.where(delta > 0, 0)).fillna(0)
    loss = (-delta.where(delta < 0, 0)).fillna(0)
    avg_gain = gain.rolling(window=period).mean()
    avg_loss = loss.rolling(window=period).mean()
    rs = avg_gain / avg_loss.replace(0, np.nan)
    rsi = 100 - (100 / (1 + rs))
    return rsi

def calculate_macd(data, fast_period=12, slow_period=26, signal_period=9):
    """Calculate MACD (Moving Average Convergence Divergence)"""
    fast_ema = calculate_ema(data, fast_period)
    slow_ema = calculate_ema(data, slow_period)
    macd_line = fast_ema - slow_ema
    signal_line = calculate_ema(macd_line, signal_period)
    histogram = macd_line - signal_line
    return macd_line, signal_line, histogram

def calculate_atr(high, low, close, period=14):
    """Calculate Average True Range"""
    tr1 = abs(high - low)
    tr2 = abs(high - close.shift(1))
    tr3 = abs(low - close.shift(1))
    tr = pd.DataFrame({'tr1': tr1, 'tr2': tr2, 'tr3': tr3}).max(axis=1)
    atr = tr.rolling(window=period).mean()
    return atr

def calculate_stoch(high, low, close, k_period=14, d_period=3):
    """Calculate Stochastic Oscillator"""
    highest_high = high.rolling(window=k_period).max()
    lowest_low = low.rolling(window=k_period).min()
    stoch_k = 100 * ((close - lowest_low) / (highest_high - lowest_low + 1e-9))
    stoch_d = stoch_k.rolling(window=d_period).mean()
    return stoch_k, stoch_d

# インポート用の便利な関数
def add_features(df):
    """
    データフレームに特徴量を追加する関数
    
    Parameters:
    -----------
    df : pd.DataFrame
        特徴量を追加するデータフレーム
        
    Returns:
    --------
    pd.DataFrame
        特徴量が追加されたデータフレーム
    """
    # 必要な列があるか確認
    required_columns = ['Open', 'High', 'Low', 'Close', 'Volume']
    missing_columns = [col for col in required_columns if col not in df.columns]
    if missing_columns:
        raise ValueError(f"Missing required columns: {missing_columns}")
    
    # ティッカーごとにグループ化
    if 'Ticker' in df.columns:
        grouped = df.groupby('Ticker')
        result_dfs = []
        
        for ticker, group in grouped:
            # 特徴量を計算
            ticker_df = _calculate_features(group.copy())
            result_dfs.append(ticker_df)
        
        # 結果を結合
        if result_dfs:
            return pd.concat(result_dfs, axis=0)
        else:
            return df
    else:
        # ティッカー列がない場合は単一の株式と見なす
        return _calculate_features(df.copy())

def _calculate_features(df):
    """内部的に特徴量を計算する関数"""
    # 単純移動平均
    df['SMA_5'] = calculate_sma(df['Close'], 5)
    df['SMA_10'] = calculate_sma(df['Close'], 10)
    df['SMA_20'] = calculate_sma(df['Close'], 20)
    df['SMA_50'] = calculate_sma(df['Close'], 50)
    
    # 指数移動平均
    df['EMA_12'] = calculate_ema(df['Close'], 12)
    df['EMA_26'] = calculate_ema(df['Close'], 26)
    
    # MACD
    macd, signal, hist = calculate_macd(df['Close'])
    df['MACD'] = macd
    df['MACD_Signal'] = signal
    df['MACD_Hist'] = hist
    
    # RSI
    df['RSI_14'] = calculate_rsi(df['Close'], 14)
    
    # ボリンジャーバンド
    upper, middle, lower = calculate_bbands(df['Close'])
    df['BB_Upper'] = upper
    df['BB_Middle'] = middle
    df['BB_Lower'] = lower
    
    # ATR
    df['ATR_14'] = calculate_atr(df['High'], df['Low'], df['Close'], 14)
    
    # ストキャスティクス
    stoch_k, stoch_d = calculate_stoch(df['High'], df['Low'], df['Close'])
    df['STOCH_K'] = stoch_k
    df['STOCH_D'] = stoch_d
    
    # 価格変動率
    df['Returns_1D'] = df['Close'].pct_change(1)
    df['Returns_5D'] = df['Close'].pct_change(5)
    
    # ボラティリティ
    df['Volatility_5D'] = df['Returns_1D'].rolling(5).std()
    df['Volatility_20D'] = df['Returns_1D'].rolling(20).std()
    
    # 出来高変化率
    df['Volume_Change'] = df['Volume'].pct_change(1)
    df['Volume_MA_5'] = calculate_sma(df['Volume'], 5)
    df['Volume_Ratio'] = df['Volume'] / df['Volume_MA_5']
    
    return df

class FeatureEngineer:
    """Feature Engineering for Nikkei 225 Data"""

    def __init__(self):
        pass

    def engineer_features(self, df):
        """Add technical indicators and features to the dataframe"""
        return add_features(df)
