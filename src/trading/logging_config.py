#!/usr/bin/env python3
import os
import logging
import traceback
import inspect
import json
from datetime import datetime
from typing import Dict, Any, Optional, Callable

# 基本ロギング設定
def setup_logging(log_dir: str = "logs", log_level: int = logging.INFO) -> None:
    """
    ロギングの基本設定を行う
    
    Parameters:
    -----------
    log_dir : str
        ログディレクトリ
    log_level : int
        ロギングレベル
    """
    os.makedirs(log_dir, exist_ok=True)
    
    # ルートロガー設定
    root_logger = logging.getLogger()
    root_logger.setLevel(log_level)
    
    # 既存のハンドラを削除（重複防止）
    for handler in root_logger.handlers[:]:
        root_logger.removeHandler(handler)
    
    # コンソールハンドラ設定
    console_handler = logging.StreamHandler()
    console_handler.setLevel(log_level)
    console_format = logging.Formatter(
        '%(asctime)s [%(levelname)s] %(name)s: %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    console_handler.setFormatter(console_format)
    root_logger.addHandler(console_handler)
    
    # ファイルハンドラ設定
    # 現在の日付をファイル名に使用
    today = datetime.now().strftime("%Y%m%d")
    log_file = os.path.join(log_dir, f"app_{today}.log")
    
    file_handler = logging.FileHandler(log_file, encoding='utf-8')
    file_handler.setLevel(log_level)
    file_format = logging.Formatter(
        '%(asctime)s [%(levelname)s] %(name)s: %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    file_handler.setFormatter(file_format)
    root_logger.addHandler(file_handler)


def configure_logging(log_dir: str = "logs", error_log_dir: str = "logs/errors", 
                    app_name: str = "app", log_level: int = logging.INFO) -> logging.Logger:
    """
    アプリケーション用のロガーを設定し、返却する
    
    Parameters:
    -----------
    log_dir : str
        ログディレクトリ
    error_log_dir : str
        エラーログディレクトリ
    app_name : str
        アプリケーション名
    log_level : int
        ロギングレベル
        
    Returns:
    --------
    logging.Logger
        設定済みロガー
    """
    # ディレクトリ作成
    os.makedirs(log_dir, exist_ok=True)
    os.makedirs(error_log_dir, exist_ok=True)
    
    # 現在の日時をファイル名に使用
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    log_file = os.path.join(log_dir, f"{app_name}_{timestamp}.log")
    
    # アプリケーションロガー設定
    logger = logging.getLogger(app_name)
    logger.setLevel(log_level)
    
    # 既存のハンドラを削除（重複防止）
    for handler in logger.handlers[:]:
        logger.removeHandler(handler)
    
    # コンソールハンドラ設定
    console_handler = logging.StreamHandler()
    console_handler.setLevel(log_level)
    console_format = logging.Formatter(
        '%(asctime)s [%(levelname)s] %(name)s: %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    console_handler.setFormatter(console_format)
    logger.addHandler(console_handler)
    
    # ファイルハンドラ設定
    file_handler = logging.FileHandler(log_file, encoding='utf-8')
    file_handler.setLevel(log_level)
    file_format = logging.Formatter(
        '%(asctime)s [%(levelname)s] %(name)s: %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    file_handler.setFormatter(file_format)
    logger.addHandler(file_handler)
    
    return logger


def get_module_logger(module_name: str) -> logging.Logger:
    """
    モジュール用のロガーを取得
    
    Parameters:
    -----------
    module_name : str
        モジュール名（通常は__name__）
        
    Returns:
    --------
    logging.Logger
        設定済みロガー
    """
    logger = logging.getLogger(module_name)
    
    # ロガーがまだ設定されていない場合は基本設定を適用
    if not logger.handlers and not logging.getLogger().handlers:
        setup_logging()
    
    return logger


def get_error_context_from_exception(exception: Exception) -> Dict[str, Any]:
    """
    例外からエラーコンテキストを抽出
    
    Parameters:
    -----------
    exception : Exception
        発生した例外
        
    Returns:
    --------
    Dict[str, Any]
        エラーコンテキスト情報
    """
    tb = traceback.extract_tb(exception.__traceback__)
    frames = []
    
    for frame in tb:
        frames.append({
            "filename": os.path.basename(frame.filename),
            "lineno": frame.lineno,
            "name": frame.name,
            "line": frame.line
        })
    
    # 呼び出し元情報の取得
    caller_frame = None
    current_frame = inspect.currentframe()
    try:
        if current_frame:
            # 2レベル上のフレームが呼び出し元（この関数→log_error_with_context→呼び出し元）
            frame = current_frame.f_back
            if frame and frame.f_back:
                caller_frame = frame.f_back
                
                caller_info = {
                    "function": caller_frame.f_code.co_name,
                    "filename": os.path.basename(caller_frame.f_code.co_filename),
                    "lineno": caller_frame.f_lineno
                }
            else:
                caller_info = {"function": "unknown", "filename": "unknown", "lineno": 0}
    finally:
        # 循環参照を防ぐためのフレーム参照解放
        del current_frame
        del caller_frame
    
    context = {
        "exception_type": type(exception).__name__,
        "exception_message": str(exception),
        "traceback": frames,
        "caller": caller_info if 'caller_info' in locals() else {"function": "unknown", "filename": "unknown", "lineno": 0}
    }
    
    return context


def log_error_with_context(
    logger: logging.Logger, 
    exception: Exception, 
    message: str, 
    additional_context: Optional[Dict[str, Any]] = None
) -> None:
    """
    コンテキスト情報付きでエラーをログに記録
    
    Parameters:
    -----------
    logger : logging.Logger
        使用するロガーインスタンス
    exception : Exception
        発生した例外
    message : str
        ログメッセージ
    additional_context : Optional[Dict[str, Any]]
        追加のコンテキスト情報
    """
    # 基本的なエラーコンテキストを取得
    context = get_error_context_from_exception(exception)
    
    # 追加コンテキストがあれば統合
    if additional_context:
        context.update(additional_context)
    
    # コンテキストのJSON文字列化（ログ出力用）
    try:
        context_json = json.dumps(context, ensure_ascii=False, indent=None)
    except:
        # JSON化できない場合は単純な文字列として出力
        context_json = str(context)
    
    # エラーログ出力
    logger.error(f"{message}: {exception} | コンテキスト: {context_json}")
