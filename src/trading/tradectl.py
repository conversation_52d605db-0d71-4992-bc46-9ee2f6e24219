#!/usr/bin/env python3
import os
import sys
import argparse
import logging
import json
import time
from datetime import datetime
from typing import Optional

# 自作モジュール
from src.trading.kabu_system import KabuTradingSystem

# ロガー設定
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s [%(levelname)s] %(name)s: %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler(f"logs/tradectl_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log")
    ]
)
logger = logging.getLogger(__name__)

# 設定ファイルパス
CONFIG_DIR = os.path.join(os.path.dirname(os.path.abspath(__file__)), "../../config")
MODE_CONFIG_FILE = os.path.join(CONFIG_DIR, "trading_mode.json")

# 各種デフォルト値
DEFAULT_MODE = "nocash"
DEFAULT_INITIAL_BALANCE = 1000000
DEFAULT_MAX_STOCKS = 5
DEFAULT_OUTPUT_DIR = "results/kabu"

def ensure_config_dir():
    """設定ディレクトリの存在確認"""
    os.makedirs(CONFIG_DIR, exist_ok=True)

def save_mode_config(mode: str) -> None:
    """
    取引モードの保存
    
    Parameters:
    -----------
    mode : str
        取引モード ("nocash", "paper", "live")
    """
    ensure_config_dir()
    config = {
        "mode": mode,
        "updated_at": datetime.now().isoformat()
    }
    with open(MODE_CONFIG_FILE, 'w', encoding='utf-8') as f:
        json.dump(config, f, ensure_ascii=False, indent=2)
    logger.info(f"取引モードを保存しました: {mode}")

def get_current_mode() -> str:
    """
    現在の取引モード取得
    
    Returns:
    --------
    str
        取引モード
    """
    if not os.path.exists(MODE_CONFIG_FILE):
        logger.warning(f"モード設定ファイルが存在しません: {MODE_CONFIG_FILE}")
        return DEFAULT_MODE
        
    try:
        with open(MODE_CONFIG_FILE, 'r', encoding='utf-8') as f:
            config = json.load(f)
        return config.get("mode", DEFAULT_MODE)
    except Exception as e:
        logger.error(f"モード設定ファイルの読み込みエラー: {str(e)}")
        return DEFAULT_MODE

def set_mode(args) -> None:
    """
    取引モードの設定
    
    Parameters:
    -----------
    args : argparse.Namespace
        コマンドライン引数
    """
    mode = args.mode.lower()
    
    if mode not in ["nocash", "paper", "live"]:
        logger.error(f"不正なモード: {mode}。'nocash', 'paper', 'live'のいずれかを指定してください。")
        return
        
    # Liveモードの場合は確認プロンプト
    if mode == "live" and not args.force:
        confirmation = input("Liveモードでは実際の取引が行われます。よろしいですか？ (y/N): ")
        if confirmation.lower() != 'y':
            logger.info("Liveモードへの切り替えをキャンセルしました")
            return
    
    save_mode_config(mode)
    print(f"取引モードを {mode} に設定しました")

def start_trading(args) -> None:
    """
    取引システムの開始
    
    Parameters:
    -----------
    args : argparse.Namespace
        コマンドライン引数
    """
    mode = args.mode or get_current_mode()
    initial_balance = args.initial_balance
    max_stocks = args.max_stocks
    
    print(f"取引システムを開始します: モード={mode}, 初期資金={initial_balance:,}円, 最大銘柄数={max_stocks}")
    
    try:
        api_password = os.environ.get("KABU_API_PWD")
        if not api_password:
            print("エラー: 環境変数KABU_API_PWDが設定されていません")
            return
            
        trading_system = KabuTradingSystem(
            mode=mode,
            api_password=api_password,
            initial_balance=initial_balance,
            max_stocks=max_stocks,
            output_dir=args.output_dir
        )
        
        # システム開始
        trading_system.start()
        
        print("取引システムが開始されました。Ctrl+Cで停止します。")
        
        # メインスレッドをブロック
        try:
            while True:
                time.sleep(1)
        except KeyboardInterrupt:
            print("\n停止リクエストを受け付けました...")
            trading_system.stop()
            print("取引システムを停止しました")
            
    except Exception as e:
        logger.error(f"取引システム開始エラー: {str(e)}", exc_info=True)
        print(f"エラー: {str(e)}")

def stop_trading(args) -> None:
    """
    取引システムの停止（外部から使用する場合）
    
    Parameters:
    -----------
    args : argparse.Namespace
        コマンドライン引数
    """
    print("この機能は現在実装されていません。")
    print("取引システムを停止するには、起動しているプロセスでCtrl+Cを押してください。")

def generate_report(args) -> None:
    """
    レポート生成
    
    Parameters:
    -----------
    args : argparse.Namespace
        コマンドライン引数
    """
    mode = args.mode or get_current_mode()
    period = args.period
    output_dir = args.output_dir
    
    print(f"レポートを生成します: モード={mode}, 期間={period}")
    
    try:
        api_password = os.environ.get("KABU_API_PWD")
        if not api_password:
            print("エラー: 環境変数KABU_API_PWDが設定されていません")
            return
            
        trading_system = KabuTradingSystem(
            mode=mode,
            api_password=api_password,
            output_dir=output_dir
        )
        
        # レポート生成
        report_file = trading_system.generate_report(period=period)
        
        print(f"レポートを生成しました: {report_file}")
            
    except Exception as e:
        logger.error(f"レポート生成エラー: {str(e)}", exc_info=True)
        print(f"エラー: {str(e)}")

def show_status(args) -> None:
    """
    システム状態の表示
    
    Parameters:
    -----------
    args : argparse.Namespace
        コマンドライン引数
    """
    current_mode = get_current_mode()
    
    # 現在の設定を表示
    print("\n=== 取引システム状態 ===")
    print(f"現在のモード: {current_mode}")
    
    # 環境変数のチェック
    api_password = os.environ.get("KABU_API_PWD")
    if api_password:
        print("APIパスワード: 設定済み")
    else:
        print("APIパスワード: 未設定 (環境変数KABU_API_PWDを設定してください)")
    
    # 設定ファイルの存在チェック
    if os.path.exists(MODE_CONFIG_FILE):
        try:
            with open(MODE_CONFIG_FILE, 'r', encoding='utf-8') as f:
                config = json.load(f)
            updated_at = config.get("updated_at", "不明")
            print(f"設定更新日時: {updated_at}")
        except Exception:
            print("設定ファイル読み込みエラー")
    else:
        print("設定ファイル: 未作成")
    
    # 実行中プロセスの確認（簡易的な実装）
    import subprocess
    try:
        result = subprocess.run(
            "ps aux | grep kabu_system | grep -v grep",
            shell=True, capture_output=True, text=True
        )
        if result.stdout.strip():
            print("\n実行中プロセス:")
            for line in result.stdout.strip().split("\n"):
                print(f"  {line}")
        else:
            print("\n実行中のプロセスはありません")
    except Exception:
        print("\nプロセス確認エラー")
    
    print("\n使用方法:")
    print("  モード設定: tradectl set-mode [nocash|paper|live]")
    print("  開始: tradectl start")
    print("  レポート生成: tradectl report [daily|weekly|monthly]")
    print()

def main():
    """メイン関数"""
    parser = argparse.ArgumentParser(description="kabuステーションAPI取引システム制御ツール")
    subparsers = parser.add_subparsers(dest="command", help="サブコマンド")
    
    # モード設定コマンド
    mode_parser = subparsers.add_parser("set-mode", help="取引モードの設定")
    mode_parser.add_argument("mode", choices=["nocash", "paper", "live"],
                            help="取引モード (nocash: 評価のみ, paper: 仮想取引, live: 実取引)")
    mode_parser.add_argument("-f", "--force", action="store_true",
                           help="確認プロンプトをスキップ")
    mode_parser.set_defaults(func=set_mode)
    
    # 開始コマンド
    start_parser = subparsers.add_parser("start", help="取引システムの開始")
    start_parser.add_argument("-m", "--mode", choices=["nocash", "paper", "live"],
                            help="取引モード (未指定時は保存されているモードを使用)")
    start_parser.add_argument("-b", "--initial-balance", type=int, default=DEFAULT_INITIAL_BALANCE,
                            help=f"初期資金 (デフォルト: {DEFAULT_INITIAL_BALANCE:,}円)")
    start_parser.add_argument("-s", "--max-stocks", type=int, default=DEFAULT_MAX_STOCKS,
                            help=f"最大保有銘柄数 (デフォルト: {DEFAULT_MAX_STOCKS})")
    start_parser.add_argument("-o", "--output-dir", default=DEFAULT_OUTPUT_DIR,
                            help=f"出力ディレクトリ (デフォルト: {DEFAULT_OUTPUT_DIR})")
    start_parser.set_defaults(func=start_trading)
    
    # 停止コマンド
    stop_parser = subparsers.add_parser("stop", help="取引システムの停止")
    stop_parser.set_defaults(func=stop_trading)
    
    # レポート生成コマンド
    report_parser = subparsers.add_parser("report", help="レポート生成")
    report_parser.add_argument("period", choices=["daily", "weekly", "monthly"],
                             help="レポート期間")
    report_parser.add_argument("-m", "--mode", choices=["nocash", "paper", "live"],
                             help="取引モード (未指定時は保存されているモードを使用)")
    report_parser.add_argument("-o", "--output-dir", default=DEFAULT_OUTPUT_DIR,
                             help=f"出力ディレクトリ (デフォルト: {DEFAULT_OUTPUT_DIR})")
    report_parser.set_defaults(func=generate_report)
    
    # 状態表示コマンド
    status_parser = subparsers.add_parser("status", help="システム状態の表示")
    status_parser.set_defaults(func=show_status)
    
    # コマンドライン引数の解析
    args = parser.parse_args()
    
    # サブコマンドなしの場合はヘルプを表示
    if not hasattr(args, "func"):
        parser.print_help()
        return
    
    # サブコマンド関数の実行
    args.func(args)

if __name__ == "__main__":
    # logsディレクトリの作成
    os.makedirs("logs", exist_ok=True)
    main()
