#!/usr/bin/env python3
import os
import time
import json
import logging
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional, Tuple
import threading
import schedule

# 自作モジュール
from src.trading.kabu_api import KabuSession
from src.trading.kabu_collector import KabuDataCollector
from src.trading.kabu_trader import BaseTrader, NoCashTrader, PaperTrader, LiveTrader

# ロガー設定
logger = logging.getLogger(__name__)

class KabuTradingSystem:
    """
    kabuステーションAPIを使用した取引システム
    - データ収集
    - 銘柄選択
    - 売買判断
    - 取引実行
    - 銘柄ローテーション管理
    - スケジュール管理（1時間サイクル）
    """
    def __init__(self, mode: str = "nocash", api_password: str = None, 
                initial_balance: int = 1000000, max_stocks: int = 5,
                output_dir: str = "results/kabu"):
        """
        初期化
        
        Parameters:
        -----------
        mode : str
            取引モード ("nocash", "paper", "live")
        api_password : str
            kabuステーションAPIパスワード
        initial_balance : int
            初期資金
        max_stocks : int
            最大保有銘柄数
        output_dir : str
            結果出力ディレクトリ
        """
        self.mode = mode.lower()
        self.api_password = api_password or os.environ.get("KABU_API_PWD")
        self.initial_balance = initial_balance
        self.max_stocks = max_stocks
        self.output_dir = output_dir
        
        # 各種ディレクトリ
        self.config_dir = os.path.join(output_dir, "config")
        self.data_dir = os.path.join(output_dir, "data")
        self.pred_dir = os.path.join(output_dir, "predictions")
        
        # ディレクトリ作成
        for dir_path in [self.output_dir, self.config_dir, self.data_dir, self.pred_dir]:
            os.makedirs(dir_path, exist_ok=True)
        
        # 設定のバリデーション
        if not self.api_password:
            raise ValueError("kabuステーションAPIパスワードが設定されていません。環境変数KABU_API_PWDを設定してください。")
            
        if self.mode not in ["nocash", "paper", "live"]:
            raise ValueError(f"不正なモード: {self.mode}。'nocash', 'paper', 'live'のいずれかを指定してください。")
        
        # 取引サイクル管理
        self.current_cycle = 0
        self.running = False
        self.scheduler_thread = None
        
        # 銘柄管理
        self.active_symbols = []
        self.candidate_symbols = []
        self.next_symbols = []
        
        # モジュール初期化
        self.session = KabuSession(self.api_password)
        self.collector = KabuDataCollector(self.session, output_dir=self.data_dir)
        
        # トレーダー初期化
        if self.mode == "nocash":
            self.trader = NoCashTrader(self.session, self.collector, 
                                      initial_balance=self.initial_balance,
                                      max_stocks=self.max_stocks,
                                      output_dir=self.output_dir)
        elif self.mode == "paper":
            self.trader = PaperTrader(self.session, self.collector, 
                                     initial_balance=self.initial_balance,
                                     max_stocks=self.max_stocks,
                                     output_dir=self.output_dir)
        elif self.mode == "live":
            self.trader = LiveTrader(self.session, self.collector, 
                                    initial_balance=self.initial_balance,
                                    max_stocks=self.max_stocks,
                                    output_dir=self.output_dir)
        
        logger.info(f"取引システム初期化完了: モード={self.mode}, 最大銘柄数={self.max_stocks}")
    
    def start(self) -> None:
        """
        取引システムの開始
        """
        if self.running:
            logger.warning("取引システムは既に実行中です")
            return
        
        self.running = True
        
        # 現在の時刻から次の取引サイクル開始時刻を計算
        now = datetime.now()
        next_hour = (now.hour + 1) % 24
        next_cycle = now.replace(hour=next_hour, minute=0, second=0, microsecond=0)
        if next_hour <= now.hour:  # 翌日の場合
            next_cycle += timedelta(days=1)
            
        # 銘柄メタ情報の事前取得
        logger.info("銘柄メタ情報を取得中...")
        self.collector.collect_all_symbol_meta()
        
        # WebSocketストリームの設定
        logger.info("WebSocketストリームを設定中...")
        self.collector.setup_websocket_stream()
        
        # スケジューラの設定
        schedule.every().day.at("09:00").do(self.start_trading_day)  # 取引日開始
        schedule.every().day.at("15:00").do(self.end_trading_day)    # 取引日終了
        
        # 1時間ごとの取引サイクル
        for hour in range(9, 15):
            schedule.every().day.at(f"{hour:02}:00").do(self.run_trading_cycle)
        
        # スケジューラスレッドの開始
        self.scheduler_thread = threading.Thread(target=self._scheduler_worker, daemon=True)
        self.scheduler_thread.start()
        
        # 最初のサイクルを即時実行（オプション）
        wait_seconds = (next_cycle - now).total_seconds()
        logger.info(f"取引システムを開始しました。次のサイクルまで {wait_seconds:.0f} 秒待機します。")
        
        # 市場時間内なら即時実行
        current_hour = now.hour
        if 9 <= current_hour < 15:
            logger.info("市場時間内のため、最初のサイクルを即時実行します。")
            self.run_trading_cycle()
    
    def stop(self) -> None:
        """
        取引システムの停止
        """
        if not self.running:
            logger.warning("取引システムは実行されていません")
            return
        
        self.running = False
        
        # WebSocketストリームの停止
        self.collector.stop_websocket()
        
        # スケジューラスレッドの停止
        if self.scheduler_thread and self.scheduler_thread.is_alive():
            self.scheduler_thread.join(timeout=5)
        
        # 最終レポートの生成
        self.trader.generate_report(period="daily")
        
        logger.info("取引システムを停止しました")
    
    def _scheduler_worker(self) -> None:
        """
        スケジューラスレッド
        """
        while self.running:
            schedule.run_pending()
            time.sleep(1)
    
    def start_trading_day(self) -> None:
        """
        取引日の開始処理
        """
        logger.info("取引日を開始します")
        
        # WebSocketストリームの再開
        if not self.collector.ws_running:
            self.collector.setup_websocket_stream()
        
        # 銘柄メタ情報の更新
        self.collector.collect_all_symbol_meta()
        
        # 初期銘柄選択
        self._select_initial_symbols()
    
    def end_trading_day(self) -> None:
        """
        取引日の終了処理
        """
        logger.info("取引日を終了します")
        
        # 全ポジションの売却（必要に応じて）
        self._close_all_positions()
        
        # 日次レポートの生成
        self.trader.generate_report(period="daily")
        
        # WebSocketストリームの一時停止（必要に応じて）
        self.collector.stop_websocket()
    
    def run_trading_cycle(self) -> None:
        """
        1時間の取引サイクル実行
        """
        cycle_start = datetime.now()
        self.current_cycle += 1
        logger.info(f"取引サイクル {self.current_cycle} を開始します")
        
        try:
            # 1. 前回サイクルのポジションをクローズ
            self._close_positions()
            
            # 2. 次の取引候補銘柄の選択
            selected_symbols = self._select_symbols()
            
            # 3. 銘柄ローテーション処理
            self._rotate_symbols(selected_symbols)
            
            # 4. 銘柄データの収集
            board_data = self.collector.collect_board_data(self.active_symbols)
            
            # 5. 売買判断
            buy_decisions = self._make_buy_decisions(selected_symbols)
            
            # 6. 注文実行
            self._execute_buy_orders(buy_decisions)
            
            # 7. ポートフォリオ情報と取引履歴の保存
            self.trader.save_portfolio()
            self.trader.save_trades()
            
            # サイクル完了ログ
            cycle_time = (datetime.now() - cycle_start).total_seconds()
            logger.info(f"取引サイクル {self.current_cycle} が完了しました (所要時間: {cycle_time:.2f}秒)")
            
        except Exception as e:
            logger.error(f"取引サイクル {self.current_cycle} でエラーが発生しました: {str(e)}", exc_info=True)
    
    def _close_positions(self) -> None:
        """
        保有ポジションのクローズ
        """
        portfolio = self.trader.get_portfolio()
        positions = list(portfolio["positions"].items())
        
        if not positions:
            logger.info("クローズするポジションがありません")
            return
        
        logger.info(f"{len(positions)}件のポジションをクローズします")
        
        for symbol, pos in positions:
            try:
                # 売却注文
                qty = pos["qty"]
                result = self.trader.execute_order(symbol, "sell", qty)
                
                if result:
                    logger.info(f"ポジションをクローズしました: {symbol} {qty}株 @ ¥{result['price']:,.2f}")
                else:
                    logger.warning(f"ポジションのクローズに失敗しました: {symbol}")
            
            except Exception as e:
                logger.error(f"ポジションクローズ中にエラーが発生しました: {symbol} - {str(e)}")
    
    def _close_all_positions(self) -> None:
        """
        全ポジションのクローズ（取引日終了時など）
        """
        self._close_positions()  # 現状は通常のクローズと同じ処理
    
    def _select_initial_symbols(self) -> List[str]:
        """
        初期監視銘柄の選択
        
        Returns:
        --------
        List[str]
            選択された銘柄リスト
        """
        # メタ情報から取得可能な全銘柄
        all_symbols = list(self.collector.symbol_meta.keys())
        
        # 最大50銘柄をランダムに選択
        import random
        max_symbols = min(50, len(all_symbols))
        selected = random.sample(all_symbols, max_symbols)
        
        logger.info(f"初期監視銘柄として{len(selected)}銘柄を選択しました")
        
        # アクティブ銘柄の更新
        self.active_symbols = selected
        
        # 銘柄登録
        self.collector.rotate_symbols(selected)
        
        return selected
    
    def _select_symbols(self) -> List[str]:
        """
        取引候補銘柄の選択
        
        Returns:
        --------
        List[str]
            選択された取引候補銘柄リスト
        """
        # 簡易実装: アクティブ銘柄から最大5銘柄をランダムに選択
        # 実際の実装では、AIモデルによる予測や特徴量エンジニアリングを行い、
        # 最も期待リターンの高い銘柄を選択する必要があります
        import random
        
        # 現在のアクティブ銘柄から選択
        candidates = self.active_symbols.copy()
        
        # ポートフォリオ内の銘柄を除外（既に保有中の銘柄は選択しない）
        portfolio = self.trader.get_portfolio()
        existing_positions = set(portfolio["positions"].keys())
        candidates = [s for s in candidates if s not in existing_positions]
        
        # 最大銘柄数を考慮
        max_to_select = min(self.max_stocks, len(candidates))
        
        if max_to_select == 0:
            logger.info("選択可能な銘柄がありません")
            return []
        
        # ランダム選択（実際のシステムではAIモデルによる予測に置き換え）
        selected = random.sample(candidates, max_to_select)
        
        logger.info(f"取引候補として{len(selected)}銘柄を選択しました: {', '.join(selected)}")
        return selected
    
    def _rotate_symbols(self, new_symbols: List[str]) -> None:
        """
        監視銘柄のローテーション
        
        Parameters:
        -----------
        new_symbols : List[str]
            新しく追加する銘柄リスト
        """
        # 現在のポートフォリオ内の銘柄を優先
        portfolio = self.trader.get_portfolio()
        portfolio_symbols = list(portfolio["positions"].keys())
        
        # 新しいアクティブ銘柄リストの作成
        new_active = portfolio_symbols.copy()
        
        # 取引候補銘柄を追加
        for symbol in new_symbols:
            if symbol not in new_active:
                new_active.append(symbol)
        
        # 50銘柄制限を考慮
        if len(new_active) > 50:
            # ポートフォリオ銘柄を優先し、残りは新しい候補から選択
            overflow = len(new_active) - 50
            # ポートフォリオ銘柄は残す
            candidates_only = [s for s in new_active if s not in portfolio_symbols]
            # 候補から削除する銘柄を選択
            to_remove = candidates_only[:overflow]
            new_active = [s for s in new_active if s not in to_remove]
        
        # 更新が必要な場合のみローテーション
        if set(new_active) != set(self.active_symbols):
            logger.info(f"監視銘柄をローテーションします: {len(new_active)}銘柄")
            self.active_symbols = new_active
            self.collector.rotate_symbols(new_active)
        else:
            logger.info("監視銘柄の更新は不要です")
    
    def _make_buy_decisions(self, candidate_symbols: List[str]) -> List[Dict]:
        """
        買い注文の判断
        
        Parameters:
        -----------
        candidate_symbols : List[str]
            取引候補銘柄リスト
            
        Returns:
        --------
        List[Dict]
            買い注文の詳細リスト
        """
        decisions = []
        
        # ポートフォリオ情報の取得
        portfolio = self.trader.get_portfolio()
        available_cash = portfolio["cash"]
        
        # 既存ポジションの銘柄は除外
        existing_positions = set(portfolio["positions"].keys())
        candidates = [s for s in candidate_symbols if s not in existing_positions]
        
        if not candidates:
            logger.info("買い注文の判断: 候補銘柄がありません")
            return decisions
        
        # 利用可能な資金がない場合
        if available_cash <= 0:
            logger.info("買い注文の判断: 利用可能な資金がありません")
            return decisions
        
        # 各銘柄の配分額を計算（均等配分）
        alloc_per_symbol = available_cash / len(candidates)
        
        for symbol in candidates:
            try:
                # 銘柄情報取得
                board_data = self.collector.get_latest_board_data(symbol)
                if not board_data or "AskPrice" not in board_data or not board_data["AskPrice"]:
                    logger.warning(f"買い注文の判断: 板情報が取得できません: {symbol}")
                    continue
                
                # 現在価格
                price = float(board_data["AskPrice"])
                
                # 購入数量の計算
                qty = int(alloc_per_symbol / price)
                
                # 最低数量チェック
                if qty <= 0:
                    logger.warning(f"買い注文の判断: 資金不足のため購入見送り: {symbol}, 価格={price:,.2f}, 配分額={alloc_per_symbol:,.0f}")
                    continue
                
                # 注文情報の作成
                decision = {
                    "symbol": symbol,
                    "side": "buy",
                    "qty": qty,
                    "price": price,
                    "order_type": "market"
                }
                
                decisions.append(decision)
                logger.info(f"買い注文判断: {symbol}, {qty}株, 価格={price:,.2f}, 金額={qty*price:,.0f}")
                
            except Exception as e:
                logger.error(f"買い注文判断中にエラーが発生しました: {symbol} - {str(e)}")
        
        return decisions
    
    def _execute_buy_orders(self, decisions: List[Dict]) -> None:
        """
        買い注文の実行
        
        Parameters:
        -----------
        decisions : List[Dict]
            買い注文の詳細リスト
        """
        if not decisions:
            logger.info("実行する買い注文がありません")
            return
        
        logger.info(f"{len(decisions)}件の買い注文を実行します")
        
        for decision in decisions:
            try:
                symbol = decision["symbol"]
                qty = decision["qty"]
                
                # 注文実行
                result = self.trader.execute_order(
                    symbol=symbol,
                    side="buy",
                    qty=qty,
                    order_type=decision["order_type"],
                    price=decision.get("price")
                )
                
                if result:
                    logger.info(f"買い注文を実行しました: {symbol}, {qty}株, 価格={result['price']:,.2f}, 金額={result['amount']:,.0f}")
                else:
                    logger.warning(f"買い注文の実行に失敗しました: {symbol}")
                
            except Exception as e:
                logger.error(f"買い注文実行中にエラーが発生しました: {decision['symbol']} - {str(e)}")
    
    def generate_report(self, period: str = "daily") -> str:
        """
        レポート生成
        
        Parameters:
        -----------
        period : str
            レポート期間 ("daily", "weekly", "monthly")
            
        Returns:
        --------
        str
            レポートファイルパス
        """
        return self.trader.generate_report(period=period)
