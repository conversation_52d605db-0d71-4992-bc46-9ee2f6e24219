#!/usr/bin/env python3
import os
import csv
import json
import logging
import pandas as pd
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional, Tuple, Set
from abc import ABC, abstractmethod

# 自作モジュール
from src.trading.kabu_api import KabuSession
from src.trading.kabu_collector import KabuDataCollector

# ロガー設定
logger = logging.getLogger(__name__)

class BaseTrader(ABC):
    """
    取引基本クラス - 全モード共通のインターフェース
    No-Cash/Paper/Liveの基底クラス
    """
    def __init__(self, session: KabuSession, collector: KabuDataCollector, 
                initial_balance: int = 1000000, max_stocks: int = 5,
                output_dir: str = "results/kabu"):
        """
        初期化
        
        Parameters:
        -----------
        session : KabuSession
            KabuSessionインスタンス
        collector : KabuDataCollector
            KabuDataCollectorインスタンス
        initial_balance : int
            初期資金
        max_stocks : int
            最大保有銘柄数
        output_dir : str
            結果出力ディレクトリ
        """
        self.session = session
        self.collector = collector
        self.initial_balance = initial_balance
        self.max_stocks = max_stocks
        self.output_dir = output_dir
        
        # 取引結果ディレクトリ
        self.trades_dir = os.path.join(output_dir, "trades")
        self.reports_dir = os.path.join(output_dir, "reports")
        
        # ディレクトリ作成
        for dir_path in [self.output_dir, self.trades_dir, self.reports_dir]:
            os.makedirs(dir_path, exist_ok=True)
        
        # ポートフォリオ初期化
        self.portfolio = {
            "cash": initial_balance,
            "positions": {},  # {symbol: {"qty": 数量, "avg_price": 平均取得単価, "current_price": 現在価格}}
            "total_value": initial_balance
        }
        
        # 取引履歴
        self.trades = []
        
        # 現在のセッション情報
        self.session_id = datetime.now().strftime("%Y%m%d_%H%M%S")
        self.start_time = datetime.now()
        
        logger.info(f"トレーダー初期化完了: モード={self.__class__.__name__}, 初期資金={initial_balance}円")
    
    def update_portfolio_value(self) -> float:
        """
        ポートフォリオ価値の更新
        
        Returns:
        --------
        float
            ポートフォリオ総価値
        """
        total = self.portfolio["cash"]
        
        # 各ポジションの現在価値を計算
        for symbol, position in list(self.portfolio["positions"].items()):
            try:
                # 銘柄情報取得
                board_data = self.collector.get_latest_board_data(symbol)
                
                if board_data and "BidPrice" in board_data and board_data["BidPrice"]:
                    # 現在価格を更新
                    current_price = float(board_data["BidPrice"])
                    position["current_price"] = current_price
                    position["value"] = position["qty"] * current_price
                    total += position["value"]
                else:
                    # 板情報が取得できない場合は最後の価格を使用
                    total += position["qty"] * position["current_price"]
            except Exception as e:
                logger.error(f"ポートフォリオ価値更新エラー: {symbol} - {str(e)}")
                # エラー時は最後の価格を使用
                total += position["qty"] * position["current_price"]
        
        # ポートフォリオ総価値を更新
        self.portfolio["total_value"] = total
        return total
    
    def get_portfolio(self) -> Dict:
        """
        ポートフォリオ情報取得
        
        Returns:
        --------
        Dict
            ポートフォリオ情報
        """
        # 最新の価値に更新
        self.update_portfolio_value()
        return self.portfolio
    
    def get_trades(self) -> List[Dict]:
        """
        取引履歴取得
        
        Returns:
        --------
        List[Dict]
            取引履歴リスト
        """
        return self.trades
    
    @abstractmethod
    def execute_order(self, symbol: str, side: str, qty: int, order_type: str = "market", 
                    price: Optional[float] = None) -> Optional[Dict]:
        """
        注文実行（派生クラスで実装）
        
        Parameters:
        -----------
        symbol : str
            銘柄コード
        side : str
            売買区分 ("buy" or "sell")
        qty : int
            数量
        order_type : str
            注文タイプ ("market" or "limit")
        price : Optional[float]
            指値価格（指値注文の場合）
            
        Returns:
        --------
        Optional[Dict]
            注文結果
        """
        pass
    
    def save_trades(self) -> str:
        """
        取引履歴の保存
        
        Returns:
        --------
        str
            保存したファイルパス
        """
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        file_path = os.path.join(self.trades_dir, f"trades_{timestamp}.csv")
        
        # CSVに保存
        with open(file_path, 'w', newline='', encoding='utf-8') as f:
            writer = csv.writer(f)
            writer.writerow([
                "Timestamp", "Symbol", "Side", "Quantity", 
                "Price", "Amount", "OrderType", "Status"
            ])
            for trade in self.trades:
                writer.writerow([
                    trade.get("timestamp", ""),
                    trade.get("symbol", ""),
                    trade.get("side", ""),
                    trade.get("qty", 0),
                    trade.get("price", 0),
                    trade.get("amount", 0),
                    trade.get("order_type", "market"),
                    trade.get("status", "")
                ])
        
        logger.info(f"取引履歴を保存しました: {file_path}")
        return file_path
    
    def save_portfolio(self) -> str:
        """
        ポートフォリオ情報の保存
        
        Returns:
        --------
        str
            保存したファイルパス
        """
        # 最新の価値に更新
        self.update_portfolio_value()
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        file_path = os.path.join(self.output_dir, f"portfolio_{timestamp}.json")
        
        # JSONに保存
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(self.portfolio, f, ensure_ascii=False, indent=2)
        
        logger.info(f"ポートフォリオ情報を保存しました: {file_path}")
        return file_path
    
    def generate_report(self, period: str = "daily") -> str:
        """
        レポート生成
        
        Parameters:
        -----------
        period : str
            レポート期間 ("daily", "weekly", "monthly")
            
        Returns:
        --------
        str
            レポートファイルパス
        """
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        md_file = os.path.join(self.reports_dir, f"report_{period}_{timestamp}.md")
        
        # 最新の価値に更新
        self.update_portfolio_value()
        
        # セッション時間の計算
        elapsed = datetime.now() - self.start_time
        elapsed_hours = elapsed.total_seconds() / 3600
        
        # 損益の計算
        profit = self.portfolio["total_value"] - self.initial_balance
        profit_pct = (profit / self.initial_balance) * 100
        
        # 売買統計
        buys = [t for t in self.trades if t["side"] == "buy"]
        sells = [t for t in self.trades if t["side"] == "sell"]
        total_buy_amount = sum(t["amount"] for t in buys)
        total_sell_amount = sum(t["amount"] for t in sells)
        
        # Markdownレポート生成
        with open(md_file, 'w', encoding='utf-8') as f:
            # ヘッダー
            f.write(f"# {period.capitalize()}取引レポート\n\n")
            f.write(f"**生成日時**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"**取引モード**: {self.__class__.__name__}\n")
            f.write(f"**取引期間**: {self.start_time.strftime('%Y-%m-%d %H:%M:%S')} ～ {datetime.now().strftime('%Y-%m-%d %H:%M:%S')} ({elapsed_hours:.2f}時間)\n\n")
            
            # ポートフォリオサマリー
            f.write("## ポートフォリオサマリー\n\n")
            f.write(f"- **初期資金**: ¥{self.initial_balance:,}\n")
            f.write(f"- **現金残高**: ¥{self.portfolio['cash']:,}\n")
            f.write(f"- **ポジション価値**: ¥{(self.portfolio['total_value'] - self.portfolio['cash']):,}\n")
            f.write(f"- **ポートフォリオ総価値**: ¥{self.portfolio['total_value']:,}\n")
            f.write(f"- **損益**: ¥{profit:,} ({profit_pct:.2f}%)\n\n")
            
            # 取引統計
            f.write("## 取引統計\n\n")
            f.write(f"- **総取引数**: {len(self.trades)}\n")
            f.write(f"- **買付取引**: {len(buys)}件 (¥{total_buy_amount:,})\n")
            f.write(f"- **売却取引**: {len(sells)}件 (¥{total_sell_amount:,})\n\n")
            
            # 保有ポジション
            f.write("## 保有ポジション\n\n")
            if not self.portfolio["positions"]:
                f.write("*保有ポジションはありません*\n\n")
            else:
                f.write("| 銘柄 | 数量 | 平均取得価格 | 現在価格 | 評価額 | 損益 | 損益率 |\n")
                f.write("|------|------|------------|---------|-------|------|-------|\n")
                
                for symbol, pos in self.portfolio["positions"].items():
                    avg_price = pos["avg_price"]
                    current_price = pos["current_price"]
                    qty = pos["qty"]
                    value = qty * current_price
                    pos_profit = qty * (current_price - avg_price)
                    pos_profit_pct = (current_price / avg_price - 1) * 100
                    
                    f.write(f"| {symbol} | {qty} | ¥{avg_price:,.2f} | ¥{current_price:,.2f} | ")
                    f.write(f"¥{value:,.0f} | ¥{pos_profit:,.0f} | {pos_profit_pct:+.2f}% |\n")
                f.write("\n")
            
            # 最近の取引
            f.write("## 最近の取引\n\n")
            if not self.trades:
                f.write("*取引履歴はありません*\n\n")
            else:
                f.write("| 日時 | 銘柄 | 売買 | 数量 | 価格 | 金額 | 状態 |\n")
                f.write("|------|------|------|------|------|------|------|\n")
                
                # 最新10件
                for trade in self.trades[-10:]:
                    f.write(f"| {trade['timestamp']} | {trade['symbol']} | ")
                    f.write(f"{'買' if trade['side'] == 'buy' else '売'} | {trade['qty']} | ")
                    f.write(f"¥{trade['price']:,.2f} | ¥{trade['amount']:,.0f} | {trade['status']} |\n")
                f.write("\n")
            
            # 添付ファイル情報
            f.write("## 添付ファイル\n\n")
            trades_file = self.save_trades()
            portfolio_file = self.save_portfolio()
            
            f.write(f"- [取引履歴CSV]({os.path.basename(trades_file)})\n")
            f.write(f"- [ポートフォリオJSON]({os.path.basename(portfolio_file)})\n")
        
        logger.info(f"{period}レポートを生成しました: {md_file}")
        return md_file


class NoCashTrader(BaseTrader):
    """
    No-Cashモード：売買リクエストを一切発行せず、精度評価のみ
    """
    def execute_order(self, symbol: str, side: str, qty: int, order_type: str = "market", 
                    price: Optional[float] = None) -> Optional[Dict]:
        """
        注文実行（シミュレーションのみ、注文は発行しない）
        
        Parameters:
        -----------
        symbol : str
            銘柄コード
        side : str
            売買区分 ("buy" or "sell")
        qty : int
            数量
        order_type : str
            注文タイプ ("market" or "limit")
        price : Optional[float]
            指値価格（指値注文の場合）
            
        Returns:
        --------
        Optional[Dict]
            シミュレーション結果
        """
        # 現在の板情報から価格を取得
        if price is None:
            board_data = self.collector.get_latest_board_data(symbol)
            if not board_data:
                logger.warning(f"No-Cash: 板情報が取得できません: {symbol}")
                return None
                
            if side == "buy":
                # 買い注文の場合はAsk価格（売り手の提示価格）
                price = float(board_data.get("AskPrice", 0))
            else:
                # 売り注文の場合はBid価格（買い手の提示価格）
                price = float(board_data.get("BidPrice", 0))
                
            if price <= 0:
                logger.warning(f"No-Cash: 有効な価格が取得できません: {symbol}, {side}")
                return None
        
        # 取引記録の作成
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        amount = price * qty
        
        trade = {
            "timestamp": timestamp,
            "symbol": symbol,
            "side": side,
            "qty": qty,
            "price": price,
            "amount": amount,
            "order_type": order_type,
            "status": "simulated"  # シミュレーションのみ
        }
        
        self.trades.append(trade)
        logger.info(f"No-Cash: {side} {qty} {symbol} @ ¥{price:,.2f} = ¥{amount:,.0f} (シミュレーションのみ)")
        
        return trade


class PaperTrader(BaseTrader):
    """
    Paperモード：注文エンドポイント呼び出しをモックに置換
    """
    def execute_order(self, symbol: str, side: str, qty: int, order_type: str = "market", 
                    price: Optional[float] = None) -> Optional[Dict]:
        """
        注文実行（仮想取引、実際の注文は発行しない）
        
        Parameters:
        -----------
        symbol : str
            銘柄コード
        side : str
            売買区分 ("buy" or "sell")
        qty : int
            数量
        order_type : str
            注文タイプ ("market" or "limit")
        price : Optional[float]
            指値価格（指値注文の場合）
            
        Returns:
        --------
        Optional[Dict]
            仮想取引結果
        """
        # 現在の板情報から価格を取得
        if price is None:
            board_data = self.collector.get_latest_board_data(symbol)
            if not board_data:
                logger.warning(f"Paper: 板情報が取得できません: {symbol}")
                return None
                
            if side == "buy":
                # 買い注文の場合はAsk価格（売り手の提示価格）
                price = float(board_data.get("AskPrice", 0))
            else:
                # 売り注文の場合はBid価格（買い手の提示価格）
                price = float(board_data.get("BidPrice", 0))
                
            if price <= 0:
                logger.warning(f"Paper: 有効な価格が取得できません: {symbol}, {side}")
                return None
        
        # 取引記録の作成
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        amount = price * qty
        
        # 仮想取引の実行
        if side == "buy":
            # 買い注文の場合は資金チェック
            if self.portfolio["cash"] < amount:
                logger.warning(f"Paper: 資金不足: 必要={amount:,.0f}, 残高={self.portfolio['cash']:,.0f}")
                return None
                
            # 資金を減らしポジションを追加/更新
            self.portfolio["cash"] -= amount
            
            if symbol in self.portfolio["positions"]:
                # 既存ポジションの更新
                pos = self.portfolio["positions"][symbol]
                total_qty = pos["qty"] + qty
                total_cost = (pos["qty"] * pos["avg_price"]) + amount
                
                pos["qty"] = total_qty
                pos["avg_price"] = total_cost / total_qty
                pos["current_price"] = price
                pos["value"] = total_qty * price
            else:
                # 新規ポジション作成
                self.portfolio["positions"][symbol] = {
                    "qty": qty,
                    "avg_price": price,
                    "current_price": price,
                    "value": amount
                }
                
            trade_status = "filled"
                
        elif side == "sell":
            # 売り注文の場合はポジションチェック
            if symbol not in self.portfolio["positions"]:
                logger.warning(f"Paper: ポジションなし: {symbol}")
                return None
                
            pos = self.portfolio["positions"][symbol]
            if pos["qty"] < qty:
                logger.warning(f"Paper: 数量不足: 必要={qty}, 保有={pos['qty']}")
                return None
                
            # 資金を増やしポジションを減らす/削除
            self.portfolio["cash"] += amount
            
            if pos["qty"] == qty:
                # ポジション全体を売却
                del self.portfolio["positions"][symbol]
            else:
                # ポジションの一部を売却
                pos["qty"] -= qty
                pos["value"] = pos["qty"] * price
                pos["current_price"] = price  # 現在価格を更新
                
            trade_status = "filled"
        else:
            logger.warning(f"Paper: 不明な売買区分: {side}")
            return None
        
        # 取引記録
        trade = {
            "timestamp": timestamp,
            "symbol": symbol,
            "side": side,
            "qty": qty,
            "price": price,
            "amount": amount,
            "order_type": order_type,
            "status": trade_status
        }
        
        self.trades.append(trade)
        logger.info(f"Paper: {side} {qty} {symbol} @ ¥{price:,.2f} = ¥{amount:,.0f}")
        
        # ポートフォリオ価値の更新
        self.update_portfolio_value()
        
        return trade


class LiveTrader(BaseTrader):
    """
    Liveモード：/sendorder/* 系をそのまま実発注
    """
    def execute_order(self, symbol: str, side: str, qty: int, order_type: str = "market", 
                    price: Optional[float] = None) -> Optional[Dict]:
        """
        注文実行（実取引）
        
        Parameters:
        -----------
        symbol : str
            銘柄コード
        side : str
            売買区分 ("buy" or "sell")
        qty : int
            数量
        order_type : str
            注文タイプ ("market" or "limit")
        price : Optional[float]
            指値価格（指値注文の場合）
            
        Returns:
        --------
        Optional[Dict]
            注文結果
        """
        # 現在の板情報から価格を取得（指値の場合や記録用）
        board_data = self.collector.get_latest_board_data(symbol)
        if not board_data:
            logger.warning(f"Live: 板情報が取得できません: {symbol}")
            return None
            
        if price is None and order_type == "limit":
            if side == "buy":
                # 買い注文の場合はAsk価格に少し足した価格を指値に
                ask_price = float(board_data.get("AskPrice", 0))
                if ask_price <= 0:
                    logger.warning(f"Live: 有効な価格が取得できません: {symbol}, {side}")
                    return None
                price = ask_price * 1.001  # 0.1%高い価格で指値
            else:
                # 売り注文の場合はBid価格から少し引いた価格を指値に
                bid_price = float(board_data.get("BidPrice", 0))
                if bid_price <= 0:
                    logger.warning(f"Live: 有効な価格が取得できません: {symbol}, {side}")
                    return None
                price = bid_price * 0.999  # 0.1%低い価格で指値
        
        # 現在価格（記録用）
        current_price = 0
        if side == "buy":
            current_price = float(board_data.get("AskPrice", 0))
        else:
            current_price = float(board_data.get("BidPrice", 0))
        
        if current_price <= 0:
            logger.warning(f"Live: 有効な価格が取得できません: {symbol}, {side}")
            return None
        
        # 取引前チェック
        if side == "buy":
            # 買い注文の場合は資金チェック
            estimated_amount = current_price * qty
            if self.portfolio["cash"] < estimated_amount:
                logger.warning(f"Live: 資金不足: 必要={estimated_amount:,.0f}, 残高={self.portfolio['cash']:,.0f}")
                return None
        elif side == "sell":
            # 売り注文の場合はポジションチェック
            if symbol not in self.portfolio["positions"]:
                logger.warning(f"Live: ポジションなし: {symbol}")
                return None
                
            pos = self.portfolio["positions"][symbol]
            if pos["qty"] < qty:
                logger.warning(f"Live: 数量不足: 必要={qty}, 保有={pos['qty']}")
                return None
        
        # 実際の注文処理
        try:
            # 銘柄コードからティッカーシンボルを抽出（1301.T → 1301）
            code = symbol.split('.')[0]
            
            # 注文処理（実際のAPIコール）
            order_result = self.session.rest("POST", "/sendorder/stock", json={
                "Symbol": code,
                "Exchange": 1,  # 東証
                "SecurityType": 1,  # 株式
                "Side": "1" if side == "buy" else "2",  # 1:買, 2:売
                "CashMargin": 1,  # 現物
                "DelivType": 2,  # お預り金
                "AccountType": 4,  # 特定
                "Qty": qty,
                "FrontOrderType": 10 if order_type == "market" else 20,  # 10:成行, 20:指値
                "Price": price if order_type == "limit" else 0,
                "ExpireDay": 0  # 当日中
            }).json()
            
            order_id = order_result.get("OrderId", "unknown")
            
            # 注文結果の記録
            timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            amount = current_price * qty  # 概算金額
            
            trade = {
                "timestamp": timestamp,
                "symbol": symbol,
                "side": side,
                "qty": qty,
                "price": current_price,  # 約定前なので概算
                "amount": amount,  # 概算金額
                "order_type": order_type,
                "order_id": order_id,
                "status": "ordered"  # 注文済み
            }
            
            self.trades.append(trade)
            logger.info(f"Live: {side} {qty} {symbol} @ ¥{current_price:,.2f} = ¥{amount:,.0f}, OrderId={order_id}")
            
            # TODO: 注文後の処理（約定確認など）
            # 実際の約定結果を確認するには、別途注文状況を確認する必要があります
            # 簡略化のため、ここではすぐに約定したものとして処理します
            
            # 注文が成功したとみなして、ポートフォリオを更新
            if side == "buy":
                # 資金を減らしポジションを追加/更新
                self.portfolio["cash"] -= amount
                
                if symbol in self.portfolio["positions"]:
                    # 既存ポジションの更新
                    pos = self.portfolio["positions"][symbol]
                    total_qty = pos["qty"] + qty
                    total_cost = (pos["qty"] * pos["avg_price"]) + amount
                    
                    pos["qty"] = total_qty
                    pos["avg_price"] = total_cost / total_qty
                    pos["current_price"] = current_price
                    pos["value"] = total_qty * current_price
                else:
                    # 新規ポジション作成
                    self.portfolio["positions"][symbol] = {
                        "qty": qty,
                        "avg_price": current_price,
                        "current_price": current_price,
                        "value": amount
                    }
            elif side == "sell":
                # 資金を増やしポジションを減らす/削除
                self.portfolio["cash"] += amount
                
                pos = self.portfolio["positions"][symbol]
                if pos["qty"] == qty:
                    # ポジション全体を売却
                    del self.portfolio["positions"][symbol]
                else:
                    # ポジションの一部を売却
                    pos["qty"] -= qty
                    pos["value"] = pos["qty"] * current_price
                    pos["current_price"] = current_price  # 現在価格を更新
            
            # ポートフォリオ価値の更新
            self.update_portfolio_value()
            
            # 取引状態を更新
            trade["status"] = "filled"  # 約定済みに更新
            
            return trade
            
        except Exception as e:
            logger.error(f"Live: 注文エラー: {symbol} {side} {qty} - {str(e)}")
            return None
