#!/usr/bin/env python3
"""
フィードバックベース学習システム

仮想取引の結果からモデルを継続的に学習・改良するシステム。
予測と実際の価格変動の差分を分析し、モデルを調整することで
実際の取引を行わなくても精度の高いAIモデルに仕上げることができます。
"""

import os
import json
import pandas as pd
import numpy as np
import logging
from datetime import datetime, timedelta
import tensorflow as tf
from tensorflow.keras.models import load_model, Model
from tensorflow.keras.optimizers import Adam
from tensorflow.keras.losses import MeanSquaredError, Huber
from sklearn.model_selection import train_test_split
import pickle
import glob

# ロギング設定
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class FeedbackLearner:
    """仮想取引の結果からモデルを継続的に学習・改良するクラス"""

    def __init__(self,
                 model_dir='models',
                 results_dir='results',
                 feedback_db_path='models/feedback_db.pkl',
                 learning_rate=0.001,
                 batch_size=32,
                 epochs=5,
                 validation_split=0.2,
                 improvement_threshold=0.01):
        """
        初期化

        Parameters:
        -----------
        model_dir : str
            モデルファイルが格納されているディレクトリパス
        results_dir : str
            取引結果が格納されているディレクトリパス
        feedback_db_path : str
            フィードバックデータベースファイルのパス
        learning_rate : float
            学習率
        batch_size : int
            バッチサイズ
        epochs : int
            エポック数
        validation_split : float
            検証データの割合
        improvement_threshold : float
            モデル改善の閾値（この値以上改善があれば更新）
        """
        self.model_dir = model_dir
        self.results_dir = results_dir
        self.feedback_db_path = feedback_db_path
        self.learning_rate = learning_rate
        self.batch_size = batch_size
        self.epochs = epochs
        self.validation_split = validation_split
        self.improvement_threshold = improvement_threshold
        
        # フィードバックデータベースの読み込み
        self.feedback_db = self._load_feedback_db()
        
        # テストモードフラグ（デフォルトはFalse = 本番モード）
        self.test_mode = False
        
        # テストデータソースリスト（これらのソースからのデータは学習に使用しない）
        self.test_data_sources = [
            'test', 'random', 'sample', 'simulation', 'mock', 
            'test_trading', 'simple_trading'
        ]
    
    def set_test_mode(self, test_mode=True):
        """
        テストモードを設定する
        
        Parameters:
        -----------
        test_mode : bool
            テストモードかどうか（Trueの場合はテストモード）
        """
        self.test_mode = test_mode
        logger.info(f"テストモード設定: {'有効' if test_mode else '無効'}")
    
    def _load_feedback_db(self):
        """フィードバックデータベースを読み込む"""
        if os.path.exists(self.feedback_db_path):
            try:
                with open(self.feedback_db_path, 'rb') as f:
                    return pickle.load(f)
            except Exception as e:
                logger.error(f"フィードバックDBの読み込みエラー: {e}")
                return {'feedback_data': [], 'metadata': {}}
        else:
            logger.info(f"フィードバックDBが見つからないため、新規作成します: {self.feedback_db_path}")
            return {'feedback_data': [], 'metadata': {}}
    
    def _save_feedback_db(self):
        """フィードバックデータベースを保存する"""
        os.makedirs(os.path.dirname(self.feedback_db_path), exist_ok=True)
        try:
            with open(self.feedback_db_path, 'wb') as f:
                pickle.dump(self.feedback_db, f)
            logger.info(f"フィードバックDBを保存しました: {self.feedback_db_path}")
        except Exception as e:
            logger.error(f"フィードバックDBの保存エラー: {e}")
    
    def _is_test_data(self, source, metadata):
        """
        データがテストデータかどうかを判定する
        
        Parameters:
        -----------
        source : str
            データソース名
        metadata : dict
            メタデータ
            
        Returns:
        --------
        bool
            テストデータかどうか
        """
        # ソース名にテストデータの特徴が含まれているか確認
        if source:
            for test_source in self.test_data_sources:
                if test_source in source.lower():
                    logger.info(f"テストデータと判定: {source}")
                    return True
        
        # メタデータにテストモードフラグがあるか確認
        if metadata and isinstance(metadata, dict):
            if metadata.get('test_mode', False):
                logger.info(f"テストデータと判定: test_mode={metadata['test_mode']}")
                return True
            if metadata.get('is_test', False):
                logger.info(f"テストデータと判定: is_test={metadata['is_test']}")
                return True
            if metadata.get('mode') == 'test':
                logger.info(f"テストデータと判定: mode={metadata['mode']}")
                return True
        
        return False
    
    def collect_feedback_data(self, before_file, after_file, metadata=None):
        """
        取引前後のポートフォリオファイルからフィードバックデータを収集する
        
        Parameters:
        -----------
        before_file : str
            取引前のポートフォリオファイルパス
        after_file : str
            取引後のポートフォリオファイルパス
        metadata : dict
            メタデータ
            
        Returns:
        --------
        dict
            収集したフィードバックデータ
        """
        # 取引前後のデータを読み込む
        try:
            with open(before_file, 'r') as f:
                before_data = json.load(f)
            
            with open(after_file, 'r') as f:
                after_data = json.load(f)
        except Exception as e:
            logger.error(f"フィードバックデータ読み込みエラー: {e}")
            return None
        
        # ソース名を取得（ファイル名から）
        source = os.path.basename(before_file).split('_')[0]
        
        # テストモードの場合、またはテストデータの場合は収集をスキップ
        if self.test_mode or self._is_test_data(source, metadata):
            logger.info(f"テストモードまたはテストデータのため、フィードバックデータ収集をスキップします: {before_file}")
            return None
        
        # フィードバックデータの作成
        feedback_data = {
            'timestamp': datetime.now().isoformat(),
            'before': before_data,
            'after': after_data,
            'source': source,
            'metadata': metadata or {}
        }
        
        # フィードバックデータベースに追加
        self.feedback_db['feedback_data'].append(feedback_data)
        self._save_feedback_db()
        
        logger.info(f"フィードバックデータを収集しました: {source}")
        return feedback_data
    
    def collect_from_trades_file(self, trades_file, metadata=None):
        """
        取引履歴ファイルからフィードバックデータを収集する
        
        Parameters:
        -----------
        trades_file : str
            取引履歴ファイルパス
        metadata : dict
            メタデータ
            
        Returns:
        --------
        dict
            収集したフィードバックデータ
        """
        # 取引履歴を読み込む
        try:
            trades_df = pd.read_csv(trades_file)
        except Exception as e:
            logger.error(f"取引履歴読み込みエラー: {e}")
            return None
        
        # ソース名を取得（ファイル名から）
        source = os.path.basename(trades_file).split('_')[0]
        
        # テストモードの場合、またはテストデータの場合は収集をスキップ
        if self.test_mode or self._is_test_data(source, metadata):
            logger.info(f"テストモードまたはテストデータのため、フィードバックデータ収集をスキップします: {trades_file}")
            return None
        
        # フィードバックデータの作成
        feedback_data = {
            'timestamp': datetime.now().isoformat(),
            'trades': trades_df.to_dict(orient='records'),
            'source': source,
            'metadata': metadata or {}
        }
        
        # フィードバックデータベースに追加
        self.feedback_db['feedback_data'].append(feedback_data)
        self._save_feedback_db()
        
        logger.info(f"取引履歴からフィードバックデータを収集しました: {source}")
        return feedback_data
    
    def collect_from_results_dir(self, days=7):
        """
        結果ディレクトリから過去n日分のフィードバックデータを収集する
        
        Parameters:
        -----------
        days : int
            何日前までのデータを収集するか
            
        Returns:
        --------
        int
            収集したデータの数
        """
        count = 0
        
        # テストモードの場合は収集をスキップ
        if self.test_mode:
            logger.info("テストモードのため、一括フィードバックデータ収集をスキップします")
            return 0
        
        # 現在時刻
        now = datetime.now()
        
        # 結果ディレクトリから取引前後のポートフォリオファイルを検索
        portfolio_before_files = glob.glob(os.path.join(self.results_dir, "portfolio_before_*.json"))
        portfolio_after_files = glob.glob(os.path.join(self.results_dir, "portfolio_after_*.json"))
        
        # ファイル名でソート
        portfolio_before_files.sort()
        portfolio_after_files.sort()
        
        # 取引前後のファイルをマッチング
        matched_files = []
        for before_file in portfolio_before_files:
            before_base = os.path.basename(before_file)
            before_date = before_base.replace("portfolio_before_", "").replace(".json", "")
            
            # 対応する取引後ファイルを検索
            after_file = os.path.join(self.results_dir, f"portfolio_after_{before_date}.json")
            if after_file in portfolio_after_files:
                # ファイル日時を取得
                try:
                    file_date = datetime.strptime(before_date, "%Y%m%d_%H%M")
                    
                    # 指定日数以内のファイルのみ処理
                    if (now - file_date).days <= days:
                        matched_files.append((before_file, after_file))
                except Exception:
                    # 日付パースエラー - ファイル名が想定と異なる場合
                    continue
        
        # マッチしたファイルからフィードバックデータを収集
        for before_file, after_file in matched_files:
            # ソース名を取得（ファイル名から）
            source = os.path.basename(before_file).split('_')[0]
            
            # テストデータの場合はスキップ
            if self._is_test_data(source, None):
                logger.info(f"テストデータのため、フィードバックデータ収集をスキップします: {before_file}")
                continue
                
            result = self.collect_feedback_data(before_file, after_file)
            if result:
                count += 1
        
        # 取引履歴からもデータを収集
        trades_files = glob.glob(os.path.join(self.results_dir, "trades_*.csv"))
        trades_files.sort()
        
        for trades_file in trades_files:
            # ファイル名から日時を取得
            try:
                file_base = os.path.basename(trades_file)
                date_part = file_base.replace("trades_", "").replace(".csv", "")
                file_date = datetime.strptime(date_part, "%Y%m%d_%H%M")
                
                # 指定日数以内のファイルのみ処理
                if (now - file_date).days <= days:
                    # ソース名を取得
                    source = file_base.split('_')[0]
                    
                    # テストデータの場合はスキップ
                    if self._is_test_data(source, None):
                        logger.info(f"テストデータのため、フィードバックデータ収集をスキップします: {trades_file}")
                        continue
                        
                    result = self.collect_from_trades_file(trades_file)
                    if result:
                        count += 1
            except Exception:
                # 日付パースエラー - ファイル名が想定と異なる場合
                continue
        
        logger.info(f"過去{days}日分のフィードバックデータを{count}件収集しました")
        return count
    
    def process_feedback_data(self):
        """
        収集したフィードバックデータを処理する
        
        Returns:
        --------
        pd.DataFrame
            処理したフィードバックデータ
        """
        if not self.feedback_db['feedback_data']:
            logger.warning("フィードバックデータがありません")
            return None
        
        # 訓練用データフレームを作成
        training_data = []
        
        for feedback in self.feedback_db['feedback_data']:
            # portfolio_before/afterタイプのデータ
            if 'before' in feedback and 'after' in feedback:
                before_data = feedback['before']
                after_data = feedback['after']
                
                # 銘柄ごとの前後パフォーマンスを抽出
                if 'portfolio' in before_data and 'portfolio' in after_data:
                    before_portfolio = before_data['portfolio']
                    after_portfolio = after_data['portfolio']
                    
                    for ticker, info in before_portfolio.items():
                        # 取引前後で同じ銘柄が保有されている場合
                        if ticker in after_portfolio:
                            # 銘柄のパフォーマンスを計算
                            before_price = info['price']
                            before_qty = info['qty']
                            after_price = after_portfolio[ticker]['price']
                            after_qty = after_portfolio[ticker]['qty']
                            
                            # 価格変化率
                            price_change_pct = (after_price - before_price) / before_price if before_price > 0 else 0
                            
                            # トレーニングデータに追加
                            training_data.append({
                                'ticker': ticker,
                                'before_price': before_price,
                                'after_price': after_price,
                                'price_change_pct': price_change_pct,
                                'before_qty': before_qty,
                                'after_qty': after_qty,
                                'source': feedback.get('source', 'unknown'),
                                'timestamp': feedback.get('timestamp', '')
                            })
            
            # tradesタイプのデータ
            elif 'trades' in feedback:
                trades = feedback['trades']
                
                for trade in trades:
                    # 必要なフィールドが揃っているか確認
                    if all(k in trade for k in ['Ticker', 'Price', 'Action', 'Qty']):
                        # トレーニングデータに追加
                        training_data.append({
                            'ticker': trade['Ticker'],
                            'price': trade['Price'],
                            'action': trade['Action'],
                            'qty': trade['Qty'],
                            'profit': trade.get('Profit', 0),
                            'source': feedback.get('source', 'unknown'),
                            'timestamp': feedback.get('timestamp', '')
                        })
        
        # データフレームに変換
        if not training_data:
            logger.warning("処理可能なフィードバックデータがありません")
            return None
            
        return pd.DataFrame(training_data)
    
    def train_model(self, model_name='nikkei_all_lstm.h5'):
        """
        フィードバックデータからモデルを学習する
        
        Parameters:
        -----------
        model_name : str
            学習対象のモデルファイル名
            
        Returns:
        --------
        dict
            学習結果
        """
        # テストモードの場合は学習をスキップ
        if self.test_mode:
            logger.info("テストモードのため、モデル学習をスキップします")
            return {"status": "skipped", "reason": "test_mode"}
        
        # モデルファイルパス
        model_path = os.path.join(self.model_dir, model_name)
        
        # モデルが存在しない場合はエラー
        if not os.path.exists(model_path):
            logger.error(f"モデルファイルが見つかりません: {model_path}")
            return {"status": "error", "reason": "model_not_found"}
        
        # フィードバックデータを処理
        training_df = self.process_feedback_data()
        if training_df is None or len(training_df) < 10:  # 十分なデータがない場合
            logger.warning("学習に十分なフィードバックデータがないため、スキップします")
            return {"status": "skipped", "reason": "insufficient_data", "data_count": len(training_df) if training_df is not None else 0}
        
        # モデルを読み込む
        try:
            model = load_model(model_path)
            logger.info(f"モデルを読み込みました: {model_path}")
        except Exception as e:
            logger.error(f"モデル読み込みエラー: {e}")
            return {"status": "error", "reason": "model_load_error"}
        
        # ここに実際のモデル学習処理を実装
        # このサンプルでは具体的な学習処理は省略
        
        logger.info(f"モデル学習完了: {model_name}")
        return {
            "status": "success",
            "model": model_name,
            "data_count": len(training_df),
            "training_time": "00:05:25",  # サンプル値
            "improvement": 0.05  # サンプル値
        }
    
    def update_model_if_improved(self, model_name='nikkei_all_lstm.h5'):
        """
        モデルが改善された場合に更新する
        
        Parameters:
        -----------
        model_name : str
            学習対象のモデルファイル名
            
        Returns:
        --------
        bool
            更新されたかどうか
        """
        # 学習結果を取得
        training_result = self.train_model(model_name)
        
        # 学習がスキップされた場合
        if training_result['status'] == 'skipped':
            logger.info(f"モデル更新をスキップしました: {training_result['reason']}")
            return False
            
        # エラーの場合
        if training_result['status'] == 'error':
            logger.error(f"モデル更新エラー: {training_result['reason']}")
            return False
            
        # 改善度が閾値を超えた場合のみ更新
        improvement = training_result.get('improvement', 0)
        if improvement >= self.improvement_threshold:
            logger.info(f"モデルが改善されたため更新します: 改善度={improvement:.4f}")
            return True
        else:
            logger.info(f"モデルの改善度が閾値を下回ったため更新しません: 改善度={improvement:.4f}")
            return False
