#!/usr/bin/env python3
import os
import time
import logging
import threading
from typing import Dict, Any, Optional, Callable, Union
from datetime import datetime, timedelta
import json

from src.trading.logging_config import get_module_logger

# ロガー設定
logger = get_module_logger(__name__)

class APIHealthMonitor:
    """
    API健全性監視クラス
    - APIサービスの稼働状態を監視
    - 障害の検出と回復処理の実行
    - 健全性メトリクスの記録
    """
    def __init__(self, 
                 health_check_func: Callable[[], bool],
                 recovery_func: Optional[Callable[[], None]] = None,
                 check_interval: int = 60,
                 recovery_interval: int = 30,
                 max_failures: int = 10,
                 log_dir: str = "logs/api_health"):
        """
        初期化
        
        Parameters:
        -----------
        health_check_func : Callable[[], bool]
            API健全性をチェックする関数（Trueなら正常、Falseなら異常）
        recovery_func : Optional[Callable[[], None]]
            APIが回復した際に実行する関数
        check_interval : int
            正常時のチェック間隔（秒）
        recovery_interval : int
            障害検出時のチェック間隔（秒）
        max_failures : int
            完全な障害と判断する連続失敗回数
        log_dir : str
            健全性ログの出力ディレクトリ
        """
        self.health_check_func = health_check_func
        self.recovery_func = recovery_func
        self.check_interval = check_interval
        self.recovery_interval = recovery_interval
        self.max_failures = max_failures
        self.log_dir = log_dir
        
        # モニタリング状態
        self.running = False
        self.monitor_thread = None
        self.is_available = False
        self.consecutive_failures = 0
        self.last_check_time = 0
        self.last_available_time = 0
        self.last_unavailable_time = 0
        self.total_checks = 0
        self.total_failures = 0
        self.recovery_attempts = 0
        self.successful_recoveries = 0
        self.monitoring_start_time = 0
        
        # ログディレクトリ作成
        os.makedirs(log_dir, exist_ok=True)
    
    def start(self) -> bool:
        """
        監視を開始
        
        Returns:
        --------
        bool
            開始に成功したかどうか
        """
        if self.running:
            logger.warning("健全性監視は既に開始されています")
            return False
        
        self.running = True
        self.monitoring_start_time = time.time()
        self.monitor_thread = threading.Thread(
            target=self._monitoring_loop,
            daemon=True
        )
        self.monitor_thread.start()
        
        logger.info("API健全性監視を開始しました")
        return True
    
    def stop(self) -> bool:
        """
        監視を停止
        
        Returns:
        --------
        bool
            停止に成功したかどうか
        """
        if not self.running:
            logger.warning("健全性監視は既に停止しています")
            return False
        
        self.running = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=2.0)
            
        # 最終状態のログ記録
        self._log_health_status()
        
        logger.info("API健全性監視を停止しました")
        return True
    
    def _monitoring_loop(self) -> None:
        """
        監視ループの実装
        """
        while self.running:
            try:
                # 健全性チェック実行
                self._check_health()
                
                # 次のチェックまでの間隔を決定（障害中は短い間隔でチェック）
                interval = self.recovery_interval if not self.is_available else self.check_interval
                
                # 次のチェックまで待機
                for _ in range(int(interval / 0.5)):
                    if not self.running:
                        break
                    time.sleep(0.5)
                    
            except Exception as e:
                logger.error(f"健全性監視中にエラーが発生: {str(e)}")
                time.sleep(self.recovery_interval)
    
    def _check_health(self) -> None:
        """
        健全性チェックを実行し、状態を更新
        """
        self.last_check_time = time.time()
        self.total_checks += 1
        
        is_healthy = False
        try:
            # ヘルスチェック関数を実行
            is_healthy = self.health_check_func()
        except Exception as e:
            logger.error(f"ヘルスチェック関数実行エラー: {str(e)}")
            is_healthy = False
        
        # 状態の更新
        prev_state = self.is_available
        
        if is_healthy:
            # 正常時の処理
            if self.consecutive_failures > 0:
                logger.info(f"API健全性が回復しました（連続失敗: {self.consecutive_failures}回）")
            
            self.is_available = True
            self.consecutive_failures = 0
            self.last_available_time = self.last_check_time
            
            # 状態が変わった場合（復旧した場合）、回復処理を実行
            if not prev_state and self.recovery_func:
                self.recovery_attempts += 1
                try:
                    self.recovery_func()
                    self.successful_recoveries += 1
                except Exception as e:
                    logger.error(f"API回復処理実行エラー: {str(e)}")
        else:
            # 異常時の処理
            self.consecutive_failures += 1
            self.total_failures += 1
            
            if self.is_available and self.consecutive_failures >= self.max_failures:
                # 利用不可状態に遷移
                self.is_available = False
                self.last_unavailable_time = self.last_check_time
                logger.warning(f"APIが利用できません（連続失敗: {self.consecutive_failures}回）")
            elif self.consecutive_failures == 1 or self.consecutive_failures % 5 == 0:
                # 最初の失敗時と、5の倍数回失敗時にログ出力
                severity = "警告" if self.consecutive_failures < self.max_failures else "エラー"
                logger.warning(f"API健全性チェック失敗 ({severity}): 連続{self.consecutive_failures}回")
        
        # 定期的に状態をログに記録
        if self.total_checks % 10 == 0 or prev_state != self.is_available:
            self._log_health_status()
    
    def _log_health_status(self) -> None:
        """
        健全性状態をログに記録
        """
        # 現在の状態の概要
        status = self.get_status()
        
        # ログファイル名（日付ベース）
        date_str = datetime.now().strftime("%Y%m%d")
        log_file = os.path.join(self.log_dir, f"api_health_{date_str}.jsonl")
        
        # 状態をJSONL形式で記録
        try:
            with open(log_file, "a", encoding="utf-8") as f:
                f.write(json.dumps(status) + "\n")
        except Exception as e:
            logger.error(f"健全性ログ記録中にエラー発生: {str(e)}")
    
    def get_status(self) -> Dict[str, Any]:
        """
        現在の健全性状態を取得
        
        Returns:
        --------
        Dict[str, Any]
            健全性状態情報
        """
        now = time.time()
        uptime = now - self.monitoring_start_time if self.monitoring_start_time > 0 else 0
        
        # 直近の状態情報
        status = {
            "timestamp": datetime.now().isoformat(),
            "is_available": self.is_available,
            "consecutive_failures": self.consecutive_failures,
            "total_checks": self.total_checks,
            "total_failures": self.total_failures,
            "failure_rate": (self.total_failures / self.total_checks) if self.total_checks > 0 else 0,
            "uptime_seconds": uptime,
            "last_check_time": datetime.fromtimestamp(self.last_check_time).isoformat() if self.last_check_time > 0 else None,
        }
        
        # 利用可能/不可の経過時間
        if self.last_available_time > 0:
            status["last_available_time"] = datetime.fromtimestamp(self.last_available_time).isoformat()
            if self.is_available:
                status["available_duration_seconds"] = now - self.last_available_time
                
        if self.last_unavailable_time > 0:
            status["last_unavailable_time"] = datetime.fromtimestamp(self.last_unavailable_time).isoformat()
            if not self.is_available:
                status["unavailable_duration_seconds"] = now - self.last_unavailable_time
        
        # 回復試行の統計
        status["recovery_attempts"] = self.recovery_attempts
        status["successful_recoveries"] = self.successful_recoveries
        status["recovery_success_rate"] = (self.successful_recoveries / self.recovery_attempts) if self.recovery_attempts > 0 else 0
        
        return status
