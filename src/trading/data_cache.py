#!/usr/bin/env python3
import os
import pandas as pd
import json
from datetime import datetime, timedelta
import logging

logger = logging.getLogger(__name__)

class DataCache:
    def __init__(self, cache_dir="data/cache"):
        """
        データキャッシュシステムの初期化
        
        Parameters:
        -----------
        cache_dir : str
            キャッシュディレクトリのパス
        """
        self.cache_dir = cache_dir
        self.metadata_file = os.path.join(cache_dir, "metadata.json")
        self.metadata = self._load_metadata()
        
        # キャッシュディレクトリの作成
        os.makedirs(cache_dir, exist_ok=True)
        
    def _load_metadata(self):
        """メタデータの読み込み"""
        if os.path.exists(self.metadata_file):
            try:
                with open(self.metadata_file, 'r') as f:
                    return json.load(f)
            except Exception as e:
                logger.error(f"メタデータ読み込みエラー: {str(e)}")
        
        # メタデータがない場合は新規作成
        return {
            "last_update": {},
            "tickers": []
        }
    
    def _save_metadata(self):
        """メタデータの保存"""
        try:
            with open(self.metadata_file, 'w') as f:
                json.dump(self.metadata, f, indent=2)
        except Exception as e:
            logger.error(f"メタデータ保存エラー: {str(e)}")
    
    def get_cached_data(self, ticker, interval="1h", period="3mo"):
        """
        キャッシュからデータを取得
        
        Parameters:
        -----------
        ticker : str
            銘柄コード
        interval : str
            データ間隔
        period : str
            データ期間
            
        Returns:
        --------
        pd.DataFrame or None
            キャッシュされたデータ、なければNone
        """
        cache_file = os.path.join(self.cache_dir, f"{ticker}_{interval}.csv")
        
        if not os.path.exists(cache_file):
            return None
            
        # キャッシュの有効期限チェック
        last_update = self.metadata.get("last_update", {}).get(ticker, "")
        if last_update:
            last_update_time = datetime.fromisoformat(last_update)
            # 1時間以上経過していたら古いと判断
            if datetime.now() - last_update_time > timedelta(hours=1):
                return None
        
        try:
            df = pd.read_csv(cache_file, parse_dates=['Datetime'])
            
            # 期間に応じてデータをフィルタリング
            if period == "1d":
                cutoff = datetime.now() - timedelta(days=1)
            elif period == "1wk":
                cutoff = datetime.now() - timedelta(weeks=1)
            elif period == "1mo":
                cutoff = datetime.now() - timedelta(days=30)
            elif period == "3mo":
                cutoff = datetime.now() - timedelta(days=90)
            else:  # デフォルトは3ヶ月
                cutoff = datetime.now() - timedelta(days=90)
                
            df = df[df['Datetime'] >= cutoff]
            
            return df
        except Exception as e:
            logger.error(f"キャッシュ読み込みエラー ({ticker}): {str(e)}")
            return None
    
    def save_to_cache(self, ticker, data, interval="1h"):
        """
        データをキャッシュに保存
        
        Parameters:
        -----------
        ticker : str
            銘柄コード
        data : pd.DataFrame
            保存するデータ
        interval : str
            データ間隔
        """
        if data is None or data.empty:
            logger.warning(f"保存するデータがありません ({ticker})")
            return
            
        try:
            # キャッシュファイルのパス
            cache_file = os.path.join(self.cache_dir, f"{ticker}_{interval}.csv")
            
            # 既存のキャッシュと結合
            if os.path.exists(cache_file):
                existing_data = pd.read_csv(cache_file, parse_dates=['Datetime'])
                combined_data = pd.concat([existing_data, data])
                # 重複を削除
                combined_data = combined_data.drop_duplicates(subset=['Datetime']).sort_values('Datetime')
                combined_data.to_csv(cache_file, index=False)
            else:
                data.to_csv(cache_file, index=False)
            
            # メタデータを更新
            self.metadata["last_update"][ticker] = datetime.now().isoformat()
            if ticker not in self.metadata["tickers"]:
                self.metadata["tickers"].append(ticker)
            
            self._save_metadata()
            
            logger.info(f"データをキャッシュに保存しました ({ticker}, {len(data)}行)")
        except Exception as e:
            logger.error(f"キャッシュ保存エラー ({ticker}): {str(e)}")
    
    def get_all_cached_tickers(self):
        """キャッシュされている全銘柄のリストを取得"""
        return self.metadata.get("tickers", [])
    
    def clear_old_cache(self, max_age_days=30):
        """古いキャッシュを削除"""
        cutoff = datetime.now() - timedelta(days=max_age_days)
        
        for ticker, last_update in list(self.metadata["last_update"].items()):
            try:
                update_time = datetime.fromisoformat(last_update)
                if update_time < cutoff:
                    # キャッシュファイルを削除
                    cache_file = os.path.join(self.cache_dir, f"{ticker}_1h.csv")
                    if os.path.exists(cache_file):
                        os.remove(cache_file)
                    
                    # メタデータから削除
                    del self.metadata["last_update"][ticker]
                    if ticker in self.metadata["tickers"]:
                        self.metadata["tickers"].remove(ticker)
                    
                    logger.info(f"古いキャッシュを削除しました ({ticker})")
            except Exception as e:
                logger.error(f"キャッシュ削除エラー ({ticker}): {str(e)}")
        
        self._save_metadata()
