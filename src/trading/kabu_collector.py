#!/usr/bin/env python3
import os
import time
import json
import logging
import pandas as pd
import numpy as np
import threading
import queue
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional, Set, Callable, Tuple

# 自作モジュール
from src.trading.kabu_api import KabuSession

# ロガー設定
logger = logging.getLogger(__name__)

class KabuDataCollector:
    """
    kabuステーションAPIからデータを収集するクラス
    - 銘柄情報取得
    - 板情報取得
    - WebSocketによる歩み値データ取得
    - 銘柄ローテーション管理
    """
    def __init__(self, session: KabuSession, output_dir: str = "data/kabu"):
        """
        初期化
        
        Parameters:
        -----------
        session : KabuSession
            KabuSessionインスタンス
        output_dir : str
            データ出力ディレクトリ
        """
        self.session = session
        self.output_dir = output_dir
        self.meta_dir = os.path.join(output_dir, "meta")
        self.board_dir = os.path.join(output_dir, "board")
        self.tick_dir = os.path.join(output_dir, "tick")
        self.bar_dir = os.path.join(output_dir, "bar60")
        
        # ディレクトリ作成
        for dir_path in [self.output_dir, self.meta_dir, self.board_dir, 
                         self.tick_dir, self.bar_dir]:
            os.makedirs(dir_path, exist_ok=True)
        
        # データバッファ
        self.symbol_meta: Dict[str, Dict] = {}  # 銘柄メタデータ
        self.board_data: Dict[str, Dict] = {}   # 板情報
        self.tick_data: Dict[str, List] = {}    # Tickデータ
        self.bar_data: Dict[str, pd.DataFrame] = {}  # 分足データ
        
        # WebSocket関連
        self.ws_thread = None
        self.ws_running = False
        self.ws_queue = queue.Queue()
    
    def collect_all_symbol_meta(self) -> Dict[str, Dict]:
        """
        日経225構成銘柄のメタ情報を取得
        
        Returns:
        --------
        Dict[str, Dict]
            銘柄コードをキーとするメタ情報辞書
        """
        logger.info("日経225構成銘柄のメタ情報を取得します")
        
        # Nikkei 225銘柄リスト（READMEから抽出した一部）
        nikkei225_tickers = [
            '1332.T', '1333.T', '1376.T', '1605.T', '1721.T', '1801.T', '1802.T', '1803.T', 
            '1808.T', '1812.T', '1925.T', '1928.T', '1963.T', '2002.T', '2269.T', '2501.T', 
            '2502.T', '2503.T', '2531.T', '2768.T', '2801.T', '2802.T', '2871.T', '2914.T', 
            '3003.T', '3086.T', '3101.T', '3103.T', '3289.T', '3382.T', '3401.T', '3402.T', 
            '3405.T', '3407.T', '3436.T', '3861.T', '3863.T', '4004.T', '4005.T', '4021.T', 
            '4042.T', '4043.T', '4061.T', '4063.T', '4151.T', '4183.T', '4188.T', '4208.T', 
            '4324.T', '4452.T', '4502.T', '4503.T', '4506.T', '4507.T', '4519.T', '4523.T', 
            '4568.T', '4578.T', '4689.T', '4704.T', '4751.T', '4901.T', '4911.T', '5020.T', 
            '5101.T', '5108.T', '5201.T', '5202.T', '5214.T', '5232.T', '5233.T', '5301.T', 
            '5332.T', '5333.T', '5401.T', '5406.T', '5411.T', '5541.T', '5711.T', '5713.T', 
            '5714.T', '5801.T', '5802.T', '5803.T', '5901.T', '6098.T', '6103.T', '6113.T', 
            '6301.T', '6302.T', '6305.T', '6326.T', '6361.T', '6367.T', '6471.T', '6472.T', 
            '6473.T', '6479.T', '6501.T', '6503.T', '6504.T', '6506.T', '6594.T', '6645.T', 
            '6674.T', '6701.T', '6702.T', '6703.T', '6723.T', '6724.T', '6752.T', '6758.T', 
            '6762.T', '6770.T', '6841.T', '6857.T', '6902.T', '6952.T', '6954.T', '6971.T', 
            '6976.T', '7003.T', '7004.T', '7011.T', '7013.T', '7186.T', '7201.T', '7202.T', 
            '7203.T', '7205.T', '7211.T', '7261.T', '7267.T', '7269.T', '7270.T', '7272.T', 
            '7731.T', '7733.T', '7735.T', '7751.T', '7752.T', '7762.T', '7832.T', '7911.T', 
            '7912.T', '7951.T', '8001.T', '8002.T', '8015.T', '8031.T', '8035.T', '8053.T', 
            '8058.T', '8233.T', '8252.T', '8253.T', '8267.T', '8273.T', '8303.T', '8304.T', 
            '8306.T', '8308.T', '8309.T', '8316.T', '8331.T', '8354.T', '8355.T', '8411.T', 
            '8601.T', '8604.T', '8628.T', '8630.T', '8697.T', '8725.T', '8750.T', '8766.T', 
            '8795.T', '8801.T', '8802.T', '8804.T', '8830.T', '9001.T', '9005.T', '9007.T', 
            '9008.T', '9009.T', '9020.T', '9021.T', '9022.T', '9062.T', '9064.T', '9101.T', 
            '9104.T', '9107.T', '9202.T', '9301.T', '9432.T', '9433.T', '9434.T', '9502.T', 
            '9503.T', '9531.T', '9532.T', '9602.T', '9613.T', '9735.T', '9766.T', '9983.T', '9984.T'
        ]
        
        meta_data = {}
        today = datetime.now().strftime("%Y%m%d")
        meta_file = os.path.join(self.meta_dir, f"symbol_meta_{today}.json")
        
        # キャッシュがあればそれを使用
        if os.path.exists(meta_file):
            try:
                with open(meta_file, 'r', encoding='utf-8') as f:
                    meta_data = json.load(f)
                logger.info(f"銘柄メタ情報をキャッシュから読み込みました: {len(meta_data)}銘柄")
                self.symbol_meta = meta_data
                return meta_data
            except Exception as e:
                logger.warning(f"メタ情報キャッシュ読み込みエラー: {str(e)}")
        
        # APIから取得
        for ticker in nikkei225_tickers:
            try:
                symbol = ticker.split('.')[0]
                data = self.session.get_symbol_info(symbol)
                meta_data[ticker] = data
                # APIレート制限に配慮
                time.sleep(0.1)
            except Exception as e:
                logger.error(f"銘柄情報取得エラー: {ticker} - {str(e)}")
        
        # キャッシュに保存
        if meta_data:
            try:
                with open(meta_file, 'w', encoding='utf-8') as f:
                    json.dump(meta_data, f, ensure_ascii=False, indent=2)
                logger.info(f"銘柄メタ情報をキャッシュに保存しました: {len(meta_data)}銘柄")
            except Exception as e:
                logger.warning(f"メタ情報キャッシュ保存エラー: {str(e)}")
        
        self.symbol_meta = meta_data
        return meta_data
    
    def collect_board_data(self, symbols: List[str]) -> Dict[str, Dict]:
        """
        板情報収集
        
        Parameters:
        -----------
        symbols : List[str]
            取得対象の銘柄リスト
            
        Returns:
        --------
        Dict[str, Dict]
            銘柄コードをキーとする板情報辞書
        """
        logger.info(f"板情報を取得します: {len(symbols)}銘柄")
        result = {}
        
        for symbol in symbols:
            try:
                data = self.session.get_board(symbol)
                result[symbol] = data
                self.board_data[symbol] = data
                
                # 時刻、ファイル名用のタイムスタンプ
                timestamp = datetime.now()
                ts_str = timestamp.strftime("%Y%m%d_%H%M%S")
                minute_ts = timestamp.strftime("%Y%m%d_%H%M")
                
                # 板情報を保存（1分ごとにファイル更新）
                symbol_dir = os.path.join(self.board_dir, symbol.replace('.', '_'))
                os.makedirs(symbol_dir, exist_ok=True)
                
                board_file = os.path.join(symbol_dir, f"board_{minute_ts}.json")
                with open(board_file, 'w', encoding='utf-8') as f:
                    json.dump(data, f, ensure_ascii=False, indent=2)
                
                # APIレート制限に配慮
                time.sleep(0.1)
            except Exception as e:
                logger.error(f"板情報取得エラー: {symbol} - {str(e)}")
        
        return result
    
    def setup_websocket_stream(self, on_tick_callback: Optional[Callable] = None) -> None:
        """
        WebSocketストリーム接続の設定
        
        Parameters:
        -----------
        on_tick_callback : Optional[Callable]
            Tickデータ受信時のコールバック関数
        """
        if self.ws_thread and self.ws_thread.is_alive():
            logger.warning("WebSocketスレッドは既に実行中です")
            return
        
        self.ws_running = True
        self.ws_thread = threading.Thread(
            target=self._websocket_worker,
            args=(on_tick_callback,),
            daemon=True
        )
        self.ws_thread.start()
        logger.info("WebSocketスレッドを開始しました")
    
    def _websocket_worker(self, on_tick_callback: Optional[Callable]) -> None:
        """
        WebSocketワーカースレッド
        
        Parameters:
        -----------
        on_tick_callback : Optional[Callable]
            Tickデータ受信時のコールバック関数
        """
        ws = None
        reconnect_delay = 1
        
        while self.ws_running:
            try:
                if not ws:
                    # WebSocket接続
                    ws = self.session.ws()
                    logger.info("WebSocket接続確立")
                    reconnect_delay = 1  # 接続成功したらリトライ遅延をリセット
                
                # メッセージ受信
                message = ws.recv()
                if message:
                    data = json.loads(message)
                    self._process_ws_message(data)
                    
                    # コールバック呼び出し
                    if on_tick_callback:
                        on_tick_callback(data)
                
            except Exception as e:
                logger.error(f"WebSocketエラー: {str(e)}")
                if ws:
                    try:
                        ws.close()
                    except:
                        pass
                    ws = None
                
                # 再接続の前に少し待機
                time.sleep(reconnect_delay)
                reconnect_delay = min(reconnect_delay * 2, 60)  # 指数バックオフ（最大60秒）
        
        # スレッド終了時にWebSocketを閉じる
        if ws:
            try:
                ws.close()
                logger.info("WebSocket接続を閉じました")
            except:
                pass
    
    def _process_ws_message(self, data: Dict) -> None:
        """
        WebSocketメッセージの処理
        
        Parameters:
        -----------
        data : Dict
            受信したJSONデータ
        """
        # PUSHデータの種類を判別
        if 'Symbol' not in data:
            logger.warning(f"不明なメッセージ形式: {data}")
            return
        
        symbol = data['Symbol']
        ticker = f"{symbol}.T"  # コード形式を統一（1301 → 1301.T）
        
        # Tickデータとして処理
        timestamp = datetime.now()
        
        # データにタイムスタンプを追加
        data['ReceivedTime'] = timestamp.isoformat()
        
        # ティックデータをバッファに追加
        if ticker not in self.tick_data:
            self.tick_data[ticker] = []
        self.tick_data[ticker].append(data)
        
        # ファイルに保存（1分ごとに新しいファイル）
        minute_ts = timestamp.strftime("%Y%m%d_%H%M")
        symbol_dir = os.path.join(self.tick_dir, ticker.replace('.', '_'))
        os.makedirs(symbol_dir, exist_ok=True)
        
        tick_file = os.path.join(symbol_dir, f"tick_{minute_ts}.jsonl")
        
        # JSONLinesとして追記
        with open(tick_file, 'a', encoding='utf-8') as f:
            f.write(json.dumps(data, ensure_ascii=False) + '\n')
        
        # 1分足データの更新
        self._update_minute_bar(ticker, data, timestamp)
    
    def _update_minute_bar(self, ticker: str, tick_data: Dict, timestamp: datetime) -> None:
        """
        1分足データの更新
        
        Parameters:
        -----------
        ticker : str
            銘柄コード
        tick_data : Dict
            Tickデータ
        timestamp : datetime
            受信時刻
        """
        # 価格情報の取得
        price = None
        if 'CurrentPrice' in tick_data and tick_data['CurrentPrice']:
            price = float(tick_data['CurrentPrice'])
        elif 'LastPrice' in tick_data and tick_data['LastPrice']:
            price = float(tick_data['LastPrice'])
        else:
            return  # 価格情報がない場合は処理しない
        
        # 分を切り捨てた時刻（1分足の開始時刻）
        bar_time = timestamp.replace(second=0, microsecond=0)
        bar_key = bar_time.strftime("%Y-%m-%d %H:%M:00")
        
        # 銘柄の分足データ初期化
        if ticker not in self.bar_data:
            self.bar_data[ticker] = pd.DataFrame(
                columns=['Open', 'High', 'Low', 'Close', 'Volume', 'TickCount']
            )
        
        df = self.bar_data[ticker]
        
        # 新しい1分足の作成または更新
        if bar_key not in df.index:
            df.loc[bar_key] = {
                'Open': price,
                'High': price,
                'Low': price,
                'Close': price,
                'Volume': tick_data.get('Volume', 0) or 0,
                'TickCount': 1
            }
        else:
            # 既存の1分足を更新
            current = df.loc[bar_key]
            
            # High/Low更新
            if price > current['High']:
                df.at[bar_key, 'High'] = price
            if price < current['Low']:
                df.at[bar_key, 'Low'] = price
            
            # Close更新
            df.at[bar_key, 'Close'] = price
            
            # 出来高更新
            volume = tick_data.get('Volume', 0) or 0
            if volume > 0:
                df.at[bar_key, 'Volume'] += volume
            
            # Tick数更新
            df.at[bar_key, 'TickCount'] += 1
        
        # 1時間ごとにファイルに保存
        hour_ts = bar_time.strftime("%Y%m%d_%H")
        if bar_time.minute == 59:
            self._save_hour_bar(ticker, hour_ts)
    
    def _save_hour_bar(self, ticker: str, hour_ts: str) -> None:
        """
        1時間分の分足データを保存
        
        Parameters:
        -----------
        ticker : str
            銘柄コード
        hour_ts : str
            時間のタイムスタンプ（YYYYMMDD_HH形式）
        """
        if ticker not in self.bar_data or self.bar_data[ticker].empty:
            return
        
        # 保存先ディレクトリ
        symbol_dir = os.path.join(self.bar_dir, ticker.replace('.', '_'))
        os.makedirs(symbol_dir, exist_ok=True)
        
        # 保存ファイル名
        bar_file = os.path.join(symbol_dir, f"bar60_{hour_ts}.parquet")
        
        # 1時間分の分足データを取得
        df = self.bar_data[ticker].copy()
        df.index = pd.to_datetime(df.index)
        
        # 古いデータを削除（メモリ節約）
        hour_ago = datetime.now() - timedelta(hours=1)
        self.bar_data[ticker] = df[df.index >= hour_ago]
        
        # 1時間分のデータをParquet形式で保存
        df.to_parquet(bar_file)
        logger.info(f"1時間分の分足データを保存しました: {bar_file}")
    
    def stop_websocket(self) -> None:
        """
        WebSocketストリームの停止
        """
        if self.ws_running:
            self.ws_running = False
            if self.ws_thread:
                self.ws_thread.join(timeout=5)
                logger.info("WebSocketスレッドを停止しました")
    
    def rotate_symbols(self, symbols: List[str]) -> None:
        """
        監視銘柄の入れ替え
        
        Parameters:
        -----------
        symbols : List[str]
            新しい監視銘柄リスト（最大50銘柄）
        """
        if not symbols:
            logger.warning("空の銘柄リストが指定されました")
            return
            
        # 現在の登録を解除してから新しい銘柄を登録
        self.session.register_symbols(symbols)
        logger.info(f"監視銘柄を更新しました: {len(symbols)}銘柄")
    
    def get_current_bar_data(self, ticker: str) -> pd.DataFrame:
        """
        現在のバッファ内の分足データを取得
        
        Parameters:
        -----------
        ticker : str
            銘柄コード
            
        Returns:
        --------
        pd.DataFrame
            分足データ
        """
        if ticker in self.bar_data:
            return self.bar_data[ticker].copy()
        return pd.DataFrame(columns=['Open', 'High', 'Low', 'Close', 'Volume', 'TickCount'])
    
    def get_latest_board_data(self, ticker: str) -> Optional[Dict]:
        """
        最新の板情報を取得
        
        Parameters:
        -----------
        ticker : str
            銘柄コード
            
        Returns:
        --------
        Optional[Dict]
            板情報
        """
        return self.board_data.get(ticker)
    
    def load_hour_bar_data(self, ticker: str, date: Optional[str] = None) -> pd.DataFrame:
        """
        指定日の時間足データを読み込み
        
        Parameters:
        -----------
        ticker : str
            銘柄コード
        date : Optional[str]
            日付（YYYYMMDD形式）、未指定なら当日
            
        Returns:
        --------
        pd.DataFrame
            時間足データ
        """
        if not date:
            date = datetime.now().strftime("%Y%m%d")
            
        symbol_dir = os.path.join(self.bar_dir, ticker.replace('.', '_'))
        
        # 指定日のファイルを検索
        pattern = f"bar60_{date}_*.parquet"
        import glob
        files = glob.glob(os.path.join(symbol_dir, pattern))
        
        if not files:
            logger.warning(f"指定日の時間足データが見つかりません: {ticker} {date}")
            return pd.DataFrame(columns=['Open', 'High', 'Low', 'Close', 'Volume', 'TickCount'])
        
        # 全ファイルの読み込みと結合
        dfs = []
        for file in sorted(files):
            try:
                df = pd.read_parquet(file)
                dfs.append(df)
            except Exception as e:
                logger.error(f"ファイル読み込みエラー: {file} - {str(e)}")
        
        if not dfs:
            return pd.DataFrame(columns=['Open', 'High', 'Low', 'Close', 'Volume', 'TickCount'])
        
        # 結合して時間足にリサンプリング
        all_data = pd.concat(dfs)
        all_data = all_data.sort_index()
        
        # 重複インデックスの除去
        all_data = all_data[~all_data.index.duplicated(keep='last')]
        
        # 時間足にリサンプリング
        hour_data = all_data.resample('1H').agg({
            'Open': 'first',
            'High': 'max',
            'Low': 'min',
            'Close': 'last',
            'Volume': 'sum',
            'TickCount': 'sum'
        })
        
        return hour_data
