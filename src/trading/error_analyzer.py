#!/usr/bin/env python3
import os
import json
import re
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, timedelta
import logging
from collections import Counter, defaultdict
import matplotlib
matplotlib.use('Agg')  # GUIなしで動作するバックエンド
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from matplotlib.figure import Figure
import numpy as np

from src.trading.logging_config import get_module_logger

# ロガー設定
logger = get_module_logger(__name__)

class ErrorAnalyzer:
    """
    エラーログ解析クラス
    - 構造化エラーログの解析
    - エラーパターンの特定と集計
    - 時系列分析とレポート生成
    """
    def __init__(self, log_dir: str = "logs/errors"):
        """
        初期化
        
        Parameters:
        -----------
        log_dir : str
            エラーログディレクトリ
        """
        self.log_dir = log_dir
        self.errors = []
        self.error_patterns = {}
        self.hourly_counts = defaultdict(int)
        self.type_counts = Counter()
        self.source_counts = Counter()
        self.common_patterns = []
        
    def load_logs(self, specific_date: Optional[str] = None) -> int:
        """
        エラーログを読み込む
        
        Parameters:
        -----------
        specific_date : Optional[str]
            特定の日付のログのみを読み込む場合はYYYYMMDD形式で指定
            
        Returns:
        --------
        int
            読み込んだエラーの総数
        """
        if not os.path.exists(self.log_dir):
            logger.warning(f"ログディレクトリが存在しません: {self.log_dir}")
            return 0
            
        # ログファイル一覧取得
        log_files = []
        for filename in os.listdir(self.log_dir):
            if not filename.endswith('.jsonl'):
                continue
                
            # 特定日付の指定がある場合はフィルタリング
            if specific_date:
                file_date_match = re.search(r'errors_(\d{8})\.jsonl', filename)
                if file_date_match and file_date_match.group(1) == specific_date:
                    log_files.append(os.path.join(self.log_dir, filename))
            else:
                log_files.append(os.path.join(self.log_dir, filename))
                
        if not log_files:
            logger.warning("対象のログファイルが見つかりません")
            return 0
            
        # 各ログファイルからエラーを読み込む
        self.errors = []
        for log_file in log_files:
            try:
                with open(log_file, 'r', encoding='utf-8') as f:
                    for line in f:
                        line = line.strip()
                        if line:
                            try:
                                error_entry = json.loads(line)
                                self.errors.append(error_entry)
                            except json.JSONDecodeError:
                                logger.warning(f"無効なJSON行: {line}")
            except Exception as e:
                logger.error(f"ログファイル読み込みエラー ({log_file}): {str(e)}")
                
        # エラーが読み込めた場合は解析を実行
        if self.errors:
            self._analyze_errors()
            
        return len(self.errors)
        
    def _analyze_errors(self) -> None:
        """
        読み込んだエラーの解析を実行
        """
        if not self.errors:
            return
            
        # カウンター初期化
        self.hourly_counts = defaultdict(int)
        self.type_counts = Counter()
        self.source_counts = Counter()
        pattern_counter = Counter()
        
        # 各エラーを解析
        for error in self.errors:
            # タイムスタンプ解析
            try:
                dt = datetime.fromisoformat(error.get('timestamp', ''))
                hour = dt.replace(minute=0, second=0, microsecond=0)
                self.hourly_counts[hour] += 1
            except (ValueError, TypeError):
                pass
                
            # エラータイプとソース集計
            error_type = error.get('error_type', 'Unknown')
            source = error.get('source', 'unknown')
            
            self.type_counts[error_type] += 1
            self.source_counts[source] += 1
            
            # エラーパターン抽出
            error_message = error.get('error_message', '')
            pattern = self._extract_error_pattern(error_type, error_message)
            pattern_counter[pattern] += 1
            
        # 共通パターンの抽出（発生回数の多い順）
        self.common_patterns = [(pattern, count) for pattern, count in pattern_counter.most_common(10)]
            
    def _extract_error_pattern(self, error_type: str, message: str) -> str:
        """
        エラーメッセージからパターンを抽出
        
        Parameters:
        -----------
        error_type : str
            エラータイプ
        message : str
            エラーメッセージ
            
        Returns:
        --------
        str
            抽出されたパターン
        """
        # エラーメッセージから変動部分を除去して一般化
        # IPアドレス、数値、ハッシュ、タイムスタンプなどを置換
        
        # URLパスの標準化
        pattern = re.sub(r'/[0-9a-zA-Z_\.-]+/[0-9a-zA-Z_\.-/]+', '/PATH', message)
        
        # IPアドレスの置換
        pattern = re.sub(r'\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}', 'IP_ADDRESS', pattern)
        
        # ポート番号の置換
        pattern = re.sub(r'port=\d+', 'port=PORT', pattern)
        
        # 数値の置換
        pattern = re.sub(r'\b\d+\b', 'N', pattern)
        
        # メモリアドレスの置換
        pattern = re.sub(r'0x[0-9a-f]+', '0xXXXXXX', pattern)
        
        # 最終的なパターン
        pattern = f"{error_type}: {pattern}"
        
        return pattern
        
    def generate_report(self, output_file: str = "logs/reports/error_report.md") -> Optional[str]:
        """
        エラー分析レポートを生成
        
        Parameters:
        -----------
        output_file : str
            出力ファイルパス
            
        Returns:
        --------
        Optional[str]
            生成されたレポートファイルのパス、失敗した場合はNone
        """
        if not self.errors:
            logger.warning("レポート生成のためのエラーデータがありません")
            return None
            
        # 出力ディレクトリ作成
        os.makedirs(os.path.dirname(output_file), exist_ok=True)
        
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                # レポートヘッダー
                f.write("# エラー分析レポート\n\n")
                f.write(f"生成日時: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
                f.write(f"分析対象エラー数: {len(self.errors)}\n\n")
                
                # エラータイプ別集計
                f.write("## エラータイプ別集計\n\n")
                f.write("| エラータイプ | 発生回数 | 割合 |\n")
                f.write("|------------|---------|------|\n")
                
                for error_type, count in self.type_counts.most_common():
                    percentage = (count / len(self.errors)) * 100
                    f.write(f"| {error_type} | {count} | {percentage:.2f}% |\n")
                f.write("\n")
                
                # ソース別集計
                f.write("## エラー発生源別集計\n\n")
                f.write("| 発生源 | 発生回数 | 割合 |\n")
                f.write("|-------|---------|------|\n")
                
                for source, count in self.source_counts.most_common():
                    percentage = (count / len(self.errors)) * 100
                    f.write(f"| {source} | {count} | {percentage:.2f}% |\n")
                f.write("\n")
                
                # 時間帯別エラー発生状況
                f.write("## 時間帯別エラー発生状況\n\n")
                f.write("| 時間帯 | 発生回数 |\n")
                f.write("|-------|----------|\n")
                
                sorted_hours = sorted(self.hourly_counts.keys())
                for hour in sorted_hours:
                    count = self.hourly_counts[hour]
                    f.write(f"| {hour.strftime('%Y-%m-%d %H:00')} | {count} |\n")
                f.write("\n")
                
                # 主要エラーパターン
                f.write("## 主要エラーパターン\n\n")
                f.write("| パターン | 発生回数 | 割合 |\n")
                f.write("|---------|---------|------|\n")
                
                for pattern, count in self.common_patterns:
                    percentage = (count / len(self.errors)) * 100
                    # 長いパターンは切り詰める
                    pattern_display = pattern[:100] + "..." if len(pattern) > 100 else pattern
                    f.write(f"| {pattern_display} | {count} | {percentage:.2f}% |\n")
                f.write("\n")
                
                # 問題の重大度評価
                f.write("## 重大度評価\n\n")
                
                # 最も多いエラータイプと発生源を特定
                most_common_type = self.type_counts.most_common(1)[0][0] if self.type_counts else "なし"
                most_common_source = self.source_counts.most_common(1)[0][0] if self.source_counts else "なし"
                
                f.write(f"- 最も頻発しているエラータイプ: **{most_common_type}**\n")
                f.write(f"- 最も問題の多い発生源: **{most_common_source}**\n\n")
                
                # 解決策の提案
                f.write("## 解決策の提案\n\n")
                
                # エラーパターンに基づいた解決策を提案
                for pattern, count in self.common_patterns[:3]:  # 上位3つのパターンに対して
                    f.write(f"### パターン: {pattern}\n\n")
                    f.write(f"発生回数: {count}\n\n")
                    
                    # ネットワーク関連のエラーに対する解決策
                    if "Connection refused" in pattern or "connect" in pattern.lower():
                        f.write("このエラーはネットワーク接続の問題を示しています。\n\n")
                        f.write("**推奨される対策:**\n")
                        f.write("1. サーバーが実行中であることを確認する\n")
                        f.write("2. 正しいホスト名とポート番号が設定されているか確認する\n")
                        f.write("3. ファイアウォール設定を確認する\n")
                        f.write("4. 接続タイムアウト設定を見直し、バックオフ戦略を実装する\n")
                        f.write("5. サービスの健全性監視を導入し、問題を早期に検出する\n\n")
                    
                    # タイムアウトエラーに対する解決策
                    elif "timeout" in pattern.lower() or "timed out" in pattern.lower():
                        f.write("このエラーはリクエストのタイムアウトを示しています。\n\n")
                        f.write("**推奨される対策:**\n")
                        f.write("1. ネットワーク接続の品質と安定性を確認する\n")
                        f.write("2. タイムアウト設定を適切な値に調整する\n")
                        f.write("3. リトライメカニズムを実装する\n")
                        f.write("4. サーバー側のパフォーマンスを確認する\n\n")
                    
                    # 認証エラーに対する解決策
                    elif "auth" in pattern.lower() or "token" in pattern.lower() or "permission" in pattern.lower():
                        f.write("このエラーは認証または権限の問題を示しています。\n\n")
                        f.write("**推奨される対策:**\n")
                        f.write("1. 認証情報（トークン、パスワードなど）が正しいか確認する\n")
                        f.write("2. 認証情報の有効期限を確認し、必要に応じて更新する\n")
                        f.write("3. 権限設定を確認する\n")
                        f.write("4. トークン更新メカニズムが正しく機能しているか確認する\n\n")
                    
                    # その他のエラーに対する一般的な解決策
                    else:
                        f.write("このエラーパターンの詳細な分析が必要です。\n\n")
                        f.write("**推奨される対策:**\n")
                        f.write("1. エラーメッセージを詳細に分析し、根本原因を特定する\n")
                        f.write("2. 関連するコードを確認し、エラーハンドリングを改善する\n")
                        f.write("3. エラー発生時のコンテキスト情報をより詳細に記録する\n")
                        f.write("4. テスト環境でエラーを再現し、解決策を検証する\n\n")
                
                # 全体的な改善案
                f.write("## 全体的な改善案\n\n")
                f.write("1. **エラー監視の強化**: エラーの発生パターンをリアルタイムで監視するシステムを導入する\n")
                f.write("2. **自動リカバリーメカニズム**: 一時的なエラーに対して自動的に回復するメカニズムを実装する\n")
                f.write("3. **サーキットブレーカーパターン**: 繰り返しエラーが発生する場合に一時的にサービスを停止し、システム全体への影響を防ぐ\n")
                f.write("4. **バックオフ戦略**: リトライ間隔を徐々に増やすことで、過負荷状態のサービスに対する負担を軽減する\n")
                f.write("5. **障害テスト**: カオスエンジニアリングの手法を取り入れ、様々な障害シナリオに対するシステムの耐性を強化する\n")
            
            logger.info(f"エラー分析レポートを生成しました: {output_file}")
            return output_file
            
        except Exception as e:
            logger.error(f"レポート生成中にエラーが発生: {str(e)}")
            return None
    
    def generate_hourly_chart(self, output_file: str = "logs/reports/error_chart.png") -> Optional[str]:
        """
        時間帯別エラー発生数のグラフを生成
        
        Parameters:
        -----------
        output_file : str
            出力ファイルパス
            
        Returns:
        --------
        Optional[str]
            生成されたグラフファイルのパス、失敗した場合はNone
        """
        if not self.hourly_counts:
            logger.warning("グラフ生成のためのエラーデータがありません")
            return None
            
        try:
            # 出力ディレクトリ作成
            os.makedirs(os.path.dirname(output_file), exist_ok=True)
            
            # グラフ作成
            plt.figure(figsize=(12, 6))
            
            # データ準備
            hours = sorted(self.hourly_counts.keys())
            counts = [self.hourly_counts[hour] for hour in hours]
            
            # 棒グラフ
            plt.bar(hours, counts, width=0.02, align='center', alpha=0.7)
            
            # グラフ設定
            plt.title('時間帯別エラー発生数', fontsize=16)
            plt.xlabel('時間', fontsize=12)
            plt.ylabel('エラー発生数', fontsize=12)
            plt.grid(True, linestyle='--', alpha=0.7)
            
            # X軸の日付フォーマット
            plt.gca().xaxis.set_major_formatter(mdates.DateFormatter('%m-%d %H:%M'))
            plt.gca().xaxis.set_major_locator(mdates.HourLocator(interval=2))
            plt.gcf().autofmt_xdate()
            
            # 保存
            plt.tight_layout()
            plt.savefig(output_file, dpi=100)
            plt.close()
            
            logger.info(f"エラー発生数グラフを生成しました: {output_file}")
            return output_file
            
        except Exception as e:
            logger.error(f"グラフ生成中にエラーが発生: {str(e)}")
            return None
            
    def get_error_stats(self) -> Dict[str, Any]:
        """
        エラー統計情報を取得
        
        Returns:
        --------
        Dict[str, Any]
            エラー統計情報
        """
        if not self.errors:
            return {"error_count": 0}
            
        stats = {
            "error_count": len(self.errors),
            "type_distribution": dict(self.type_counts),
            "source_distribution": dict(self.source_counts),
            "common_patterns": [{"pattern": p, "count": c} for p, c in self.common_patterns],
            "hourly_distribution": {h.isoformat(): c for h, c in self.hourly_counts.items()}
        }
        
        return stats
