#!/usr/bin/env python3
"""
自律型取引モジュール

AIトレーダーの自律的な取引機能を提供するクラス
テストモードと本番モードを明確に分離し、各モードに適した動作を行う
"""

import os
import json
import logging
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

from src.trading.auto_trader.trading_modes import TradingMode, TradingModeManager

logger = logging.getLogger(__name__)

class AutoTrader:
    """
    自律型取引クラス

    AIモデルの予測に基づいて自律的に取引を行い、
    モードに応じて学習フィードバックを制御する
    """

    def __init__(self,
                 initial_balance=1000000,
                 commission_rate=0.0001,
                 mode=TradingMode.TEST,
                 max_positions=5,
                 analysis_days=7):
        """
        初期化

        Parameters:
        -----------
        initial_balance : float
            初期資金
        commission_rate : float
            取引手数料率
        mode : TradingMode
            取引モード
        max_positions : int
            最大保有銘柄数
        analysis_days : int
            パフォーマンス分析期間（日）
        """
        # モード管理
        self.mode_manager = TradingModeManager(mode)

        # 結果ディレクトリ
        self.results_dir = self.mode_manager.results_dir
        os.makedirs(self.results_dir, exist_ok=True)

        # ポートフォリオ関連
        self.initial_balance = initial_balance
        self.balance = initial_balance
        self.commission_rate = commission_rate
        self.portfolio = {}  # {ticker: {'qty': qty, 'price': price, 'timestamp': datetime}}
        self.trades = []
        self.max_positions = max_positions

        # 分析関連
        self.analysis_days = analysis_days
        self.prediction_history = []  # 予測履歴
        self.performance_metrics = {}  # パフォーマンス指標

        logger.info(f"AutoTrader initialized in {mode.value} mode")
        logger.info(f"Results directory: {self.results_dir}")
        logger.info(f"Initial balance: {initial_balance:,.0f} JPY")
        logger.info(f"Commission rate: {commission_rate:.4f}")
        logger.info(f"Max positions: {max_positions}")
        logger.info(f"Learning enabled: {self.mode_manager.is_learning_enabled}")

    # ポートフォリオ管理メソッド

    def buy_stock(self, ticker, price, qty, timestamp=None):
        """
        株式購入

        Parameters:
        -----------
        ticker : str
            銘柄コード
        price : float
            購入価格
        qty : int
            購入数量
        timestamp : datetime
            取引時刻（Noneの場合は現在時刻）

        Returns:
        --------
        dict
            取引情報
        """
        if timestamp is None:
            timestamp = datetime.now()

        # 購入金額計算
        amount = price * qty
        commission = amount * self.commission_rate
        total_cost = amount + commission

        # 残高チェック
        if total_cost > self.balance:
            logger.warning(f"残高不足: 必要金額 {total_cost:.2f}, 残高 {self.balance:.2f}")
            return None

        # ポートフォリオに追加（既存の銘柄なら平均取得単価を計算）
        if ticker in self.portfolio:
            current_qty = self.portfolio[ticker]["qty"]
            current_price = self.portfolio[ticker]["price"]
            total_qty = current_qty + qty
            total_amount = (current_qty * current_price) + amount
            avg_price = total_amount / total_qty

            self.portfolio[ticker] = {
                "qty": total_qty,
                "price": avg_price,
                "timestamp": timestamp
            }
        else:
            self.portfolio[ticker] = {
                "qty": qty,
                "price": price,
                "timestamp": timestamp
            }

        # 残高更新
        self.balance -= total_cost

        # 取引履歴に追加
        trade = {
            "ticker": ticker,
            "action": "BUY",
            "qty": qty,
            "price": price,
            "amount": amount,
            "commission": commission,
            "timestamp": timestamp,
            "profit": 0,  # 購入時は利益なし
            "mode": self.mode_manager.mode.value
        }
        self.trades.append(trade)

        logger.info(f"Buy: {ticker} {qty} shares @ {price:.2f} JPY = {total_cost:.2f} JPY")
        return trade

    def sell_stock(self, ticker, price, qty=None, timestamp=None):
        """
        株式売却

        Parameters:
        -----------
        ticker : str
            銘柄コード
        price : float
            売却価格
        qty : int
            売却数量（Noneの場合は全数量）
        timestamp : datetime
            取引時刻（Noneの場合は現在時刻）

        Returns:
        --------
        dict
            取引情報
        """
        if timestamp is None:
            timestamp = datetime.now()

        # ポートフォリオに銘柄があるか確認
        if ticker not in self.portfolio:
            logger.warning(f"ポートフォリオに銘柄 {ticker} が存在しません")
            return None

        position = self.portfolio[ticker]

        # 数量設定（Noneの場合は全数量）
        if qty is None or qty >= position["qty"]:
            qty = position["qty"]

        # 数量が十分にあるか確認
        if position["qty"] < qty:
            logger.warning(f"売却数量不足: 必要数量 {qty}, 保有数量 {position['qty']}")
            return None

        # 売却金額計算
        amount = price * qty
        commission = amount * self.commission_rate
        net_amount = amount - commission

        # 利益計算
        cost_basis = position["price"] * qty
        profit = net_amount - cost_basis

        # ポートフォリオ更新
        position["qty"] -= qty

        # 数量が0になったら削除
        if position["qty"] == 0:
            del self.portfolio[ticker]

        # 残高更新
        self.balance += net_amount

        # 取引履歴に追加
        trade = {
            "ticker": ticker,
            "action": "SELL",
            "qty": qty,
            "price": price,
            "amount": amount,
            "commission": commission,
            "timestamp": timestamp,
            "profit": profit,
            "mode": self.mode_manager.mode.value
        }
        self.trades.append(trade)

        logger.info(f"Sell: {ticker} {qty} shares @ {price:.2f} JPY = {net_amount:.2f} JPY (Profit: {profit:.2f} JPY)")
        return trade

    def execute_trades(self, recommendations):
        """
        推奨銘柄に基づいて取引を実行

        Parameters:
        -----------
        recommendations : pd.DataFrame
            推奨銘柄のデータフレーム

        Returns:
        --------
        pd.DataFrame
            取引結果
        """
        if recommendations is None or recommendations.empty:
            logger.warning("推奨銘柄がありません")
            return None

        # 現在の日時
        now = datetime.now()

        # 推奨銘柄のティッカーリスト
        recommended_tickers = recommendations['Ticker'].tolist() if 'Ticker' in recommendations.columns else []

        # 1. 推奨されなくなった保有銘柄を売却
        trades = []
        for ticker in list(self.portfolio.keys()):
            if ticker not in recommended_tickers:
                position = self.portfolio[ticker]
                current_price = self._get_current_price(ticker, recommendations)

                if current_price > 0:
                    # 売却実行
                    trade = self.sell_stock(ticker, current_price, position['qty'], now)
                    if trade:
                        trades.append(trade)

        # 2. 投資配分を計算
        available_balance = self.balance
        allocations = self._calculate_allocations(recommendations, available_balance)

        # 3. 新規推奨銘柄を購入
        for ticker, allocation in allocations.items():
            if ticker not in self.portfolio and allocation > 0:
                current_price = self._get_current_price(ticker, recommendations)

                if current_price > 0:
                    # 購入可能な株数を計算（整数に切り下げ）
                    max_qty = int(allocation / (current_price * (1 + self.commission_rate)))

                    if max_qty > 0:
                        # 購入実行
                        trade = self.buy_stock(ticker, current_price, max_qty, now)
                        if trade:
                            trades.append(trade)

        # 取引結果をDataFrameに変換
        if trades:
            return pd.DataFrame(trades)
        else:
            logger.info("取引なし")
            return None

    def _get_current_price(self, ticker, recommendations=None):
        """
        現在の価格を取得

        Parameters:
        -----------
        ticker : str
            銘柄コード
        recommendations : pd.DataFrame
            推奨銘柄のデータフレーム（なければNone）

        Returns:
        --------
        float
            現在価格
        """
        # 推奨銘柄から価格を取得
        if recommendations is not None and not recommendations.empty:
            ticker_col = 'Ticker' if 'Ticker' in recommendations.columns else 'ticker'
            price_col = 'Current_Price' if 'Current_Price' in recommendations.columns else 'Close'

            if ticker_col in recommendations.columns and price_col in recommendations.columns:
                ticker_data = recommendations[recommendations[ticker_col] == ticker]
                if not ticker_data.empty:
                    return ticker_data[price_col].values[0]

        # ポートフォリオに含まれる場合は保有価格を返す
        if ticker in self.portfolio:
            return self.portfolio[ticker]["price"]

        # どちらも取得できない場合は0を返す
        return 0

    def _calculate_allocations(self, recommendations, available_balance):
        """
        投資配分を計算

        Parameters:
        -----------
        recommendations : pd.DataFrame
            推奨銘柄のデータフレーム
        available_balance : float
            利用可能な残高

        Returns:
        --------
        dict
            銘柄ごとの投資金額
        """
        # 期待リターンのカラム名
        return_col = 'Predicted_Change_Pct'
        if return_col not in recommendations.columns and 'predicted_return' in recommendations.columns:
            return_col = 'predicted_return'

        ticker_col = 'Ticker' if 'Ticker' in recommendations.columns else 'ticker'

        # 期待リターンの合計を計算
        if return_col in recommendations.columns:
            total_expected_return = recommendations[return_col].sum()
        else:
            # 期待リターンのカラムがない場合は均等配分
            total_expected_return = 0

        # 各銘柄の配分比率を計算
        allocations = {}

        if total_expected_return <= 0 or return_col not in recommendations.columns:
            # 期待リターンが0以下または列がない場合は均等配分
            per_ticker = available_balance / len(recommendations)
            for _, row in recommendations.iterrows():
                allocations[row[ticker_col]] = per_ticker
        else:
            # 期待リターンに比例した配分
            for _, row in recommendations.iterrows():
                ticker = row[ticker_col]
                expected_return = row[return_col]
                weight = expected_return / total_expected_return
                allocations[ticker] = available_balance * weight

        return allocations

    def get_portfolio_value(self, current_prices=None):
        """
        ポートフォリオの現在価値を計算

        Parameters:
        -----------
        current_prices : dict
            銘柄ごとの現在価格 {ticker: price}

        Returns:
        --------
        float
            ポートフォリオの現在価値
        """
        portfolio_value = self.balance

        for ticker, position in self.portfolio.items():
            # 現在価格を取得
            if current_prices and ticker in current_prices:
                price = current_prices[ticker]
            else:
                price = position['price']

            # 価値を計算
            portfolio_value += position['qty'] * price

        return portfolio_value

    def get_portfolio_summary(self, current_prices=None):
        """
        ポートフォリオの概要を取得

        Parameters:
        -----------
        current_prices : dict
            銘柄ごとの現在価格 {ticker: price}

        Returns:
        --------
        dict
            ポートフォリオの概要
        """
        portfolio_value = self.get_portfolio_value(current_prices)
        profit_loss = portfolio_value - self.initial_balance
        profit_loss_pct = (profit_loss / self.initial_balance) * 100 if self.initial_balance > 0 else 0

        return {
            'balance': self.balance,
            'portfolio': self.portfolio,
            'portfolio_value': portfolio_value,
            'initial_balance': self.initial_balance,
            'profit_loss': profit_loss,
            'profit_loss_pct': profit_loss_pct,
            'holdings_count': len(self.portfolio),
            'trade_count': len(self.trades),
            'mode': self.mode_manager.mode.value,
            'is_learning_enabled': self.mode_manager.is_learning_enabled,
            'timestamp': datetime.now().isoformat()
        }

    def save_results(self, tag=""):
        """
        取引結果を保存

        Parameters:
        -----------
        tag : str
            ファイル名に付けるタグ

        Returns:
        --------
        tuple
            (ポートフォリオファイルパス, 取引履歴ファイルパス)
        """
        # 現在の日時
        now = datetime.now()
        date_str = now.strftime('%Y%m%d')
        time_str = now.strftime('%H%M%S')

        # タグの処理
        if tag:
            if not tag.startswith('_'):
                tag = f"_{tag}"

        # ディレクトリ作成
        os.makedirs(self.results_dir, exist_ok=True)

        # ポートフォリオ概要を保存
        summary = self.get_portfolio_summary()
        summary_file = os.path.join(self.results_dir, f"portfolio{tag}_{date_str}_{time_str}.json")

        with open(summary_file, 'w') as f:
            json.dump(summary, f, indent=2, default=str)

        # 取引履歴を保存
        trades_file = None
        if self.trades:
            trades_df = pd.DataFrame(self.trades)
            trades_file = os.path.join(self.results_dir, f"trades{tag}_{date_str}_{time_str}.csv")
            trades_df.to_csv(trades_file, index=False)

        logger.info(f"結果を保存しました: {summary_file}" + (f", {trades_file}" if trades_file else ""))
        return (summary_file, trades_file)

    def reset(self):
        """
        トレーダーをリセット

        Returns:
        --------
        bool
            リセット成功時True
        """
        self.balance = self.initial_balance
        self.portfolio = {}
        self.trades = []
        self.prediction_history = []
        logger.info("トレーダーをリセットしました")
        return True

    # 予測関連のメソッド

    def record_prediction(self, ticker, predicted_value, actual_value=None, timestamp=None):
        """
        予測を記録

        Parameters:
        -----------
        ticker : str
            銘柄コード
        predicted_value : float
            予測値
        actual_value : float
            実際の値（わかっている場合）
        timestamp : datetime
            予測時刻（Noneの場合は現在時刻）

        Returns:
        --------
        dict
            記録した予測
        """
        if timestamp is None:
            timestamp = datetime.now()

        prediction = {
            'ticker': ticker,
            'predicted': predicted_value,
            'actual': actual_value,
            'timestamp': timestamp,
            'mode': self.mode_manager.mode.value
        }

        self.prediction_history.append(prediction)

        # 履歴が長くなりすぎないように制限
        max_history = 1000
        if len(self.prediction_history) > max_history:
            self.prediction_history = self.prediction_history[-max_history:]

        return prediction

    def update_prediction_actual(self, ticker, actual_value, timestamp=None):
        """
        予測の実際値を更新

        Parameters:
        -----------
        ticker : str
            銘柄コード
        actual_value : float
            実際の値
        timestamp : datetime
            更新時刻（Noneの場合は現在時刻）

        Returns:
        --------
        int
            更新された予測の数
        """
        if timestamp is None:
            timestamp = datetime.now()

        count = 0
        for prediction in self.prediction_history:
            if prediction['ticker'] == ticker and prediction['actual'] is None:
                prediction['actual'] = actual_value
                prediction['updated_at'] = timestamp
                count += 1

        return count

    def analyze_prediction_accuracy(self, days=None):
        """
        予測精度を分析

        Parameters:
        -----------
        days : int
            分析する日数（Noneの場合はすべての履歴）

        Returns:
        --------
        dict
            予測精度の分析結果
        """
        if not self.prediction_history:
            return {
                'accuracy': 0,
                'count': 0,
                'correct_count': 0,
                'error_rate': 0,
                'mean_error': 0
            }

        # 期間フィルタリング
        filtered_predictions = self.prediction_history
        if days is not None:
            cutoff_time = datetime.now() - timedelta(days=days)
            filtered_predictions = [p for p in self.prediction_history
                                   if p['timestamp'] > cutoff_time]

        # 実際値が記録されている予測のみ分析
        valid_predictions = [p for p in filtered_predictions if p['actual'] is not None]

        if not valid_predictions:
            return {
                'accuracy': 0,
                'count': 0,
                'correct_count': 0,
                'error_rate': 0,
                'mean_error': 0
            }

        # 方向性の正解率（上がる/下がると予測して実際にそうなったか）
        correct_count = sum(1 for p in valid_predictions
                            if (p['predicted'] > 0 and p['actual'] > 0) or
                               (p['predicted'] < 0 and p['actual'] < 0))

        accuracy = correct_count / len(valid_predictions)

        # 誤差率
        errors = [abs(p['predicted'] - p['actual']) for p in valid_predictions]
        mean_error = sum(errors) / len(errors)

        # 相対誤差率
        rel_errors = [abs((p['predicted'] - p['actual']) / p['actual'])
                     if p['actual'] != 0 else 1.0
                     for p in valid_predictions]
        error_rate = sum(rel_errors) / len(rel_errors)

        return {
            'accuracy': accuracy,
            'count': len(valid_predictions),
            'correct_count': correct_count,
            'error_rate': error_rate,
            'mean_error': mean_error,
            'analyzed_at': datetime.now().isoformat(),
            'days': days
        }

    def save_prediction_analysis(self):
        """
        予測分析結果を保存

        Returns:
        --------
        str
            保存したファイルパス
        """
        # 7日、30日、全期間の分析
        analyses = {
            '7days': self.analyze_prediction_accuracy(7),
            '30days': self.analyze_prediction_accuracy(30),
            'all': self.analyze_prediction_accuracy()
        }

        # 保存用データ作成
        data = {
            'timestamp': datetime.now().isoformat(),
            'mode': self.mode_manager.mode.value,
            'is_learning_enabled': self.mode_manager.is_learning_enabled,
            'analyses': analyses
        }

        # ファイル保存
        date_str = datetime.now().strftime('%Y%m%d')
        file_path = os.path.join(self.results_dir, f"prediction_analysis_{date_str}.json")

        with open(file_path, 'w') as f:
            json.dump(data, f, indent=2, default=str)

        logger.info(f"予測分析結果を保存しました: {file_path}")
        return file_path

    # 学習関連のメソッド

    def should_update_model(self):
        """
        モデルを更新すべきかどうかを判断

        Returns:
        --------
        bool
            更新すべき場合True
        """
        # テストモードの場合は更新しない
        if not self.mode_manager.is_learning_enabled:
            logger.info("テストモードのため、モデル更新をスキップします")
            return False

        # 予測履歴が少ない場合は更新しない
        if len(self.prediction_history) < 10:
            logger.info("予測履歴が少ないため、モデル更新をスキップします")
            return False

        # 予測精度を分析
        analysis = self.analyze_prediction_accuracy()
        accuracy = analysis['accuracy']

        # 精度が一定値を下回る場合に更新
        threshold = 0.6  # 60%
        if accuracy < threshold:
            logger.info(f"予測精度が閾値を下回ったため、モデルを更新します: {accuracy:.2f} < {threshold}")
            return True
        else:
            logger.info(f"予測精度が十分なため、モデル更新をスキップします: {accuracy:.2f} >= {threshold}")
            return False

    def update_model(self):
        """
        モデルを更新（実装は別途必要）

        Returns:
        --------
        bool
            更新成功時True
        """
        # テストモードの場合は更新しない
        if not self.mode_manager.is_learning_enabled:
            logger.info("テストモードのため、モデル更新をスキップします")
            return False

        logger.info("モデル更新を実行します")

        # TODO: ここにモデル更新処理を実装
        # - 予測履歴や取引履歴からデータを収集
        # - モデルの再学習を実行
        # - 新しいモデルを保存

        # 仮の実装
        logger.info("モデル更新が完了しました（模擬）")
        return True

    # 日次パフォーマンス関連

    def save_daily_performance(self):
        """
        日次パフォーマンスを保存

        Returns:
        --------
        str
            保存したファイルパス
        """
        date_str = datetime.now().strftime('%Y%m%d')

        # ポートフォリオ概要
        summary = self.get_portfolio_summary()

        # 予測精度
        prediction_analysis = self.analyze_prediction_accuracy()

        # 日次データ
        daily_data = {
            'date': date_str,
            'timestamp': datetime.now().isoformat(),
            'mode': self.mode_manager.mode.value,
            'is_learning_enabled': self.mode_manager.is_learning_enabled,
            'portfolio': summary,
            'prediction_analysis': prediction_analysis,
            'trade_count_today': len([t for t in self.trades if t['timestamp'].date() == datetime.now().date()])
        }

        # ファイル保存
        file_path = os.path.join(self.results_dir, f"daily_performance_{date_str}.json")

        with open(file_path, 'w') as f:
            json.dump(daily_data, f, indent=2, default=str)

        logger.info(f"日次パフォーマンスを保存しました: {file_path}")
        return file_path
