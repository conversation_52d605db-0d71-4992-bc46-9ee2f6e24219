#!/usr/bin/env python3
"""
取引モード定義と管理モジュール

テスト環境と本番環境を明確に分離するためのモード管理システム
"""

from enum import Enum
import os
import logging

logger = logging.getLogger(__name__)

class TradingMode(Enum):
    """取引モードを定義する列挙型"""
    TEST = "test"           # テストモード - 学習なし
    PRODUCTION = "production"  # 本番モード - 継続学習あり
    HYBRID = "hybrid"       # ハイブリッドモード - 特定条件下での学習

class TradingModeManager:
    """取引モードを管理するクラス"""

    def __init__(self, mode=TradingMode.TEST):
        """
        初期化

        Parameters:
        -----------
        mode : TradingMode
            取引モード
        """
        self.mode = mode
        self._results_dir = self._get_results_dir()

        # 結果ディレクトリが存在しない場合は作成
        os.makedirs(self._results_dir, exist_ok=True)

        logger.info(f"TradingModeManager initialized in {mode.value} mode")
        logger.info(f"Results directory: {self._results_dir}")

    def _get_results_dir(self):
        """
        モードに応じた結果ディレクトリを取得

        Returns:
        --------
        str
            結果ディレクトリパス
        """
        if self.mode == TradingMode.TEST:
            return "results/test"
        elif self.mode == TradingMode.PRODUCTION:
            return "results/production"
        elif self.mode == TradingMode.HYBRID:
            return "results/hybrid"
        else:
            # デフォルトはテストモード
            return "results/test"

    @property
    def is_test_mode(self):
        """
        テストモードかどうか

        Returns:
        --------
        bool
            テストモードの場合True
        """
        return self.mode == TradingMode.TEST

    @property
    def is_learning_enabled(self):
        """
        学習が有効かどうか

        Returns:
        --------
        bool
            学習が有効な場合True
        """
        return self.mode in [TradingMode.PRODUCTION, TradingMode.HYBRID]

    @property
    def results_dir(self):
        """
        結果ディレクトリ

        Returns:
        --------
        str
            結果ディレクトリパス
        """
        return self._results_dir

    def set_mode(self, mode):
        """
        モードを設定

        Parameters:
        -----------
        mode : TradingMode
            設定するモード

        Returns:
        --------
        bool
            設定成功時True
        """
        if isinstance(mode, TradingMode):
            self.mode = mode
            self._results_dir = self._get_results_dir()
            # 結果ディレクトリが存在しない場合は作成
            os.makedirs(self._results_dir, exist_ok=True)
            logger.info(f"Trading mode changed to {mode.value}")
            return True
        else:
            logger.error(f"Invalid trading mode: {mode}")
            return False

    def get_metadata(self):
        """
        モードのメタデータを取得

        Returns:
        --------
        dict
            メタデータ
        """
        return {
            "mode": self.mode.value,
            "is_test_mode": self.is_test_mode,
            "is_learning_enabled": self.is_learning_enabled,
            "results_dir": self.results_dir
        }
