#!/usr/bin/env python3
import yfinance as yf
import pandas as pd
import numpy as np
import time
import os
import random
import logging
from datetime import datetime, timedelta
from src.trading.data_cache import DataCache

logger = logging.getLogger(__name__)

class EnhancedDataCollector:
    """
    拡張データ収集クラス
    キャッシュシステムを活用して効率的にデータを収集する
    """
    def __init__(self, output_dir="data", interval="1h", period="3mo"):
        """
        初期化
        
        Parameters:
        -----------
        output_dir : str
            出力ディレクトリ
        interval : str
            データ間隔（例: 1h, 1d）
        period : str
            データ期間（例: 1mo, 3mo, 1y）
        """
        # 日経225のティッカーリスト
        self.tickers = [
            '1332.T', '1333.T', '1376.T', '1605.T', '1721.T', '1801.T', '1802.T', '1803.T', '1808.T', '1812.T',
            '1925.T', '1928.T', '1963.T', '2002.T', '2269.T', '2501.T', '2502.T', '2503.T', '2531.T', '2768.T',
            '2801.T', '2802.T', '2871.T', '2914.T', '3003.T', '3086.T', '3101.T', '3103.T', '3289.T', '3382.T',
            '3401.T', '3402.T', '3405.T', '3407.T', '3436.T', '3861.T', '3863.T', '4004.T', '4005.T', '4021.T',
            '4042.T', '4043.T', '4061.T', '4063.T', '4151.T', '4183.T', '4188.T', '4208.T', '4324.T', '4452.T',
            '4502.T', '4503.T', '4506.T', '4507.T', '4519.T', '4523.T', '4568.T', '4578.T', '4689.T', '4704.T',
            '4751.T', '4901.T', '4911.T', '5020.T', '5101.T', '5108.T', '5201.T', '5202.T', '5214.T', '5232.T',
            '5233.T', '5301.T', '5332.T', '5401.T', '5406.T', '5411.T', '5541.T', '5631.T', '5703.T', '5706.T',
            '5707.T', '5711.T', '5713.T', '5801.T', '5802.T', '5803.T', '5901.T', '5902.T', '6005.T', '6028.T',
            '6103.T', '6113.T', '6135.T', '6141.T', '6146.T', '6201.T', '6203.T', '6208.T', '6268.T', '6273.T',
            '6301.T', '6302.T', '6305.T', '6326.T', '6361.T', '6366.T', '6367.T', '6370.T', '6378.T', '6383.T',
            '6407.T', '6417.T', '6418.T', '6448.T', '6455.T', '6460.T', '6465.T', '6471.T', '6472.T', '6473.T',
            '6474.T', '6479.T', '6481.T', '6501.T', '6503.T', '6504.T', '6506.T', '6508.T', '6586.T', '6588.T',
            '6594.T', '6619.T', '6645.T', '6674.T', '6701.T', '6702.T', '6723.T', '6724.T', '6727.T', '6752.T',
            '6753.T', '6758.T', '6762.T', '6770.T', '6841.T', '6857.T', '6902.T', '6952.T', '6954.T', '6963.T',
            '6981.T', '6988.T', '7003.T', '7011.T', '7012.T', '7201.T', '7202.T', '7203.T', '7231.T', '7238.T',
            '7240.T', '7261.T', '7267.T', '7269.T', '7270.T', '7272.T', '7731.T', '7733.T', '7735.T', '7751.T',
            '7752.T', '7762.T', '7832.T', '7911.T', '7912.T', '7951.T', '7974.T', '8001.T', '8002.T', '8015.T',
            '8031.T', '8035.T', '8053.T', '8058.T', '8233.T', '8252.T', '8253.T', '8267.T', '8303.T', '8304.T',
            '8306.T', '8308.T', '8309.T', '8316.T', '8331.T', '8354.T', '8355.T', '8411.T', '8601.T', '8604.T',
            '8628.T', '8630.T', '8725.T', '8750.T', '8766.T', '8795.T', '8801.T', '8802.T', '8804.T', '8830.T',
            '9001.T', '9005.T', '9007.T', '9008.T', '9009.T', '9020.T', '9021.T', '9022.T', '9062.T', '9064.T',
            '9101.T', '9104.T', '9107.T', '9202.T', '9301.T', '9432.T', '9433.T', '9434.T', '9501.T', '9502.T',
            '9503.T', '9531.T', '9532.T', '9602.T', '9613.T', '9735.T', '9766.T', '9983.T', '9984.T'
        ]
        
        self.output_dir = output_dir
        self.interval = interval
        self.period = period
        
        # データキャッシュを初期化
        self.cache = DataCache(cache_dir=os.path.join(output_dir, "cache"))
        
        # APIリクエスト制限管理
        self.request_count = 0
        self.last_request_time = 0
        
        # 出力ディレクトリの作成
        os.makedirs(output_dir, exist_ok=True)
        
        # 全データ保存ファイル
        self.all_data_file = os.path.join(output_dir, "nikkei225_data.csv")
        self.cleaned_data_file = os.path.join(output_dir, "nikkei225_cleaned.csv")
    
    def fetch_data(self, batch_size=5, retry_attempts=3, sleep_time=2):
        """
        全銘柄のデータを取得
        キャッシュを活用して効率的に取得する
        
        Parameters:
        -----------
        batch_size : int
            バッチサイズ
        retry_attempts : int
            リトライ回数
        sleep_time : int
            バッチ間のスリープ時間（秒）
            
        Returns:
        --------
        pd.DataFrame
            取得したデータ
        """
        logger.info(f"{len(self.tickers)}銘柄のデータを取得します")
        
        all_data = []
        failed_tickers = []
        
        # ティッカーをシャッフルして毎回異なる順序で取得
        shuffled_tickers = self.tickers.copy()
        random.shuffle(shuffled_tickers)
        
        # バッチ処理
        for i in range(0, len(shuffled_tickers), batch_size):
            batch = shuffled_tickers[i:i+batch_size]
            
            # 各ティッカーについてキャッシュをチェック
            batch_data = []
            tickers_to_fetch = []
            
            for ticker in batch:
                cached_data = self.cache.get_cached_data(ticker, self.interval, self.period)
                if cached_data is not None:
                    logger.info(f"キャッシュからデータを使用: {ticker}")
                    batch_data.append(cached_data)
                else:
                    tickers_to_fetch.append(ticker)
            
            # キャッシュにないティッカーのみ取得
            if tickers_to_fetch:
                # APIリクエスト制限を考慮
                self._respect_rate_limit()
                
                attempts = 0
                success = False
                
                while attempts < retry_attempts and not success:
                    try:
                        logger.info(f"バッチ {i//batch_size + 1}/{len(self.tickers)//batch_size + 1} をダウンロード: {tickers_to_fetch}")
                        
                        # リクエストカウントを更新
                        self.request_count += 1
                        self.last_request_time = time.time()
                        
                        # データをダウンロード
                        fetched_data = yf.download(
                            tickers_to_fetch, 
                            period=self.period, 
                            interval=self.interval,
                            group_by='ticker',
                            progress=False
                        )
                        
                        # データを処理
                        if not isinstance(fetched_data.columns, pd.MultiIndex):
                            # 1つのティッカーの場合
                            ticker = tickers_to_fetch[0]
                            if not fetched_data.empty:
                                processed_data = self._process_single_ticker_data(fetched_data, ticker)
                                batch_data.append(processed_data)
                                # キャッシュに保存
                                self.cache.save_to_cache(ticker, processed_data, self.interval)
                        else:
                            # 複数ティッカーの場合
                            for ticker in tickers_to_fetch:
                                if ticker in fetched_data.columns.levels[0]:
                                    ticker_data = fetched_data[ticker].copy()
                                    if not ticker_data.empty:
                                        processed_data = self._process_single_ticker_data(ticker_data, ticker)
                                        batch_data.append(processed_data)
                                        # キャッシュに保存
                                        self.cache.save_to_cache(ticker, processed_data, self.interval)
                                else:
                                    logger.warning(f"データが取得できませんでした: {ticker}")
                                    failed_tickers.append(ticker)
                        
                        success = True
                        
                    except Exception as e:
                        logger.error(f"バッチ {tickers_to_fetch} の取得エラー: {str(e)}")
                        attempts += 1
                        # エクスポネンシャルバックオフ
                        backoff_time = sleep_time * (2 ** attempts)
                        logger.info(f"リトライ {attempts}/{retry_attempts}, {backoff_time}秒待機...")
                        time.sleep(backoff_time)
                        
                if not success:
                    failed_tickers.extend(tickers_to_fetch)
            
            # バッチデータを結合
            all_data.extend(batch_data)
            
            # バッチ間のスリープ
            time.sleep(sleep_time)
        
        # 失敗したティッカーを報告
        if failed_tickers:
            logger.warning(f"{len(failed_tickers)}銘柄のデータ取得に失敗: {failed_tickers}")
            
        if all_data:
            # 全データを結合
            combined_data = pd.concat(all_data, ignore_index=True)
            
            # ソート
            combined_data = combined_data.sort_values(['Ticker', 'Datetime'])
            
            # CSVに保存
            combined_data.to_csv(self.all_data_file, index=False)
            logger.info(f"全データを保存しました: {self.all_data_file}")
            return combined_data
        else:
            logger.error("データが取得できませんでした")
            return pd.DataFrame()
    
    def _respect_rate_limit(self):
        """APIレート制限を尊重"""
        # 1分間に最大300リクエスト
        MAX_REQUESTS_PER_MINUTE = 300
        
        # 前回のリクエストからの経過時間
        elapsed = time.time() - self.last_request_time
        
        # リクエストカウントをリセット（1分経過）
        if elapsed > 60:
            self.request_count = 0
        
        # リクエスト数が制限に近づいたら待機
        if self.request_count > MAX_REQUESTS_PER_MINUTE * 0.8:  # 80%以上で待機
            wait_time = max(60 - elapsed, 0) + 1  # 残り時間 + 1秒
            logger.info(f"レート制限に近づいています。{wait_time:.1f}秒待機...")
            time.sleep(wait_time)
            self.request_count = 0
    
    def _process_single_ticker_data(self, df, ticker):
        """
        単一銘柄のデータを処理
        
        Parameters:
        -----------
        df : pd.DataFrame
            銘柄のデータ
        ticker : str
            銘柄コード
            
        Returns:
        --------
        pd.DataFrame
            処理済みデータ
        """
        # インデックスをリセットしてDatetimeをカラムに
        df = df.reset_index()
        
        # ティッカーカラムを追加
        df['Ticker'] = ticker
        
        # 欠損値を処理（前方補完、後方補完）
        df = df.fillna(method='ffill').fillna(method='bfill')
        
        # 残りの欠損値を削除
        df = df.dropna()
        
        # データ型を確保
        if 'Volume' in df.columns:
            df['Volume'] = df['Volume'].astype(np.int64)
            
        # カラムを選択・並べ替え
        columns = ['Datetime', 'Ticker', 'Open', 'High', 'Low', 'Close']
        if 'Adj Close' in df.columns:
            columns.append('Adj Close')
        if 'Volume' in df.columns:
            columns.append('Volume')
            
        # 必要なカラムが存在することを確認
        for col in columns:
            if col not in df.columns:
                if col == 'Adj Close':
                    df[col] = df['Close']
                elif col == 'Volume':
                    df[col] = 0
        
        df = df[columns]
        
        return df
    
    def clean_data(self):
        """
        収集したデータをクリーニング
        
        Returns:
        --------
        pd.DataFrame
            クリーニング済みデータ
        """
        if not os.path.exists(self.all_data_file):
            logger.error(f"データファイルが見つかりません: {self.all_data_file}")
            return pd.DataFrame()
            
        # データを読み込み
        df = pd.read_csv(self.all_data_file, parse_dates=['Datetime'])
        
        # 共通クリーニングメソッドを使用
        return self._clean_dataframe(df)
    
    def clean_data_from_df(self, df):
        """
        提供されたDataFrameを直接クリーニング
        
        Parameters:
        -----------
        df : pd.DataFrame
            クリーニングするDataFrame
            
        Returns:
        --------
        pd.DataFrame
            クリーニング済みDataFrame
        """
        # 入力を変更しないようにコピー
        df = df.copy()
        
        # Datetimeが正しく解析されていることを確認
        if 'Datetime' in df.columns and not pd.api.types.is_datetime64_any_dtype(df['Datetime']):
            df['Datetime'] = pd.to_datetime(df['Datetime'])
            
        # 共通クリーニングメソッドを使用
        return self._clean_dataframe(df)
    
    def _clean_dataframe(self, df):
        """
        DataFrameのクリーニング共通ロジック
        
        Parameters:
        -----------
        df : pd.DataFrame
            クリーニングするDataFrame
            
        Returns:
        --------
        pd.DataFrame
            クリーニング済みDataFrame
        """
        # 欠損値を処理
        for col in ['Open', 'High', 'Low', 'Close', 'Volume']:
            if col in df.columns:
                # ティッカーグループごとに欠損値を補完
                df[col] = df.groupby('Ticker')[col].transform(
                    lambda x: x.fillna(method='ffill').fillna(method='bfill')
                )
        
        # 外れ値を処理（Zスコア法）
        for col in ['Open', 'High', 'Low', 'Close']:
            if col in df.columns:
                # ティッカーグループごとにZスコアを計算
                df[f'{col}_zscore'] = df.groupby('Ticker')[col].transform(
                    lambda x: (x - x.mean()) / x.std()
                )
                
                # 外れ値（|z| > 3）をグループの中央値で置換
                mask = df[f'{col}_zscore'].abs() > 3
                df.loc[mask, col] = df.loc[mask, 'Ticker'].map(
                    df.groupby('Ticker')[col].median()
                )
                
                # Zスコアカラムを削除
                df = df.drop(f'{col}_zscore', axis=1)
        
        # ティッカーと日時でソート
        df = df.sort_values(['Ticker', 'Datetime'])
        
        # クリーニング済みデータを保存
        df.to_csv(self.cleaned_data_file, index=False)
        logger.info(f"クリーニング済みデータを保存しました: {self.cleaned_data_file}")
        
        return df
    
    def fetch_specific_tickers(self, tickers, batch_size=1, retry_attempts=3, sleep_time=5):
        """
        特定の銘柄のデータのみを取得
        
        Parameters:
        -----------
        tickers : list
            取得する銘柄のリスト
        batch_size : int
            バッチサイズ
        retry_attempts : int
            リトライ回数
        sleep_time : int
            バッチ間のスリープ時間（秒）
            
        Returns:
        --------
        pd.DataFrame
            取得したデータ
        """
        logger.info(f"{len(tickers)}銘柄の特定データを取得します")
        
        all_data = []
        failed_tickers = []
        
        # バッチ処理
        for i in range(0, len(tickers), batch_size):
            batch = tickers[i:i+batch_size]
            
            # 各ティッカーについてキャッシュをチェック
            batch_data = []
            tickers_to_fetch = []
            
            for ticker in batch:
                cached_data = self.cache.get_cached_data(ticker, self.interval, self.period)
                if cached_data is not None:
                    logger.info(f"キャッシュからデータを使用: {ticker}")
                    batch_data.append(cached_data)
                else:
                    tickers_to_fetch.append(ticker)
            
            # キャッシュにないティッカーのみ取得
            if tickers_to_fetch:
                # APIリクエスト制限を考慮
                self._respect_rate_limit()
                
                attempts = 0
                success = False
                
                while attempts < retry_attempts and not success:
                    try:
                        logger.info(f"バッチ {i//batch_size + 1}/{len(tickers)//batch_size + 1} をダウンロード: {tickers_to_fetch}")
                        
                        # リクエストカウントを更新
                        self.request_count += 1
                        self.last_request_time = time.time()
                        
                        # データをダウンロード
                        fetched_data = yf.download(
                            tickers_to_fetch, 
                            period=self.period, 
                            interval=self.interval,
                            group_by='ticker',
                            progress=False
                        )
                        
                        # データを処理
                        if not isinstance(fetched_data.columns, pd.MultiIndex):
                            # 1つのティッカーの場合
                            ticker = tickers_to_fetch[0]
                            if not fetched_data.empty:
                                processed_data = self._process_single_ticker_data(fetched_data, ticker)
                                batch_data.append(processed_data)
                                # キャッシュに保存
                                self.cache.save_to_cache(ticker, processed_data, self.interval)
                        else:
                            # 複数ティッカーの場合
                            for ticker in tickers_to_fetch:
                                if ticker in fetched_data.columns.levels[0]:
                                    ticker_data = fetched_data[ticker].copy()
                                    if not ticker_data.empty:
                                        processed_data = self._process_single_ticker_data(ticker_data, ticker)
                                        batch_data.append(processed_data)
                                        # キャッシュに保存
                                        self.cache.save_to_cache(ticker, processed_data, self.interval)
                                else:
                                    logger.warning(f"データが取得できませんでした: {ticker}")
                                    failed_tickers.append(ticker)
                        
                        success = True
                        
                    except Exception as e:
                        logger.error(f"バッチ {tickers_to_fetch} の取得エラー: {str(e)}")
                        attempts += 1
                        # エクスポネンシャルバックオフ
                        backoff_time = sleep_time * (2 ** attempts)
                        logger.info(f"リトライ {attempts}/{retry_attempts}, {backoff_time}秒待機...")
                        time.sleep(backoff_time)
                        
                if not success:
                    failed_tickers.extend(tickers_to_fetch)
            
            # バッチデータを結合
            all_data.extend(batch_data)
            
            # バッチ間のスリープ
            time.sleep(sleep_time)
        
        # 失敗したティッカーを報告
        if failed_tickers:
            logger.warning(f"{len(failed_tickers)}銘柄のデータ取得に失敗: {failed_tickers}")
            
        if all_data:
            # 全データを結合
            combined_data = pd.concat(all_data, ignore_index=True)
            
            # ソート
            combined_data = combined_data.sort_values(['Ticker', 'Datetime'])
            
            return combined_data
        else:
            logger.error("データが取得できませんでした")
            return pd.DataFrame()
