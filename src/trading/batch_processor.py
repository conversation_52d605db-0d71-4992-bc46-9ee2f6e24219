#!/usr/bin/env python3
# Force CPU only mode first, before any TensorFlow imports
import os
# Force TensorFlow to use CPU only
os.environ["CUDA_VISIBLE_DEVICES"] = "-1"

import sys
import logging
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import time
import threading
import queue
import pytz
import schedule
import json

# sys.pathを最初に設定
import sys
root_dir = os.path.abspath(os.path.join(os.path.dirname(__file__), '../..'))
sys.path.insert(0, root_dir)

# 絶対インポートに変更
from src.trading.enhanced_data_collector import EnhancedDataCollector
from src.trading.virtual_trader import VirtualTrader
from src.trading.trading_strategy import TradingStrategy
from src.feature_engineering import FeatureEngineer
from src.model import NikkeiAIModel

# Create necessary directories
os.makedirs("logs", exist_ok=True)
os.makedirs("results", exist_ok=True)
os.makedirs("data", exist_ok=True)
os.makedirs("data/cache", exist_ok=True)

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(os.path.join("logs", f"batch_processor_{datetime.now().strftime('%Y%m%d')}.log")),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class EnhancedBatchProcessor:
    """
    拡張バッチプロセッサー
    仮想取引モードを組み込んだバッチ処理を行う
    """
    def __init__(self, trading_mode=False, initial_balance=1000000, max_stocks=5, test_data=False):
        """
        初期化
        
        Parameters:
        -----------
        trading_mode : bool
            取引モード（Trueの場合は実際の取引を行う、Falseの場合は仮想取引）
        initial_balance : float
            初期資金
        max_stocks : int
            最大銘柄数
        test_data : bool
            テストデータを使用する場合はTrue
        """
        self.trading_mode = trading_mode
        self.initial_balance = initial_balance
        self.max_stocks = max_stocks
        self.test_data = test_data
        
        # 日本のタイムゾーン
        self.jst_timezone = pytz.timezone('Asia/Tokyo')
        
        # 市場時間
        self.market_open_time = "09:00"  # JST
        self.market_close_time = "15:00"  # JST
        
        # モデル
        self.model = None
        
        # 取引戦略
        self.strategy = TradingStrategy(max_stocks=max_stocks)
        
        # 仮想トレーダー
        self.virtual_trader = VirtualTrader(
            initial_balance=initial_balance,
            commission_rate=0.0,  # au kabu APIを利用するため手数料なし
            results_dir="results"
        )
        
        # データ収集
        self.data_collector = EnhancedDataCollector(
            output_dir="data",
            interval="1h",
            period="3mo"
        )
        
        # 最後の予測結果
        self.last_predictions = None
        
        # パフォーマンス記録
        self.performance_tracker = {}
        
        # ディレクトリの初期化
        self._initialize_directories()
    
    def _initialize_directories(self):
        """ディレクトリの初期化"""
        directories = ["data", "models", "results", "logs", "data/cache"]
        for directory in directories:
            os.makedirs(directory, exist_ok=True)
    
    def is_market_open(self):
        """
        市場が開いているかをチェック
        
        Returns:
        --------
        bool
            市場が開いている場合はTrue、閉じている場合はFalse
        """
        now = datetime.now(self.jst_timezone)
        
        # 土日はスキップ
        if now.weekday() >= 5:  # 5=土曜日, 6=日曜日
            return False
        
        # 市場時間内かチェック
        market_open = datetime.strptime(self.market_open_time, "%H:%M").time()
        market_close = datetime.strptime(self.market_close_time, "%H:%M").time()
        
        return market_open <= now.time() <= market_close
    
    def setup_model(self):
        """
        モデルのセットアップ
        必要に応じてトレーニングを行う
        
        Returns:
        --------
        bool
            セットアップ成功の場合はTrue、失敗の場合はFalse
        """
        logger.info("モデルをセットアップしています")
        
        # 既存のモデルがあるかチェック
        if os.path.exists(os.path.join("models", "nikkei_all_lstm.h5")):
            logger.info("既存のモデルを読み込みます")
            self.model = NikkeiAIModel(model_dir="models", data_dir="data")
            return True
        
        # 新しいモデルをトレーニング
        logger.info("新しいモデルをトレーニングします")
        
        # 特徴量エンジニアリング
        feature_engineer = FeatureEngineer(
            input_file=os.path.join("data", "nikkei225_cleaned.csv"),
            output_dir="data"
        )
        processed_data = feature_engineer.process_and_save(include_market_features=False)
        
        # モデルの初期化
        self.model = NikkeiAIModel(model_dir="models", data_dir="data")
        
        # 特徴量の準備
        prepared_data = self.model.prepare_features(
            processed_data,
            ticker=None,  # 全銘柄でトレーニング
            target_col='Close',
            sequence_length=24,
            prediction_horizon=1,  # 1時間先を予測
            train_ratio=0.8
        )
        
        # モデルのトレーニング
        results = self.model.train_models(
            prepared_data,
            ticker="nikkei_all",
            epochs=50,
            batch_size=32
        )
        
        # モデルのメタデータを保存
        self.model.save_model_metadata("nikkei_all", results)
        
        return True
    
    def run_hourly_batch(self):
        """
        1時間ごとのバッチ処理を実行
        
        Returns:
        --------
        pd.DataFrame
            選択された銘柄
        """
        current_time = datetime.now(self.jst_timezone)
        logger.info(f"1時間バッチを実行しています: {current_time}")
        
        # 市場が開いているかチェック（テストモードでは常に実行）
        # if not self.is_market_open():
        #     logger.info("市場が閉じています。バッチをスキップします。")
        #     return None
        
        try:
            # 1. データ収集
            logger.info("データを収集しています")
            # テストデータモードの場合
            if self.test_data:
                # テスト用のサンプルデータを使用
                sample_data_path = os.path.join("data", "nikkei225_sample_data.csv")
                if os.path.exists(sample_data_path):
                    logger.info(f"テストデータを使用します: {sample_data_path}")
                    
                    # サンプルデータを nikkei225_cleaned.csv としてコピー
                    cleaned_data_path = os.path.join("data", "nikkei225_cleaned.csv")
                    
                    # データをコピー
                    sample_df = pd.read_csv(sample_data_path)
                    sample_df.to_csv(cleaned_data_path, index=False)
                    logger.info(f"サンプルデータをコピーしました: {cleaned_data_path}")
                else:
                    logger.warning("テストデータファイルが見つかりません。既存のデータまたはデータ取得を試みます。")
                    if os.path.exists(os.path.join("data", "nikkei225_cleaned.csv")):
                        logger.info("既存のデータを使用します")
                    else:
                        logger.info("データを取得します")
                        self.data_collector.fetch_data(batch_size=5)
                        self.data_collector.clean_data()
            # 通常モード
            elif os.path.exists(os.path.join("data", "nikkei225_cleaned.csv")):
                logger.info("既存のデータを使用します")
            else:
                logger.info("データを取得します")
                self.data_collector.fetch_data(batch_size=5)
                self.data_collector.clean_data()
            
            # 2. 特徴量エンジニアリング
            logger.info("特徴量を計算しています")
            feature_engineer = FeatureEngineer(
                input_file=os.path.join("data", "nikkei225_cleaned.csv"),
                output_dir="data"
            )
            processed_data = feature_engineer.process_and_save(include_market_features=False)
            
            # 3. 全銘柄の予測
            logger.info("予測を行っています")
            top_stocks = self.model.evaluate_and_select_stocks(
                processed_data,
                prediction_horizon=1,  # 1時間先を予測
                top_n=self.max_stocks  # 上位N銘柄を選択
            )
            
            # 4. 予測結果を保存
            logger.info("予測結果を保存しています")
            results_file = os.path.join("results", f"top_stocks_{current_time.strftime('%Y%m%d_%H%M')}.csv")
            top_stocks.to_csv(results_file, index=False)
            
            # 5. 仮想取引を実行
            if top_stocks is not None and not top_stocks.empty:
                logger.info("仮想取引を実行しています")
                trade_results = self.virtual_trader.execute_trades(top_stocks)
                
                # 取引結果を保存
                if trade_results is not None:
                    trade_results_file = os.path.join("results", f"trades_{current_time.strftime('%Y%m%d_%H%M')}.csv")
                    trade_results.to_csv(trade_results_file, index=False)
                
                # ポートフォリオ概要を保存
                portfolio_summary = self.virtual_trader.get_portfolio_summary()
                portfolio_file = os.path.join("results", f"portfolio_{current_time.strftime('%Y%m%d_%H%M')}.json")
                with open(portfolio_file, 'w') as f:
                    json.dump(portfolio_summary, f, indent=2, default=str)
                
                # パフォーマンスを記録
                self._track_performance(top_stocks, portfolio_summary)
                
                # 最後の予測を保存
                self.last_predictions = top_stocks
            else:
                logger.warning("予測結果がありません")
            
            # 6. パフォーマンスレポートを生成
            self._generate_performance_report()
            
            logger.info(f"バッチ完了。上位{self.max_stocks}銘柄: {', '.join(top_stocks['Ticker'].tolist()) if top_stocks is not None and not top_stocks.empty else '銘柄なし'}")
            return top_stocks
            
        except Exception as e:
            logger.error(f"バッチ処理エラー: {str(e)}")
            import traceback
            logger.error(traceback.format_exc())
            return None
    
    def _track_performance(self, predictions, portfolio_summary):
        """
        パフォーマンスを記録
        
        Parameters:
        -----------
        predictions : pd.DataFrame
            予測結果
        portfolio_summary : dict
            ポートフォリオ概要
        """
        now = datetime.now(self.jst_timezone)
        date_str = now.strftime('%Y-%m-%d')
        
        # 日次パフォーマンスを初期化
        if date_str not in self.performance_tracker:
            self.performance_tracker[date_str] = {
                'predictions': [],
                'portfolio_value': [],
                'profit_loss': [],
                'profit_loss_pct': []
            }
        
        # 予測を記録
        if predictions is not None and not predictions.empty:
            for _, row in predictions.iterrows():
                self.performance_tracker[date_str]['predictions'].append({
                    'Ticker': row['Ticker'],
                    'Current_Price': row['Current_Price'],
                    'Predicted_Price': row['Predicted_Price'],
                    'Predicted_Change_Pct': row['Predicted_Change_Pct'],
                    'Timestamp': now.isoformat()
                })
        
        # ポートフォリオ価値を記録
        self.performance_tracker[date_str]['portfolio_value'].append({
            'Value': portfolio_summary['portfolio_value'],
            'Timestamp': now.isoformat()
        })
        
        # 損益を記録
        self.performance_tracker[date_str]['profit_loss'].append({
            'Value': portfolio_summary['profit_loss'],
            'Timestamp': now.isoformat()
        })
        
        # 損益率を記録
        self.performance_tracker[date_str]['profit_loss_pct'].append({
            'Value': portfolio_summary['profit_loss_pct'],
            'Timestamp': now.isoformat()
        })
    
    def _generate_performance_report(self):
        """パフォーマンスレポートを生成"""
        now = datetime.now(self.jst_timezone)
        date_str = now.strftime('%Y-%m-%d')
        
        # パフォーマンスデータがない場合はスキップ
        if date_str not in self.performance_tracker:
            return
        
        # 日次パフォーマンスを計算
        daily_performance = {
            'Date': date_str,
            'Initial_Balance': self.initial_balance,
            'Current_Portfolio_Value': self.virtual_trader.get_portfolio_value(),
            'Profit_Loss': self.virtual_trader.get_portfolio_value() - self.initial_balance,
            'Profit_Loss_Pct': (self.virtual_trader.get_portfolio_value() - self.initial_balance) / self.initial_balance * 100,
            'Num_Trades': len(self.virtual_trader.trade_history),
            'Timestamp': now.isoformat()
        }
        
        # パフォーマンスレポートを保存
        performance_file = os.path.join("results", f"performance_{date_str}.json")
        with open(performance_file, 'w') as f:
            json.dump(daily_performance, f, indent=2, default=str)
        
        logger.info(f"パフォーマンスレポートを保存しました: {performance_file}")
        logger.info(f"日次パフォーマンス: 現在価値={daily_performance['Current_Portfolio_Value']:.2f}円, 損益={daily_performance['Profit_Loss']:.2f}円 ({daily_performance['Profit_Loss_Pct']:.2f}%)")
    
    def schedule_jobs(self):
        """ジョブをスケジュール"""
        logger.info("ジョブをスケジュールしています")
        
        # モデルのセットアップ
        self.setup_model()
        
        # 市場時間中に1時間ごとにジョブをスケジュール
        schedule.every().hour.at(":00").do(self.run_hourly_batch)
        
        # 起動時にも実行
        self.run_hourly_batch()
        
        logger.info("ジョブがスケジュールされました")
        
        # スケジューラを実行
        while True:
            schedule.run_pending()
            time.sleep(1)
    
    def shutdown(self):
        """シャットダウン処理"""
        logger.info("シャットダウンしています")
        
        # 取引結果を保存
        self.virtual_trader.save_results()
        
        logger.info("シャットダウンが完了しました")

def main():
    """メイン関数"""
    logger.info("バッチプロセッサーを開始しています")
    
    # コマンドライン引数の解析
    import argparse
    parser = argparse.ArgumentParser(description='Nikkei 225 AI Trading Batch Processor')
    parser.add_argument('--trading-mode', action='store_true', help='実際の取引を行う（デフォルトは仮想取引）')
    parser.add_argument('--initial-balance', type=float, default=1000000, help='初期資金')
    parser.add_argument('--max-stocks', type=int, default=5, help='最大銘柄数')
    parser.add_argument('--test-data', action='store_true', help='テスト用の生成データを使用')
    args = parser.parse_args()
    
    processor = EnhancedBatchProcessor(
        trading_mode=args.trading_mode,
        initial_balance=args.initial_balance,
        max_stocks=args.max_stocks,
        test_data=args.test_data
    )
    
    if args.test_data:
        logger.info("テストデータモードで実行します")
    
    try:
        processor.schedule_jobs()
    except KeyboardInterrupt:
        logger.info("キーボード割り込みを受信しました。シャットダウンします。")
        processor.shutdown()
    except Exception as e:
        logger.error(f"バッチプロセッサーエラー: {str(e)}")
        processor.shutdown()
        raise

if __name__ == "__main__":
    main()
