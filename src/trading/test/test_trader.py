#!/usr/bin/env python3
"""
テスト環境用取引モジュール

テスト用に特化した取引機能を提供するクラス
継続学習を行わず、結果がフィードバックシステムに取り込まれないようにする
"""

import os
import json
import logging
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import random

from src.trading.auto_trader.auto_trader import AutoTrader
from src.trading.auto_trader.trading_modes import TradingMode

logger = logging.getLogger(__name__)

class TestTrader(AutoTrader):
    """
    テスト環境用取引クラス

    テスト用に特化した取引機能を提供し、
    継続学習を行わず、結果がフィードバックシステムに取り込まれないようにする
    """

    def __init__(self,
                 initial_balance=1000000,
                 commission_rate=0.0,  # テスト環境では手数料ゼロがデフォルト
                 max_positions=10,
                 analysis_days=7,
                 use_random_data=False,
                 volatility=0.02,
                 test_days=30):
        """
        初期化

        Parameters:
        -----------
        initial_balance : float
            初期資金
        commission_rate : float
            取引手数料率
        max_positions : int
            最大保有銘柄数
        analysis_days : int
            パフォーマンス分析期間（日）
        use_random_data : bool
            ランダムデータを使用するかどうか
        volatility : float
            ランダムデータの価格変動率
        test_days : int
            テスト期間（日）
        """
        # 親クラスの初期化（強制的にTESTモード）
        super().__init__(
            initial_balance=initial_balance,
            commission_rate=commission_rate,
            mode=TradingMode.TEST,  # 強制的にTESTモード
            max_positions=max_positions,
            analysis_days=analysis_days
        )

        # テスト用の追加パラメータ
        self.use_random_data = use_random_data
        self.volatility = volatility
        self.test_days = test_days
        
        # 予測記録用の辞書
        self.predictions = {}
        
        # 日次収益記録用の辞書
        self.daily_results = {}

        # テスト用メタデータ
        self.test_metadata = {
            "test_id": f"test_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
            "test_type": "random" if use_random_data else "real",
            "start_time": datetime.now().isoformat(),
            "description": f"Test trader with {'random' if use_random_data else 'real'} data",
            "volatility": volatility if use_random_data else None,
            "test_days": test_days
        }

        logger.info(f"TestTrader initialized in TEST mode")
        logger.info(f"Using {'random' if use_random_data else 'real'} data")
        logger.info(f"Test ID: {self.test_metadata['test_id']}")

    def generate_random_market_data(self, tickers, days=None):
        """
        ランダムな市場データを生成

        Parameters:
        -----------
        tickers : list
            銘柄リスト
        days : int
            日数（指定しない場合はself.test_days）

        Returns:
        --------
        pd.DataFrame
            生成されたデータフレーム
        """
        if days is None:
            days = self.test_days

        logger.info(f"Generating random market data for {len(tickers)} tickers over {days} days")

        # 現在日時から過去days日分の日付を生成
        end_date = datetime.now()
        start_date = end_date - timedelta(days=days)
        date_range = pd.date_range(start=start_date, end=end_date, freq='D')

        all_data = []

        for ticker in tickers:
            # 基本価格の設定
            base_price = random.uniform(1000, 10000)  # 1000円〜10000円の範囲でランダム

            # ランダムウォークで株価生成
            np.random.seed(hash(ticker) % 2**32)  # ティッカーごとに異なるシードを設定

            # 日次リターンを生成（ボラティリティと弱いトレンドを加える）
            trend = random.uniform(-0.001, 0.002)  # わずかなトレンド
            daily_returns = np.random.normal(trend, self.volatility, len(date_range))

            # 各日の価格を計算
            prices = [base_price]
            for ret in daily_returns:
                prices.append(prices[-1] * (1 + ret))
            prices = prices[1:]  # 初期値を除外

            # 株価データの生成
            for i, date in enumerate(date_range):
                # 乱数成分
                random_factor = 0.005  # 0.5%の乱数成分

                # 終値
                close = prices[i]

                # 始値、高値、安値を生成
                open_price = close * (1 + np.random.uniform(-random_factor, random_factor))
                high = max(open_price, close) * (1 + abs(np.random.uniform(0, random_factor * 2)))
                low = min(open_price, close) * (1 - abs(np.random.uniform(0, random_factor * 2)))

                # 出来高
                volume = int(np.random.uniform(50000, 1000000))

                # データに追加
                all_data.append({
                    'Date': date,
                    'Open': open_price,
                    'High': high,
                    'Low': low,
                    'Close': close,
                    'Volume': volume,
                    'Ticker': ticker
                })

        # DataFrameに変換
        df = pd.DataFrame(all_data)
        logger.info(f"Generated random market data: {len(df)} rows")

        return df
    
    def record_prediction(self, ticker, predicted_change):
        """
        予測を記録する

        Parameters:
        -----------
        ticker : str
            銘柄コード
        predicted_change : float
            予測変化率（%）
        """
        current_time = datetime.now()
        date_str = current_time.strftime("%Y-%m-%d")
        
        if date_str not in self.predictions:
            self.predictions[date_str] = {}
            
        self.predictions[date_str][ticker] = {
            "predicted_change": predicted_change,
            "timestamp": current_time.isoformat(),
            "actual_change": None,  # 実際の変化は後で記録
            "was_correct": None     # 予測の正誤も後で記録
        }
    
    def execute_trades(self, signals_df):
        """
        取引を実行する

        Parameters:
        -----------
        signals_df : pd.DataFrame
            取引シグナルのデータフレーム

        Returns:
        --------
        pd.DataFrame
            実行された取引の詳細
        """
        logger.info("テスト取引を実行します")
        
        # 現在の日時
        timestamp = datetime.now()
        date_str = timestamp.strftime("%Y-%m-%d")
        
        # 日次結果の初期化
        if date_str not in self.daily_results:
            self.daily_results[date_str] = {
                "timestamp": timestamp.isoformat(),
                "starting_balance": self.balance,
                "starting_portfolio_value": 0,  # 後で更新
                "trades": [],
                "ending_balance": 0,  # 後で更新
                "ending_portfolio_value": 0,  # 後で更新
                "daily_profit": 0,  # 後で更新
                "daily_profit_pct": 0  # 後で更新
            }
        
        # 開始時のポートフォリオ価値を計算
        current_prices = {}
        if "Current_Price" in signals_df.columns:
            for _, row in signals_df.iterrows():
                current_prices[row["Ticker"]] = row["Current_Price"]
        
        starting_portfolio_value = self.calculate_portfolio_value(current_prices)
        self.daily_results[date_str]["starting_portfolio_value"] = starting_portfolio_value
        
        # 推奨銘柄の選定
        buy_signals = []
        sell_signals = []
        
        if not signals_df.empty:
            # スコアでソート
            if "Score" in signals_df.columns:
                signals_df = signals_df.sort_values("Score", ascending=False)
            
            # 推奨銘柄と現在価格を抽出
            for _, row in signals_df.iterrows():
                ticker = row["Ticker"]
                price = row["Current_Price"]
                score = row.get("Score", 0)
                
                # 既に保有しているなら売却候補に
                if ticker in self.portfolio:
                    sell_signals.append((ticker, price, score))
                # それ以外なら購入候補に
                else:
                    buy_signals.append((ticker, price, score))
        
        # 取引実行のリスト
        executed_trades = []
        
        # 1. 売却処理
        for ticker, price, score in sell_signals:
            # スコアが低いものは売却
            if score < -0.2:
                qty = self.portfolio[ticker]["qty"]
                logger.info(f"売却: {ticker} {qty}株 @ {price:.2f}円 (スコア: {score:.2f})")
                
                try:
                    result = self.sell_stock(ticker, price, qty, timestamp)
                    if result:
                        executed_trades.append(result)
                        # 日次結果に追加
                        self.daily_results[date_str]["trades"].append(result)
                except Exception as e:
                    logger.error(f"売却エラー ({ticker}): {str(e)}")
        
        # 2. 購入処理
        # ポートフォリオに空きがあれば
        available_slots = max(0, self.max_positions - len(self.portfolio))
        
        if available_slots > 0:
            # 高スコアのものから処理
            for ticker, price, score in buy_signals[:available_slots]:
                if score > 0.2:  # スコアが高いものだけ購入
                    # 購入金額（現金の10%を上限に）
                    max_amount = self.balance * 0.1
                    qty = min(int(max_amount / price), 100)  # 最大でも100株まで
                    
                    if qty > 0:
                        logger.info(f"購入: {ticker} {qty}株 @ {price:.2f}円 (スコア: {score:.2f})")
                        
                        try:
                            result = self.buy_stock(ticker, price, qty, timestamp)
                            if result:
                                executed_trades.append(result)
                                # 日次結果に追加
                                self.daily_results[date_str]["trades"].append(result)
                        except Exception as e:
                            logger.error(f"購入エラー ({ticker}): {str(e)}")
        
        # 終了時のポートフォリオ価値を計算
        ending_portfolio_value = self.calculate_portfolio_value(current_prices)
        self.daily_results[date_str]["ending_balance"] = self.balance
        self.daily_results[date_str]["ending_portfolio_value"] = ending_portfolio_value
        
        # 日次損益計算
        total_value_start = self.daily_results[date_str]["starting_balance"] + self.daily_results[date_str]["starting_portfolio_value"]
        total_value_end = self.balance + ending_portfolio_value
        daily_profit = total_value_end - total_value_start
        daily_profit_pct = (daily_profit / total_value_start) * 100 if total_value_start > 0 else 0
        
        self.daily_results[date_str]["daily_profit"] = daily_profit
        self.daily_results[date_str]["daily_profit_pct"] = daily_profit_pct
        
        logger.info(f"取引完了: {len(executed_trades)}件")
        logger.info(f"日次損益: {daily_profit:,.2f}円 ({daily_profit_pct:.2f}%)")
        
        # データフレームに変換して返す
        if executed_trades:
            return pd.DataFrame(executed_trades)
        else:
            return pd.DataFrame()

    def save_test_results(self):
        """
        テスト結果を保存

        Returns:
        --------
        tuple
            (サマリーファイルパス, 取引履歴ファイルパス)
        """
        # 現在の日時
        now = datetime.now()
        date_str = now.strftime("%Y%m%d")

        # テスト終了時のメタデータを更新
        self.test_metadata["end_time"] = now.isoformat()
        self.test_metadata["duration"] = str(now - datetime.fromisoformat(self.test_metadata["start_time"]))

        # ポートフォリオの概要
        portfolio_summary = self.get_portfolio_summary()

        # テスト結果
        test_results = {
            "metadata": self.test_metadata,
            "portfolio": portfolio_summary,
            "trade_count": len(self.trades),
            "profit_trades": len([t for t in self.trades if t["profit"] > 0]),
            "loss_trades": len([t for t in self.trades if t["profit"] < 0]),
            "break_even_trades": len([t for t in self.trades if t["profit"] == 0]),
            "total_profit": sum(t["profit"] for t in self.trades),
            "average_profit": sum(t["profit"] for t in self.trades) / len(self.trades) if self.trades else 0,
            "max_profit": max([t["profit"] for t in self.trades]) if self.trades else 0,
            "max_loss": min([t["profit"] for t in self.trades]) if self.trades else 0,
            "daily_results": self.daily_results
        }

        # 銘柄別パフォーマンス
        ticker_performance = {}
        for trade in self.trades:
            ticker = trade["ticker"]
            if ticker not in ticker_performance:
                ticker_performance[ticker] = {
                    "trade_count": 0,
                    "profit": 0,
                    "buy_count": 0,
                    "sell_count": 0
                }

            ticker_performance[ticker]["trade_count"] += 1
            ticker_performance[ticker]["profit"] += trade["profit"]

            if trade["action"].upper() == "BUY":
                ticker_performance[ticker]["buy_count"] += 1
            elif trade["action"].upper() == "SELL":
                ticker_performance[ticker]["sell_count"] += 1

        test_results["ticker_performance"] = ticker_performance

        # ファイル名
        test_id = self.test_metadata["test_id"]
        summary_file = os.path.join(self.results_dir, f"test_summary_{test_id}.json")

        # サマリー保存
        with open(summary_file, 'w') as f:
            json.dump(test_results, f, indent=2, default=str)

        # 取引履歴の保存
        trades_file = None
        if self.trades:
            trades_df = pd.DataFrame(self.trades)
            trades_file = os.path.join(self.results_dir, f"test_trades_{test_id}.csv")
            trades_df.to_csv(trades_file, index=False)
            
        # 日次結果も保存
        daily_results_file = os.path.join(self.results_dir, f"daily_results_{date_str}.json")
        with open(daily_results_file, 'w') as f:
            json.dump(self.daily_results, f, indent=2, default=str)

        logger.info(f"テスト結果を保存しました: {summary_file}" + (f", {trades_file}" if trades_file else ""))
        logger.info(f"日次結果を保存しました: {daily_results_file}")
        return (summary_file, trades_file)
    
    def get_daily_results(self, date_str=None):
        """
        指定日の日次結果を取得

        Parameters:
        -----------
        date_str : str
            日付文字列（YYYY-MM-DD形式）。Noneの場合は今日

        Returns:
        --------
        dict
            日次結果の辞書
        """
        if date_str is None:
            date_str = datetime.now().strftime("%Y-%m-%d")
            
        if date_str in self.daily_results:
            return self.daily_results[date_str]
        else:
            logger.warning(f"{date_str}の日次結果がありません")
            return {
                "timestamp": datetime.now().isoformat(),
                "starting_balance": self.initial_balance,
                "starting_portfolio_value": 0,
                "trades": [],
                "ending_balance": self.balance,
                "ending_portfolio_value": 0,
                "daily_profit": 0,
                "daily_profit_pct": 0
            }

    # 継続学習関連メソッドをオーバーライド（テスト環境では常に無効）

    def should_update_model(self):
        """
        テスト環境ではモデル更新不要

        Returns:
        --------
        bool
            常にFalse
        """
        logger.info("テスト環境ではモデル更新は行われません")
        return False

    def update_model(self):
        """
        テスト環境ではモデル更新不可

        Returns:
        --------
        bool
            常にFalse
        """
        logger.info("テスト環境ではモデル更新は行われません")
        return False
