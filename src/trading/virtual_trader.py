#!/usr/bin/env python3
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging
import os
import json

logger = logging.getLogger(__name__)

class VirtualTrader:
    """
    仮想取引を行うクラス
    実際の取引は行わず、シミュレーションのみを行う
    """
    def __init__(self, initial_balance=1000000, commission_rate=0.0, results_dir="results"):
        """
        初期化
        
        Parameters:
        -----------
        initial_balance : float
            初期資金
        commission_rate : float
            取引手数料率（例: 0.0005 = 0.05%）。au kabu APIを利用する場合は0.0。
        results_dir : str
            結果保存ディレクトリ
        """
        self.initial_balance = initial_balance
        self.balance = initial_balance
        self.commission_rate = commission_rate
        self.portfolio = {}  # {ticker: {'qty': 100, 'price': 1000, 'timestamp': datetime}}
        self.trade_history = []  # 取引履歴
        self.results_dir = results_dir
        
        # 結果ディレクトリの作成
        os.makedirs(results_dir, exist_ok=True)
        
    def execute_trades(self, recommendations):
        """
        推奨銘柄に基づいて仮想取引を実行
        
        Parameters:
        -----------
        recommendations : pd.DataFrame
            推奨銘柄のデータフレーム（Ticker, Current_Price, Predicted_Change_Pct等を含む）
            
        Returns:
        --------
        pd.DataFrame
            取引結果
        """
        if recommendations is None or recommendations.empty:
            logger.warning("推奨銘柄がありません")
            return None
            
        # 現在の日時
        now = datetime.now()
        
        # 推奨銘柄のティッカーリスト
        recommended_tickers = recommendations['Ticker'].tolist()
        
        # 1. 推奨されなくなった保有銘柄を売却
        trades = []
        for ticker in list(self.portfolio.keys()):
            if ticker not in recommended_tickers:
                position = self.portfolio[ticker]
                current_price = self._get_current_price(ticker, recommendations)
                
                if current_price > 0:
                    # 売却金額の計算（手数料を考慮）
                    qty = position['qty']
                    sell_amount = qty * current_price * (1 - self.commission_rate)
                    
                    # 残高に追加
                    self.balance += sell_amount
                    
                    # 取引履歴に追加
                    trade = {
                        'Ticker': ticker,
                        'Action': 'SELL',
                        'Qty': qty,
                        'Price': current_price,
                        'Amount': sell_amount,
                        'Commission': qty * current_price * self.commission_rate,
                        'Timestamp': now,
                        'Profit': sell_amount - (qty * position['price'])
                    }
                    trades.append(trade)
                    self.trade_history.append(trade)
                    
                    # ポートフォリオから削除
                    del self.portfolio[ticker]
                    
                    logger.info(f"売却: {ticker} {qty}株 @ {current_price:.2f}円 = {sell_amount:.2f}円")
        
        # 2. 投資配分を計算
        available_balance = self.balance
        allocations = self._calculate_allocations(recommendations, available_balance)
        
        # 3. 新規推奨銘柄を購入
        for ticker, allocation in allocations.items():
            if ticker not in self.portfolio and allocation > 0:
                current_price = self._get_current_price(ticker, recommendations)
                
                if current_price > 0:
                    # 購入可能な株数を計算（整数に切り下げ）
                    max_qty = int(allocation / (current_price * (1 + self.commission_rate)))
                    
                    if max_qty > 0:
                        # 購入金額の計算（手数料を含む）
                        buy_amount = max_qty * current_price
                        commission = buy_amount * self.commission_rate
                        total_cost = buy_amount + commission
                        
                        # 残高から差し引く
                        self.balance -= total_cost
                        
                        # ポートフォリオに追加
                        self.portfolio[ticker] = {
                            'qty': max_qty,
                            'price': current_price,
                            'timestamp': now
                        }
                        
                        # 取引履歴に追加
                        trade = {
                            'Ticker': ticker,
                            'Action': 'BUY',
                            'Qty': max_qty,
                            'Price': current_price,
                            'Amount': buy_amount,
                            'Commission': commission,
                            'Timestamp': now,
                            'Profit': 0  # 購入時は利益なし
                        }
                        trades.append(trade)
                        self.trade_history.append(trade)
                        
                        logger.info(f"購入: {ticker} {max_qty}株 @ {current_price:.2f}円 = {total_cost:.2f}円")
        
        # 取引結果をDataFrameに変換
        if trades:
            return pd.DataFrame(trades)
        else:
            logger.info("取引なし")
            return None
    
    def _get_current_price(self, ticker, recommendations=None):
        """
        現在の価格を取得
        
        Parameters:
        -----------
        ticker : str
            銘柄コード
        recommendations : pd.DataFrame
            推奨銘柄のデータフレーム（なければNone）
            
        Returns:
        --------
        float
            現在価格
        """
        # 推奨銘柄から価格を取得
        if recommendations is not None and not recommendations.empty:
            ticker_data = recommendations[recommendations['Ticker'] == ticker]
            if not ticker_data.empty and 'Current_Price' in ticker_data.columns:
                return ticker_data['Current_Price'].values[0]
        
        # ポートフォリオに含まれる場合は保有価格を返す
        if ticker in self.portfolio:
            return self.portfolio[ticker]['price']
            
        # どちらも取得できない場合は0を返す
        return 0
    
    def _calculate_allocations(self, recommendations, available_balance):
        """
        投資配分を計算
        
        Parameters:
        -----------
        recommendations : pd.DataFrame
            推奨銘柄のデータフレーム
        available_balance : float
            利用可能な残高
            
        Returns:
        --------
        dict
            銘柄ごとの投資金額
        """
        # 期待リターンの合計を計算
        total_expected_return = recommendations['Predicted_Change_Pct'].sum()
        
        # 各銘柄の配分比率を計算
        allocations = {}
        
        if total_expected_return <= 0:
            # 期待リターンが0以下の場合は均等配分
            per_ticker = available_balance / len(recommendations)
            for _, row in recommendations.iterrows():
                allocations[row['Ticker']] = per_ticker
        else:
            # 期待リターンに比例した配分
            for _, row in recommendations.iterrows():
                ticker = row['Ticker']
                expected_return = row['Predicted_Change_Pct']
                weight = expected_return / total_expected_return
                allocations[ticker] = available_balance * weight
        
        return allocations
    
    def get_portfolio_value(self, current_prices=None):
        """
        ポートフォリオの現在価値を計算
        
        Parameters:
        -----------
        current_prices : dict
            銘柄ごとの現在価格 {ticker: price}
            
        Returns:
        --------
        float
            ポートフォリオの現在価値
        """
        portfolio_value = self.balance
        
        for ticker, position in self.portfolio.items():
            # 現在価格を取得
            if current_prices and ticker in current_prices:
                price = current_prices[ticker]
            else:
                price = position['price']
                
            # 価値を計算
            portfolio_value += position['qty'] * price
            
        return portfolio_value
    
    def get_portfolio_summary(self):
        """
        ポートフォリオの概要を取得
        
        Returns:
        --------
        dict
            ポートフォリオの概要
        """
        return {
            'balance': self.balance,
            'portfolio': self.portfolio,
            'portfolio_value': self.get_portfolio_value(),
            'initial_balance': self.initial_balance,
            'profit_loss': self.get_portfolio_value() - self.initial_balance,
            'profit_loss_pct': (self.get_portfolio_value() - self.initial_balance) / self.initial_balance * 100
        }
    
    def save_results(self):
        """
        取引結果を保存
        
        Returns:
        --------
        str
            保存したファイルのパス
        """
        # 現在の日時
        now = datetime.now()
        date_str = now.strftime('%Y%m%d')
        time_str = now.strftime('%H%M%S')
        
        # 取引履歴をDataFrameに変換
        if self.trade_history:
            trades_df = pd.DataFrame(self.trade_history)
            
            # 取引履歴を保存
            trades_file = os.path.join(self.results_dir, f"trades_{date_str}.csv")
            trades_df.to_csv(trades_file, index=False)
            
            # ポートフォリオ概要を保存
            summary = self.get_portfolio_summary()
            summary_file = os.path.join(self.results_dir, f"portfolio_{date_str}_{time_str}.json")
            
            with open(summary_file, 'w') as f:
                json.dump(summary, f, indent=2, default=str)
                
            logger.info(f"取引結果を保存しました: {trades_file}, {summary_file}")
            return trades_file
        else:
            logger.warning("取引履歴がありません")
            return None
    
    def reset(self):
        """
        トレーダーをリセット
        """
        self.balance = self.initial_balance
        self.portfolio = {}
        self.trade_history = []
        logger.info("トレーダーをリセットしました")
