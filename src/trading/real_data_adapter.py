#!/usr/bin/env python3
"""
Real Data Adapter for Kabu Station API Mock Server

This module provides real-time market data to the existing dryrun system
by integrating Yahoo Finance and international market data.
"""

import yfinance as yf
import pandas as pd
import json
import logging
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional
import requests
from pathlib import Path

# International data collector integration
import sys
sys.path.append('src')
from data_collector.international_collector import InternationalDataCollector

class RealDataAdapter:
    """
    Adapter to provide real market data to the Kabu Station dryrun system.
    Integrates Yahoo Finance and international market data.
    """
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.international_collector = InternationalDataCollector()
        self.nikkei_symbols = self._load_nikkei_symbols()
        self.cache_duration = 60  # Cache data for 60 seconds
        self.data_cache = {}
        
    def _load_nikkei_symbols(self) -> List[str]:
        """Load Nikkei 225 symbols from the existing system."""
        symbols_file = Path("results/kabu/data/meta/symbol_meta_20250702.json")
        
        if symbols_file.exists():
            try:
                with open(symbols_file, 'r') as f:
                    data = json.load(f)
                return list(data.keys())
            except Exception as e:
                self.logger.error(f"Error loading symbols: {e}")
        
        # Fallback to major Nikkei symbols
        return [
            "7203.T",  # Toyota
            "9984.T",  # SoftBank
            "8306.T",  # Mitsubishi UFJ
            "6758.T",  # Sony
            "7974.T",  # Nintendo
            "4519.T",  # Takeda
            "8035.T",  # Tokyo Electron
            "6861.T",  # Keyence
            "8001.T",  # ITOCHU
            "9432.T"   # NTT
        ]
    
    def get_real_symbol_data(self, symbol: str) -> Dict:
        """
        Get real-time data for a specific symbol.
        
        Args:
            symbol: Stock symbol (e.g., "7203.T")
            
        Returns:
            Dictionary with real market data
        """
        # Check cache first
        cache_key = f"{symbol}_{int(time.time() // self.cache_duration)}"
        if cache_key in self.data_cache:
            return self.data_cache[cache_key]
        
        try:
            # Get real data from Yahoo Finance
            ticker = yf.Ticker(symbol)
            
            # Get current data
            info = ticker.info
            hist = ticker.history(period="2d", interval="1m")
            
            if hist.empty:
                raise ValueError(f"No data available for {symbol}")
            
            # Get latest price data
            latest_data = hist.iloc[-1]
            previous_close = info.get('previousClose', latest_data['Close'])
            
            # Calculate change from previous close
            current_price = float(latest_data['Close'])
            change_from_previous = current_price - previous_close
            change_percent = (change_from_previous / previous_close * 100) if previous_close != 0 else 0
            
            # Format data in Kabu Station API format
            real_data = {
                "Symbol": symbol,
                "SymbolName": info.get('longName', f"Stock {symbol}"),
                "CurrentPrice": current_price,
                "CurrentPriceTime": datetime.now().strftime("%H:%M:%S"),
                "ChangeFromPreviousClose": change_from_previous,
                "ChangeFromPreviousClosePercent": change_percent,
                "OpeningPrice": float(latest_data['Open']),
                "HighPrice": float(latest_data['High']),
                "LowPrice": float(latest_data['Low']),
                "Volume": int(latest_data['Volume']),
                "MarketOrderAcceptable": True,
                "BidPrice": current_price - 1,  # Approximate bid
                "BidQuantity": 100,
                "AskPrice": current_price + 1,  # Approximate ask
                "AskQuantity": 100,
                "Exchange": "T",  # Tokyo Stock Exchange
                "LastUpdate": datetime.now().isoformat(),
                "IsRealData": True  # Flag to indicate this is real data
            }
            
            # Cache the result
            self.data_cache[cache_key] = real_data
            
            self.logger.info(f"Retrieved real data for {symbol}: ¥{current_price:.2f}")
            return real_data
            
        except Exception as e:
            self.logger.error(f"Error getting real data for {symbol}: {e}")
            # Return fallback mock data
            return self._get_fallback_data(symbol)
    
    def _get_fallback_data(self, symbol: str) -> Dict:
        """Generate fallback data when real data is unavailable."""
        import random
        
        base_price = 1000 + random.randint(-500, 500)
        change = random.uniform(-50, 50)
        
        return {
            "Symbol": symbol,
            "SymbolName": f"Stock {symbol}",
            "CurrentPrice": base_price,
            "CurrentPriceTime": datetime.now().strftime("%H:%M:%S"),
            "ChangeFromPreviousClose": change,
            "ChangeFromPreviousClosePercent": (change / base_price * 100),
            "OpeningPrice": base_price - random.randint(-20, 20),
            "HighPrice": base_price + random.randint(0, 30),
            "LowPrice": base_price - random.randint(0, 30),
            "Volume": random.randint(100000, 1000000),
            "MarketOrderAcceptable": True,
            "BidPrice": base_price - 1,
            "BidQuantity": 100,
            "AskPrice": base_price + 1,
            "AskQuantity": 100,
            "Exchange": "T",
            "LastUpdate": datetime.now().isoformat(),
            "IsRealData": False
        }
    
    def get_forex_data(self) -> Dict[str, float]:
        """Get real-time forex data that affects Japanese markets."""
        try:
            forex_pairs = {
                "USDJPY=X": "USD/JPY",
                "EURJPY=X": "EUR/JPY", 
                "GBPJPY=X": "GBP/JPY",
                "AUDJPY=X": "AUD/JPY"
            }
            
            forex_data = {}
            for symbol, name in forex_pairs.items():
                try:
                    ticker = yf.Ticker(symbol)
                    hist = ticker.history(period="1d", interval="1m")
                    if not hist.empty:
                        current_rate = float(hist.iloc[-1]['Close'])
                        forex_data[name] = current_rate
                        self.logger.info(f"Retrieved {name}: {current_rate:.3f}")
                except Exception as e:
                    self.logger.warning(f"Error getting {name}: {e}")
            
            return forex_data
            
        except Exception as e:
            self.logger.error(f"Error getting forex data: {e}")
            return {}
    
    def get_international_market_status(self) -> Dict[str, Dict]:
        """Get status of major international markets affecting Nikkei."""
        try:
            markets = {
                "^GSPC": "S&P 500",
                "^IXIC": "NASDAQ",
                "^VIX": "VIX",
                "^GDAXI": "DAX"
            }
            
            market_data = {}
            for symbol, name in markets.items():
                try:
                    ticker = yf.Ticker(symbol)
                    hist = ticker.history(period="1d", interval="5m")
                    if not hist.empty:
                        current = float(hist.iloc[-1]['Close'])
                        previous = float(hist.iloc[-2]['Close']) if len(hist) > 1 else current
                        change = current - previous
                        change_percent = (change / previous * 100) if previous != 0 else 0
                        
                        market_data[name] = {
                            "current": current,
                            "change": change,
                            "change_percent": change_percent,
                            "timestamp": datetime.now().isoformat()
                        }
                        
                        self.logger.info(f"Retrieved {name}: {current:.2f} ({change_percent:+.2f}%)")
                        
                except Exception as e:
                    self.logger.warning(f"Error getting {name}: {e}")
            
            return market_data
            
        except Exception as e:
            self.logger.error(f"Error getting international market data: {e}")
            return {}
    
    def update_mock_server_with_real_data(self, mock_server_url: str = "http://localhost:18082"):
        """
        Update the mock server with real market data.
        This can be called periodically to keep the mock server updated with real data.
        """
        try:
            # Get forex data
            forex_data = self.get_forex_data()
            
            # Get international market data
            international_data = self.get_international_market_status()
            
            # Update symbol data for key Nikkei stocks
            updated_count = 0
            for symbol in self.nikkei_symbols[:10]:  # Update top 10 symbols
                try:
                    real_data = self.get_real_symbol_data(symbol)
                    
                    # Post update to mock server (if it supports updates)
                    # This would require the mock server to have an update endpoint
                    # For now, we'll log the real data
                    self.logger.info(f"Real data for {symbol}: ¥{real_data['CurrentPrice']:.2f}")
                    updated_count += 1
                    
                    # Small delay to avoid overwhelming the data source
                    time.sleep(0.1)
                    
                except Exception as e:
                    self.logger.warning(f"Error updating {symbol}: {e}")
            
            self.logger.info(f"Updated {updated_count} symbols with real data")
            
            # Save comprehensive market status
            market_status = {
                "timestamp": datetime.now().isoformat(),
                "forex": forex_data,
                "international_markets": international_data,
                "updated_symbols": updated_count,
                "data_source": "Yahoo Finance API"
            }
            
            # Save to file for the trading system to use
            status_file = Path("results/kabu/data/real_market_status.json")
            status_file.parent.mkdir(parents=True, exist_ok=True)
            
            with open(status_file, 'w') as f:
                json.dump(market_status, f, indent=2, default=str)
            
            self.logger.info(f"Market status saved to {status_file}")
            return market_status
            
        except Exception as e:
            self.logger.error(f"Error updating mock server with real data: {e}")
            return {}
    
    def start_real_data_feed(self, update_interval: int = 60):
        """
        Start a continuous real data feed.
        
        Args:
            update_interval: Update interval in seconds
        """
        self.logger.info(f"Starting real data feed with {update_interval}s interval")
        
        while True:
            try:
                self.update_mock_server_with_real_data()
                time.sleep(update_interval)
            except KeyboardInterrupt:
                self.logger.info("Real data feed stopped by user")
                break
            except Exception as e:
                self.logger.error(f"Error in real data feed: {e}")
                time.sleep(10)  # Wait before retrying


def main():
    """Main function for testing the real data adapter."""
    logging.basicConfig(level=logging.INFO)
    
    adapter = RealDataAdapter()
    
    print("=== Real Data Adapter Test ===")
    
    # Test symbol data
    print("\n1. Testing symbol data:")
    for symbol in ["7203.T", "9984.T"]:
        data = adapter.get_real_symbol_data(symbol)
        print(f"  {symbol}: ¥{data['CurrentPrice']:.2f} ({data['ChangeFromPreviousClosePercent']:+.2f}%)")
    
    # Test forex data
    print("\n2. Testing forex data:")
    forex = adapter.get_forex_data()
    for pair, rate in forex.items():
        print(f"  {pair}: {rate:.3f}")
    
    # Test international markets
    print("\n3. Testing international markets:")
    markets = adapter.get_international_market_status()
    for market, data in markets.items():
        print(f"  {market}: {data['current']:.2f} ({data['change_percent']:+.2f}%)")
    
    # Update mock server
    print("\n4. Updating mock server with real data:")
    status = adapter.update_mock_server_with_real_data()
    print(f"  Updated {status.get('updated_symbols', 0)} symbols")
    
    print("\n=== Test Complete ===")


if __name__ == "__main__":
    main()