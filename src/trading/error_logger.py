#!/usr/bin/env python3
import os
import json
import time
import logging
import threading
from datetime import datetime
from typing import Dict, Any, Optional, List, Set
from collections import defaultdict

from src.trading.logging_config import get_module_logger

# ロガー設定
logger = get_module_logger(__name__)

class ErrorLogManager:
    """
    エラーログの重複抑制を管理するクラス
    - 同一エラーの頻発を防ぎ、ログの肥大化を抑制
    - 一定時間内の同一エラーをまとめる
    """
    def __init__(self, cooldown_period: int = 300):
        """
        初期化
        
        Parameters:
        -----------
        cooldown_period : int
            同一エラーの抑制期間（秒）
        """
        self.cooldown_period = cooldown_period
        self.last_logged = {}  # エラーキーごとの最終ログ時間
        self.error_counts = defaultdict(int)  # エラーキーごとの発生回数
        self.lock = threading.RLock()  # スレッドセーフな操作のためのロック
    
    def should_log(self, error_key: str) -> bool:
        """
        特定のエラーをログに記録すべきかを判断
        
        Parameters:
        -----------
        error_key : str
            エラーの識別キー
            
        Returns:
        --------
        bool
            ログに記録すべき場合はTrue、抑制すべき場合はFalse
        """
        with self.lock:
            now = time.time()
            
            # このエラーを初めて見た場合
            if error_key not in self.last_logged:
                self.last_logged[error_key] = now
                self.error_counts[error_key] = 1
                return True
            
            # クールダウン期間内かチェック
            time_since_last = now - self.last_logged[error_key]
            
            if time_since_last < self.cooldown_period:
                # クールダウン期間内ならカウントだけ増やして抑制
                self.error_counts[error_key] += 1
                
                # 10回ごとには出力（多数発生時の通知のため）
                if self.error_counts[error_key] % 10 == 0:
                    # タイミングを更新して出力
                    self.last_logged[error_key] = now
                    return True
                    
                return False
            else:
                # クールダウン期間を過ぎていれば出力
                count = self.error_counts[error_key]
                if count > 1:
                    # 抑制されていた場合は累積回数をリセット
                    logger.info(f"前回から{count}回同じエラーが発生していました: {error_key}")
                    self.error_counts[error_key] = 1
                
                self.last_logged[error_key] = now
                return True
    
    def get_stats(self) -> Dict[str, Any]:
        """
        エラー発生統計情報を取得
        
        Returns:
        --------
        Dict[str, Any]
            エラー統計情報
        """
        with self.lock:
            stats = {
                "total_error_types": len(self.last_logged),
                "total_error_count": sum(self.error_counts.values()),
                "error_distribution": dict(self.error_counts),
                "last_occurrence": {k: datetime.fromtimestamp(v).isoformat() 
                                  for k, v in self.last_logged.items()}
            }
            return stats


class ErrorHandler:
    """
    構造化エラーログの保存・管理クラス
    - JSONLフォーマットでエラーを保存
    - エラータイプや発生源ごとの集計
    - 日付ごとのログファイル分割
    """
    def __init__(self, log_dir: str = "logs/errors"):
        """
        初期化
        
        Parameters:
        -----------
        log_dir : str
            エラーログ出力ディレクトリ
        """
        self.log_dir = log_dir
        os.makedirs(log_dir, exist_ok=True)
        
        self.lock = threading.RLock()  # スレッドセーフな操作のためのロック
        self.current_date = datetime.now().strftime("%Y%m%d")
        self.current_file = os.path.join(log_dir, f"errors_{self.current_date}.jsonl")
        
        # エラー集計用
        self.error_counts = defaultdict(int)  # エラータイプごとの発生回数
        self.source_counts = defaultdict(int)  # 発生源ごとの発生回数
        
        # エラーログマネージャ
        self.log_manager = ErrorLogManager()
    
    def log_error(self, error_type: str, source: str, error_message: str, 
                 context: Optional[Dict[str, Any]] = None) -> None:
        """
        エラーをログに記録
        
        Parameters:
        -----------
        error_type : str
            エラーの種類（例: ConnectionError, ValueError）
        source : str
            エラーの発生源（例: kabu_api.get_token）
        error_message : str
            エラーメッセージ
        context : Optional[Dict[str, Any]]
            エラー発生時のコンテキスト情報
        """
        with self.lock:
            # 日付が変わっていたら新しいファイルに切り替え
            current_date = datetime.now().strftime("%Y%m%d")
            if current_date != self.current_date:
                self.current_date = current_date
                self.current_file = os.path.join(self.log_dir, f"errors_{self.current_date}.jsonl")
            
            # タイムスタンプ
            timestamp = datetime.now().isoformat()
            
            # エラーデータ作成
            error_data = {
                "timestamp": timestamp,
                "error_type": error_type,
                "source": source,
                "error_message": error_message
            }
            
            # コンテキスト情報があれば追加
            if context:
                error_data["context"] = context
            
            # JSONLとして保存
            try:
                with open(self.current_file, 'a', encoding='utf-8') as f:
                    f.write(json.dumps(error_data, ensure_ascii=False) + '\n')
                
                # 集計更新
                self.error_counts[error_type] += 1
                self.source_counts[source] += 1
                
            except Exception as e:
                logger.error(f"エラーログの保存に失敗: {str(e)}")
    
    def get_error_counts(self) -> Dict[str, int]:
        """
        エラータイプごとの発生回数を取得
        
        Returns:
        --------
        Dict[str, int]
            エラータイプごとの発生回数
        """
        with self.lock:
            return dict(self.error_counts)
    
    def get_source_counts(self) -> Dict[str, int]:
        """
        発生源ごとの発生回数を取得
        
        Returns:
        --------
        Dict[str, int]
            発生源ごとの発生回数
        """
        with self.lock:
            return dict(self.source_counts)
    
    def get_log_stats(self) -> Dict[str, Any]:
        """
        エラーログの統計情報を取得
        
        Returns:
        --------
        Dict[str, Any]
            エラーログ統計情報
        """
        with self.lock:
            stats = {
                "total_errors": sum(self.error_counts.values()),
                "error_types": len(self.error_counts),
                "error_sources": len(self.source_counts),
                "top_errors": {k: v for k, v in sorted(
                    self.error_counts.items(), 
                    key=lambda item: item[1], 
                    reverse=True
                )[:10]},  # 上位10種類のエラー
                "top_sources": {k: v for k, v in sorted(
                    self.source_counts.items(), 
                    key=lambda item: item[1], 
                    reverse=True
                )[:10]},  # 上位10種類の発生源
                "current_log_file": self.current_file,
                "error_suppression_stats": self.log_manager.get_stats()
            }
            return stats
    
    def get_error_summary(self) -> Dict[str, Any]:
        """
        エラーサマリーを取得（get_log_statsのエイリアス）
        
        Returns:
        --------
        Dict[str, Any]
            エラーログ統計情報
        """
        return self.get_log_stats()

# シングルトンインスタンスの取得
_error_handler_instance = None

def get_error_handler(log_dir: str = "logs/errors") -> ErrorHandler:
    """
    ErrorHandlerのシングルトンインスタンスを取得
    
    Parameters:
    -----------
    log_dir : str
        エラーログ出力ディレクトリ
        
    Returns:
    --------
    ErrorHandler
        ErrorHandlerインスタンス
    """
    global _error_handler_instance
    
    if _error_handler_instance is None:
        _error_handler_instance = ErrorHandler(log_dir)
    
    return _error_handler_instance
