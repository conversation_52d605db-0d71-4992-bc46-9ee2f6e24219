#!/usr/bin/env python3
import pandas as pd
import numpy as np
from datetime import datetime, time
import logging

logger = logging.getLogger(__name__)

class TradingStrategy:
    """
    取引戦略を提供するクラス
    投資配分の計算や利益確定・損切りの条件を決定する
    """
    def __init__(self, max_stocks=5):
        """
        初期化
        
        Parameters:
        -----------
        max_stocks : int
            最大銘柄数
        """
        self.max_stocks = max_stocks
        
    def calculate_allocations(self, recommendations, available_balance):
        """
        期待値に基づく投資金額の動的配分
        
        Parameters:
        -----------
        recommendations : pd.DataFrame
            推奨銘柄のデータフレーム（Ticker, Predicted_Change_Pct等を含む）
        available_balance : float
            利用可能な残高
            
        Returns:
        --------
        dict
            銘柄ごとの投資金額
        """
        if recommendations is None or recommendations.empty:
            logger.warning("推奨銘柄がありません")
            return {}
            
        # 最大銘柄数に制限
        if len(recommendations) > self.max_stocks:
            recommendations = recommendations.head(self.max_stocks)
            
        # 期待値（予測変化率）の合計を計算
        total_expected_return = recommendations['Predicted_Change_Pct'].sum()
        
        # 各銘柄の配分比率を計算
        allocations = {}
        
        if total_expected_return <= 0:
            # 期待リターンが0以下の場合は均等配分
            per_ticker = available_balance / len(recommendations)
            for _, row in recommendations.iterrows():
                allocations[row['Ticker']] = per_ticker
        else:
            # 期待リターンに比例した配分
            for _, row in recommendations.iterrows():
                ticker = row['Ticker']
                expected_return = row['Predicted_Change_Pct']
                weight = expected_return / total_expected_return
                allocations[ticker] = available_balance * weight
        
        logger.info(f"投資配分計算: {allocations}")
        return allocations
    
    def calculate_exit_conditions(self, entry_price, predicted_change):
        """
        利益確定と損切りの条件を計算
        
        Parameters:
        -----------
        entry_price : float
            エントリー価格
        predicted_change : float
            予測変化率（%）
            
        Returns:
        --------
        tuple
            (利益確定価格, ストップロス価格)
        """
        # 予測変化率に基づく利益確定レベル
        # 予測が大きいほど高い利益確定レベルを設定
        take_profit_pct = min(predicted_change * 0.8, 5.0)  # 予測の80%か5%のいずれか小さい方
        
        # ストップロスは固定比率または予測の逆方向
        stop_loss_pct = -max(predicted_change * 0.4, 2.0)  # 予測の40%か2%のいずれか大きい方
        
        take_profit_price = entry_price * (1 + take_profit_pct / 100)
        stop_loss_price = entry_price * (1 + stop_loss_pct / 100)
        
        logger.info(f"出口条件計算: エントリー価格={entry_price}, 利益確定={take_profit_price} ({take_profit_pct:.2f}%), ストップロス={stop_loss_price} ({stop_loss_pct:.2f}%)")
        return take_profit_price, stop_loss_price
    
    def should_exit_position(self, ticker, current_price, take_profit_price, stop_loss_price, entry_time, max_hold_hours=1):
        """
        ポジションを決済すべきかを判断
        
        Parameters:
        -----------
        ticker : str
            銘柄コード
        current_price : float
            現在価格
        take_profit_price : float
            利益確定価格
        stop_loss_price : float
            ストップロス価格
        entry_time : datetime
            エントリー時間
        max_hold_hours : int
            最大保有時間（時間）
            
        Returns:
        --------
        tuple
            (決済すべきか, 理由)
        """
        # 利益確定条件
        if current_price >= take_profit_price:
            return True, "利益確定"
            
        # ストップロス条件
        if current_price <= stop_loss_price:
            return True, "ストップロス"
            
        # 時間経過条件
        now = datetime.now()
        if (now - entry_time).total_seconds() >= max_hold_hours * 3600:
            return True, "保有時間超過"
            
        # 市場終了間近（14:55以降）
        if now.time() >= time(14, 55):
            return True, "市場終了間近"
            
        return False, ""
    
    def is_market_open(self):
        """
        市場が開いているかをチェック
        
        Returns:
        --------
        bool
            市場が開いている場合はTrue、閉じている場合はFalse
        """
        now = datetime.now()
        current_time = now.time()
        
        # 9:00-15:00が市場時間
        market_open = time(9, 0)
        market_close = time(15, 0)
        
        # 土日はスキップ
        if now.weekday() >= 5:  # 5=土曜日, 6=日曜日
            return False
            
        return market_open <= current_time <= market_close
    
    def select_top_stocks(self, predictions, top_n=None):
        """
        予測結果から上位銘柄を選択
        
        Parameters:
        -----------
        predictions : pd.DataFrame
            予測結果のデータフレーム
        top_n : int
            選択する銘柄数（Noneの場合はself.max_stocksを使用）
            
        Returns:
        --------
        pd.DataFrame
            選択された上位銘柄
        """
        if predictions is None or predictions.empty:
            logger.warning("予測結果がありません")
            return pd.DataFrame()
            
        if top_n is None:
            top_n = self.max_stocks
            
        # 予測変化率でソート（降順）
        sorted_predictions = predictions.sort_values('Predicted_Change_Pct', ascending=False)
        
        # 上位N銘柄を選択
        top_stocks = sorted_predictions.head(top_n)
        
        logger.info(f"上位{top_n}銘柄を選択: {', '.join(top_stocks['Ticker'].tolist())}")
        return top_stocks
    
    def filter_profitable_stocks(self, predictions, min_change_pct=0.1):
        """
        予測結果から利益が見込める銘柄のみをフィルタリング
        
        Parameters:
        -----------
        predictions : pd.DataFrame
            予測結果のデータフレーム
        min_change_pct : float
            最小予測変化率（%）
            
        Returns:
        --------
        pd.DataFrame
            フィルタリングされた銘柄
        """
        if predictions is None or predictions.empty:
            logger.warning("予測結果がありません")
            return pd.DataFrame()
            
        # 予測変化率が最小値以上の銘柄をフィルタリング
        profitable_stocks = predictions[predictions['Predicted_Change_Pct'] >= min_change_pct]
        
        logger.info(f"利益が見込める銘柄をフィルタリング: {len(profitable_stocks)}銘柄")
        return profitable_stocks
    
    def optimize_daily_profit(self, predictions, available_balance, max_positions=None):
        """
        1日の収益が最大になるように銘柄を選択
        
        Parameters:
        -----------
        predictions : pd.DataFrame
            予測結果のデータフレーム
        available_balance : float
            利用可能な残高
        max_positions : int
            最大ポジション数（Noneの場合はself.max_stocksを使用）
            
        Returns:
        --------
        tuple
            (選択された銘柄, 投資配分)
        """
        if predictions is None or predictions.empty:
            logger.warning("予測結果がありません")
            return pd.DataFrame(), {}
            
        if max_positions is None:
            max_positions = self.max_stocks
            
        # 利益が見込める銘柄のみをフィルタリング
        profitable_stocks = self.filter_profitable_stocks(predictions)
        
        if profitable_stocks.empty:
            logger.warning("利益が見込める銘柄がありません")
            return pd.DataFrame(), {}
            
        # 最大ポジション数に制限
        if len(profitable_stocks) > max_positions:
            profitable_stocks = profitable_stocks.head(max_positions)
            
        # 投資配分を計算
        allocations = self.calculate_allocations(profitable_stocks, available_balance)
        
        logger.info(f"1日の収益最大化: {len(profitable_stocks)}銘柄を選択")
        return profitable_stocks, allocations
