#!/usr/bin/env python3
import os
import time
import json
import logging
import requests
import websocket
from datetime import datetime, timedelta
from typing import Set, List, Dict, Any, Optional, Union

# ロガー設定
logger = logging.getLogger(__name__)

class KabuSession:
    """
    kabuステーションAPI通信の基本クラス
    - トークン管理（20分ごとの更新）
    - REST API呼び出し
    - WebSocket接続
    - 銘柄登録管理（最大50銘柄の制限対応）
    """
    def __init__(self, pwd: str, host: str = "http://localhost:18080/kabusapi"):
        """
        初期化
        
        Parameters:
        -----------
        pwd : str
            APIパスワード
        host : str
            API接続先ホスト
        """
        self.pwd = pwd
        self.host = host
        self.token = None
        self.exp = 0  # トークン有効期限（UNIX時間）
        self._symbols_registered: Set[str] = set()  # 現在登録中の銘柄
        self._max_symbols = 50  # 最大登録可能銘柄数
        
    def ensure_token(self) -> None:
        """
        トークンが有効か確認し、必要に応じて更新
        20分の有効期限に対し、1分の余裕を持たせる
        """
        if not self.token or time.time() > self.exp - 60:
            logger.info("トークン取得/更新")
            try:
                res = requests.post(f"{self.host}/token", 
                                  json={"APIPassword": self.pwd})
                data = res.json()
                self.token = data["Token"]
                self.exp = time.time() + 60*20  # 20分有効
                logger.info(f"トークン更新完了: 有効期限 {datetime.fromtimestamp(self.exp)}")
            except Exception as e:
                logger.error(f"トークン取得エラー: {str(e)}")
                raise
            
    def rest(self, method: str, path: str, **kwargs) -> requests.Response:
        """
        REST API呼び出し共通処理
        
        Parameters:
        -----------
        method : str
            HTTPメソッド（GET, POST, PUT等）
        path : str
            APIパス（/から始まる相対パス）
        **kwargs : dict
            requestsに渡す追加パラメータ
            
        Returns:
        --------
        requests.Response
            API呼び出し結果
        """
        self.ensure_token()
        url = f"{self.host}{path}"
        headers = {"X-API-KEY": self.token}
        if "headers" in kwargs:
            kwargs["headers"].update(headers)
        else:
            kwargs["headers"] = headers
            
        logger.debug(f"REST呼び出し: {method} {url}")
        return requests.request(method, url, **kwargs)
    
    def ws(self) -> websocket.WebSocket:
        """
        WebSocket接続取得
        
        Returns:
        --------
        websocket.WebSocket
            接続済みWebSocketオブジェクト
        """
        self.ensure_token()
        ws_url = f"ws://localhost:18080/kabusapi/websocket?token={self.token}"
        try:
            conn = websocket.create_connection(ws_url)
            logger.info("WebSocket接続確立")
            return conn
        except Exception as e:
            logger.error(f"WebSocket接続エラー: {str(e)}")
            raise
        
    def register_symbols(self, symbols: List[str]) -> Set[str]:
        """
        銘柄登録（PUSH配信対象設定）
        
        Parameters:
        -----------
        symbols : List[str]
            登録する銘柄リスト（最大50銘柄）
            
        Returns:
        --------
        Set[str]
            登録された銘柄セット
            
        Raises:
        -------
        ValueError
            登録銘柄数が上限を超えた場合
        """
        if len(symbols) > self._max_symbols:
            raise ValueError(f"登録銘柄数は{self._max_symbols}以下にしてください")
            
        # 現在の登録を解除（既存の登録銘柄と異なる場合）
        if self._symbols_registered and self._symbols_registered != set(symbols):
            logger.info("既存の登録銘柄を解除します")
            self.unregister_symbols(list(self._symbols_registered))
            self._symbols_registered.clear()
            
        # 新しい銘柄を登録
        new_symbols = set(symbols) - self._symbols_registered
        if new_symbols:
            logger.info(f"新規銘柄を登録: {new_symbols}")
            for symbol in new_symbols:
                code = symbol.split('.')[0]  # 1301.T → 1301
                try:
                    res = self.rest("PUT", "/register", json={
                        "Symbols": [{"Symbol": code, "Exchange": 1}]
                    })
                    if res.status_code == 200:
                        self._symbols_registered.add(symbol)
                        logger.debug(f"銘柄登録成功: {symbol}")
                    else:
                        logger.warning(f"銘柄登録失敗: {symbol} - {res.text}")
                except Exception as e:
                    logger.error(f"銘柄登録エラー: {symbol} - {str(e)}")
                
        return self._symbols_registered
        
    def unregister_symbols(self, symbols: List[str]) -> None:
        """
        銘柄登録解除
        
        Parameters:
        -----------
        symbols : List[str]
            登録解除する銘柄リスト
        """
        for symbol in symbols:
            if symbol in self._symbols_registered:
                code = symbol.split('.')[0]
                try:
                    res = self.rest("PUT", "/unregister", json={
                        "Symbols": [{"Symbol": code, "Exchange": 1}]
                    })
                    if res.status_code == 200:
                        self._symbols_registered.remove(symbol)
                        logger.debug(f"銘柄登録解除成功: {symbol}")
                    else:
                        logger.warning(f"銘柄登録解除失敗: {symbol} - {res.text}")
                except Exception as e:
                    logger.error(f"銘柄登録解除エラー: {symbol} - {str(e)}")
    
    def get_symbol_info(self, symbol: str) -> Dict[str, Any]:
        """
        銘柄情報取得
        
        Parameters:
        -----------
        symbol : str
            銘柄コード（1301.T等）
            
        Returns:
        --------
        Dict[str, Any]
            銘柄情報
        """
        code = symbol.split('.')[0]
        try:
            res = self.rest("GET", f"/symbol/{code}", 
                           params={"symbol": code, "exchange": 1})
            return res.json()
        except Exception as e:
            logger.error(f"銘柄情報取得エラー: {symbol} - {str(e)}")
            raise
    
    def get_board(self, symbol: str) -> Dict[str, Any]:
        """
        板情報取得
        
        Parameters:
        -----------
        symbol : str
            銘柄コード（1301.T等）
            
        Returns:
        --------
        Dict[str, Any]
            板情報
        """
        code = symbol.split('.')[0]
        try:
            res = self.rest("GET", f"/board/{code}", 
                           params={"symbol": code, "exchange": 1})
            return res.json()
        except Exception as e:
            logger.error(f"板情報取得エラー: {symbol} - {str(e)}")
            raise
