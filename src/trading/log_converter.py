#!/usr/bin/env python3
import os
import re
import json
import logging
from datetime import datetime
from typing import Dict, List, Any, Tuple, Optional, Set
import argparse
from collections import defaultdict

from src.trading.error_logger import get_error_handler
from src.trading.logging_config import get_module_logger

# ロガー設定
logger = get_module_logger(__name__)

class LogConverter:
    """
    既存ログファイルから構造化エラーログを生成するクラス
    - 従来のログファイルからエラー情報を抽出
    - JSONLフォーマットの構造化エラーログに変換
    - パターン解析によるエラータイプと発生源の識別
    """
    def __init__(self, 
                output_dir: str = "logs/errors", 
                error_patterns: Optional[Dict[str, str]] = None):
        """
        初期化
        
        Parameters:
        -----------
        output_dir : str
            出力先ディレクトリ
        error_patterns : Optional[Dict[str, str]]
            エラーパターン定義（正規表現パターン→エラータイプのマッピング）
        """
        self.output_dir = output_dir
        os.makedirs(output_dir, exist_ok=True)
        
        # エラーハンドラ
        self.error_handler = get_error_handler(output_dir)
        
        # エラーパターン定義（未指定時はデフォルトパターンを使用）
        self.error_patterns = error_patterns or {
            r'Connection refused': 'ConnectionRefusedError',
            r'Failed to establish.*connection': 'ConnectionError',
            r'Max retries exceeded': 'MaxRetriesExceededError',
            r'Timeout': 'TimeoutError',
            r'timed out': 'TimeoutError',
            r'トークン取得エラー': 'TokenError',
            r'認証エラー': 'AuthenticationError',
            r'WebSocketエラー': 'WebSocketError',
            r'Invalid (token|response)': 'ValidationError',
            r'JSONDecodeError': 'JSONDecodeError',
            r'Not Found': 'NotFoundError',
            r'Permission denied': 'PermissionError',
            r'Out of memory': 'OutOfMemoryError'
        }
        
        # モジュールパターン定義（発生源の特定用）
        self.module_patterns = {
            r'src\.trading\.kabu_api': 'kabu_api',
            r'src\.trading\.kabu_collector': 'kabu_collector',
            r'src\.trading\.kabu_trader': 'kabu_trader',
            r'src\.trading\.kabu_system': 'kabu_system',
            r'src\.trading\.tradectl': 'tradectl',
            r'WebSocket': 'websocket',
            r'Token': 'token_service'
        }
        
        # 変換統計
        self.stats = defaultdict(int)
        self.converted_files = set()
        self.parsed_errors = []
    
    def convert_logfile(self, log_path: str, output_date: Optional[str] = None) -> int:
        """
        ログファイルを変換して構造化エラーログを生成
        
        Parameters:
        -----------
        log_path : str
            入力ログファイルパス
        output_date : Optional[str]
            出力ファイルの日付（YYYYMMDDフォーマット）、未指定時はファイル名から推定
            
        Returns:
        --------
        int
            変換されたエラーログの数
        """
        if not os.path.exists(log_path):
            logger.error(f"ログファイルが見つかりません: {log_path}")
            return 0
        
        # 出力日付の決定（ファイル名から推定または指定値を使用）
        if output_date is None:
            # ファイル名からの日付抽出を試みる (例: app_20250401.log -> 20250401)
            date_match = re.search(r'(\d{8})', os.path.basename(log_path))
            if date_match:
                output_date = date_match.group(1)
            else:
                # 現在の日付をデフォルトとする
                output_date = datetime.now().strftime("%Y%m%d")
        
        output_file = os.path.join(self.output_dir, f"errors_{output_date}.jsonl")
        
        # 既に処理済みのファイルをスキップ（重複防止）
        if log_path in self.converted_files:
            logger.info(f"既に処理済みのファイルです: {log_path}")
            return 0
        
        logger.info(f"ログファイル変換: {log_path} → {output_file}")
        
        error_count = 0
        line_count = 0
        
        try:
            with open(log_path, 'r', encoding='utf-8', errors='ignore') as f:
                for line in f:
                    line_count += 1
                    line = line.strip()
                    
                    # エラーログ行を検出（ERROR, CRITICAL, WARNINGなど）
                    if '[ERROR]' in line or '[CRITICAL]' in line or ': Error:' in line:
                        error_data = self._parse_error_line(line)
                        if error_data:
                            error_count += 1
                            self.parsed_errors.append(error_data)
                            
                            # エラーハンドラに記録
                            self.error_handler.log_error(
                                error_type=error_data['error_type'],
                                source=error_data['source'],
                                error_message=error_data['error_message'],
                                context=error_data.get('context')
                            )
                        
            # 処理済みファイルとして記録
            self.converted_files.add(log_path)
            
            # 統計更新
            self.stats['processed_files'] += 1
            self.stats['total_lines'] += line_count
            self.stats['total_errors'] += error_count
            
            logger.info(f"変換完了: {error_count}件のエラーを抽出（全{line_count}行）")
            return error_count
            
        except Exception as e:
            logger.error(f"ログファイル変換中にエラーが発生: {log_path} - {str(e)}")
            return 0
    
    def _parse_error_line(self, line: str) -> Optional[Dict[str, Any]]:
        """
        エラーログ行を解析して構造化データに変換
        
        Parameters:
        -----------
        line : str
            エラーログ行
            
        Returns:
        --------
        Optional[Dict[str, Any]]
            構造化されたエラーデータ、解析失敗時はNone
        """
        try:
            # タイムスタンプ抽出
            timestamp_match = re.search(r'(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2},\d{3})', line)
            if timestamp_match:
                timestamp_str = timestamp_match.group(1)
                timestamp = datetime.strptime(timestamp_str, '%Y-%m-%d %H:%M:%S,%f')
            else:
                timestamp = datetime.now()  # 時刻不明の場合は現在時刻
            
            # モジュール名抽出
            module_match = re.search(r'\[ERROR\] ([a-zA-Z0-9_\.]+):', line)
            module = module_match.group(1) if module_match else "unknown"
            
            # メッセージ部分の抽出
            message_match = re.search(r'\[ERROR\] [a-zA-Z0-9_\.]+: (.*)', line)
            message = message_match.group(1) if message_match else line
            
            # エラータイプの特定
            error_type = 'UnknownError'
            for pattern, type_name in self.error_patterns.items():
                if re.search(pattern, message, re.IGNORECASE):
                    error_type = type_name
                    break
            
            # 発生源の特定
            source = 'unknown'
            for pattern, source_name in self.module_patterns.items():
                if re.search(pattern, line, re.IGNORECASE):
                    source = source_name
                    break
            
            # コンテキスト情報の抽出（特定パターンに対応）
            context = {}
            
            # トークン関連エラーのコンテキスト
            if 'トークン取得エラー' in line or 'token' in line.lower():
                host_match = re.search(r'host=\'([^\']+)\'', line)
                if host_match:
                    context['host'] = host_match.group(1)
            
            # WebSocket関連エラーのコンテキスト
            if 'WebSocketエラー' in line or 'websocket' in line.lower():
                ws_status_match = re.search(r'ws_status=\'([^\']+)\'', line)
                if ws_status_match:
                    context['ws_status'] = ws_status_match.group(1)
            
            # 構造化エラーデータの作成
            error_data = {
                'timestamp': timestamp.isoformat(),
                'error_type': error_type,
                'source': source,
                'error_message': message,
                'module': module
            }
            
            # コンテキストがあれば追加
            if context:
                error_data['context'] = context
                
            return error_data
            
        except Exception as e:
            logger.warning(f"ログ行の解析に失敗: {line[:100]}... - {str(e)}")
            return None
    
    def convert_directory(self, 
                         log_dir: str, 
                         file_pattern: str = r'.*\.log', 
                         recursive: bool = False) -> int:
        """
        ディレクトリ内のログファイルを一括変換
        
        Parameters:
        -----------
        log_dir : str
            入力ログディレクトリ
        file_pattern : str
            対象ファイル名パターン（正規表現）
        recursive : bool
            サブディレクトリも再帰的に処理するか
            
        Returns:
        --------
        int
            変換されたエラーログの総数
        """
        if not os.path.exists(log_dir) or not os.path.isdir(log_dir):
            logger.error(f"ログディレクトリが見つかりません: {log_dir}")
            return 0
        
        total_errors = 0
        file_pattern_re = re.compile(file_pattern)
        
        # 再帰的または非再帰的にファイルを検索
        if recursive:
            for root, _, files in os.walk(log_dir):
                for filename in files:
                    if file_pattern_re.match(filename):
                        file_path = os.path.join(root, filename)
                        total_errors += self.convert_logfile(file_path)
        else:
            for filename in os.listdir(log_dir):
                if file_pattern_re.match(filename) and os.path.isfile(os.path.join(log_dir, filename)):
                    file_path = os.path.join(log_dir, filename)
                    total_errors += self.convert_logfile(file_path)
        
        logger.info(f"ディレクトリ変換完了: {log_dir} - {total_errors}件のエラーを抽出")
        return total_errors
    
    def get_conversion_stats(self) -> Dict[str, Any]:
        """
        変換処理の統計情報を取得
        
        Returns:
        --------
        Dict[str, Any]
            変換統計情報
        """
        # エラータイプごとの集計
        error_types = defaultdict(int)
        sources = defaultdict(int)
        
        for error in self.parsed_errors:
            error_types[error['error_type']] += 1
            sources[error['source']] += 1
        
        stats = {
            'processed_files': self.stats['processed_files'],
            'total_lines': self.stats['total_lines'],
            'total_errors': self.stats['total_errors'],
            'error_types': dict(error_types),
            'error_sources': dict(sources)
        }
        
        return stats


def main():
    """
    コマンドライン実行用のメイン関数
    """
    parser = argparse.ArgumentParser(description='ログファイルをJSONL形式の構造化エラーログに変換')
    parser.add_argument('--input', '-i', required=True, help='入力ログファイルまたはディレクトリ')
    parser.add_argument('--output-dir', '-o', default='logs/errors', help='出力ディレクトリ')
    parser.add_argument('--recursive', '-r', action='store_true', help='ディレクトリを再帰的に処理')
    parser.add_argument('--pattern', '-p', default=r'.*\.log', help='ファイル名パターン（正規表現）')
    parser.add_argument('--date', '-d', help='出力ファイルの日付（YYYYMMDD形式）')
    parser.add_argument('--verbose', '-v', action='store_true', help='詳細なログ出力')
    
    args = parser.parse_args()
    
    # ログレベル設定
    log_level = logging.DEBUG if args.verbose else logging.INFO
    logging.basicConfig(level=log_level, format='%(asctime)s [%(levelname)s] %(name)s: %(message)s')
    
    converter = LogConverter(output_dir=args.output_dir)
    
    # ファイルまたはディレクトリの処理
    if os.path.isfile(args.input):
        # 単一ファイルの処理
        error_count = converter.convert_logfile(args.input, args.date)
        logger.info(f"変換完了: {error_count}件のエラーを抽出")
    elif os.path.isdir(args.input):
        # ディレクトリの処理
        error_count = converter.convert_directory(args.input, args.pattern, args.recursive)
        logger.info(f"ディレクトリ変換完了: {error_count}件のエラーを抽出")
        
        # 変換統計の表示
        stats = converter.get_conversion_stats()
        logger.info(f"変換統計: 処理ファイル数={stats['processed_files']}, "
                  f"総行数={stats['total_lines']}, 抽出エラー数={stats['total_errors']}")
    else:
        logger.error(f"指定されたパスは存在しません: {args.input}")
        return 1
    
    return 0


if __name__ == "__main__":
    exit(main())
