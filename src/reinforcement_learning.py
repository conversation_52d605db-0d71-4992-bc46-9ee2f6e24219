import os
import numpy as np
import pandas as pd
import tensorflow as tf
from tensorflow.keras.models import Sequential, Model, load_model
from tensorflow.keras.layers import Dense, LSTM, Dropout, Input, Flatten
from tensorflow.keras.optimizers import Adam
import gym
from gym import spaces
from stable_baselines3 import DQN, PPO
from stable_baselines3.common.vec_env import DummyVecEnv
from stable_baselines3.common.evaluation import evaluate_policy
import matplotlib.pyplot as plt
import logging

logger = logging.getLogger(__name__)

class StockTradingEnv(gym.Env):
    """
    A custom stock trading environment that implements the gym interface
    for reinforcement learning with the Stable Baselines library
    """
    
    def __init__(self, df, initial_balance=10000, commission=0.0005, window_size=50):
        """
        Initialize the stock trading environment
        
        Parameters:
        -----------
        df : pd.DataFrame
            DataFrame containing stock data with at least 'Ticker', 'Datetime', and 'Close' columns
        initial_balance : float
            Initial cash balance
        commission : float
            Commission rate for trading (e.g., 0.0005 = 0.05%)
        window_size : int
            Size of the observation window (number of time steps)
        """
        super(StockTradingEnv, self).__init__()
        
        self.df = df.sort_values('Datetime').reset_index(drop=True)
        self.initial_balance = initial_balance
        self.commission = commission
        self.window_size = window_size
        
        # Dynamic action space based on number of tickers
        self.tickers = df['Ticker'].unique()
        self.n_tickers = len(self.tickers)
        
        # Action space: 0 = hold, 1 = buy, 2 = sell for each ticker
        # For n tickers, we have 3^n possible actions
        # However, this grows too large, so we'll simplify to:
        # 0 = do nothing, 1...n = buy ticker i, n+1...2n = sell ticker i
        self.action_space = spaces.Discrete(1 + 2 * self.n_tickers)
        
        # Observation space: price data for each ticker + portfolio state
        # Each ticker has window_size historical prices
        # Portfolio state includes: cash balance and shares held for each ticker
        obs_shape = (window_size * self.n_tickers + 1 + self.n_tickers,)
        self.observation_space = spaces.Box(
            low=0, high=np.inf, shape=obs_shape, dtype=np.float32
        )
        
        # Initialize state
        self.reset()
    
    def _next_observation(self):
        """
        Get the current observation (state)
        
        Returns:
        --------
        np.array
            Current state observation
        """
        # If we have less history than window_size, pad with zeros
        if self.current_step < self.window_size:
            padding = self.window_size - self.current_step
            obs = np.zeros((padding + self.current_step) * self.n_tickers)
            for i, ticker in enumerate(self.tickers):
                ticker_data = self.df[self.df['Ticker'] == ticker]
                if self.current_step > 0:
                    prices = ticker_data['Close'].iloc[:self.current_step].values
                    obs[padding*self.n_tickers + i*self.current_step:(padding+i)*self.n_tickers] = prices
        else:
            obs = np.zeros(self.window_size * self.n_tickers)
            for i, ticker in enumerate(self.tickers):
                ticker_data = self.df[self.df['Ticker'] == ticker]
                start = self.current_step - self.window_size
                end = self.current_step
                if start >= 0 and end <= len(ticker_data):
                    prices = ticker_data['Close'].iloc[start:end].values
                    obs[i*self.window_size:(i+1)*self.window_size] = prices
        
        # Append portfolio state: cash balance and shares held
        portfolio = np.array([self.balance] + list(self.shares.values()))
        
        # Concatenate price history and portfolio state
        obs = np.append(obs, portfolio)
        
        return obs
    
    def _get_current_price(self, ticker):
        """
        Get the current price for a ticker
        
        Parameters:
        -----------
        ticker : str
            Ticker symbol
            
        Returns:
        --------
        float
            Current price
        """
        ticker_data = self.df[self.df['Ticker'] == ticker]
        if self.current_step < len(ticker_data):
            return ticker_data['Close'].iloc[self.current_step]
        return 0
    
    def _get_ticker_from_action(self, action):
        """
        Convert action to ticker and action type
        
        Parameters:
        -----------
        action : int
            Action index
            
        Returns:
        --------
        tuple
            (ticker, action_type) where action_type is 'hold', 'buy', or 'sell'
        """
        if action == 0:
            return None, 'hold'
        
        if action <= self.n_tickers:
            ticker_idx = action - 1
            return self.tickers[ticker_idx], 'buy'
        else:
            ticker_idx = action - self.n_tickers - 1
            return self.tickers[ticker_idx], 'sell'
    
    def reset(self):
        """
        Reset the environment
        
        Returns:
        --------
        np.array
            Initial observation
        """
        # Reset portfolio
        self.balance = self.initial_balance
        self.shares = {ticker: 0 for ticker in self.tickers}
        self.current_step = 0
        self.net_worth_history = [self.initial_balance]
        
        return self._next_observation()
    
    def step(self, action):
        """
        Take a step in the environment
        
        Parameters:
        -----------
        action : int
            Action to take
            
        Returns:
        --------
        tuple
            (observation, reward, done, info)
        """
        # Get ticker and action type
        ticker, action_type = self._get_ticker_from_action(action)
        
        # Execute action
        reward = 0
        done = False
        
        if ticker is not None:
            current_price = self._get_current_price(ticker)
            
            if action_type == 'buy' and current_price > 0:
                # Buy as many shares as possible
                max_shares = self.balance // (current_price * (1 + self.commission))
                if max_shares > 0:
                    shares_to_buy = max_shares
                    cost = shares_to_buy * current_price * (1 + self.commission)
                    self.balance -= cost
                    self.shares[ticker] += shares_to_buy
                    #print(f"Bought {shares_to_buy} shares of {ticker} at {current_price}")
            
            elif action_type == 'sell':
                # Sell all shares
                shares_to_sell = self.shares[ticker]
                if shares_to_sell > 0:
                    proceeds = shares_to_sell * current_price * (1 - self.commission)
                    self.balance += proceeds
                    self.shares[ticker] = 0
                    #print(f"Sold {shares_to_sell} shares of {ticker} at {current_price}")
        
        # Calculate net worth
        net_worth = self.balance
        for ticker in self.tickers:
            price = self._get_current_price(ticker)
            net_worth += self.shares[ticker] * price
        
        # Calculate reward (change in net worth)
        if len(self.net_worth_history) > 0:
            prev_net_worth = self.net_worth_history[-1]
            reward = (net_worth - prev_net_worth) / prev_net_worth
        
        # Save net worth history
        self.net_worth_history.append(net_worth)
        
        # Move to the next step
        self.current_step += 1
        
        # Check if we're done
        if self.current_step >= len(self.df) // self.n_tickers:
            done = True
        
        # Get next observation
        obs = self._next_observation()
        
        info = {
            'net_worth': net_worth,
            'balance': self.balance,
            'shares': self.shares.copy()
        }
        
        return obs, reward, done, info
    
    def render(self, mode='human'):
        """
        Render the environment
        
        Parameters:
        -----------
        mode : str
            Rendering mode
        """
        print(f"Step: {self.current_step}")
        print(f"Balance: {self.balance:.2f}")
        
        for ticker in self.tickers:
            price = self._get_current_price(ticker)
            print(f"{ticker}: {self.shares[ticker]} shares at {price:.2f} = {self.shares[ticker] * price:.2f}")
        
        net_worth = self.balance
        for ticker in self.tickers:
            price = self._get_current_price(ticker)
            net_worth += self.shares[ticker] * price
        
        print(f"Net worth: {net_worth:.2f}")
        print("--------------------")

class RLStockTrader:
    """
    Reinforcement Learning for stock trading using DQN and PPO
    """
    
    def __init__(self, model_dir="models"):
        """
        Initialize the RL stock trader
        
        Parameters:
        -----------
        model_dir : str
            Directory to save trained models
        """
        self.model_dir = model_dir
        self.dqn_model = None
        self.ppo_model = None
        os.makedirs(model_dir, exist_ok=True)
    
    def train_dqn(self, env, learning_rate=0.0001, buffer_size=10000, 
                  learning_starts=1000, batch_size=64, tau=1.0,
                  gamma=0.99, train_freq=4, gradient_steps=1,
                  total_timesteps=10000, verbose=1):
        """
        Train a DQN model
        
        Parameters:
        -----------
        env : gym.Env
            Training environment
        learning_rate : float
            Learning rate
        buffer_size : int
            Size of the replay buffer
        learning_starts : int
            Number of steps before learning starts
        batch_size : int
            Batch size for sampling from replay buffer
        tau : float
            Soft update coefficient for target network update
        gamma : float
            Discount factor
        train_freq : int
            Update the model every `train_freq` steps
        gradient_steps : int
            Number of gradient steps to take after each rollout
        total_timesteps : int
            Total number of timesteps to train for
        verbose : int
            Verbosity level (0 = no output, 1 = info)
            
        Returns:
        --------
        stable_baselines3.DQN
            Trained DQN model
        """
        logger.info("Training DQN model...")
        
        # Initialize model
        model = DQN(
            "MlpPolicy",
            env,
            learning_rate=learning_rate,
            buffer_size=buffer_size,
            learning_starts=learning_starts,
            batch_size=batch_size,
            tau=tau,
            gamma=gamma,
            train_freq=train_freq,
            gradient_steps=gradient_steps,
            verbose=verbose
        )
        
        # Train model
        model.learn(total_timesteps=total_timesteps)
        
        # Save model
        model.save(os.path.join(self.model_dir, "dqn_trader"))
        
        # Save for later use
        self.dqn_model = model
        
        return model
    
    def train_ppo(self, env, learning_rate=0.0003, n_steps=2048,
                  batch_size=64, n_epochs=10, gamma=0.99,
                  gae_lambda=0.95, clip_range=0.2, clip_range_vf=None,
                  ent_coef=0.0, vf_coef=0.5, max_grad_norm=0.5,
                  total_timesteps=20000, verbose=1):
        """
        Train a PPO model
        
        Parameters:
        -----------
        env : gym.Env
            Training environment
        learning_rate : float
            Learning rate
        n_steps : int
            Number of steps to run for each environment per update
        batch_size : int
            Minibatch size for each gradient update
        n_epochs : int
            Number of epochs when optimizing the surrogate loss
        gamma : float
            Discount factor
        gae_lambda : float
            Factor for trade-off of bias vs variance for Generalized Advantage Estimator
        clip_range : float
            Clipping parameter for PPO
        clip_range_vf : float
            Clipping parameter for value function
        ent_coef : float
            Entropy coefficient for loss calculation
        vf_coef : float
            Value function coefficient for loss calculation
        max_grad_norm : float
            Maximum norm for gradient clipping
        total_timesteps : int
            Total number of timesteps to train for
        verbose : int
            Verbosity level (0 = no output, 1 = info)
            
        Returns:
        --------
        stable_baselines3.PPO
            Trained PPO model
        """
        logger.info("Training PPO model...")
        
        # Initialize model
        model = PPO(
            "MlpPolicy",
            env,
            learning_rate=learning_rate,
            n_steps=n_steps,
            batch_size=batch_size,
            n_epochs=n_epochs,
            gamma=gamma,
            gae_lambda=gae_lambda,
            clip_range=clip_range,
            clip_range_vf=clip_range_vf,
            ent_coef=ent_coef,
            vf_coef=vf_coef,
            max_grad_norm=max_grad_norm,
            verbose=verbose
        )
        
        # Train model
        model.learn(total_timesteps=total_timesteps)
        
        # Save model
        model.save(os.path.join(self.model_dir, "ppo_trader"))
        
        # Save for later use
        self.ppo_model = model
        
        return model
    
    def evaluate_model(self, model, env, n_eval_episodes=10):
        """
        Evaluate a trained model
        
        Parameters:
        -----------
        model : stable_baselines3.BaseAlgorithm
            Trained model
        env : gym.Env
            Evaluation environment
        n_eval_episodes : int
            Number of evaluation episodes
            
        Returns:
        --------
        tuple
            (mean_reward, std_reward)
        """
        logger.info(f"Evaluating model over {n_eval_episodes} episodes...")
        
        # Evaluate model
        mean_reward, std_reward = evaluate_policy(
            model,
            env,
            n_eval_episodes=n_eval_episodes,
            deterministic=True
        )
        
        logger.info(f"Mean reward: {mean_reward:.2f} +/- {std_reward:.2f}")
        
        return mean_reward, std_reward
    
    def backtest(self, model, env, verbose=False):
        """
        Backtest a trained model
        
        Parameters:
        -----------
        model : stable_baselines3.BaseAlgorithm
            Trained model
        env : gym.Env
            Backtesting environment
        verbose : bool
            Whether to display detailed information
            
        Returns:
        --------
        pd.DataFrame
            DataFrame with backtesting results
        """
        logger.info("Backtesting model...")
        
        # Reset environment
        obs = env.reset()
        done = False
        
        # Lists to store results
        actions = []
        net_worths = []
        balances = []
        shares_held = []
        steps = []
        
        # Run backtest
        while not done:
            action, _ = model.predict(obs, deterministic=True)
            obs, reward, done, info = env.step(action)
            
            actions.append(action)
            net_worths.append(info['net_worth'])
            balances.append(info['balance'])
            shares_held.append(info['shares'])
            steps.append(env.current_step)
            
            if verbose:
                env.render()
        
        # Calculate performance metrics
        initial_balance = env.initial_balance
        final_net_worth = net_worths[-1]
        absolute_return = final_net_worth - initial_balance
        percent_return = (absolute_return / initial_balance) * 100
        
        # Calculate Sharpe ratio
        returns = np.diff(net_worths) / net_worths[:-1]
        sharpe_ratio = np.mean(returns) / np.std(returns) * np.sqrt(252)  # Annualized
        
        # Calculate maximum drawdown
        def calculate_max_drawdown(net_worths):
            peak = net_worths[0]
            max_drawdown = 0
            
            for net_worth in net_worths:
                if net_worth > peak:
                    peak = net_worth
                drawdown = (peak - net_worth) / peak
                max_drawdown = max(max_drawdown, drawdown)
                
            return max_drawdown
        
        max_drawdown = calculate_max_drawdown(net_worths)
        
        # Create results dictionary
        results = {
            'steps': steps,
            'actions': actions,
            'net_worths': net_worths,
            'balances': balances,
            'shares_held': shares_held,
            'absolute_return': absolute_return,
            'percent_return': percent_return,
            'sharpe_ratio': sharpe_ratio,
            'max_drawdown': max_drawdown
        }
        
        # Print summary
        logger.info(f"Initial balance: ${initial_balance:.2f}")
        logger.info(f"Final net worth: ${final_net_worth:.2f}")
        logger.info(f"Absolute return: ${absolute_return:.2f}")
        logger.info(f"Percent return: {percent_return:.2f}%")
        logger.info(f"Sharpe ratio: {sharpe_ratio:.2f}")
        logger.info(f"Maximum drawdown: {max_drawdown*100:.2f}%")
        
        # Plot results
        self.plot_backtest_results(results)
        
        return results
    
    def plot_backtest_results(self, results):
        """
        Plot backtest results
        
        Parameters:
        -----------
        results : dict
            Dictionary with backtest results
        """
        # Create the figure and subplots
        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 10), gridspec_kw={'height_ratios': [3, 1]})
        
        # Plot net worth
        ax1.plot(results['steps'], results['net_worths'], label='Net Worth', color='blue')
        ax1.set_title('Backtest Results')
        ax1.set_xlabel('Step')
        ax1.set_ylabel('Net Worth ($)')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        
        # Plot portfolio composition
        balances = results['balances']
        
        # Get first shares_held entry to determine number of tickers
        first_shares = results['shares_held'][0]
        tickers = list(first_shares.keys())
        
        # Create a list of lists for shares value for each ticker
        shares_values = []
        for ticker in tickers:
            ticker_values = []
            for i, shares in enumerate(results['shares_held']):
                # This assumes we have the prices for each ticker at each step
                # In a real implementation, you would need to have this data
                ticker_values.append(shares[ticker] * 100)  # Placeholder price
            shares_values.append(ticker_values)
        
        # Stack the values
        stacked_data = np.vstack([balances] + shares_values)
        
        # Plot the stacked area
        labels = ['Cash'] + tickers
        ax2.stackplot(results['steps'], stacked_data, labels=labels, alpha=0.7)
        ax2.set_title('Portfolio Composition')
        ax2.set_xlabel('Step')
        ax2.set_ylabel('Value ($)')
        ax2.legend(loc='upper left')
        ax2.grid(True, alpha=0.3)
        
        # Adjust layout and save
        plt.tight_layout()
        plt.savefig(os.path.join(self.model_dir, "backtest_results.png"))
        plt.close()
    
    def load_model(self, model_type='dqn'):
        """
        Load a trained model
        
        Parameters:
        -----------
        model_type : str
            Type of model to load ('dqn' or 'ppo')
            
        Returns:
        --------
        stable_baselines3.BaseAlgorithm
            Loaded model
        """
        if model_type.lower() == 'dqn':
            model_path = os.path.join(self.model_dir, "dqn_trader")
            model = DQN.load(model_path)
            self.dqn_model = model
            return model
        elif model_type.lower() == 'ppo':
            model_path = os.path.join(self.model_dir, "ppo_trader")
            model = PPO.load(model_path)
            self.ppo_model = model
            return model
        else:
            raise ValueError(f"Unknown model type: {model_type}")
    
    def create_rl_ensemble(self, df, window_size=50, n_eval_episodes=5):
        """
        Create an ensemble of RL models
        
        Parameters:
        -----------
        df : pd.DataFrame
            DataFrame containing stock data
        window_size : int
            Size of the observation window
        n_eval_episodes : int
            Number of episodes for evaluation
            
        Returns:
        --------
        dict
            Dictionary with ensemble information
        """
        logger.info("Creating RL ensemble...")
        
        # Create environment
        env = StockTradingEnv(df, window_size=window_size)
        env = DummyVecEnv([lambda: env])
        
        # Train DQN model
        dqn_model = self.train_dqn(env, total_timesteps=5000)
        
        # Train PPO model
        ppo_model = self.train_ppo(env, total_timesteps=10000)
        
        # Evaluate models
        dqn_mean, dqn_std = self.evaluate_model(dqn_model, env, n_eval_episodes=n_eval_episodes)
        ppo_mean, ppo_std = self.evaluate_model(ppo_model, env, n_eval_episodes=n_eval_episodes)
        
        # Create ensemble weights based on performance
        total_mean = dqn_mean + ppo_mean
        dqn_weight = dqn_mean / total_mean
        ppo_weight = ppo_mean / total_mean
        
        # Create ensemble information
        ensemble = {
            'dqn_model': dqn_model,
            'ppo_model': ppo_model,
            'weights': {
                'dqn': dqn_weight,
                'ppo': ppo_weight
            },
            'performance': {
                'dqn': dqn_mean,
                'ppo': ppo_mean
            }
        }
        
        logger.info(f"Ensemble weights: DQN={dqn_weight:.2f}, PPO={ppo_weight:.2f}")
        
        return ensemble
    
    def ensemble_predict(self, ensemble, obs):
        """
        Make a prediction using the ensemble
        
        Parameters:
        -----------
        ensemble : dict
            Dictionary with ensemble information
        obs : np.array
            Observation (state)
            
        Returns:
        --------
        int
            Predicted action
        """
        # Get predictions from each model
        dqn_action, _ = ensemble['dqn_model'].predict(obs, deterministic=True)
        ppo_action, _ = ensemble['ppo_model'].predict(obs, deterministic=True)
        
        # Get weights
        dqn_weight = ensemble['weights']['dqn']
        ppo_weight = ensemble['weights']['ppo']
        
        # Simple ensemble: Choose action based on weights
        if np.random.random() < dqn_weight:
            return dqn_action
        else:
            return ppo_action
