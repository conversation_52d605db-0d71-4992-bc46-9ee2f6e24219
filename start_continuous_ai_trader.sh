#!/bin/bash
# 継続的AIトレーダー起動スクリプト

# カレントディレクトリをスクリプトのあるディレクトリに変更
cd "$(dirname "$0")" || exit

# 必要なディレクトリの作成
mkdir -p logs
mkdir -p results
mkdir -p models

# デフォルト設定
INITIAL_BALANCE=1000000
MAX_STOCKS=5
DEMO_MODE=""

# コマンドライン引数の解析
while [[ $# -gt 0 ]]; do
    key="$1"
    case $key in
        --initial-balance|-b)
            INITIAL_BALANCE="$2"
            shift
            shift
            ;;
        --max-stocks|-s)
            MAX_STOCKS="$2"
            shift
            shift
            ;;
        --demo-mode|-d)
            DEMO_MODE="--demo-mode"
            shift
            ;;
        --help|-h)
            echo "使用方法: $0 [オプション]"
            echo ""
            echo "オプション:"
            echo "  -b, --initial-balance AMOUNT 初期資金（デフォルト: 1000000）"
            echo "  -s, --max-stocks NUMBER      最大保有銘柄数（デフォルト: 5）"
            echo "  -d, --demo-mode              デモモード（市場時間に関係なく実行）"
            echo "  -h, --help                   ヘルプメッセージの表示"
            exit 0
            ;;
        *)
            echo "不明なオプション: $key"
            exit 1
            ;;
    esac
done

# 実行コマンドの構築
COMMAND="python continuous_ai_trader.py --initial-balance $INITIAL_BALANCE --max-stocks $MAX_STOCKS $DEMO_MODE"

echo "継続的AIトレーダーを開始します"
echo "実行コマンド: $COMMAND"
echo "開始時刻: $(date)"
echo "Ctrl+Cで停止します"
echo ""

# コマンド実行
eval "$COMMAND"

# 終了処理
echo ""
echo "継続的AIトレーダーを終了しました"
echo "終了時刻: $(date)"