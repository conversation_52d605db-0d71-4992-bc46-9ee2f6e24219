FROM python:3.9-slim

WORKDIR /app

# 必要なパッケージをインストール
RUN pip install --no-cache-dir flask flask-cors websocket-server python-dotenv requests

# アプリケーションのコードをコピー
COPY app.py /app/
COPY kabu_token.py /app/
COPY kabu_websocket.py /app/
COPY kabu_symbol.py /app/
COPY kabu_board.py /app/
COPY kabu_register.py /app/

# 開始スクリプト
COPY start.sh /app/
RUN chmod +x /app/start.sh

# ポート公開
EXPOSE 18081

# アプリケーションの実行
CMD ["/app/start.sh"]
