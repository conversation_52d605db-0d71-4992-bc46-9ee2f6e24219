#!/usr/bin/env python3
import os
import logging
import random
from datetime import datetime
from flask import Blueprint, request, jsonify

# 自作モジュール
from kabu_token import validate_token, get_token_from_request
from kabu_symbol import add_market_data, MOCK_SYMBOLS

# ロギング設定
logger = logging.getLogger(__name__)

# 板情報用ブループリント
board_blueprint = Blueprint('board', __name__)

def generate_board_data(symbol_code, base_price=None):
    """
    銘柄の板情報を生成
    
    Parameters:
    -----------
    symbol_code : str
        銘柄コード
    base_price : int, optional
        基準価格（指定なしの場合は銘柄に応じた値を使用）
        
    Returns:
    --------
    dict
        板情報データ
    """
    # 各銘柄の基準価格を設定
    base_prices = {
        "1301": 9000,
        "1925": 3000,
        "3626": 6100,
        "4689": 225,
        "6758": 12100,
        "7974": 6300,
        "8306": 1050,
        "9432": 4200,
        "9984": 6500
    }
    
    if base_price is None:
        base_price = base_prices.get(symbol_code, 1000)  # 未定義の場合は1000円
    
    # 現在値は基準価格から±1%の範囲でランダム
    current_price = int(base_price * (1 + random.uniform(-1.0, 1.0) / 100))
    
    # 現在時刻
    now = datetime.now()
    time_str = now.strftime("%Y-%m-%d %H:%M:%S")
    
    # 売り注文（高い順に10件）
    sell_orders = []
    sell_price = current_price + 5
    for i in range(10):
        price_step = random.randint(1, 5)
        sell_price += price_step
        sell_orders.append({
            "Price": sell_price,
            "Qty": random.randint(1, 50) * 100
        })
    sell_orders.reverse()  # 価格の高い順に
    
    # 買い注文（高い順に10件）
    buy_orders = []
    buy_price = current_price - 5
    for i in range(10):
        price_step = random.randint(1, 5)
        buy_price -= price_step
        buy_orders.append({
            "Price": buy_price,
            "Qty": random.randint(1, 50) * 100
        })
    
    # 板情報データ
    board_data = {
        "Symbol": symbol_code,
        "SymbolName": MOCK_SYMBOLS.get(symbol_code, {}).get("SymbolName", f"銘柄{symbol_code}"),
        "Exchange": 1,
        "ExchangeName": "東証プライム",
        "CurrentPrice": current_price,
        "CurrentPriceTime": time_str,
        "CurrentPriceStatus": 1,
        "CurrentPriceChangeStatus": random.choice([1, 2, 3]),  # 1:上昇, 2:下降, 3:変化なし
        "CalculationPrice": current_price,
        "PreviousClose": base_price,
        "PreviousCloseTime": (now.replace(hour=15, minute=0, second=0)).strftime("%Y-%m-%d %H:%M:%S"),
        "ChangePreviousClose": current_price - base_price,
        "ChangePreviousClosePer": round((current_price - base_price) / base_price * 100, 2),
        "OpeningPrice": int(base_price * (1 + random.uniform(-0.5, 0.5) / 100)),
        "OpeningPriceTime": (now.replace(hour=9, minute=0, second=0)).strftime("%Y-%m-%d %H:%M:%S"),
        "HighPrice": int(current_price * (1 + random.uniform(0.1, 1.0) / 100)),
        "HighPriceTime": (now.replace(hour=random.randint(9, 14), minute=random.randint(0, 59))).strftime("%Y-%m-%d %H:%M:%S"),
        "LowPrice": int(current_price * (1 - random.uniform(0.1, 1.0) / 100)),
        "LowPriceTime": (now.replace(hour=random.randint(9, 14), minute=random.randint(0, 59))).strftime("%Y-%m-%d %H:%M:%S"),
        "TradingVolume": random.randint(10000, 1000000),
        "TradingVolumeTime": time_str,
        "VWAP": current_price * (1 + random.uniform(-0.3, 0.3) / 100),
        "TradingValue": random.randint(100000, 10000000) * 1000,
        "BidQty": random.randint(100, 5000),
        "BidPrice": current_price - random.randint(1, 3),
        "BidTime": time_str,
        "BidSign": random.choice(["0000", "0101"]),
        "MarketOrderBidQty": random.randint(0, 2000),
        "AskQty": random.randint(100, 5000),
        "AskPrice": current_price + random.randint(1, 3),
        "AskTime": time_str,
        "AskSign": random.choice(["0000", "0101"]),
        "MarketOrderAskQty": random.randint(0, 2000),
        "OverSellQty": random.randint(0, 1000),
        "UnderBuyQty": random.randint(0, 1000),
        "TotalMarketValue": MOCK_SYMBOLS.get(symbol_code, {}).get("TotalMarketValue", 100000000000),
        "ClearingPrice": current_price * (1 + random.uniform(-0.1, 0.1) / 100),
        "IV": random.uniform(20.0, 40.0),
        "Gamma": random.uniform(0.01, 0.1),
        "Theta": random.uniform(-10.0, -1.0),
        "Vega": random.uniform(10.0, 50.0),
        "Delta": random.uniform(0.3, 0.7),
        # 板情報
        "BidOrders": buy_orders,
        "AskOrders": sell_orders
    }
    
    return board_data

@board_blueprint.route('/board/<symbol>', methods=['GET'])
def get_board(symbol):
    """
    板情報取得エンドポイント
    kabuステーションAPIと同様の形式で板情報を返す
    """
    # トークン検証
    token = get_token_from_request(request)
    if not token or not validate_token(token):
        return jsonify({
            "ErrorMessage": "Invalid token",
            "ErrorCode": 401
        }), 401
    
    # クエリパラメータから情報取得
    query_symbol = request.args.get('symbol', symbol)
    
    logger.info(f"板情報リクエスト: {query_symbol}")
    
    # 板情報生成
    board_data = generate_board_data(query_symbol)
    
    return jsonify(board_data)
