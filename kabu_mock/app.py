#!/usr/bin/env python3
import os
import logging
import threading
from flask import Flask, jsonify, request
from flask_cors import CORS
import json
import time

# 各モジュールのインポート
from kabu_token import token_blueprint
from kabu_symbol import symbol_blueprint
from kabu_board import board_blueprint
from kabu_register import register_blueprint, unregister_blueprint
from kabu_websocket import start_websocket_server

# ロギング設定
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

app = Flask(__name__)
CORS(app)  # CORS対応

# 各ブループリントを登録
app.register_blueprint(token_blueprint)
app.register_blueprint(symbol_blueprint)
app.register_blueprint(board_blueprint)
app.register_blueprint(register_blueprint)
app.register_blueprint(unregister_blueprint)

# ルートエンドポイント
@app.route('/')
def index():
    return jsonify({
        "status": "running",
        "name": "kabuステーションAPI モックサーバー",
        "version": "1.0.0",
        "endpoints": [
            "/token",
            "/symbol/{symbol}",
            "/board/{symbol}",
            "/register",
            "/unregister",
            "/websocket (WebSocketエンドポイント)"
        ]
    })

# エラーハンドリング
@app.errorhandler(404)
def not_found(e):
    return jsonify({"ErrorMessage": "Not Found", "ErrorCode": 404}), 404

@app.errorhandler(500)
def server_error(e):
    return jsonify({"ErrorMessage": "Internal Server Error", "ErrorCode": 500}), 500

# WebSocketサーバー起動（別スレッド）
def start_ws_server():
    logger.info("WebSocketサーバー起動")
    start_websocket_server()

if __name__ == '__main__':
    # WebSocketサーバーを別スレッドで起動
    ws_thread = threading.Thread(target=start_ws_server)
    ws_thread.daemon = True
    ws_thread.start()
    
    # RESTサーバー起動
    port = int(os.environ.get("KABU_MOCK_PORT", 18081))
    logger.info(f"RESTサーバー起動 (ポート: {port})")
    app.run(host='0.0.0.0', port=port)
