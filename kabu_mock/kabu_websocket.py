#!/usr/bin/env python3
import os
import time
import json
import random
import threading
import logging
from datetime import datetime
import websocket_server
from websocket_server import WebsocketServer

# 自作モジュール
from kabu_register import get_registered_symbols
from kabu_board import generate_board_data

# ロギング設定
logger = logging.getLogger(__name__)

# クライアント管理
connected_clients = {}  # {client_id: クライアントオブジェクト}
clients_lock = threading.Lock()

# WebSocketサーバーオブジェクト
websocket_server_instance = None

# WebSocketサーバーのポート
WEBSOCKET_PORT = 18083

def new_client(client, server):
    """
    新しいクライアント接続時のコールバック
    
    Parameters:
    -----------
    client : dict
        接続したクライアント情報
    server : WebsocketServer
        WebSocketサーバーインスタンス
    """
    with clients_lock:
        connected_clients[client['id']] = client
    
    logger.info(f"新規WebSocket接続: ID={client['id']}, アドレス={client['address']}")
    
    # 接続確認メッセージを送信
    message = {
        "Type": "SystemMessage",
        "Message": "WebSocket接続確立",
        "Time": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    }
    server.send_message(client, json.dumps(message))

def client_left(client, server):
    """
    クライアント切断時のコールバック
    
    Parameters:
    -----------
    client : dict
        切断したクライアント情報
    server : WebsocketServer
        WebSocketサーバーインスタンス
    """
    with clients_lock:
        if client['id'] in connected_clients:
            del connected_clients[client['id']]
    
    logger.info(f"WebSocket切断: ID={client['id']}")

def message_received(client, server, message):
    """
    クライアントからメッセージ受信時のコールバック
    
    Parameters:
    -----------
    client : dict
        メッセージを送信したクライアント情報
    server : WebsocketServer
        WebSocketサーバーインスタンス
    message : str
        受信したメッセージ
    """
    logger.info(f"WebSocketメッセージ受信: ID={client['id']}, メッセージ={message}")
    
    # 必要に応じてメッセージに応答する処理を実装

def broadcast_price_updates():
    """
    登録された銘柄の価格更新情報をブロードキャスト
    定期的に呼び出されることを想定
    """
    if not websocket_server_instance:
        return
    
    # 接続クライアントがいない場合は何もしない
    if not connected_clients:
        return
    
    # 登録された銘柄を取得
    symbols = get_registered_symbols()
    if not symbols:
        return
    
    # 各銘柄の情報をブロードキャスト
    for symbol_key in symbols:
        # symbol_key は "1301:1" のような形式
        symbol, exchange = symbol_key.split(':')
        
        # 板情報からモック価格データを生成
        board_data = generate_board_data(symbol)
        
        # PUSH APIフォーマットで配信するデータを作成
        push_data = {
            "Symbol": symbol,
            "SymbolName": board_data.get("SymbolName", f"銘柄{symbol}"),
            "Exchange": int(exchange),
            "ExchangeName": "東証プライム",
            "CurrentPrice": board_data.get("CurrentPrice"),
            "CurrentPriceTime": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "CurrentPriceStatus": random.choice([1, 2, 3]),  # 1:上昇, 2:下降, 3:変化なし
            "CurrentPriceChangeStatus": random.choice([1, 2, 3]),  # 1:上昇, 2:下降, 3:変化なし
            "CalcPrice": board_data.get("CalculationPrice"),
            "PreviousClose": board_data.get("PreviousClose"),
            "PreviousCloseTime": board_data.get("PreviousCloseTime"),
            "ChangePreviousClose": board_data.get("ChangePreviousClose"),
            "ChangePreviousClosePer": board_data.get("ChangePreviousClosePer"),
            "OpeningPrice": board_data.get("OpeningPrice"),
            "OpeningPriceTime": board_data.get("OpeningPriceTime"),
            "HighPrice": board_data.get("HighPrice"),
            "HighPriceTime": board_data.get("HighPriceTime"),
            "LowPrice": board_data.get("LowPrice"),
            "LowPriceTime": board_data.get("LowPriceTime"),
            "TradingVolume": board_data.get("TradingVolume"),
            "TradingVolumeTime": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "VWAP": board_data.get("VWAP"),
            "TradingValue": board_data.get("TradingValue"),
            "BidPrice": board_data.get("BidPrice"),
            "BidTime": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "BidQty": board_data.get("BidQty"),
            "BidSign": "0000",
            "MarketOrderBidQty": board_data.get("MarketOrderBidQty"),
            "AskPrice": board_data.get("AskPrice"),
            "AskTime": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "AskQty": board_data.get("AskQty"),
            "AskSign": "0000",
            "MarketOrderAskQty": board_data.get("MarketOrderAskQty"),
            "OverSellQty": board_data.get("OverSellQty"),
            "UnderBuyQty": board_data.get("UnderBuyQty"),
            "TotalMarketValue": board_data.get("TotalMarketValue"),
            "ISIN": f"JP{random.randint(1000000000, 9999999999)}",
            "TradingDate": datetime.now().strftime("%Y-%m-%d"),
            "ServerTime": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "MessageType": "BOARD"
        }
        
        # 全クライアントに送信
        with clients_lock:
            for client_id, client in connected_clients.items():
                try:
                    websocket_server_instance.send_message(client, json.dumps(push_data))
                except Exception as e:
                    logger.error(f"クライアントへの送信エラー: ID={client_id}, エラー={str(e)}")
        
        # 連続送信による負荷を避けるため少し待機
        time.sleep(0.05)

def price_update_worker():
    """
    価格更新ワーカースレッド
    定期的に価格更新情報をブロードキャスト
    """
    logger.info("価格更新ワーカー開始")
    
    while True:
        try:
            # 価格更新の送信間隔（3秒〜8秒のランダムな間隔）
            interval = random.uniform(3, 8)
            
            # 登録銘柄の価格更新をブロードキャスト
            broadcast_price_updates()
            
            # 次の更新まで待機
            time.sleep(interval)
            
        except Exception as e:
            logger.error(f"価格更新ワーカーエラー: {str(e)}")
            time.sleep(5)  # エラー時は5秒待機してリトライ

def start_websocket_server():
    """
    WebSocketサーバーを起動
    """
    global websocket_server_instance
    
    try:
        # サーバーポート設定（環境変数または固定値）
        port = int(os.environ.get("KABU_WEBSOCKET_PORT", WEBSOCKET_PORT))
        
        # WebSocketサーバーインスタンス作成
        server = WebsocketServer(host='0.0.0.0', port=port)
        websocket_server_instance = server
        
        # コールバック設定
        server.set_fn_new_client(new_client)
        server.set_fn_client_left(client_left)
        server.set_fn_message_received(message_received)
        
        logger.info(f"WebSocketサーバー起動 (ポート: {port})")
        
        # 価格更新ワーカーをバックグラウンドで起動
        update_thread = threading.Thread(target=price_update_worker)
        update_thread.daemon = True
        update_thread.start()
        
        # サーバー起動（ブロッキング呼び出し）
        server.run_forever()
        
    except Exception as e:
        logger.error(f"WebSocketサーバー起動エラー: {str(e)}")
        raise
