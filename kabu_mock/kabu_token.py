#!/usr/bin/env python3
import os
import uuid
import time
import logging
from flask import Blueprint, request, jsonify

# ロギング設定
logger = logging.getLogger(__name__)

# トークン管理用ブループリント
token_blueprint = Blueprint('token', __name__)

# 有効なトークンを保持する辞書
# {トークン: 有効期限(UNIX時間)}
active_tokens = {}

@token_blueprint.route('/token', methods=['POST'])
def issue_token():
    """
    トークン発行エンドポイント
    kabuステーションAPIと同様の形式でトークンを発行
    """
    try:
        data = request.json
        api_password = data.get('APIPassword', '')
        
        # APIパスワードチェック（モックなので実際には検証しない）
        expected_pwd = os.environ.get('KABU_API_PWD', 'marumori02')
        
        # パスワードログは開発時のみ
        if os.environ.get('DEBUG', 'false').lower() == 'true':
            logger.debug(f"受信パスワード: {api_password}, 期待パスワード: {expected_pwd}")
        
        # 新しいトークンを生成
        token = str(uuid.uuid4())
        # 20分（1200秒）の有効期限を設定
        expiry = time.time() + 1200
        active_tokens[token] = expiry
        
        logger.info(f"新規トークン発行: {token[:10]}...（有効期限: {int(expiry)}）")
        
        # レスポンス形式（kabuステーションAPI準拠）
        return jsonify({
            "ResultCode": 0,
            "Token": token
        })
        
    except Exception as e:
        logger.error(f"トークン発行エラー: {str(e)}")
        return jsonify({
            "ErrorMessage": str(e),
            "ErrorCode": 500
        }), 500

def validate_token(token):
    """
    トークンの有効性を検証
    
    Parameters:
    -----------
    token : str
        検証するトークン
        
    Returns:
    --------
    bool
        トークンが有効ならTrue、そうでなければFalse
    """
    now = time.time()
    if token in active_tokens and now < active_tokens[token]:
        return True
    return False

def get_token_from_request(request):
    """
    リクエストからトークンを取得
    
    Parameters:
    -----------
    request : flask.Request
        リクエストオブジェクト
        
    Returns:
    --------
    str
        トークン文字列。ない場合はNone
    """
    return request.headers.get('X-API-KEY')
