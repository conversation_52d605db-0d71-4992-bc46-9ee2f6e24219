#!/usr/bin/env python3
import os
import logging
import threading
from flask import Blueprint, request, jsonify

# 自作モジュール
from kabu_token import validate_token, get_token_from_request

# ロギング設定
logger = logging.getLogger(__name__)

# 登録銘柄の管理用リスト（グローバル変数として定義）
# システム全体で共有するための設計
registered_symbols = set()
registered_symbols_lock = threading.Lock()  # スレッドセーフな操作のためのロック

# 最大登録可能銘柄数
MAX_REGISTERED_SYMBOLS = 50

# 銘柄登録用ブループリント
register_blueprint = Blueprint('register', __name__)
# 銘柄登録解除用ブループリント
unregister_blueprint = Blueprint('unregister', __name__)

@register_blueprint.route('/register', methods=['PUT'])
def register_symbols():
    """
    銘柄登録エンドポイント（PUSH配信対象設定）
    kabuステーションAPIと同様の形式で銘柄を登録
    """
    # トークン検証
    token = get_token_from_request(request)
    if not token or not validate_token(token):
        return jsonify({
            "ErrorMessage": "Invalid token",
            "ErrorCode": 401
        }), 401
    
    try:
        data = request.json
        symbols_to_register = data.get('Symbols', [])
        
        if not symbols_to_register:
            return jsonify({
                "ErrorMessage": "No symbols specified",
                "ErrorCode": 400
            }), 400
        
        # 登録情報をログに出力
        logger.info(f"銘柄登録リクエスト: {symbols_to_register}")
        
        # 登録結果を格納するリスト
        register_success = []
        register_error = []
        
        # スレッドセーフな操作のためにロックを取得
        with registered_symbols_lock:
            # 現在の登録数をチェック
            current_count = len(registered_symbols)
            
            for symbol_info in symbols_to_register:
                symbol = symbol_info.get('Symbol')
                exchange = symbol_info.get('Exchange', 1)
                
                # 最大登録数チェック
                if current_count >= MAX_REGISTERED_SYMBOLS:
                    register_error.append({
                        "Symbol": symbol,
                        "Exchange": exchange,
                        "ErrorCode": 10,
                        "ErrorMessage": f"Maximum number of registered symbols ({MAX_REGISTERED_SYMBOLS}) exceeded"
                    })
                    continue
                
                # 既に登録されているかチェック
                symbol_key = f"{symbol}:{exchange}"
                if symbol_key in registered_symbols:
                    register_success.append({
                        "Symbol": symbol,
                        "Exchange": exchange
                    })
                    continue
                
                # 新規登録
                registered_symbols.add(symbol_key)
                current_count += 1
                register_success.append({
                    "Symbol": symbol,
                    "Exchange": exchange
                })
                
                logger.info(f"銘柄登録成功: {symbol_key}")
        
        # 登録結果のレスポンス
        result = {
            "RegisterList": register_success
        }
        
        if register_error:
            result["ErrorList"] = register_error
        
        return jsonify(result)
        
    except Exception as e:
        logger.error(f"銘柄登録エラー: {str(e)}")
        return jsonify({
            "ErrorMessage": str(e),
            "ErrorCode": 500
        }), 500

@unregister_blueprint.route('/unregister', methods=['PUT'])
def unregister_symbols():
    """
    銘柄登録解除エンドポイント
    kabuステーションAPIと同様の形式で銘柄の登録を解除
    """
    # トークン検証
    token = get_token_from_request(request)
    if not token or not validate_token(token):
        return jsonify({
            "ErrorMessage": "Invalid token",
            "ErrorCode": 401
        }), 401
    
    try:
        data = request.json
        symbols_to_unregister = data.get('Symbols', [])
        
        if not symbols_to_unregister:
            return jsonify({
                "ErrorMessage": "No symbols specified",
                "ErrorCode": 400
            }), 400
        
        # 登録解除情報をログに出力
        logger.info(f"銘柄登録解除リクエスト: {symbols_to_unregister}")
        
        # 登録解除結果を格納するリスト
        unregister_success = []
        unregister_error = []
        
        # スレッドセーフな操作のためにロックを取得
        with registered_symbols_lock:
            for symbol_info in symbols_to_unregister:
                symbol = symbol_info.get('Symbol')
                exchange = symbol_info.get('Exchange', 1)
                
                symbol_key = f"{symbol}:{exchange}"
                
                # 登録されているかチェック
                if symbol_key in registered_symbols:
                    registered_symbols.remove(symbol_key)
                    unregister_success.append({
                        "Symbol": symbol,
                        "Exchange": exchange
                    })
                    logger.info(f"銘柄登録解除成功: {symbol_key}")
                else:
                    unregister_error.append({
                        "Symbol": symbol,
                        "Exchange": exchange,
                        "ErrorCode": 11,
                        "ErrorMessage": "Symbol not registered"
                    })
        
        # 登録解除結果のレスポンス
        result = {
            "UnregisterList": unregister_success
        }
        
        if unregister_error:
            result["ErrorList"] = unregister_error
        
        return jsonify(result)
        
    except Exception as e:
        logger.error(f"銘柄登録解除エラー: {str(e)}")
        return jsonify({
            "ErrorMessage": str(e),
            "ErrorCode": 500
        }), 500

def get_registered_symbols():
    """
    現在登録されている銘柄のリストを取得
    
    Returns:
    --------
    set
        登録銘柄のセット
    """
    with registered_symbols_lock:
        return registered_symbols.copy()
