#!/usr/bin/env python3
import os
import logging
import random
from datetime import datetime
from flask import Blueprint, request, jsonify

# 自作モジュール
from kabu_token import validate_token, get_token_from_request

# ロギング設定
logger = logging.getLogger(__name__)

# 銘柄情報用ブループリント
symbol_blueprint = Blueprint('symbol', __name__)

# モック用の銘柄情報テンプレート
MOCK_SYMBOLS = {
    # 東証プライム銘柄
    "1301": {
        "SymbolName": "極洋",
        "DisplayName": "極洋",
        "Exchange": 1,
        "ExchangeName": "東証プライム",
        "TotalMarketValue": 95040000000,
        "TotalStocks": 10600000,
        "TradingUnit": 100,
        "FiscalYearEndBasic": 3
    },
    "1925": {
        "SymbolName": "大和ハウス工業",
        "DisplayName": "大和ハウス",
        "Exchange": 1,
        "ExchangeName": "東証プライム",
        "TotalMarketValue": 2070000000000,
        "TotalStocks": 690000000,
        "TradingUnit": 100,
        "FiscalYearEndBasic": 3
    },
    "3626": {
        "SymbolName": "TIS",
        "DisplayName": "TIS",
        "Exchange": 1,
        "ExchangeName": "東証プライム",
        "TotalMarketValue": 760000000000,
        "TotalStocks": 124000000,
        "TradingUnit": 100,
        "FiscalYearEndBasic": 3
    },
    "4689": {
        "SymbolName": "Zホールディングス",
        "DisplayName": "Zホールディングス",
        "Exchange": 1,
        "ExchangeName": "東証プライム",
        "TotalMarketValue": 1750000000000,
        "TotalStocks": 7800000000,
        "TradingUnit": 100,
        "FiscalYearEndBasic": 3
    },
    "6758": {
        "SymbolName": "ソニーグループ",
        "DisplayName": "ソニーG",
        "Exchange": 1,
        "ExchangeName": "東証プライム",
        "TotalMarketValue": 15000000000000,
        "TotalStocks": 1240000000,
        "TradingUnit": 100,
        "FiscalYearEndBasic": 3
    },
    "7974": {
        "SymbolName": "任天堂",
        "DisplayName": "任天堂",
        "Exchange": 1,
        "ExchangeName": "東証プライム",
        "TotalMarketValue": 8200000000000,
        "TotalStocks": 129900000,
        "TradingUnit": 100,
        "FiscalYearEndBasic": 3
    },
    "8306": {
        "SymbolName": "三菱UFJフィナンシャル・グループ",
        "DisplayName": "三菱UFJFG",
        "Exchange": 1,
        "ExchangeName": "東証プライム",
        "TotalMarketValue": 13600000000000,
        "TotalStocks": 12900000000,
        "TradingUnit": 100,
        "FiscalYearEndBasic": 3
    },
    "9432": {
        "SymbolName": "日本電信電話",
        "DisplayName": "NTT",
        "Exchange": 1,
        "ExchangeName": "東証プライム",
        "TotalMarketValue": 15100000000000,
        "TotalStocks": 3600000000,
        "TradingUnit": 100,
        "FiscalYearEndBasic": 3
    },
    "9984": {
        "SymbolName": "ソフトバンクグループ",
        "DisplayName": "ソフトバンクG",
        "Exchange": 1,
        "ExchangeName": "東証プライム",
        "TotalMarketValue": 11100000000000,
        "TotalStocks": 1700000000,
        "TradingUnit": 100,
        "FiscalYearEndBasic": 3
    }
}

# 銘柄データにランダムな市場価格情報を追加
def add_market_data(symbol_data, symbol_code):
    # 各銘柄の基準価格を設定
    base_prices = {
        "1301": 9000,
        "1925": 3000,
        "3626": 6100,
        "4689": 225,
        "6758": 12100,
        "7974": 6300,
        "8306": 1050,
        "9432": 4200,
        "9984": 6500
    }
    
    base_price = base_prices.get(symbol_code, 1000)  # 未定義の場合は1000円
    
    # 前日比を±3%の範囲でランダム生成
    change_percent = random.uniform(-3.0, 3.0)
    change_amount = int(base_price * change_percent / 100)
    
    # 現在値 = 基準価格 + 変動額
    current_price = base_price + change_amount
    
    # 出来高は基準価格によって調整（高い株ほど出来高は少なめ）
    volume_factor = 1000000 / (base_price + 1000)
    volume = int(random.uniform(5000, 50000) * volume_factor)
    
    # 現在時刻
    now = datetime.now()
    time_str = now.strftime("%Y-%m-%d %H:%M:%S")
    
    # 市場データを追加
    market_data = {
        "CurrentPrice": current_price,
        "CurrentPriceTime": time_str,
        "CurrentPriceStatus": 1,
        "CurrentPriceChangeStatus": 1 if change_amount > 0 else 2 if change_amount < 0 else 3,
        "CalculationPrice": current_price,
        "PreviousClose": base_price,
        "PreviousCloseTime": (now.replace(hour=15, minute=0, second=0)).strftime("%Y-%m-%d %H:%M:%S"),
        "ChangePreviousClose": change_amount,
        "ChangePreviousClosePer": round(change_percent, 2),
        "OpeningPrice": int(base_price * (1 + random.uniform(-1.0, 1.0) / 100)),
        "OpeningPriceTime": (now.replace(hour=9, minute=0, second=0)).strftime("%Y-%m-%d %H:%M:%S"),
        "HighPrice": int(current_price * (1 + random.uniform(0, 2.0) / 100)),
        "HighPriceTime": (now.replace(hour=random.randint(9, 14), minute=random.randint(0, 59), second=random.randint(0, 59))).strftime("%Y-%m-%d %H:%M:%S"),
        "LowPrice": int(current_price * (1 - random.uniform(0, 2.0) / 100)),
        "LowPriceTime": (now.replace(hour=random.randint(9, 14), minute=random.randint(0, 59), second=random.randint(0, 59))).strftime("%Y-%m-%d %H:%M:%S"),
        "Volume": volume,
        "AskPrice": current_price + random.randint(1, 5),
        "AskQty": random.randint(100, 5000),
        "BidPrice": current_price - random.randint(1, 5),
        "BidQty": random.randint(100, 5000),
        "VWAP": current_price * (1 + random.uniform(-0.5, 0.5) / 100),
        "TradingValue": volume * current_price,
        "MarketOrderSellQty": random.randint(0, 1000),
        "MarketOrderBuyQty": random.randint(0, 1000),
        "OverSellQty": random.randint(0, 500),
        "UnderBuyQty": random.randint(0, 500),
        "TotalMarketValue": symbol_data["TotalMarketValue"],
        "ClearingPrice": current_price * (1 + random.uniform(-0.2, 0.2) / 100)
    }
    
    # 元のデータとマージ
    return {**symbol_data, **market_data}

@symbol_blueprint.route('/symbol/<symbol>', methods=['GET'])
def get_symbol(symbol):
    """
    銘柄情報取得エンドポイント
    kabuステーションAPIと同様の形式で銘柄情報を返す
    """
    # トークン検証
    token = get_token_from_request(request)
    if not token or not validate_token(token):
        return jsonify({
            "ErrorMessage": "Invalid token",
            "ErrorCode": 401
        }), 401
    
    # クエリパラメータから情報取得
    query_symbol = request.args.get('symbol', symbol)
    exchange = request.args.get('exchange', 1)
    
    logger.info(f"銘柄情報リクエスト: {query_symbol}")
    
    # モックデータ取得
    if query_symbol in MOCK_SYMBOLS:
        # 基本データにランダムな市場データを追加
        result = add_market_data(MOCK_SYMBOLS[query_symbol], query_symbol)
        return jsonify(result)
    else:
        # 存在しない銘柄の場合はデフォルトデータを返す
        logger.warning(f"未定義銘柄リクエスト: {query_symbol} - デフォルトデータを返します")
        
        # デフォルトデータを生成
        default_data = {
            "Symbol": query_symbol,
            "SymbolName": f"銘柄{query_symbol}",
            "DisplayName": f"銘柄{query_symbol}",
            "Exchange": int(exchange),
            "ExchangeName": "東証プライム",
            "TotalMarketValue": 100000000000,
            "TotalStocks": 10000000,
            "TradingUnit": 100,
            "FiscalYearEndBasic": 3
        }
        
        # 市場データを追加
        result = add_market_data(default_data, query_symbol)
        return jsonify(result)
