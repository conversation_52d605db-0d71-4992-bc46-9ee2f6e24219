#!/bin/bash
# Quick Start Script for Nikkei225 AI Trading System

echo "🚀 Starting Nikkei225 AI Trading System..."

# Check if virtual environment exists
if [ ! -d "venv" ]; then
    echo "Creating virtual environment..."
    python3 -m venv venv
fi

# Activate virtual environment
source venv/bin/activate

# Install/update dependencies
echo "Installing dependencies..."
pip install -r requirements.txt

# Run system setup
echo "Running system setup..."
python setup_system.py

# Start the system
echo "Starting trading system..."
python continuous_ai_trader.py --demo-mode

echo "✅ System started successfully!"
