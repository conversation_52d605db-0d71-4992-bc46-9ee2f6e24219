#!/usr/bin/env python3
"""
日経225 AI取引システム - ランダム仮想取引テストスクリプト

ランダムな株価データを生成して仮想取引を行うことで、
AIモデルと取引パイプラインのテストを行います。
このスクリプトで生成されたデータはテスト用なので、継続学習パイプラインには取り込まれません。
"""

import os
import sys
import argparse
import logging
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import json
import time
import random

# 自作モジュールのインポート
from src.simple_virtual_trader import SimpleVirtualTrader
from src.feature_engineering import add_features
from src.trading.trading_strategy import TradingStrategy
from src.trading.feedback_learner import FeedbackLearner

# ロギング設定
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f"logs/random_trading_test_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class RandomDataGenerator:
    """ランダムな株価データ生成クラス"""

    def __init__(self, config):
        """
        初期化

        Parameters:
        -----------
        config : dict
            設定情報
        """
        self.config = config
        self.tickers = config.get("tickers", [
            "7203.T", "9984.T", "6758.T", "6861.T", "8306.T",
            "9432.T", "9433.T", "6501.T", "6502.T", "7267.T"
        ])
        self.days = config.get("data_days", 30)
        self.volatility = config.get("volatility", 0.02)  # 日次ボラティリティ
        self.trend = config.get("trend", 0.0005)  # 上昇トレンド係数

        logger.info(f"ランダムデータジェネレータを初期化しました: {len(self.tickers)}銘柄, {self.days}日分")

    def generate_random_stock_data(self, ticker, base_price=None):
        """
        ティッカーのランダムな株価データを生成

        Parameters:
        -----------
        ticker : str
            ティッカーシンボル
        base_price : float
            初期価格（Noneの場合はランダムに設定）

        Returns:
        --------
        pd.DataFrame
            生成された株価データ
        """
        # 今日から過去days日分の日付を生成
        end_date = datetime.now()
        start_date = end_date - timedelta(days=self.days)
        date_range = pd.date_range(start=start_date, end=end_date, freq='D')

        # 基本価格の設定
        if base_price is None:
            base_price = random.uniform(1000, 10000)  # 1000円〜10000円の範囲でランダム

        # ランダムウォークで株価生成
        np.random.seed(hash(ticker) % 2**32)  # ティッカーごとに異なるシードを設定

        # 日次リターンを生成（ボラティリティと弱いトレンドを加える）
        daily_returns = np.random.normal(self.trend, self.volatility, len(date_range))

        # 各日の価格を計算
        prices = [base_price]
        for ret in daily_returns:
            prices.append(prices[-1] * (1 + ret))
        prices = prices[1:]  # 初期値を除外

        # 株価データの生成
        data = []
        for i, date in enumerate(date_range):
            # 乱数成分
            random_factor = 0.005  # 0.5%の乱数成分

            # 終値
            close = prices[i]

            # 始値、高値、安値を生成
            open_price = close * (1 + np.random.uniform(-random_factor, random_factor))
            high = max(open_price, close) * (1 + abs(np.random.uniform(0, random_factor * 2)))
            low = min(open_price, close) * (1 - abs(np.random.uniform(0, random_factor * 2)))

            # 出来高
            volume = int(np.random.uniform(50000, 1000000))

            # データに追加
            data.append({
                'Date': date,
                'Open': open_price,
                'High': high,
                'Low': low,
                'Close': close,
                'Volume': volume,
                'Ticker': ticker
            })

        return pd.DataFrame(data)

    def generate_dataset(self):
        """
        全ティッカーのランダムデータセットを生成

        Returns:
        --------
        pd.DataFrame
            全ティッカーの株価データ
        """
        all_data = []

        for ticker in self.tickers:
            # ティッカーのランダムデータ生成
            ticker_data = self.generate_random_stock_data(ticker)
            all_data.append(ticker_data)

        # 全データを結合
        if all_data:
            df = pd.concat(all_data, axis=0)
            logger.info(f"生成されたデータセット: {len(df)}行, {len(self.tickers)}銘柄")
            return df
        else:
            logger.error("データセットの生成に失敗しました")
            return pd.DataFrame()

class RandomTradingTester:
    """ランダムデータで仮想取引をテストするクラス"""

    def __init__(self, config):
        """
        初期化

        Parameters:
        -----------
        config : dict
            設定情報
        """
        self.config = config
        self.results_dir = config.get("results_dir", "results/random_testing")

        # 必ず「テストモード」として実行
        self.test_mode = True

        # 結果ディレクトリ作成
        os.makedirs(self.results_dir, exist_ok=True)

        # 仮想取引インスタンス作成
        self.trader = SimpleVirtualTrader(
            initial_balance=config.get("initial_balance", 1000000),
            commission_rate=config.get("commission_rate", 0.0001),
            results_dir=self.results_dir
        )

        # 取引戦略
        self.strategy = TradingStrategy()

        # ティッカーリスト
        self.tickers = config.get("tickers", [
            "7203.T", "9984.T", "6758.T", "6861.T", "8306.T",
            "9432.T", "9433.T", "6501.T", "6502.T", "7267.T"
        ])

        # ランダムデータジェネレータ
        self.data_generator = RandomDataGenerator(config)

        # フィードバックラーナー（テストモード設定）
        self.feedback_learner = FeedbackLearner(results_dir=self.results_dir)
        self.feedback_learner.set_test_mode(self.test_mode)

        logger.info(f"ランダム取引テスターを初期化しました: {len(self.tickers)}銘柄")
        logger.info(f"モード: テストモード（学習なし）")
        logger.info(f"初期残高: {self.trader.initial_balance:,.0f}円")

    def prepare_data(self, market_data):
        """
        データ準備（特徴量エンジニアリング）

        Parameters:
        -----------
        market_data : pd.DataFrame
            元データ

        Returns:
        --------
        pd.DataFrame
            特徴量追加後のデータ
        """
        logger.info("特徴量の計算を開始します...")

        try:
            # 特徴量追加
            features_df = add_features(market_data)
            logger.info(f"追加された特徴量: {len([c for c in features_df.columns if c not in market_data.columns])}個")

            return features_df
        except Exception as e:
            logger.error(f"特徴量計算エラー: {str(e)}")
            return market_data  # エラー時は元のデータを返す

    def generate_signals(self, df):
        """
        取引シグナル生成

        Parameters:
        -----------
        df : pd.DataFrame
            特徴量データ

        Returns:
        --------
        dict
            ティッカーごとのスコア
        """
        logger.info("取引シグナルを生成しています...")

        # 最新データ（ティッカーごとに最後の行）を取得
        latest_data = {}
        ticker_scores = {}

        # ティッカーごとの取引シグナル生成
        for ticker in self.tickers:
            ticker_df = df[df["Ticker"] == ticker].copy() if "Ticker" in df.columns else df.copy()

            if ticker_df.empty:
                logger.warning(f"{ticker}のデータがありません")
                continue

            latest = ticker_df.iloc[-1]
            latest_data[ticker] = latest

            # シンプルなルールベースのスコアリング
            score = 0

            # トレンド指標
            if "SMA_5" in latest and "SMA_20" in latest:
                if latest["SMA_5"] > latest["SMA_20"]:
                    score += 1
                else:
                    score -= 1

            # RSI指標
            if "RSI_14" in latest:
                if latest["RSI_14"] < 30:  # 買われすぎ
                    score += 2
                elif latest["RSI_14"] > 70:  # 売られすぎ
                    score -= 2

            # MACD指標
            if "MACD" in latest and "MACD_Signal" in latest:
                if latest["MACD"] > latest["MACD_Signal"]:
                    score += 1
                else:
                    score -= 1

            # ボリンジャーバンド
            if "BB_Lower" in latest and "Close" in latest and "BB_Upper" in latest:
                if latest["Close"] < latest["BB_Lower"]:  # 下限を下回る（買い）
                    score += 2
                elif latest["Close"] > latest["BB_Upper"]:  # 上限を上回る（売り）
                    score -= 2

            # 最終スコア（-5〜+5）
            ticker_scores[ticker] = max(-5, min(5, score)) / 5  # -1.0〜+1.0に正規化

        logger.info(f"{len(ticker_scores)}銘柄のスコアを生成しました")
        return ticker_scores

    def execute_trading(self, ticker_scores, market_data):
        """
        取引実行

        Parameters:
        -----------
        ticker_scores : dict
            ティッカーごとのスコア
        market_data : pd.DataFrame
            市場データ
        """
        logger.info("仮想取引を実行しています...")

        # 現在価格の取得
        latest_prices = {}
        for ticker in self.tickers:
            ticker_df = market_data[market_data["Ticker"] == ticker].copy() if "Ticker" in market_data.columns else market_data.copy()

            if not ticker_df.empty:
                latest_prices[ticker] = ticker_df["Close"].iloc[-1]

        # 現在のポートフォリオ状態
        logger.info(f"現在の残高: {self.trader.balance:,.0f}円")
        logger.info(f"現在のポートフォリオ: {len(self.trader.portfolio)}銘柄")

        # ポートフォリオ価値
        portfolio_value = self.trader.calculate_portfolio_value(latest_prices)
        profit_loss, profit_loss_pct = self.trader.get_profit_loss(portfolio_value)

        logger.info(f"ポートフォリオ価値: {portfolio_value:,.0f}円")
        logger.info(f"損益: {profit_loss:,.0f}円 ({profit_loss_pct:.2f}%)")

        # 取引前のポートフォリオを保存
        timestamp = datetime.now()
        timestamp_str = timestamp.strftime("%Y%m%d_%H%M")

        # メタデータを作成（テストモードを含む）
        metadata = {
            "timestamp": timestamp.isoformat(),
            "test_mode": self.test_mode,
            "source": "random_testing",
            "data_type": "random",
            "is_test": True
        }

        # 取引前ポートフォリオ保存
        before_portfolio_file = os.path.join(self.results_dir, f"portfolio_before_{timestamp_str}.json")
        before_data = {
            "balance": self.trader.balance,
            "portfolio": self.trader.portfolio,
            "portfolio_value": portfolio_value,
            "initial_balance": self.trader.initial_balance,
            "profit_loss": profit_loss,
            "profit_loss_pct": profit_loss_pct,
            "metadata": metadata
        }

        with open(before_portfolio_file, "w") as f:
            json.dump(before_data, f, indent=2, default=str)
        logger.info(f"取引前ポートフォリオを保存しました: {before_portfolio_file}")

        # 全ての保有銘柄について売却判断
        sold_tickers = []

        for ticker, position in list(self.trader.portfolio.items()):
            score = ticker_scores.get(ticker, 0)
            price = latest_prices.get(ticker)

            if price is None:
                logger.warning(f"{ticker}の価格データがないため、売却判断をスキップします")
                continue

            # 売り条件: スコアが低いか、一定の利益確定条件に達した場合
            sell_threshold = self.config.get("sell_threshold", -0.3)
            profit_taking = self.config.get("profit_taking", 0.05)  # 5%の利益で確定
            stop_loss = self.config.get("stop_loss", -0.05)  # 5%の損失でカット

            # 現在の損益計算
            cost_basis = position["price"]
            profit_pct = (price - cost_basis) / cost_basis

            sell_decision = False
            sell_reason = ""

            if score <= sell_threshold:
                sell_decision = True
                sell_reason = "低スコア"
            elif profit_pct >= profit_taking:
                sell_decision = True
                sell_reason = "利益確定"
            elif profit_pct <= stop_loss:
                sell_decision = True
                sell_reason = "損切り"

            if sell_decision:
                try:
                    qty = position["qty"]
                    logger.info(f"{ticker}を売却: {qty}株 @ {price:.2f}円 ({sell_reason}, 損益: {profit_pct:.2f}%)")
                    self.trader.sell_stock(ticker, price, qty, timestamp)
                    sold_tickers.append(ticker)
                except Exception as e:
                    logger.error(f"{ticker}の売却エラー: {str(e)}")

        # 購入候補の選定（スコアが高い順）
        buy_candidates = [(t, s) for t, s in ticker_scores.items() if s > 0 and t not in self.trader.portfolio]
        buy_candidates.sort(key=lambda x: x[1], reverse=True)

        # 最大保有銘柄数の設定
        max_positions = self.config.get("max_positions", 5)
        max_allocation = self.config.get("max_allocation", 0.2)  # 最大配分（20%）

        # 購入可能な銘柄数
        available_slots = max_positions - len(self.trader.portfolio)

        if available_slots > 0 and buy_candidates:
            # 購入予算の計算（現金の範囲内）
            total_budget = self.trader.balance * self.config.get("cash_allocation", 0.8)  # 現金の80%を使用
            budget_per_stock = total_budget / min(available_slots, len(buy_candidates))

            # 購入実行
            for ticker, score in buy_candidates[:available_slots]:
                price = latest_prices.get(ticker)

                if price is None:
                    logger.warning(f"{ticker}の価格データがないため、購入をスキップします")
                    continue

                # スコアに基づいた配分調整（0〜max_allocation）
                allocation = max_allocation * score
                position_budget = min(budget_per_stock, self.trader.balance * allocation)

                # 数量計算
                qty = int(position_budget / price)

                if qty > 0:
                    try:
                        logger.info(f"{ticker}を購入: {qty}株 @ {price:.2f}円 (スコア: {score:.2f})")
                        self.trader.buy_stock(ticker, price, qty, timestamp)
                    except Exception as e:
                        logger.error(f"{ticker}の購入エラー: {str(e)}")

        # 取引後のポートフォリオ価値
        portfolio_value_after = self.trader.calculate_portfolio_value(latest_prices)
        profit_loss_after, profit_loss_pct_after = self.trader.get_profit_loss(portfolio_value_after)

        # 取引後ポートフォリオ保存
        after_portfolio_file = os.path.join(self.results_dir, f"portfolio_after_{timestamp_str}.json")
        after_data = {
            "balance": self.trader.balance,
            "portfolio": self.trader.portfolio,
            "portfolio_value": portfolio_value_after,
            "initial_balance": self.trader.initial_balance,
            "profit_loss": profit_loss_after,
            "profit_loss_pct": profit_loss_pct_after,
            "metadata": metadata
        }

        with open(after_portfolio_file, "w") as f:
            json.dump(after_data, f, indent=2, default=str)
        logger.info(f"取引後ポートフォリオを保存しました: {after_portfolio_file}")

        # 取引履歴保存
        trades_file = os.path.join(self.results_dir, f"trades_{timestamp_str}.csv")
        trades_df = pd.DataFrame(self.trader.trades)
        if not trades_df.empty:
            trades_df.to_csv(trades_file, index=False)
            logger.info(f"取引履歴を保存しました: {trades_file}")
        else:
            logger.info("取引がありませんでした")

        # メタデータファイルも保存
        metadata_file = os.path.join(self.results_dir, f"metadata_{timestamp_str}.json")
        with open(metadata_file, "w") as f:
            json.dump(metadata, f, indent=2, default=str)
        logger.info(f"メタデータを保存しました: {metadata_file}")

        # フィードバックラーナーにデータを渡す
        # テストモードのため、この処理はスキップされる（ラーナー内部で判断）
        self.feedback_learner.collect_feedback_data(before_portfolio_file, after_portfolio_file, metadata)

        # 取引後の状態
        logger.info(f"取引後のポートフォリオ: {len(self.trader.portfolio)}銘柄")
        logger.info(f"残高: {self.trader.balance:,.0f}円")

    def run_test(self):
        """
        テスト実行
        """
        logger.info("ランダムデータによる仮想取引テストを開始します...")
        logger.info(f"モード: テストモード（学習なし）")

        # ランダム市場データ生成
        market_data = self.data_generator.generate_dataset()
        if market_data.empty:
            logger.error("市場データの生成に失敗しました。テストを中止します。")
            return False

        # データ準備
        processed_data = self.prepare_data(market_data)

        # シグナル生成
        ticker_scores = self.generate_signals(processed_data)

        # 取引実行
        self.execute_trading(ticker_scores, market_data)

        logger.info("テストが正常に完了しました。")
        return True

def main():
    """メイン関数"""
    parser = argparse.ArgumentParser(description="ランダムデータによる仮想取引テスト（学習なし）")
    parser.add_argument("--config", type=str, default="config/random_testing_config.json",
                      help="設定ファイルのパス")
    parser.add_argument("--balance", type=float, default=1000000,
                      help="初期資金")
    parser.add_argument("--commission", type=float, default=0.0001,
                      help="取引手数料率")
    parser.add_argument("--days", type=int, default=30,
                      help="生成するデータの日数")
    parser.add_argument("--tickers", type=int, default=10,
                      help="生成するティッカー数")
    parser.add_argument("--volatility", type=float, default=0.02,
                      help="株価ボラティリティ")
    parser.add_argument("--trend", type=float, default=0.0005,
                      help="トレンド係数（正=上昇、負=下降）")
    args = parser.parse_args()

    # 設定ファイルの読み込み
    config = {
        "initial_balance": args.balance,
        "commission_rate": args.commission,
        "data_days": args.days,
        "results_dir": "results/random_testing",
        "max_positions": 5,
        "cash_allocation": 0.8,  # 現金の80%を使用
        "max_allocation": 0.2,  # 1銘柄あたり最大20%配分
        "sell_threshold": -0.3,  # 売り判断しきい値
        "profit_taking": 0.05,  # 5%の利益で確定
        "stop_loss": -0.05,  # 5%の損失でカット
        "volatility": args.volatility,
        "trend": args.trend,
        "tickers": []
    }

    # ティッカーリストの生成（ランダム）
    for i in range(args.tickers):
        config["tickers"].append(f"TEST{i+1}.T")

    # 設定ファイルが存在する場合は読み込み
    if os.path.exists(args.config):
        try:
            with open(args.config, 'r') as f:
                file_config = json.load(f)
                config.update(file_config)
        except Exception as e:
            logger.error(f"設定ファイルのロードエラー: {e}")
    else:
        # 設定ファイルが存在しない場合は作成
        os.makedirs(os.path.dirname(args.config), exist_ok=True)
        try:
            with open(args.config, 'w') as f:
                json.dump(config, f, indent=4)
            logger.info(f"設定ファイルを作成しました: {args.config}")
        except Exception as e:
            logger.error(f"設定ファイルの作成エラー: {e}")

    # プログラム実行
    tester = RandomTradingTester(config)
    tester.run_test()

if __name__ == "__main__":
    main()
