#!/usr/bin/env python3
"""
Nikkei225 AI Trading System - Environment Setup Script

This script sets up the complete environment for the trading system,
handling Python 3.12 compatibility and dependency installation.
"""

import os
import sys
import subprocess
import platform
import logging
from pathlib import Path

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def check_python_version():
    """Check if Python version is compatible"""
    version = sys.version_info
    logger.info(f"Python version: {version.major}.{version.minor}.{version.micro}")
    
    if version.major != 3 or version.minor < 8:
        logger.error("Python 3.8+ is required. Please upgrade Python.")
        return False
    
    if version.minor >= 12:
        logger.info("Python 3.12+ detected - using compatible package versions")
    
    return True

def create_directories():
    """Create required directories"""
    directories = [
        'data', 'models', 'results', 'logs', 'cache',
        'data/raw', 'data/processed', 'data/international',
        'data/forex', 'data/commodities', 'data/indices',
        'models/ensemble', 'models/individual',
        'results/backtests', 'results/live_trading',
        'logs/trading', 'logs/system', 'logs/errors'
    ]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
        logger.info(f"Created directory: {directory}")

def install_system_dependencies():
    """Install system-level dependencies if possible"""
    system = platform.system().lower()
    
    if system == "linux":
        logger.info("Detected Linux system")
        # Check if we can install system packages
        try:
            # Try to install build essentials for TA-Lib
            subprocess.run(["which", "gcc"], check=True, capture_output=True)
            logger.info("Build tools already available")
        except subprocess.CalledProcessError:
            logger.warning("Build tools not available. TA-Lib installation may fail.")
            logger.info("To install build tools: sudo apt-get install build-essential")
    
    elif system == "darwin":
        logger.info("Detected macOS system")
        try:
            subprocess.run(["which", "brew"], check=True, capture_output=True)
            logger.info("Homebrew detected. You can install TA-Lib with: brew install ta-lib")
        except subprocess.CalledProcessError:
            logger.warning("Homebrew not found. Consider installing it for easier TA-Lib setup.")
    
    elif system == "windows":
        logger.info("Detected Windows system")
        logger.info("For TA-Lib on Windows, consider using conda: conda install -c conda-forge ta-lib")

def install_core_packages():
    """Install core packages that are essential"""
    core_packages = [
        "numpy>=1.24.0",
        "pandas>=2.0.0", 
        "matplotlib>=3.7.0",
        "seaborn>=0.12.0",
        "scikit-learn>=1.3.0",
        "requests>=2.31.0",
        "beautifulsoup4>=4.12.0",
        "lxml>=4.9.0",
        "yfinance>=0.2.18",
        "flask>=3.0.0",
        "python-dotenv>=1.0.0",
        "tqdm>=4.65.0",
        "pytz>=2023.3"
    ]
    
    logger.info("Installing core packages...")
    for package in core_packages:
        try:
            subprocess.run([sys.executable, "-m", "pip", "install", package], 
                         check=True, capture_output=True)
            logger.info(f"✓ Installed: {package}")
        except subprocess.CalledProcessError as e:
            logger.error(f"✗ Failed to install: {package}")
            logger.error(f"Error: {e}")

def install_ml_packages():
    """Install machine learning packages"""
    ml_packages = [
        "torch>=2.0.0",
        "torchvision>=0.15.0", 
        "tensorflow>=2.15.0",
        "xgboost>=2.0.0",
        "lightgbm>=4.0.0",
        "gymnasium>=0.29.0",
        "stable-baselines3>=2.0.0"
    ]
    
    logger.info("Installing ML packages...")
    for package in ml_packages:
        try:
            subprocess.run([sys.executable, "-m", "pip", "install", package], 
                         check=True, capture_output=True)
            logger.info(f"✓ Installed: {package}")
        except subprocess.CalledProcessError as e:
            logger.warning(f"⚠ Failed to install: {package}")
            logger.warning(f"Error: {e}")
            logger.info(f"You may need to install {package} manually")

def install_optional_packages():
    """Install optional packages with fallback"""
    optional_packages = [
        ("pandas-datareader>=0.10.0", "Alternative data sources"),
        ("flask-socketio>=5.3.0", "Real-time web interface"),
        ("eventlet>=0.33.0", "Web server performance"),
        ("pytest>=7.4.0", "Testing framework")
    ]
    
    logger.info("Installing optional packages...")
    for package, description in optional_packages:
        try:
            subprocess.run([sys.executable, "-m", "pip", "install", package], 
                         check=True, capture_output=True)
            logger.info(f"✓ Installed: {package} ({description})")
        except subprocess.CalledProcessError as e:
            logger.warning(f"⚠ Failed to install: {package}")
            logger.info(f"This package provides: {description}")

def setup_ta_lib():
    """Setup TA-Lib with fallback options"""
    logger.info("Setting up TA-Lib (Technical Analysis Library)...")
    
    try:
        # Try to install TA-Lib
        subprocess.run([sys.executable, "-m", "pip", "install", "TA-Lib"], 
                     check=True, capture_output=True)
        logger.info("✓ TA-Lib installed successfully")
        return True
    except subprocess.CalledProcessError:
        logger.warning("⚠ TA-Lib installation failed")
        logger.info("TA-Lib requires system-level dependencies.")
        logger.info("Installation options:")
        logger.info("1. Ubuntu/Debian: sudo apt-get install libta-lib-dev")
        logger.info("2. macOS: brew install ta-lib")
        logger.info("3. Windows: conda install -c conda-forge ta-lib")
        logger.info("4. Or download from: https://www.ta-lib.org/")
        
        # Create a fallback custom indicators module
        create_custom_indicators_fallback()
        return False

def create_custom_indicators_fallback():
    """Create a fallback for TA-Lib functionality"""
    logger.info("Creating custom indicators fallback...")
    
    fallback_content = '''"""
Custom Technical Indicators - TA-Lib Fallback

This module provides basic technical indicators when TA-Lib is not available.
"""

import pandas as pd
import numpy as np

def sma(data, window):
    """Simple Moving Average"""
    return data.rolling(window=window).mean()

def ema(data, window):
    """Exponential Moving Average"""
    return data.ewm(span=window).mean()

def rsi(data, window=14):
    """Relative Strength Index"""
    delta = data.diff()
    gain = (delta.where(delta > 0, 0)).rolling(window=window).mean()
    loss = (-delta.where(delta < 0, 0)).rolling(window=window).mean()
    rs = gain / loss
    return 100 - (100 / (1 + rs))

def bollinger_bands(data, window=20, num_std=2):
    """Bollinger Bands"""
    sma_val = sma(data, window)
    std_val = data.rolling(window=window).std()
    upper = sma_val + (std_val * num_std)
    lower = sma_val - (std_val * num_std)
    return upper, sma_val, lower

def macd(data, fast=12, slow=26, signal=9):
    """MACD Indicator"""
    ema_fast = ema(data, fast)
    ema_slow = ema(data, slow)
    macd_line = ema_fast - ema_slow
    signal_line = ema(macd_line, signal)
    histogram = macd_line - signal_line
    return macd_line, signal_line, histogram

# Add more indicators as needed
'''
    
    with open('src/custom_indicators_fallback.py', 'w') as f:
        f.write(fallback_content)
    
    logger.info("✓ Created custom indicators fallback")

def create_environment_file():
    """Create a sample .env file"""
    env_content = '''# Nikkei225 AI Trading System Environment Variables

# KabuStation API Configuration
KABU_API_PWD=your_kabu_password_here
KABU_API_URL=http://localhost:18080/kabusapi

# Trading Configuration
TRADING_MODE=paper  # nocash, paper, dryrun, live
INITIAL_BALANCE=1000000
MAX_STOCKS=5

# System Configuration
GPU_ENABLED=false
LOG_LEVEL=INFO
DATA_UPDATE_INTERVAL=3600

# Web Interface
WEB_HOST=localhost
WEB_PORT=5000
WEB_DEBUG=false

# Data Sources
USE_CACHE=true
CACHE_EXPIRY_HOURS=24
API_RATE_LIMIT=true

# Backup and Recovery
AUTO_BACKUP=true
BACKUP_INTERVAL_HOURS=24
'''
    
    if not os.path.exists('.env'):
        with open('.env.example', 'w') as f:
            f.write(env_content)
        logger.info("✓ Created .env.example file")
        logger.info("Copy .env.example to .env and configure your settings")
    else:
        logger.info("✓ .env file already exists")

def main():
    """Main setup function"""
    logger.info("🚀 Starting Nikkei225 AI Trading System setup...")
    
    # Check Python version
    if not check_python_version():
        sys.exit(1)
    
    # Create directories
    create_directories()
    
    # Check system dependencies
    install_system_dependencies()
    
    # Install packages in order of importance
    install_core_packages()
    install_ml_packages()
    install_optional_packages()
    
    # Setup TA-Lib
    setup_ta_lib()
    
    # Create environment file
    create_environment_file()
    
    logger.info("✅ Environment setup completed!")
    logger.info("Next steps:")
    logger.info("1. Copy .env.example to .env and configure your settings")
    logger.info("2. If TA-Lib installation failed, install it manually")
    logger.info("3. Run: python src/main.py --help to see available options")

if __name__ == "__main__":
    main()
