# docker-compose.yml for Nikkei225 AI Trading System

services:
  # メインのAI取引システム
  nikkei225-ai-trading:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: nikkei225-ai-system
    restart: unless-stopped
    ports:
      - "5001:5000"    # Webダッシュボード
      - "8080:8080"    # API エンドポイント
    environment:
      - KABU_API_PWD=${KABU_API_PWD}
      - ALPHA_VANTAGE_API_KEY=${ALPHA_VANTAGE_API_KEY:-}
      - TF_ENABLE_ONEDNN_OPTS=0
      - TF_CPP_MIN_LOG_LEVEL=2
      - TZ=Asia/Tokyo
    volumes:
      # データ永続化
      - ./data:/app/data
      - ./logs:/app/logs
      - ./models:/app/models
      - ./results:/app/results
      - ./config:/app/config
      # タイムゾーン設定
      - /etc/localtime:/etc/localtime:ro
    # GPU support removed for compatibility
    healthcheck:
      test: ["CMD", "python3", "-c", "import requests; requests.get('http://localhost:5000/health', timeout=5)"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    networks:
      - nikkei225-network

  # Webダッシュボード専用（オプション）
  nikkei225-web:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: nikkei225-web-dashboard
    restart: unless-stopped
    ports:
      - "5001:5000"
    environment:
      - KABU_API_PWD=${KABU_API_PWD}
      - TZ=Asia/Tokyo
    volumes:
      - ./data:/app/data:ro
      - ./logs:/app/logs:ro
      - ./results:/app/results:ro
    command: ["web"]
    depends_on:
      - nikkei225-ai-trading
    networks:
      - nikkei225-network
    profiles:
      - web-only

  # データ収集専用サービス（オプション）
  nikkei225-data-collector:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: nikkei225-data-collector
    restart: unless-stopped
    environment:
      - KABU_API_PWD=${KABU_API_PWD}
      - ALPHA_VANTAGE_API_KEY=${ALPHA_VANTAGE_API_KEY:-}
      - TZ=Asia/Tokyo
    volumes:
      - ./data:/app/data
      - ./logs:/app/logs
      - ./config:/app/config
    command: ["data"]
    networks:
      - nikkei225-network
    profiles:
      - data-only

  # 取引システム専用（オプション）
  nikkei225-trading:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: nikkei225-trading-engine
    restart: unless-stopped
    environment:
      - KABU_API_PWD=${KABU_API_PWD}
      - TF_ENABLE_ONEDNN_OPTS=0
      - TF_CPP_MIN_LOG_LEVEL=2
      - TZ=Asia/Tokyo
    volumes:
      - ./data:/app/data
      - ./logs:/app/logs
      - ./models:/app/models
      - ./results:/app/results
      - ./config:/app/config
    command: ["trading"]
    # GPU support removed for compatibility
    depends_on:
      - nikkei225-data-collector
    networks:
      - nikkei225-network
    profiles:
      - trading-only

  # Kabu Station モック（開発・テスト用）
  kabu-mock:
    build:
      context: ./kabu_mock
      dockerfile: Dockerfile
    container_name: kabu-mock
    environment:
      - TZ=Asia/Tokyo
      - KABU_MOCK_PORT=18082
      - KABU_WEBSOCKET_PORT=18083
      - KABU_API_PWD=${KABU_API_PWD:-marumori02}
      - DEBUG=false
    ports:
      - "18082:18082"
      - "18083:18083"
    restart: unless-stopped
    networks:
      - nikkei225-network
    profiles:
      - development

networks:
  nikkei225-network:
    driver: bridge

volumes:
  nikkei225-data:
    driver: local
  nikkei225-logs:
    driver: local
  nikkei225-models:
    driver: local
