#!/bin/bash
# 継続学習プロセスを停止するスクリプト
if [ -f "logs/continuous_learning.pid" ]; then
    PID=$(cat "logs/continuous_learning.pid")
    if ps -p $PID > /dev/null; then
        echo "継続学習プロセス (PID: $PID) を停止します..."
        kill $PID
        echo "プロセスを停止しました。"
    else
        echo "プロセス (PID: $PID) は既に実行されていません。"
    fi
    rm "logs/continuous_learning.pid"
else
    echo "PIDファイルが見つかりません。"
    echo "手動で停止するには: pkill -f \"python continuous_learning_batch_processor.py\""
fi
