#!/bin/bash
# kabuステーションAPI取引モード設定スクリプト

# カレントディレクトリをスクリプトのあるディレクトリに変更
cd "$(dirname "$0")" || exit

# 引数チェック
if [ $# -ne 1 ]; then
    echo "使用方法: $0 <mode>"
    echo "  mode: nocash, paper, live のいずれか"
    exit 1
fi

MODE="$1"

# モードのバリデーション
if [ "$MODE" != "nocash" ] && [ "$MODE" != "paper" ] && [ "$MODE" != "live" ]; then
    echo "エラー: 不正なモード '$MODE'"
    echo "有効なモード: nocash, paper, live"
    exit 1
fi

# Liveモードの場合は確認
if [ "$MODE" = "live" ]; then
    echo "警告: Liveモードでは実際の取引が行われます。"
    read -p "続行しますか？ (y/N): " CONFIRM
    if [ "$CONFIRM" != "y" ] && [ "$CONFIRM" != "Y" ]; then
        echo "キャンセルされました"
        exit 0
    fi
fi

# モード設定コマンドの実行
COMMAND="python -m src.trading.tradectl set-mode $MODE --force"
echo "実行コマンド: $COMMAND"
eval "$COMMAND"
