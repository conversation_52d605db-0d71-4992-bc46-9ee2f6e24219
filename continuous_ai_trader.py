#!/usr/bin/env python3
"""
継続的AIトレーディングシステム

完全自立型の継続学習パイプライン
- 市場時間中（9:00-15:00）に自動的に取引を実行
- 一定間隔で取引結果を収集し、AIモデルを改良
- 24時間365日稼働可能な設計

使用方法:
python continuous_ai_trader.py --initial-balance 1000000 --max-stocks 5
"""

import os
import sys
import time
import argparse
import json
import logging
import random
import signal
import traceback
from datetime import datetime, timedelta
from pathlib import Path

# ロギング設定
log_dir = 'logs'
os.makedirs(log_dir, exist_ok=True)
log_file = os.path.join(log_dir, f'continuous_trader_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log')

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler(log_file)
    ]
)
logger = logging.getLogger('continuous_ai_trader')

# グローバル変数
running = True
last_trading_time = None
last_learning_time = None
trading_count = 0
learning_count = 0
current_portfolio = None
day_start_portfolio = None
daily_stats = []

def signal_handler(sig, frame):
    """シグナルハンドラー（Ctrl+Cでの終了処理）"""
    global running
    logger.info("終了シグナルを受信しました。完了までお待ちください...")
    running = False

def is_market_hours():
    """市場時間内かどうかを確認（9:00-15:00）"""
    now = datetime.now()
    
    # 土日はスキップ
    if now.weekday() >= 5:  # 5=土曜日, 6=日曜日
        return False
    
    # 市場時間（9:00-15:00）
    market_start = now.replace(hour=9, minute=0, second=0, microsecond=0)
    market_end = now.replace(hour=15, minute=0, second=0, microsecond=0)
    
    return market_start <= now <= market_end

def is_new_trading_day():
    """新しい取引日かどうかを確認"""
    global day_start_portfolio
    
    if day_start_portfolio is None:
        return True
        
    now = datetime.now()
    day_start = now.replace(hour=9, minute=0, second=0, microsecond=0)
    
    # 初回または午前9時以降で前回の取引日と異なる場合
    if last_trading_time is None or (now >= day_start and now.date() != last_trading_time.date()):
        return True
        
    return False

def run_trading():
    """仮想取引を実行"""
    global last_trading_time, trading_count, current_portfolio, day_start_portfolio
    
    try:
        logger.info(f"取引を実行中... (実行回数: {trading_count + 1})")
        
        # 新しい取引日の開始
        if is_new_trading_day() and is_market_hours():
            logger.info("新しい取引日を開始します")
            day_start_portfolio = current_portfolio
            record_daily_stats()
        
        # API制限を考慮してデータキャッシュシステムを活用
        use_cache = "--use-cache"
        
        # 取引実行間隔に基づいてリクエスト数を制限
        # yfinanceは1分間に300リクエスト制限があるため、取引間隔を調整
        if trading_count > 0 and last_trading_time is not None:
            time_since_last = (datetime.now() - last_trading_time).total_seconds()
            if time_since_last < 60:  # 1分以内の連続実行
                logger.info(f"API制限を避けるため、キャッシュのみを使用します（前回から {time_since_last:.1f}秒）")
                use_cache = "--force-cache"
        
        # 仮想取引の実行
        cmd = f"python src/simple_virtual_trader.py {use_cache}"
        logger.info(f"コマンド実行: {cmd}")
        
        os.system(cmd)  # 実行
        
        # 結果の収集
        portfolio_files = sorted([f for f in os.listdir('results') if f.startswith('portfolio_')], reverse=True)
        if portfolio_files:
            latest_file = os.path.join('results', portfolio_files[0])
            try:
                with open(latest_file, 'r') as f:
                    current_portfolio = json.load(f)
                logger.info(f"ポートフォリオ更新: {latest_file}")
                
                # ポートフォリオの内容を表示
                portfolio_value = current_portfolio.get('portfolio_value', 0)
                profit_loss = current_portfolio.get('profit_loss', 0)
                profit_loss_pct = current_portfolio.get('profit_loss_pct', 0)
                
                logger.info(f"ポートフォリオ価値: {portfolio_value:,.2f}円")
                logger.info(f"損益: {profit_loss:,.2f}円 ({profit_loss_pct:.2f}%)")
                
                # 保有銘柄の表示
                portfolio = current_portfolio.get('portfolio', {})
                if portfolio:
                    logger.info("保有銘柄:")
                    for ticker, details in portfolio.items():
                        qty = details.get('qty', 0)
                        price = details.get('price', 0)
                        value = qty * price
                        logger.info(f"  {ticker}: {qty}株 @ {price:,.2f}円 = {value:,.2f}円")
            except Exception as e:
                logger.error(f"ポートフォリオファイルの読み込みエラー: {e}")
        
        trading_count += 1
        last_trading_time = datetime.now()
        logger.info(f"取引完了 (合計実行回数: {trading_count})")
        
        return True
    except Exception as e:
        logger.error(f"取引実行中にエラーが発生しました: {e}")
        traceback.print_exc()
        return False

def run_learning(demo_mode=False):
    """フィードバック学習を実行"""
    global last_learning_time, learning_count
    
    try:
        logger.info(f"フィードバック学習を実行中... (実行回数: {learning_count + 1})")
        
        # デモモードの場合は警告を表示
        if demo_mode:
            logger.warning("デモモードでの学習は実行されません。デモモードのデータは実際の市場データではないため、AIモデルの学習には適していません。")
            logger.info("実際の学習を行うには通常モードで実行してください。")
            learning_count += 1
            last_learning_time = datetime.now()
            return True
        
        # 強制学習モードは3回に1回
        force_train = (learning_count % 3 == 0)
        force_param = "--force-train" if force_train else ""
        
        # 学習期間は直近1-10日間（ランダム）でバリエーションを持たせる
        days = random.randint(1, 10)
        
        # 本番データのみを対象とするフラグを追加
        real_data_only = "--real-data-only"
        
        cmd = f"python -m src.trading.feedback_learner --collect-days {days} {force_param} {real_data_only}"
        logger.info(f"コマンド実行: {cmd}")
        
        os.system(cmd)  # 実行
        
        learning_count += 1
        last_learning_time = datetime.now()
        logger.info(f"学習完了 (合計実行回数: {learning_count})")
        
        return True
    except Exception as e:
        logger.error(f"学習実行中にエラーが発生しました: {e}")
        traceback.print_exc()
        return False

def record_daily_stats():
    """日次パフォーマンスを記録"""
    global daily_stats, day_start_portfolio, current_portfolio
    
    if day_start_portfolio is None or current_portfolio is None:
        return
    
    try:
        # 日次の損益を計算
        start_value = day_start_portfolio.get('portfolio_value', 0)
        end_value = current_portfolio.get('portfolio_value', 0)
        daily_profit = end_value - start_value
        daily_profit_pct = (daily_profit / start_value * 100) if start_value > 0 else 0
        
        daily_stat = {
            'date': datetime.now().strftime('%Y-%m-%d'),
            'start_value': start_value,
            'end_value': end_value,
            'profit': daily_profit,
            'profit_pct': daily_profit_pct,
            'trades': trading_count
        }
        
        daily_stats.append(daily_stat)
        
        # 統計情報をJSONに保存
        stats_file = f"results/daily_stats_{datetime.now().strftime('%Y%m%d')}.json"
        with open(stats_file, 'w') as f:
            json.dump({
                'stats': daily_stats,
                'updated_at': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }, f, indent=2)
            
        logger.info(f"日次統計を保存しました: {stats_file}")
        logger.info(f"本日の損益: {daily_profit:,.2f}円 ({daily_profit_pct:.2f}%)")
    except Exception as e:
        logger.error(f"日次統計の記録中にエラーが発生しました: {e}")

def get_next_execution_time(current_time, is_trading=True):
    """次の実行時間を決定"""
    if is_trading:
        # 取引は市場時間内の場合5分間隔、市場時間外は30分間隔
        if is_market_hours():
            return current_time + timedelta(minutes=5)
        else:
            return current_time + timedelta(minutes=30)
    else:
        # 学習は1-2時間間隔（市場時間外の方が頻度を上げる）
        if is_market_hours():
            return current_time + timedelta(hours=2)
        else:
            return current_time + timedelta(hours=1)

def initialize_system():
    """システムの初期化"""
    global current_portfolio
    
    # 必要なディレクトリを作成
    directories = ['results', 'models', 'reports', 'logs']
    for directory in directories:
        os.makedirs(directory, exist_ok=True)
    
    # 初期ポートフォリオ情報を読み込み
    portfolio_files = sorted([f for f in os.listdir('results') if f.startswith('portfolio_')], reverse=True)
    if portfolio_files:
        latest_file = os.path.join('results', portfolio_files[0])
        try:
            with open(latest_file, 'r') as f:
                current_portfolio = json.load(f)
            logger.info(f"最新ポートフォリオを読み込みました: {latest_file}")
        except Exception as e:
            logger.error(f"ポートフォリオファイルの読み込みエラー: {e}")
    
    logger.info("システムの初期化が完了しました")

def main():
    """メイン実行関数"""
    global running
    
    parser = argparse.ArgumentParser(description='継続的AIトレーディングシステム')
    parser.add_argument('--initial-balance', type=float, default=1000000, help='初期資金')
    parser.add_argument('--max-stocks', type=int, default=5, help='最大保有銘柄数')
    parser.add_argument('--demo-mode', action='store_true', help='市場時間に関係なく実行するデモモード')
    args = parser.parse_args()
    
    # シグナルハンドラを設定（Ctrl+Cで安全に終了）
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    logger.info("=" * 70)
    logger.info("継続的AIトレーディングシステムを開始します")
    logger.info("=" * 70)
    logger.info(f"初期資金: {args.initial_balance:,.0f}円")
    logger.info(f"最大保有銘柄数: {args.max_stocks}")
    logger.info(f"デモモード: {'有効' if args.demo_mode else '無効'}")
    logger.info(f"ログファイル: {log_file}")
    logger.info("=" * 70)
    
    # システムを初期化
    initialize_system()
    
    # 次の実行予定時刻
    next_trading_time = datetime.now()
    next_learning_time = datetime.now() + timedelta(minutes=30)  # 学習は30分後から開始
    
    # メインループ
    while running:
        try:
            now = datetime.now()
            
            # 取引実行タイミング
            if now >= next_trading_time:
                if args.demo_mode or is_market_hours():
                    run_trading()
                else:
                    logger.info("市場時間外のため取引をスキップします")
                
                next_trading_time = get_next_execution_time(now, True)
                logger.info(f"次の取引予定時刻: {next_trading_time.strftime('%Y-%m-%d %H:%M:%S')}")
            
            # 学習実行タイミング
            if now >= next_learning_time:
                # 学習は市場時間外の方が優先度が高い
                should_learn = not is_market_hours() or args.demo_mode or random.random() > 0.5
                
                if should_learn:
                    run_learning(demo_mode=args.demo_mode)
                else:
                    logger.info("市場時間中のため学習を延期します")
                
                next_learning_time = get_next_execution_time(now, False)
                logger.info(f"次の学習予定時刻: {next_learning_time.strftime('%Y-%m-%d %H:%M:%S')}")
            
            # スリープ（30秒ごとにチェック）
            for _ in range(6):  # 30秒 = 5秒 x 6
                if not running:
                    break
                time.sleep(5)
                
        except Exception as e:
            logger.error(f"メインループでエラーが発生しました: {e}")
            traceback.print_exc()
            time.sleep(30)  # エラー発生時は30秒待機
    
    # 終了処理
    record_daily_stats()
    logger.info("継続的AIトレーディングシステムを終了します")

if __name__ == "__main__":
    main()
