# Nikkei225 AI Trading System Environment Variables

# KabuStation API Configuration
KABU_API_PWD=your_kabu_password_here
KABU_API_URL=http://localhost:18080/kabusapi

# Trading Configuration
TRADING_MODE=paper  # nocash, paper, dryrun, live
INITIAL_BALANCE=1000000
MAX_STOCKS=5

# System Configuration
GPU_ENABLED=false
LOG_LEVEL=INFO
DATA_UPDATE_INTERVAL=3600

# Web Interface
WEB_HOST=localhost
WEB_PORT=5000
WEB_DEBUG=false

# Data Sources
USE_CACHE=true
CACHE_EXPIRY_HOURS=24
API_RATE_LIMIT=true

# Backup and Recovery
AUTO_BACKUP=true
BACKUP_INTERVAL_HOURS=24
