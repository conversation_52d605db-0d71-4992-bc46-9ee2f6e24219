#!/bin/bash
# kabuステーションAPI取引システム起動スクリプト

# カレントディレクトリをスクリプトのあるディレクトリに変更
cd "$(dirname "$0")" || exit

# 必要なディレクトリの確認
mkdir -p logs
mkdir -p results/kabu

# 環境変数の設定確認
if [ -z "$KABU_API_PWD" ]; then
    echo "エラー: 環境変数KABU_API_PWDが設定されていません。"
    echo "export KABU_API_PWD='あなたのkabuステーションAPIパスワード' を実行してください。"
    exit 1
fi

# 引数のデフォルト値
MODE=""
INITIAL_BALANCE=1000000
MAX_STOCKS=5

# コマンドライン引数の解析
while [[ $# -gt 0 ]]; do
    key="$1"
    case $key in
        --mode|-m)
            MODE="$2"
            shift
            shift
            ;;
        --initial-balance|-b)
            INITIAL_BALANCE="$2"
            shift
            shift
            ;;
        --max-stocks|-s)
            MAX_STOCKS="$2"
            shift
            shift
            ;;
        --help|-h)
            echo "使用方法: $0 [オプション]"
            echo ""
            echo "オプション:"
            echo "  -m, --mode MODE              取引モード (nocash, paper, live)"
            echo "  -b, --initial-balance AMOUNT 初期資金"
            echo "  -s, --max-stocks NUMBER      最大保有銘柄数"
            echo "  -h, --help                   ヘルプメッセージの表示"
            exit 0
            ;;
        *)
            echo "不明なオプション: $key"
            exit 1
            ;;
    esac
done

# モードが指定されていない場合は現在の設定を使用
if [ -z "$MODE" ]; then
    echo "現在のモード設定を使用します"
    COMMAND="python -m src.trading.tradectl start -b $INITIAL_BALANCE -s $MAX_STOCKS"
else
    echo "モードを $MODE に設定します"
    COMMAND="python -m src.trading.tradectl start -m $MODE -b $INITIAL_BALANCE -s $MAX_STOCKS"
fi

# 起動コマンドの表示と実行
echo "実行コマンド: $COMMAND"
echo "開始時刻: $(date)"
echo "Ctrl+Cで停止します"

# コマンド実行
eval "$COMMAND"

# 終了処理
echo "終了時刻: $(date)"
