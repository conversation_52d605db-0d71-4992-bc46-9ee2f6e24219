#!/usr/bin/env python3
"""
日経225 AI取引システム - シンプル仮想取引実行スクリプト

追加学習なしで仮想取引を実行します。
ルールベースの戦略を使用した単純なシミュレーションを行います。
このスクリプトはテストモード（デフォルト）で実行され、取引結果は学習パイプラインに取り込まれません。
"""

import os
import sys
import argparse
import logging
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import json
import time

# 自作モジュールのインポート
from src.data_collection_helper import fetch_stock_data
from src.simple_virtual_trader import SimpleVirtualTrader
from src.feature_engineering import add_features
from src.trading.trading_strategy import TradingStrategy
from src.trading.feedback_learner import FeedbackLearner

# ロギング設定
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f"logs/simple_virtual_trading_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class SimpleVirtualTradingSystem:
    """シンプル仮想取引システム"""
    
    def __init__(self, config):
        """
        初期化
        
        Parameters:
        -----------
        config : dict
            設定情報
        """
        self.config = config
        self.results_dir = config.get("results_dir", "results/simple_trading")
        self.test_mode = config.get("test_mode", True)  # デフォルトでテストモード
        
        # 結果ディレクトリ作成
        os.makedirs(self.results_dir, exist_ok=True)
        
        # 仮想取引インスタンス作成
        self.trader = SimpleVirtualTrader(
            initial_balance=config.get("initial_balance", 1000000),
            commission_rate=config.get("commission_rate", 0.0001),
            results_dir=self.results_dir
        )
        
        # 取引戦略
        self.strategy = TradingStrategy()
        
        # ティッカーリスト
        self.tickers = config.get("tickers", [
            "7203.T", "9984.T", "6758.T", "6861.T", "8306.T",
            "9432.T", "9433.T", "6501.T", "6502.T", "7267.T"
        ])
        
        # フィードバックラーナー（テストモード設定）
        self.feedback_learner = FeedbackLearner(results_dir=self.results_dir)
        self.feedback_learner.set_test_mode(self.test_mode)
        
        logger.info(f"SimpleVirtualTradingSystem initialized with {len(self.tickers)} tickers")
        logger.info(f"Mode: {'TEST' if self.test_mode else 'PRODUCTION'}")
        logger.info(f"Initial balance: {self.trader.initial_balance:,.0f} JPY")
    
    def fetch_market_data(self):
        """
        市場データの取得
        
        Returns:
        --------
        pd.DataFrame
            取得したデータ
        """
        logger.info("Fetching market data...")
        
        # 日付範囲設定
        end_date = datetime.now().strftime("%Y-%m-%d")
        start_date = (datetime.now() - timedelta(days=self.config.get("data_days", 14))).strftime("%Y-%m-%d")
        interval = self.config.get("data_interval", "1d")
        
        # データ取得
        try:
            df = fetch_stock_data(self.tickers, start_date, end_date, interval)
            logger.info(f"Fetched data: {len(df)} rows")
            
            if df.empty:
                logger.error("No data fetched")
                return None
                
            return df
        except Exception as e:
            logger.error(f"Error fetching market data: {str(e)}")
            return None
    
    def prepare_data(self, df):
        """
        データ準備（特徴量エンジニアリング）
        
        Parameters:
        -----------
        df : pd.DataFrame
            元データ
        
        Returns:
        --------
        pd.DataFrame
            特徴量追加後のデータ
        """
        logger.info("Preparing data...")
        
        try:
            # 特徴量追加
            features_df = add_features(df)
            logger.info(f"Added features: {len([c for c in features_df.columns if c not in df.columns])} new columns")
            
            return features_df
        except Exception as e:
            logger.error(f"Error preparing data: {str(e)}")
            return df  # エラー時は元のデータを返す
    
    def generate_signals(self, df):
        """
        取引シグナル生成
        
        Parameters:
        -----------
        df : pd.DataFrame
            特徴量データ
            
        Returns:
        --------
        dict
            ティッカーごとのスコア
        """
        logger.info("Generating trading signals...")
        
        # 最新データ（ティッカーごとに最後の行）を取得
        latest_data = {}
        ticker_scores = {}
        
        # ティッカーごとの取引シグナル生成
        for ticker in self.tickers:
            ticker_df = df[df["Ticker"] == ticker].copy() if "Ticker" in df.columns else df.copy()
            
            if ticker_df.empty:
                logger.warning(f"No data for {ticker}")
                continue
                
            latest = ticker_df.iloc[-1]
            latest_data[ticker] = latest
            
            # シンプルなルールベースのスコアリング
            score = 0
            
            # トレンド指標
            if "SMA_5" in latest and "SMA_20" in latest:
                if latest["SMA_5"] > latest["SMA_20"]:
                    score += 1
                else:
                    score -= 1
            
            # RSI指標
            if "RSI_14" in latest:
                if latest["RSI_14"] < 30:  # 買われすぎ
                    score += 2
                elif latest["RSI_14"] > 70:  # 売られすぎ
                    score -= 2
            
            # MACD指標
            if "MACD" in latest and "MACD_Signal" in latest:
                if latest["MACD"] > latest["MACD_Signal"]:
                    score += 1
                else:
                    score -= 1
            
            # ボリンジャーバンド
            if "BB_Lower" in latest and "Close" in latest and "BB_Upper" in latest:
                if latest["Close"] < latest["BB_Lower"]:  # 下限を下回る（買い）
                    score += 2
                elif latest["Close"] > latest["BB_Upper"]:  # 上限を上回る（売り）
                    score -= 2
            
            # 最終スコア（-5〜+5）
            ticker_scores[ticker] = max(-5, min(5, score)) / 5  # -1.0〜+1.0に正規化
            
        logger.info(f"Generated scores for {len(ticker_scores)} tickers")
        return ticker_scores
    
    def execute_trading(self, ticker_scores, market_data):
        """
        取引実行
        
        Parameters:
        -----------
        ticker_scores : dict
            ティッカーごとのスコア
        market_data : pd.DataFrame
            市場データ
        """
        logger.info("Executing trades...")
        
        # 現在価格の取得
        latest_prices = {}
        for ticker in self.tickers:
            ticker_df = market_data[market_data["Ticker"] == ticker].copy() if "Ticker" in market_data.columns else market_data.copy()
            
            if not ticker_df.empty:
                latest_prices[ticker] = ticker_df["Close"].iloc[-1]
        
        # 現在のポートフォリオ状態
        logger.info(f"Current balance: {self.trader.balance:,.0f} JPY")
        logger.info(f"Current portfolio: {len(self.trader.portfolio)} stocks")
        
        # ポートフォリオ価値
        portfolio_value = self.trader.calculate_portfolio_value(latest_prices)
        profit_loss, profit_loss_pct = self.trader.get_profit_loss(portfolio_value)
        
        logger.info(f"Portfolio value: {portfolio_value:,.0f} JPY")
        logger.info(f"P&L: {profit_loss:,.0f} JPY ({profit_loss_pct:.2f}%)")
        
        # 取引前のポートフォリオを保存
        timestamp = datetime.now()
        timestamp_str = timestamp.strftime("%Y%m%d_%H%M")
        
        # メタデータを作成（テストモードを含む）
        metadata = {
            "timestamp": timestamp.isoformat(),
            "test_mode": self.test_mode,
            "source": "simple_trading",
            "data_days": self.config.get("data_days", 14),
            "data_interval": self.config.get("data_interval", "1d")
        }
        
        # 取引前ポートフォリオ保存
        before_portfolio_file = os.path.join(self.results_dir, f"portfolio_before_{timestamp_str}.json")
        before_data = {
            "balance": self.trader.balance,
            "portfolio": self.trader.portfolio,
            "portfolio_value": portfolio_value,
            "initial_balance": self.trader.initial_balance,
            "profit_loss": profit_loss,
            "profit_loss_pct": profit_loss_pct,
            "metadata": metadata
        }
        
        with open(before_portfolio_file, "w") as f:
            json.dump(before_data, f, indent=2, default=str)
        logger.info(f"Saved portfolio before trading to {before_portfolio_file}")
        
        # 全ての保有銘柄について売却判断
        sold_tickers = []
        
        for ticker, position in list(self.trader.portfolio.items()):
            score = ticker_scores.get(ticker, 0)
            price = latest_prices.get(ticker)
            
            if price is None:
                logger.warning(f"No price data for {ticker}, skipping sell decision")
                continue
            
            # 売り条件: スコアが低いか、一定の利益確定条件に達した場合
            sell_threshold = self.config.get("sell_threshold", -0.3)
            profit_taking = self.config.get("profit_taking", 0.05)  # 5%の利益で確定
            stop_loss = self.config.get("stop_loss", -0.05)  # 5%の損失でカット
            
            # 現在の損益計算
            cost_basis = position["price"]
            profit_pct = (price - cost_basis) / cost_basis
            
            sell_decision = False
            sell_reason = ""
            
            if score <= sell_threshold:
                sell_decision = True
                sell_reason = "Low score"
            elif profit_pct >= profit_taking:
                sell_decision = True
                sell_reason = "Profit taking"
            elif profit_pct <= stop_loss:
                sell_decision = True
                sell_reason = "Stop loss"
            
            if sell_decision:
                try:
                    qty = position["qty"]
                    logger.info(f"Selling {ticker}: {qty} shares at {price:,.2f} ({sell_reason}, P&L: {profit_pct:.2f}%)")
                    self.trader.sell_stock(ticker, price, qty, timestamp)
                    sold_tickers.append(ticker)
                except Exception as e:
                    logger.error(f"Error selling {ticker}: {str(e)}")
        
        # 購入候補の選定（スコアが高い順）
        buy_candidates = [(t, s) for t, s in ticker_scores.items() if s > 0 and t not in self.trader.portfolio]
        buy_candidates.sort(key=lambda x: x[1], reverse=True)
        
        # 最大保有銘柄数の設定
        max_positions = self.config.get("max_positions", 5)
        max_allocation = self.config.get("max_allocation", 0.2)  # 最大配分（20%）
        
        # 購入可能な銘柄数
        available_slots = max_positions - len(self.trader.portfolio)
        
        if available_slots > 0 and buy_candidates:
            # 購入予算の計算（現金の範囲内）
            total_budget = self.trader.balance * self.config.get("cash_allocation", 0.8)  # 現金の80%を使用
            budget_per_stock = total_budget / min(available_slots, len(buy_candidates))
            
            # 購入実行
            for ticker, score in buy_candidates[:available_slots]:
                price = latest_prices.get(ticker)
                
                if price is None:
                    logger.warning(f"No price data for {ticker}, skipping buy")
                    continue
                
                # スコアに基づいた配分調整（0〜max_allocation）
                allocation = max_allocation * score
                position_budget = min(budget_per_stock, self.trader.balance * allocation)
                
                # 数量計算
                qty = int(position_budget / price)
                
                if qty > 0:
                    try:
                        logger.info(f"Buying {ticker}: {qty} shares at {price:,.2f} (Score: {score:.2f})")
                        self.trader.buy_stock(ticker, price, qty, timestamp)
                    except Exception as e:
                        logger.error(f"Error buying {ticker}: {str(e)}")
        
        # 取引後のポートフォリオ価値
        portfolio_value_after = self.trader.calculate_portfolio_value(latest_prices)
        profit_loss_after, profit_loss_pct_after = self.trader.get_profit_loss(portfolio_value_after)
        
        # 取引後ポートフォリオ保存
        after_portfolio_file = os.path.join(self.results_dir, f"portfolio_after_{timestamp_str}.json")
        after_data = {
            "balance": self.trader.balance,
            "portfolio": self.trader.portfolio,
            "portfolio_value": portfolio_value_after,
            "initial_balance": self.trader.initial_balance,
            "profit_loss": profit_loss_after,
            "profit_loss_pct": profit_loss_pct_after,
            "metadata": metadata
        }
        
        with open(after_portfolio_file, "w") as f:
            json.dump(after_data, f, indent=2, default=str)
        logger.info(f"Saved portfolio after trading to {after_portfolio_file}")
        
        # 取引履歴保存
        trades_file = os.path.join(self.results_dir, f"trades_{timestamp_str}.csv")
        trades_df = pd.DataFrame(self.trader.trades)
        trades_df.to_csv(trades_file, index=False)
        logger.info(f"Saved trades to {trades_file}")
        
        # メタデータファイルも保存（テストモードが明確にわかるように）
        metadata_file = os.path.join(self.results_dir, f"metadata_{timestamp_str}.json")
        with open(metadata_file, "w") as f:
            json.dump(metadata, f, indent=2, default=str)
        logger.info(f"Saved metadata to {metadata_file}")
        
        # フィードバックラーナーにデータを渡す
        # テストモードの場合はスキップされる（ラーナー内部で判断）
        self.feedback_learner.collect_feedback_data(before_portfolio_file, after_portfolio_file, metadata)
        
        # 取引後の状態
        logger.info(f"Portfolio after trading: {len(self.trader.portfolio)} stocks")
        logger.info(f"Remaining balance: {self.trader.balance:,.0f} JPY")
    
    def run_simulation(self):
        """
        シミュレーションの実行
        """
        logger.info("Starting simple virtual trading simulation...")
        logger.info(f"Mode: {'TEST' if self.test_mode else 'PRODUCTION'}")
        
        # 市場データ取得
        market_data = self.fetch_market_data()
        if market_data is None:
            logger.error("Failed to fetch market data. Aborting simulation.")
            return False
        
        # データ準備
        processed_data = self.prepare_data(market_data)
        
        # シグナル生成
        ticker_scores = self.generate_signals(processed_data)
        
        # 取引実行
        self.execute_trading(ticker_scores, market_data)
        
        logger.info("Simulation completed successfully.")
        return True

def main():
    """メイン関数"""
    parser = argparse.ArgumentParser(description="シンプル仮想取引（追加学習なし）")
    parser.add_argument("--config", type=str, default="config/simple_trading_config.json",
                      help="設定ファイルのパス")
    parser.add_argument("--balance", type=float, default=1000000,
                      help="初期資金")
    parser.add_argument("--commission", type=float, default=0.0001,
                      help="取引手数料率")
    parser.add_argument("--interval", type=str, default="1d",
                      help="データ間隔 (1d, 1h, etc.)")
    parser.add_argument("--days", type=int, default=14,
                      help="取得する過去データの日数")
    parser.add_argument("--production", action="store_true",
                      help="本番モードで実行（デフォルトはテストモード）")
    args = parser.parse_args()
    
    # 設定ファイルの読み込み
    config = {
        "initial_balance": args.balance,
        "commission_rate": args.commission,
        "data_interval": args.interval,
        "data_days": args.days,
        "results_dir": "results/simple_trading",
        "max_positions": 5,
        "cash_allocation": 0.8,  # 現金の80%を使用
        "max_allocation": 0.2,  # 1銘柄あたり最大20%配分
        "sell_threshold": -0.3,  # 売り判断しきい値
        "profit_taking": 0.05,  # 5%の利益で確定
        "stop_loss": -0.05,  # 5%の損失でカット
        "test_mode": not args.production,  # --productionが指定された場合はFalse（本番モード）
        "tickers": [
            "7203.T", "9984.T", "6758.T", "6861.T", "8306.T",
            "9432.T", "9433.T", "6501.T", "6502.T", "7267.T",
            "8316.T", "4063.T", "6752.T", "4519.T", "4568.T",
            "6701.T", "7741.T", "6594.T", "4503.T", "9202.T"
        ]
    }
    
    # 設定ファイルが存在する場合は読み込み
    if os.path.exists(args.config):
        try:
            with open(args.config, 'r') as f:
                file_config = json.load(f)
                config.update(file_config)
        except Exception as e:
            logger.error(f"設定ファイルのロードエラー: {e}")
    
    # 実行モードを表示
    mode_str = "テストモード" if config["test_mode"] else "本番モード（学習あり）"
    logger.info(f"実行モード: {mode_str}")
    print(f"==== 実行モード: {mode_str} ====")
    
    # システム実行
    system = SimpleVirtualTradingSystem(config)
    system.run_simulation()

if __name__ == "__main__":
    main()
