#!/usr/bin/env python3
"""
International Multi-Market Nikkei 225 Trading System

This script orchestrates the complete international multi-market trading system
that collects data from global markets, analyzes correlations, and makes
enhanced predictions for Nikkei 225 stocks.

Usage:
    python international_trading_system.py [--mode MODE] [--config CONFIG]
    
Modes:
    - setup: Initial setup and data collection
    - collect: Collect international market data
    - analyze: Perform correlation analysis
    - train: Train enhanced ML models
    - trade: Execute trading with enhanced models
    - test: Run comprehensive tests
    - all: Run complete pipeline
"""

import argparse
import logging
import json
import sys
from pathlib import Path
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

# Add src to path
sys.path.append('src')

from data_collector.international_collector import InternationalDataCollector
from analysis.correlation_analyzer import InternationalCorrelationAnalyzer
from enhanced_model import EnhancedNikkeiModel

class InternationalTradingSystem:
    """
    Main orchestrator for the international multi-market trading system.
    """
    
    def __init__(self, config_path: str = "config/international_markets_config.json"):
        self.config_path = Path(config_path)
        self.logger = self._setup_logging()
        
        # Initialize components
        self.data_collector = None
        self.correlation_analyzer = None
        self.enhanced_model = None
        
        # System state
        self.setup_complete = False
        self.data_collected = False
        self.analysis_complete = False
        self.models_trained = False
        
    def _setup_logging(self):
        """Setup comprehensive logging."""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('logs/international_trading_system.log'),
                logging.StreamHandler()
            ]
        )
        
        # Create logs directory if it doesn't exist
        Path('logs').mkdir(exist_ok=True)
        
        return logging.getLogger('InternationalTradingSystem')
    
    def setup_system(self):
        """Initial system setup and validation."""
        self.logger.info("Setting up International Multi-Market Trading System")
        
        # Validate configuration
        if not self.config_path.exists():
            self.logger.error(f"Configuration file not found: {self.config_path}")
            return False
        
        try:
            with open(self.config_path, 'r') as f:
                config = json.load(f)
            
            # Validate config structure
            required_sections = ['international_markets', 'forex_pairs', 'collection_settings']
            for section in required_sections:
                if section not in config:
                    self.logger.error(f"Missing configuration section: {section}")
                    return False
            
            self.logger.info("Configuration validation successful")
            
        except Exception as e:
            self.logger.error(f"Configuration validation failed: {e}")
            return False
        
        # Initialize components
        try:
            self.data_collector = InternationalDataCollector(str(self.config_path))
            self.correlation_analyzer = InternationalCorrelationAnalyzer(str(self.config_path))
            self.enhanced_model = EnhancedNikkeiModel(str(self.config_path))
            
            self.logger.info("Component initialization successful")
            
        except Exception as e:
            self.logger.error(f"Component initialization failed: {e}")
            return False
        
        # Create necessary directories
        directories = [
            'international_data',
            'analysis_results',
            'models/enhanced',
            'results/enhanced',
            'logs'
        ]
        
        for directory in directories:
            Path(directory).mkdir(parents=True, exist_ok=True)
        
        self.setup_complete = True
        self.logger.info("System setup completed successfully")
        return True
    
    def collect_international_data(self, period: str = "1y", interval: str = "1h"):
        """
        Collect international market data.
        
        Args:
            period: Data collection period
            interval: Data collection interval
        """
        if not self.setup_complete:
            self.logger.error("System setup required before data collection")
            return False
        
        self.logger.info(f"Starting international data collection (period={period}, interval={interval})")
        
        try:
            # Collect all international data
            all_data = self.data_collector.collect_all_data(period=period, interval=interval)
            
            # Validate collected data
            total_datasets = sum(len(datasets) for datasets in all_data.values())
            
            if total_datasets == 0:
                self.logger.warning("No international data collected")
                return False
            
            self.logger.info(f"International data collection completed: {total_datasets} datasets")
            
            # Generate collection summary
            summary = {
                'collection_time': datetime.now().isoformat(),
                'period': period,
                'interval': interval,
                'datasets_by_type': {
                    data_type: len(datasets) for data_type, datasets in all_data.items()
                },
                'total_datasets': total_datasets
            }
            
            with open('international_data/collection_summary.json', 'w') as f:
                json.dump(summary, f, indent=2)
            
            self.data_collected = True
            return True
            
        except Exception as e:
            self.logger.error(f"International data collection failed: {e}")
            return False
    
    def analyze_correlations(self):
        """Perform comprehensive correlation analysis."""
        if not self.data_collected:
            self.logger.warning("International data not collected, attempting to load existing data")
        
        self.logger.info("Starting correlation analysis")
        
        try:
            # Generate comprehensive correlation report
            report = self.correlation_analyzer.generate_comprehensive_report()
            
            if not report:
                self.logger.error("Correlation analysis failed - no data or analysis errors")
                return False
            
            # Extract key insights
            key_findings = report.get('key_findings', [])
            recommendations = report.get('recommendations', [])
            
            self.logger.info("Correlation Analysis Results:")
            for finding in key_findings:
                self.logger.info(f"  - {finding}")
            
            self.logger.info("Recommendations:")
            for recommendation in recommendations:
                self.logger.info(f"  - {recommendation}")
            
            self.analysis_complete = True
            return True
            
        except Exception as e:
            self.logger.error(f"Correlation analysis failed: {e}")
            return False
    
    def train_enhanced_models(self):
        """Train enhanced ML models with international features."""
        if not self.analysis_complete:
            self.logger.warning("Correlation analysis not complete, proceeding with available data")
        
        self.logger.info("Starting enhanced model training")
        
        try:
            # Load data
            self.enhanced_model.load_data()
            
            # Create enhanced features
            features = self.enhanced_model.create_enhanced_features()
            
            if features.empty:
                self.logger.error("No features created - check data availability")
                return False
            
            self.logger.info(f"Created {features.shape[1]} features from {len(features)} samples")
            
            # Prepare training data
            X, y = self.enhanced_model.prepare_training_data()
            
            # Train models
            self.enhanced_model.train_models(X, y)
            
            # Generate model report
            model_report = self.enhanced_model.generate_model_report()
            
            # Display results
            best_model_name = model_report.get('best_model')
            if best_model_name:
                self.logger.info(f"Best performing model: {best_model_name}")
                
                performance = self.enhanced_model.performance_metrics.get(best_model_name, {})
                self.logger.info(f"Model performance - MSE: {performance.get('mse', 'N/A'):.6f}, "
                               f"R2: {performance.get('r2', 'N/A'):.4f}")
            
            # Display international data impact
            impact_analysis = model_report.get('international_data_impact', {})
            if impact_analysis:
                self.logger.info("International feature importance:")
                for model_name, impact in impact_analysis.items():
                    forex_impact = impact.get('forex_features', 0) * 100
                    index_impact = impact.get('index_features', 0) * 100
                    commodity_impact = impact.get('commodity_features', 0) * 100
                    
                    self.logger.info(f"  {model_name}: Forex {forex_impact:.1f}%, "
                                   f"Indices {index_impact:.1f}%, "
                                   f"Commodities {commodity_impact:.1f}%")
            
            self.models_trained = True
            return True
            
        except Exception as e:
            self.logger.error(f"Enhanced model training failed: {e}")
            return False
    
    def execute_trading(self, mode: str = "simulation"):
        """
        Execute trading with enhanced models.
        
        Args:
            mode: Trading mode (simulation, paper, live)
        """
        if not self.models_trained:
            self.logger.error("Enhanced models not trained")
            return False
        
        self.logger.info(f"Starting enhanced trading in {mode} mode")
        
        try:
            # Get best model
            best_model_name, best_model = self.enhanced_model.get_best_model()
            
            if best_model is None:
                self.logger.error("No trained models available")
                return False
            
            self.logger.info(f"Using {best_model_name} for trading predictions")
            
            # Integration with existing trading system would go here
            # For now, demonstrate prediction capability
            
            # Load latest data for prediction
            self.enhanced_model.load_data()
            latest_features = self.enhanced_model.create_enhanced_features()
            
            if len(latest_features) > 0:
                # Get latest feature vector
                latest_X = latest_features.iloc[[-1]].values
                
                # Make prediction
                prediction = self.enhanced_model.predict(latest_X, best_model_name)
                
                self.logger.info(f"Latest prediction: {prediction[0]:.6f}")
                
                # Save prediction
                prediction_data = {
                    'timestamp': datetime.now().isoformat(),
                    'model_used': best_model_name,
                    'prediction': float(prediction[0]),
                    'features_count': latest_features.shape[1],
                    'trading_mode': mode
                }
                
                with open('results/enhanced/latest_prediction.json', 'w') as f:
                    json.dump(prediction_data, f, indent=2)
                
                return True
            else:
                self.logger.error("No recent data available for prediction")
                return False
                
        except Exception as e:
            self.logger.error(f"Trading execution failed: {e}")
            return False
    
    def run_tests(self):
        """Run comprehensive system tests."""
        self.logger.info("Running comprehensive system tests")
        
        try:
            # Import and run tests
            import subprocess
            import sys
            
            result = subprocess.run([
                sys.executable, '-m', 'pytest', 
                'tests/test_international_system.py', 
                '-v'
            ], capture_output=True, text=True)
            
            if result.returncode == 0:
                self.logger.info("All tests passed successfully")
                return True
            else:
                self.logger.error(f"Tests failed:\n{result.stdout}\n{result.stderr}")
                return False
                
        except Exception as e:
            self.logger.error(f"Test execution failed: {e}")
            
            # Fallback: run basic validation
            self.logger.info("Running basic system validation")
            return self._run_basic_validation()
    
    def _run_basic_validation(self):
        """Run basic system validation."""
        try:
            # Test component initialization
            if not self.setup_system():
                return False
            
            # Test data structure creation
            test_features = self.enhanced_model._add_time_features(
                pd.DataFrame(index=pd.date_range('2023-01-01', periods=10))
            )
            
            if len(test_features.columns) < 4:
                self.logger.error("Time features creation failed")
                return False
            
            self.logger.info("Basic system validation passed")
            return True
            
        except Exception as e:
            self.logger.error(f"Basic validation failed: {e}")
            return False
    
    def run_complete_pipeline(self):
        """Run the complete international trading pipeline."""
        self.logger.info("Starting complete international trading pipeline")
        
        # Step 1: System setup
        if not self.setup_system():
            self.logger.error("Pipeline failed at setup stage")
            return False
        
        # Step 2: Data collection
        if not self.collect_international_data(period="3mo", interval="1h"):
            self.logger.error("Pipeline failed at data collection stage")
            return False
        
        # Step 3: Correlation analysis
        if not self.analyze_correlations():
            self.logger.error("Pipeline failed at correlation analysis stage")
            return False
        
        # Step 4: Model training
        if not self.train_enhanced_models():
            self.logger.error("Pipeline failed at model training stage")
            return False
        
        # Step 5: Trading execution (simulation mode)
        if not self.execute_trading(mode="simulation"):
            self.logger.error("Pipeline failed at trading execution stage")
            return False
        
        self.logger.info("Complete pipeline executed successfully!")
        
        # Generate final report
        self._generate_final_report()
        
        return True
    
    def _generate_final_report(self):
        """Generate final system report."""
        report = {
            'pipeline_completion_time': datetime.now().isoformat(),
            'system_status': {
                'setup_complete': self.setup_complete,
                'data_collected': self.data_collected,
                'analysis_complete': self.analysis_complete,
                'models_trained': self.models_trained
            },
            'components_initialized': {
                'data_collector': self.data_collector is not None,
                'correlation_analyzer': self.correlation_analyzer is not None,
                'enhanced_model': self.enhanced_model is not None
            }
        }
        
        # Add model performance if available
        if self.enhanced_model and hasattr(self.enhanced_model, 'performance_metrics'):
            best_model_name, _ = self.enhanced_model.get_best_model()
            if best_model_name:
                report['best_model'] = {
                    'name': best_model_name,
                    'performance': self.enhanced_model.performance_metrics.get(best_model_name, {})
                }
        
        with open('results/enhanced/final_pipeline_report.json', 'w') as f:
            json.dump(report, f, indent=2, default=str)
        
        self.logger.info("Final pipeline report saved to results/enhanced/final_pipeline_report.json")


def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(description='International Multi-Market Nikkei 225 Trading System')
    parser.add_argument('--mode', choices=['setup', 'collect', 'analyze', 'train', 'trade', 'test', 'all'],
                      default='all', help='Operation mode')
    parser.add_argument('--config', default='config/international_markets_config.json',
                      help='Configuration file path')
    parser.add_argument('--period', default='3mo', help='Data collection period')
    parser.add_argument('--interval', default='1h', help='Data collection interval')
    parser.add_argument('--trading-mode', default='simulation', 
                      choices=['simulation', 'paper', 'live'],
                      help='Trading execution mode')
    
    args = parser.parse_args()
    
    # Initialize system
    system = InternationalTradingSystem(config_path=args.config)
    
    success = False
    
    try:
        if args.mode == 'setup':
            success = system.setup_system()
        
        elif args.mode == 'collect':
            success = system.setup_system() and system.collect_international_data(
                period=args.period, interval=args.interval
            )
        
        elif args.mode == 'analyze':
            success = system.setup_system() and system.analyze_correlations()
        
        elif args.mode == 'train':
            success = (system.setup_system() and 
                      system.collect_international_data(period=args.period, interval=args.interval) and
                      system.analyze_correlations() and
                      system.train_enhanced_models())
        
        elif args.mode == 'trade':
            success = (system.setup_system() and
                      system.train_enhanced_models() and  # Will load existing if available
                      system.execute_trading(mode=args.trading_mode))
        
        elif args.mode == 'test':
            success = system.run_tests()
        
        elif args.mode == 'all':
            success = system.run_complete_pipeline()
        
        # Exit with appropriate code
        sys.exit(0 if success else 1)
        
    except KeyboardInterrupt:
        system.logger.info("Operation interrupted by user")
        sys.exit(1)
    except Exception as e:
        system.logger.error(f"Unexpected error: {e}")
        sys.exit(1)


if __name__ == '__main__':
    main()