#!/usr/bin/env python3
"""
日経225 AI取引システム - 日次取引結果表示スクリプト

このスクリプトは、指定した日付の取引結果を表示します。
テスト環境と本番環境の両方の結果を確認できます。
"""

import os
import sys
import json
import argparse
import logging
from datetime import datetime, timedelta
import pandas as pd
import glob

# ロギング設定
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def find_result_files(date_str=None, mode="all"):
    """
    指定した日付のリザルトファイルを検索

    Parameters:
    -----------
    date_str : str
        日付文字列（YYYYMMDD形式）。指定しない場合は今日の日付
    mode : str
        モード（"test", "production", "all"）

    Returns:
    --------
    dict
        モードごとのリザルトファイルのリスト
    """
    # 日付の設定
    if date_str:
        try:
            target_date = datetime.strptime(date_str, "%Y%m%d")
        except ValueError:
            logger.error(f"無効な日付形式: {date_str}（正しい形式: YYYYMMDD）")
            return None
    else:
        target_date = datetime.now()
    
    date_str = target_date.strftime("%Y%m%d")
    
    # 結果ディレクトリの設定
    result_dirs = []
    if mode == "test" or mode == "all":
        result_dirs.append("results/test")
        result_dirs.append("results/random_testing")
    if mode == "production" or mode == "all":
        result_dirs.append("results/production")
    
    # 日次結果ファイルのパターン
    daily_results_pattern = f"daily_results_{date_str}.json"
    portfolio_pattern = f"portfolio_*_{date_str}_*.json"
    trades_pattern = f"trades_*_{date_str}_*.csv"
    test_summary_pattern = f"test_summary_test_{date_str}_*.json"
    
    results = {
        "daily_results": [],
        "portfolio": [],
        "trades": [],
        "test_summary": []
    }
    
    # 各ディレクトリでファイルを検索
    for result_dir in result_dirs:
        if os.path.exists(result_dir):
            # 日次結果ファイル
            daily_results_files = glob.glob(os.path.join(result_dir, daily_results_pattern))
            results["daily_results"].extend(daily_results_files)
            
            # ポートフォリオファイル
            portfolio_files = glob.glob(os.path.join(result_dir, portfolio_pattern))
            results["portfolio"].extend(portfolio_files)
            
            # 取引履歴ファイル
            trades_files = glob.glob(os.path.join(result_dir, trades_pattern))
            results["trades"].extend(trades_files)
            
            # テスト概要ファイル
            summary_files = glob.glob(os.path.join(result_dir, test_summary_pattern))
            results["test_summary"].extend(summary_files)
    
    return results

def display_daily_results(date_str=None, mode="all", verbose=False):
    """
    日次取引結果を表示

    Parameters:
    -----------
    date_str : str
        日付文字列（YYYYMMDD形式）。指定しない場合は今日の日付
    mode : str
        モード（"test", "production", "all"）
    verbose : bool
        詳細表示するかどうか
    """
    # 結果ファイルの検索
    result_files = find_result_files(date_str, mode)
    if not result_files:
        logger.error("結果ファイルが見つかりませんでした")
        return
    
    # 日付を表示用に整形
    display_date = date_str
    if date_str:
        try:
            display_date = datetime.strptime(date_str, "%Y%m%d").strftime("%Y年%m月%d日")
        except ValueError:
            display_date = date_str
    else:
        display_date = datetime.now().strftime("%Y年%m月%d日")
    
    print(f"\n{'='*80}")
    print(f"{display_date}の取引結果")
    print(f"{'='*80}")
    
    # ファイルがない場合
    if not any(result_files.values()):
        print(f"\n指定された日付 ({display_date}) の取引結果は見つかりませんでした。")
        print("別の日付を指定するか、モードを変更してください。")
        return
    
    # 日次結果ファイルを表示
    if result_files["daily_results"]:
        for result_file in result_files["daily_results"]:
            try:
                with open(result_file, "r") as f:
                    results = json.load(f)
                
                # 環境名を取得（ファイルパスから）
                env_name = "テスト環境" if "test" in result_file.lower() else "本番環境"
                
                print(f"\n【{env_name}】日次結果")
                
                # 各日の結果を表示
                for date, daily_result in results.items():
                    # 日付を整形
                    try:
                        formatted_date = datetime.fromisoformat(date).strftime("%Y年%m月%d日")
                    except ValueError:
                        formatted_date = date
                    
                    print(f"\n日付: {formatted_date}")
                    print(f"取引前残高: {daily_result.get('starting_balance', 0):,.0f}円")
                    print(f"取引前ポートフォリオ価値: {daily_result.get('starting_portfolio_value', 0):,.0f}円")
                    print(f"取引後残高: {daily_result.get('ending_balance', 0):,.0f}円")
                    print(f"取引後ポートフォリオ価値: {daily_result.get('ending_portfolio_value', 0):,.0f}円")
                    print(f"日次損益: {daily_result.get('daily_profit', 0):,.0f}円 ({daily_result.get('daily_profit_pct', 0):.2f}%)")
                    
                    # 取引数
                    trade_count = len(daily_result.get("trades", []))
                    if trade_count > 0:
                        print(f"取引数: {trade_count}件")
                    else:
                        print("取引なし")
                    
                    # 詳細表示モードでは取引内容も表示
                    if verbose and trade_count > 0:
                        print("\n[取引詳細]")
                        for i, trade in enumerate(daily_result.get("trades", [])):
                            print(f"取引 {i+1}: {trade.get('action', '')} {trade.get('ticker', '')} "
                                  f"{trade.get('qty', 0)}株 @ {trade.get('price', 0):,.0f}円 "
                                  f"({trade.get('timestamp', '')})")
                            
                            # 損益があれば表示
                            if "profit" in trade:
                                print(f"  → 損益: {trade.get('profit', 0):,.0f}円")
            except Exception as e:
                logger.error(f"日次結果ファイル {result_file} の読み込みエラー: {str(e)}")
    
    # テスト概要ファイルがあれば表示
    if result_files["test_summary"]:
        for summary_file in result_files["test_summary"]:
            try:
                with open(summary_file, "r") as f:
                    summary = json.load(f)
                
                print(f"\n【テスト概要】({os.path.basename(summary_file)})")
                
                metadata = summary.get("metadata", {})
                test_type = metadata.get("test_type", "unknown")
                test_id = metadata.get("test_id", "unknown")
                
                print(f"テストID: {test_id}")
                print(f"テスト種別: {test_type}")
                print(f"開始時刻: {metadata.get('start_time', '')}")
                print(f"終了時刻: {metadata.get('end_time', '')}")
                print(f"実行時間: {metadata.get('duration', '')}")
                
                # トレード統計
                print(f"\n[取引統計]")
                print(f"取引総数: {summary.get('trade_count', 0)}件")
                print(f"利益取引: {summary.get('profit_trades', 0)}件")
                print(f"損失取引: {summary.get('loss_trades', 0)}件")
                print(f"収支均衡: {summary.get('break_even_trades', 0)}件")
                
                if summary.get('trade_count', 0) > 0:
                    print(f"総利益: {summary.get('total_profit', 0):,.0f}円")
                    print(f"平均利益: {summary.get('average_profit', 0):,.0f}円")
                    print(f"最大利益: {summary.get('max_profit', 0):,.0f}円")
                    print(f"最大損失: {summary.get('max_loss', 0):,.0f}円")
                
                # ポートフォリオ
                portfolio = summary.get("portfolio", {})
                print(f"\n[ポートフォリオ状態]")
                print(f"残高: {portfolio.get('balance', 0):,.0f}円")
                print(f"ポートフォリオ価値: {portfolio.get('portfolio_value', 0):,.0f}円")
                print(f"初期資金: {portfolio.get('initial_balance', 0):,.0f}円")
                print(f"損益: {portfolio.get('profit_loss', 0):,.0f}円 ({portfolio.get('profit_loss_pct', 0):.2f}%)")
                
                # 詳細表示モードでは銘柄ごとのパフォーマンスも表示
                if verbose and "ticker_performance" in summary:
                    print("\n[銘柄別パフォーマンス]")
                    ticker_perf = summary["ticker_performance"]
                    for ticker, perf in ticker_perf.items():
                        print(f"{ticker}: {perf.get('trade_count', 0)}回取引 "
                              f"(買: {perf.get('buy_count', 0)}回, 売: {perf.get('sell_count', 0)}回) "
                              f"損益: {perf.get('profit', 0):,.0f}円")
            except Exception as e:
                logger.error(f"テスト概要ファイル {summary_file} の読み込みエラー: {str(e)}")
    
    # 取引履歴ファイルがあって詳細表示モードなら表示
    if verbose and result_files["trades"]:
        for trades_file in result_files["trades"]:
            try:
                trades_df = pd.read_csv(trades_file)
                env_name = "テスト環境" if "test" in trades_file.lower() else "本番環境"
                
                print(f"\n【{env_name}】取引履歴 ({os.path.basename(trades_file)})")
                
                if not trades_df.empty:
                    # カラム名の大小文字違いに対応
                    # 一般的なカラム名とその可能性のあるバリエーション
                    column_variants = {
                        'ticker': ['ticker', 'Ticker', 'TICKER', 'symbol', 'Symbol'],
                        'action': ['action', 'Action', 'ACTION', 'type', 'Type'],
                        'price': ['price', 'Price', 'PRICE'],
                        'qty': ['qty', 'Qty', 'QTY', 'quantity', 'Quantity'],
                        'timestamp': ['timestamp', 'Timestamp', 'TIMESTAMP', 'date', 'Date', 'time', 'Time'],
                        'profit': ['profit', 'Profit', 'PROFIT', 'pl', 'PL', 'pnl', 'PnL']
                    }
                    
                    # 実際のカラム名をマッピング
                    column_mapping = {}
                    for standard, variants in column_variants.items():
                        for variant in variants:
                            if variant in trades_df.columns:
                                column_mapping[standard] = variant
                                break
                    
                    # 取引情報を表示
                    for i, row in trades_df.iterrows():
                        ticker = row[column_mapping.get('ticker')] if 'ticker' in column_mapping else 'N/A'
                        action = row[column_mapping.get('action')] if 'action' in column_mapping else 'N/A'
                        price = row[column_mapping.get('price')] if 'price' in column_mapping else 0
                        qty = row[column_mapping.get('qty')] if 'qty' in column_mapping else 0
                        timestamp = row[column_mapping.get('timestamp')] if 'timestamp' in column_mapping else 'N/A'
                        
                        print(f"{i+1}: {action} {ticker} {qty}株 @ {price:,.0f}円 ({timestamp})")
                        
                        # 損益情報があれば表示
                        if 'profit' in column_mapping and column_mapping['profit'] in row:
                            profit = row[column_mapping['profit']]
                            print(f"  → 損益: {profit:,.0f}円")
                else:
                    print("取引データがありません")
            except Exception as e:
                logger.error(f"取引履歴ファイル {trades_file} の読み込みエラー: {str(e)}")
    
    print(f"\n{'='*80}")

def main():
    """メイン関数"""
    parser = argparse.ArgumentParser(description="日次取引結果表示スクリプト")
    parser.add_argument("--date", type=str, default=None,
                      help="表示する日付（YYYYMMDD形式、デフォルトは今日）")
    parser.add_argument("--mode", type=str, choices=["test", "production", "all"], default="all",
                      help="表示するモード（test, production, all、デフォルトはall）")
    parser.add_argument("--verbose", "-v", action="store_true",
                      help="詳細情報を表示する")
    args = parser.parse_args()
    
    # デフォルトの日付は今日
    if args.date is None:
        args.date = datetime.now().strftime("%Y%m%d")
    
    # 日次結果の表示
    display_daily_results(args.date, args.mode, args.verbose)

if __name__ == "__main__":
    main()
