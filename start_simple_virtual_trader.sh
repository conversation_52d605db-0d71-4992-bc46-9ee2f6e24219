#!/bin/bash
# シンプル仮想トレーダー起動スクリプト

# カレントディレクトリをスクリプトのあるディレクトリに変更
cd "$(dirname "$0")" || exit

# 必要なディレクトリの作成
mkdir -p logs
mkdir -p results

# デフォルト設定
USE_CACHE=""

# コマンドライン引数の解析
while [[ $# -gt 0 ]]; do
    key="$1"
    case $key in
        --use-cache|-c)
            USE_CACHE="--use-cache"
            shift
            ;;
        --force-cache|-f)
            USE_CACHE="--force-cache"
            shift
            ;;
        --help|-h)
            echo "使用方法: $0 [オプション]"
            echo ""
            echo "オプション:"
            echo "  -c, --use-cache      キャッシュを使用"
            echo "  -f, --force-cache    キャッシュのみを使用（APIリクエストなし）"
            echo "  -h, --help           ヘルプメッセージの表示"
            exit 0
            ;;
        *)
            echo "不明なオプション: $key"
            exit 1
            ;;
    esac
done

# 実行コマンドの構築
COMMAND="python src/simple_virtual_trader.py $USE_CACHE"

echo "シンプル仮想トレーダーを開始します"
echo "実行コマンド: $COMMAND"
echo "開始時刻: $(date)"
echo ""

# コマンド実行
eval "$COMMAND"

# 終了処理
echo ""
echo "シンプル仮想トレーダーを終了しました"
echo "終了時刻: $(date)"